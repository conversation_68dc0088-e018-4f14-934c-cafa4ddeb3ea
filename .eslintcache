[{"/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/main.ts": "1", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/brandName.vue": "2", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/selectDecrPage.vue": "3", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/const.ts": "4", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/navLinkPage.vue": "5", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/navLinkage.vue": "6", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/imageConfiguration/index.vue": "7", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/imageConfiguration/setData.js": "8"}, {"size": 2319, "mtime": 1751266315286}, {"size": 6424, "mtime": 1751337289206, "results": "9", "hashOfConfig": "10"}, {"size": 10154, "mtime": 1751263553275}, {"size": 5781, "mtime": 1751526757465, "results": "11", "hashOfConfig": "12"}, {"size": 5912, "mtime": 1751527950985, "results": "13", "hashOfConfig": "10"}, {"size": 7911, "mtime": 1751526757466, "results": "14", "hashOfConfig": "10"}, {"size": 5865, "mtime": 1751526757467, "results": "15", "hashOfConfig": "10"}, {"size": 4804, "mtime": 1751526757468, "results": "16", "hashOfConfig": "17"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "hc54a", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "12gi7rj", {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1slf31v", "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/brandName.vue", ["36", "37", "38", "39", "40", "41", "42", "43", "44", "45"], [], "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/const.ts", ["46", "47", "48", "49", "50", "51", "52", "53", "54"], [], "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/components/navLinkPage.vue", ["55", "56", "57"], [], "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/cms/decoration/RightCpnts/navLinkage.vue", ["58", "59", "60", "61", "62"], [], "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/imageConfiguration/index.vue", [], [], "/Users/<USER>/Desktop/我店/code/MicroApp/woody-living-plat-new-marketing/src/pages/imageConfiguration/setData.js", [], [], {"ruleId": "63", "severity": 1, "message": "64", "line": 86, "column": 29, "nodeType": "65", "messageId": "66", "endLine": 86, "endColumn": 32, "suggestions": "67"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 87, "column": 31, "nodeType": "65", "messageId": "66", "endLine": 87, "endColumn": 34, "suggestions": "68"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 92, "column": 30, "nodeType": "65", "messageId": "66", "endLine": 92, "endColumn": 33, "suggestions": "69"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 93, "column": 31, "nodeType": "65", "messageId": "66", "endLine": 93, "endColumn": 34, "suggestions": "70"}, {"ruleId": "71", "severity": 1, "message": "72", "line": 118, "column": 7, "nodeType": "73", "messageId": "74", "endLine": 118, "endColumn": 20, "suggestions": "75"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 125, "column": 36, "nodeType": "65", "messageId": "66", "endLine": 125, "endColumn": 39, "suggestions": "76"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 147, "column": 33, "nodeType": "65", "messageId": "66", "endLine": 147, "endColumn": 36, "suggestions": "77"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 147, "column": 54, "nodeType": "65", "messageId": "66", "endLine": 147, "endColumn": 57, "suggestions": "78"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 167, "column": 32, "nodeType": "65", "messageId": "66", "endLine": 167, "endColumn": 35, "suggestions": "79"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 183, "column": 35, "nodeType": "65", "messageId": "66", "endLine": 183, "endColumn": 38, "suggestions": "80"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 14, "column": 28, "nodeType": "65", "messageId": "66", "endLine": 14, "endColumn": 31, "suggestions": "81"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 40, "column": 32, "nodeType": "65", "messageId": "66", "endLine": 40, "endColumn": 35, "suggestions": "82"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 65, "column": 29, "nodeType": "65", "messageId": "66", "endLine": 65, "endColumn": 32, "suggestions": "83"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 121, "column": 29, "nodeType": "65", "messageId": "66", "endLine": 121, "endColumn": 32, "suggestions": "84"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 155, "column": 30, "nodeType": "65", "messageId": "66", "endLine": 155, "endColumn": 33, "suggestions": "85"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 197, "column": 35, "nodeType": "65", "messageId": "66", "endLine": 197, "endColumn": 38, "suggestions": "86"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 253, "column": 32, "nodeType": "65", "messageId": "66", "endLine": 253, "endColumn": 35, "suggestions": "87"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 296, "column": 33, "nodeType": "65", "messageId": "66", "endLine": 296, "endColumn": 36, "suggestions": "88"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 322, "column": 33, "nodeType": "65", "messageId": "66", "endLine": 322, "endColumn": 36, "suggestions": "89"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 72, "column": 31, "nodeType": "65", "messageId": "66", "endLine": 72, "endColumn": 34, "suggestions": "90"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 73, "column": 31, "nodeType": "65", "messageId": "66", "endLine": 73, "endColumn": 34, "suggestions": "91"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 79, "column": 32, "nodeType": "65", "messageId": "66", "endLine": 79, "endColumn": 35, "suggestions": "92"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 127, "column": 12, "nodeType": "65", "messageId": "66", "endLine": 127, "endColumn": 15, "suggestions": "93"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 129, "column": 31, "nodeType": "65", "messageId": "66", "endLine": 129, "endColumn": 34, "suggestions": "94"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 132, "column": 28, "nodeType": "65", "messageId": "66", "endLine": 132, "endColumn": 31, "suggestions": "95"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 135, "column": 48, "nodeType": "65", "messageId": "66", "endLine": 135, "endColumn": 51, "suggestions": "96"}, {"ruleId": "71", "severity": 1, "message": "72", "line": 197, "column": 5, "nodeType": "73", "messageId": "74", "endLine": 197, "endColumn": 16, "suggestions": "97"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["98", "99"], ["100", "101"], ["102", "103"], ["104", "105"], "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["106"], ["107", "108"], ["109", "110"], ["111", "112"], ["113", "114"], ["115", "116"], ["117", "118"], ["119", "120"], ["121", "122"], ["123", "124"], ["125", "126"], ["127", "128"], ["129", "130"], ["131", "132"], ["133", "134"], ["135", "136"], ["137", "138"], ["139", "140"], ["141", "142"], ["143", "144"], ["145", "146"], ["147", "148"], ["149"], {"messageId": "150", "fix": "151", "desc": "152"}, {"messageId": "153", "fix": "154", "desc": "155"}, {"messageId": "150", "fix": "156", "desc": "152"}, {"messageId": "153", "fix": "157", "desc": "155"}, {"messageId": "150", "fix": "158", "desc": "152"}, {"messageId": "153", "fix": "159", "desc": "155"}, {"messageId": "150", "fix": "160", "desc": "152"}, {"messageId": "153", "fix": "161", "desc": "155"}, {"fix": "162", "messageId": "163", "data": "164", "desc": "165"}, {"messageId": "150", "fix": "166", "desc": "152"}, {"messageId": "153", "fix": "167", "desc": "155"}, {"messageId": "150", "fix": "168", "desc": "152"}, {"messageId": "153", "fix": "169", "desc": "155"}, {"messageId": "150", "fix": "170", "desc": "152"}, {"messageId": "153", "fix": "171", "desc": "155"}, {"messageId": "150", "fix": "172", "desc": "152"}, {"messageId": "153", "fix": "173", "desc": "155"}, {"messageId": "150", "fix": "174", "desc": "152"}, {"messageId": "153", "fix": "175", "desc": "155"}, {"messageId": "150", "fix": "176", "desc": "152"}, {"messageId": "153", "fix": "177", "desc": "155"}, {"messageId": "150", "fix": "178", "desc": "152"}, {"messageId": "153", "fix": "179", "desc": "155"}, {"messageId": "150", "fix": "180", "desc": "152"}, {"messageId": "153", "fix": "181", "desc": "155"}, {"messageId": "150", "fix": "182", "desc": "152"}, {"messageId": "153", "fix": "183", "desc": "155"}, {"messageId": "150", "fix": "184", "desc": "152"}, {"messageId": "153", "fix": "185", "desc": "155"}, {"messageId": "150", "fix": "186", "desc": "152"}, {"messageId": "153", "fix": "187", "desc": "155"}, {"messageId": "150", "fix": "188", "desc": "152"}, {"messageId": "153", "fix": "189", "desc": "155"}, {"messageId": "150", "fix": "190", "desc": "152"}, {"messageId": "153", "fix": "191", "desc": "155"}, {"messageId": "150", "fix": "192", "desc": "152"}, {"messageId": "153", "fix": "193", "desc": "155"}, {"messageId": "150", "fix": "194", "desc": "152"}, {"messageId": "153", "fix": "195", "desc": "155"}, {"messageId": "150", "fix": "196", "desc": "152"}, {"messageId": "153", "fix": "197", "desc": "155"}, {"messageId": "150", "fix": "198", "desc": "152"}, {"messageId": "153", "fix": "199", "desc": "155"}, {"messageId": "150", "fix": "200", "desc": "152"}, {"messageId": "153", "fix": "201", "desc": "155"}, {"messageId": "150", "fix": "202", "desc": "152"}, {"messageId": "153", "fix": "203", "desc": "155"}, {"messageId": "150", "fix": "204", "desc": "152"}, {"messageId": "153", "fix": "205", "desc": "155"}, {"messageId": "150", "fix": "206", "desc": "152"}, {"messageId": "153", "fix": "207", "desc": "155"}, {"fix": "208", "messageId": "163", "data": "209", "desc": "210"}, "suggestUnknown", {"range": "211", "text": "212"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "213", "text": "214"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "215", "text": "212"}, {"range": "216", "text": "214"}, {"range": "217", "text": "212"}, {"range": "218", "text": "214"}, {"range": "219", "text": "212"}, {"range": "220", "text": "214"}, {"range": "221", "text": "222"}, "removeConsole", {"propertyName": "223"}, "Remove the console.error().", {"range": "224", "text": "212"}, {"range": "225", "text": "214"}, {"range": "226", "text": "212"}, {"range": "227", "text": "214"}, {"range": "228", "text": "212"}, {"range": "229", "text": "214"}, {"range": "230", "text": "212"}, {"range": "231", "text": "214"}, {"range": "232", "text": "212"}, {"range": "233", "text": "214"}, {"range": "234", "text": "212"}, {"range": "235", "text": "214"}, {"range": "236", "text": "212"}, {"range": "237", "text": "214"}, {"range": "238", "text": "212"}, {"range": "239", "text": "214"}, {"range": "240", "text": "212"}, {"range": "241", "text": "214"}, {"range": "242", "text": "212"}, {"range": "243", "text": "214"}, {"range": "244", "text": "212"}, {"range": "245", "text": "214"}, {"range": "246", "text": "212"}, {"range": "247", "text": "214"}, {"range": "248", "text": "212"}, {"range": "249", "text": "214"}, {"range": "250", "text": "212"}, {"range": "251", "text": "214"}, {"range": "252", "text": "212"}, {"range": "253", "text": "214"}, {"range": "254", "text": "212"}, {"range": "255", "text": "214"}, {"range": "256", "text": "212"}, {"range": "257", "text": "214"}, {"range": "258", "text": "212"}, {"range": "259", "text": "214"}, {"range": "260", "text": "212"}, {"range": "261", "text": "214"}, {"range": "262", "text": "212"}, {"range": "263", "text": "214"}, {"range": "264", "text": "212"}, {"range": "265", "text": "214"}, {"range": "266", "text": "222"}, {"propertyName": "267"}, "Remove the console.log().", [2876, 2879], "unknown", [2876, 2879], "never", [2930, 2933], [2930, 2933], [3177, 3180], [3177, 3180], [3219, 3222], [3219, 3222], [3985, 4037], "", "error", [4142, 4145], [4142, 4145], [4650, 4653], [4650, 4653], [4671, 4674], [4671, 4674], [5280, 5283], [5280, 5283], [5629, 5632], [5629, 5632], [227, 230], [227, 230], [677, 680], [677, 680], [1053, 1056], [1053, 1056], [1890, 1893], [1890, 1893], [2401, 2404], [2401, 2404], [3058, 3061], [3058, 3061], [4008, 4011], [4008, 4011], [4753, 4756], [4753, 4756], [5184, 5187], [5184, 5187], [2415, 2418], [2415, 2418], [2455, 2458], [2455, 2458], [2737, 2740], [2737, 2740], [4451, 4454], [4451, 4454], [4528, 4531], [4528, 4531], [4654, 4657], [4654, 4657], [4827, 4830], [4827, 4830], [6342, 6384], "log"]