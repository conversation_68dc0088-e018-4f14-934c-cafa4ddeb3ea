# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 + TypeScript CMS platform for "我店生活" (Woody Living), a Chinese e-commerce management system. The application manages product catalogs, decoration/layout systems, marketing activities, and shop operations.

## Development Commands

### Environment Setup
```bash
npm install
```

### Development Server
```bash
npm run dev      # Development environment (port 3020)
npm run test     # Test environment  
npm run pre      # Pre-production environment
```

### Build Commands
```bash
npm run build           # Production build
npm run build:dev       # Development build
npm run build:test      # Test build
npm run build:pre       # Pre-production build
npm run build:prod      # Production build
```

### Code Quality
```bash
npm run lint            # ESLint check
npm run lint:fix        # ESLint auto-fix
npm run stylelint       # Style linting
npm run stylelint:fix   # Style linting with auto-fix
```

### Git Hooks
```bash
npm run commitlint      # Commit message linting
```

## Architecture

### Tech Stack
- **Framework**: Vue 3 with Composition API
- **State Management**: Pinia with persistence
- **Routing**: Vue Router 4 with hash mode
- **Build Tool**: Vite
- **UI Library**: Woody UI (custom Ant Design variant)
- **Micro-frontend**: @micro-zoe/micro-app
- **Rich Text**: TinyMCE
- **Styling**: Less with CSS variables

### Project Structure
```
src/
├── api/                 # API services organized by feature
├── assets/              # Static assets (images, icons)
├── components/          # Reusable Vue components
├── config/              # Configuration files
├── constants/           # Application constants
├── hooks/               # Vue composition API hooks
├── layouts/             # Layout components
├── pages/               # Page components organized by module
│   ├── cms/            # CMS decoration system
│   ├── goodsCenter/    # Product management
│   ├── marketingCenter/ # Marketing activities
│   └── operation/      # Operations management
├── request/             # HTTP request utilities
├── router/              # Route definitions
├── store/               # Pinia stores
├── style/               # Global styles and themes
├── types/               # TypeScript type definitions
└── utils/               # Utility functions
```

### Key Features
- **Decoration System**: Visual page builder with phone preview (decorationV2)
- **Product Management**: E-commerce goods, brands, categories
- **Marketing Tools**: Flash sales, promotions, activity management
- **CMS**: Content management with material center
- **Multi-environment**: dev/test/pre/prod configurations

### Router Structure
- Uses dynamic route loading from `router/modules/`
- Hash-based routing for compatibility
- Permission-based route access
- Automatic route expansion for nested menus

### Store Management
- Pinia stores with persistence
- Modular structure: permission, user, settings, tabs-router, decoration
- Token-based authentication with refresh mechanism

### Component Architecture
- Global components: PageWrap, ConfirmBtn
- Extensive use of Woody UI components
- Custom components for specific business logic
- Phone preview components for mobile layout testing

### Development Environment
- Vite dev server on port 3020
- Proxy configuration for API calls
- Mock data support via vite-plugin-mock
- Hot module replacement enabled

### Code Quality
- ESLint with @woody/eslint-config
- Conventional commits with commitlint
- Prettier for formatting
- Husky git hooks for pre-commit checks
- Lint-staged for optimized linting

### Build Process
- TypeScript compilation check before build
- Multi-environment builds with different configurations
- Less preprocessing with CSS variables
- SVG loader for icon optimization