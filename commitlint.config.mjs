// commitlint.config.mjs
export default {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能
        'fix', // Bug 修复
        'docs', // 文档变更
        'style', // 代码格式（不影响功能）
        'refactor', // 代码重构
        'test', // 测试相关
        'chore', // 构建/工具变更
        'revert', // 回滚提交
      ],
    ],
    'subject-case': [0], // 允许任意大小写（如可选项）
  },
  ignores: [
    (commit) => commit.startsWith('Merge')  // 忽略以Merge开头的提交信息
  ]
};
