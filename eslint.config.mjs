// ESLint v9 Flat Config
import { defineConfig } from 'eslint/config';
import { baseEslint, importEslint, prettierEslint, vue3WithTsEslint } from '@woody/eslint-config';

export default defineConfig([
  {
    ignores: [
      'dist',
      'node_modules',
      '*/config.js',
      '*.config.js',
      '*.config.ts',
      '**/*.d.ts',
      '**/types/**',
      'src/**/__tests__/*',
      'src/**/*.test.ts',
      'src/**/*.spec.ts',
      '**/*.md',
    ]
  },
  // 基础规则：适用于所有 JS/TS/Vue 文件
  ...baseEslint,
  // import 插件：检查模块导入规范，只对脚本和 SFC 生效
  ...importEslint,
  // Vue 3 专用规则：最后对 .vue 文件生效，确保 SFC 用 Vue parser
  ...vue3WithTsEslint,
  // Prettier：格式化插件，最后加载，关闭冲突规则
  ...prettierEslint,
]);
