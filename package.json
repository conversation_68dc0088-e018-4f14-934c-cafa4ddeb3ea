{"name": "woody-living-plat-new-marketing", "version": "0.6.1", "scripts": {"dev": "vite --open --mode dev", "test": "vite --open --mode wdtest", "pre": "vite --open --mode pre", "build": "vue-tsc --noEmit && vite build --mode prod", "build:dev": "vue-tsc --noEmit && vite build --mode dev", "build:test": "vue-tsc --noEmit && vite build --mode wdtest", "build:pre": "vue-tsc --noEmit && vite build --mode pre", "build:prod": "vue-tsc --noEmit && vite build --mode prod", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,vss,sass,less}", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install", "site:preview": "npm run build && cp -r dist _site", "test:coverage": "echo \"no test:coverage specified,work in process\"", "commitlint": "commitlint --edit"}, "dependencies": {"@micro-zoe/micro-app": "^1.0.0-rc.12", "@tinymce/tinymce-vue": "^6.1.0", "axios": "^1.1.3", "compressorjs": "^1.2.1", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "js-image-compressor": "^2.0.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^3.0.1", "qiniu-js": "3.1.2", "sass": "^1.77.4", "tinymce": "^7.8.0", "tvision-color": "^1.3.1", "vue": "^3.2.31", "vue-clipboard3": "^2.0.0", "vue-router": "~4.1.5", "vue3-colorpicker": "^2.3.0", "vuedraggable": "^4.1.0", "woody-ui": "^1.0.1"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.25.1", "@types/echarts": "^4.9.10", "@types/lodash": "^4.14.182", "@types/qs": "^6.9.7", "@types/ws": "^8.2.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.1.7", "@vue/compiler-sfc": "^3.0.5", "@woody/eslint-config": "^1.0.9", "commitizen": "^4.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^9.32.0", "globals": "^16.0.0", "husky": "^8.0.0", "less": "^4.1.1", "lint-staged": "^15.5.1", "mockjs": "^1.1.0", "prettier": "^3.5.3", "stylelint": "~13.13.1", "stylelint-config-prettier": "~9.0.3", "stylelint-less": "1.0.1", "stylelint-order": "~4.1.0", "typescript": "^4.9.5", "vite": "^2.7.1", "vite-plugin-mock": "^2.9.6", "vite-svg-loader": "^3.1.0", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^1.8.27", "vue-turnstile": "^1.0.9"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,ts,tsx,vue}": ["eslint --fix --no-warn-ignored --cache", "prettier --write"], "*.{cjs,json}": ["prettier --write"]}, "description": "我店生活新后管", "husky": {"hooks": {"pre-commit": "lint-staged"}}}