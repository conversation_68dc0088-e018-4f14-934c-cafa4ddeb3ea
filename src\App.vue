<template>
  <a-config-provider :locale="zhCN" :theme="theme">
    <router-view :class="[mode]" :key="$route.fullPath" />
  </a-config-provider>
</template>
<script setup lang="ts">
import { computed, onMounted } from "vue";
import theme from "@/style/theme-all";
import { useSettingStore } from "@/store";
// import { getCategoryDropDown } from "./utils";
import zhCN from "woody-ui/es/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
const store = useSettingStore();

const mode = computed(() => {
  return store.displayMode;
});

onMounted(async () => {
  // getCategoryDropDown();
});
</script>
<style lang="less" scoped>
#nprogress .bar {
  background: var(--td-brand-color) !important;
}
</style>
