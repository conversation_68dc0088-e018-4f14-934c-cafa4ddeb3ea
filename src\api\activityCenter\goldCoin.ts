import request from "@/request";
import { shop } from "@/types/shop/shop";
import { Response, PaginationResponse } from "./../common";

const api = "/life-platform-dashboard";

//列表

export const GetQueryPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/prod/queryPage`,
    data: params,
  });

export const getGoldCategoryDrop = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/gold/category/list`,
    data: params,
  });

export const GetRemove = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/prod/remove`,
    data: params,
  });

export const GetPageQuery = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone/page-query?page=${params.page}&size=${params.size}&sort=zone_sort,asc`,
    data: params,
  });

// 下架

export const GetSpecialOff = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone/update`,
    data: params,
  });

// 删除专区
export const GetZoneDelete = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "DELETE",
    path: `/life-platform-dashboard/gold-coin/zone/delete/${params.zoneId}`,
    data: params,
  });

// 新增
export const GetAddProductCount = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone/add-product-count`,
    data: params,
  });

export const GetZoneAdd = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone/add`,
    data: params,
  });

export const GetSpecialDetailYZH = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone-product/page-query-yzh?page=${params.page}&size=${params.size}`,
    data: params,
  });

export const GetSpecialDetail = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/gold-coin/zone/zone-detail?zoneId=${params.zoneId}`,
    data: params,
  });

export const GetSpecialUpdate = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone/update`,
    data: params,
  });

// 编辑专区商品信息
export const GetProdUpdate = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone-product/update`,
    data: params,
  });

// 删除专区

export const GetZoneProdDelete = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "DELETE",
    path: `/life-platform-dashboard/gold-coin/zone-product/delete/${params.zoneProductId}`,
    data: params,
  });
//查询场次列表

export const GetSeckillQuery = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/queryPage`,
    data: params,
  });

//-------平台满减
export const GetPlatlistPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/platform/MarketingActivity/listPage?current=${params.current}&size=${params.size}`,
    data: params,
  });

export const GetAddPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/prod/add`,
    data: params,
  });

export const GetEdit = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/platform/prod/info/${params.prodId}`,
    data: params,
  });

export const addOrEdit = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/addOrEdit`,
    data: params,
  });

export const GetActivityQuery = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/query?activityType=3`,
    data: params,
  });

//场次状态变更start
export const GetStatusChange = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/statusChange`,
    data: params,
  });

//删除场次start

export const GetSeckillRemove = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/remove?coinSeckillId=${params.coinSeckillId}`,
    data: params,
  });

//修改时间
export const GetAddOrEdit = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/addOrEdit`,
    data: params,
  });
//分页查询某个场次的商品信息
  export const GetProdList = (params: any) =>
    request<Response<PaginationResponse<shop[]>>>({
      method: "POST",
      path: `/wd-life-app-platform/coin/seckill/prod/list`,
      data: params,
    });



//添加某个场次的商品

export const GetProdSave = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/prod/save`,
    data: params,
  });

//删除秒杀商品
export const GetProdDel = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/prod/del/${params.id}`,
    data: params,
  });

//添加或编辑活动专区属性
export const GetAddEdit = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/addOrEdit`,
    data: params,
  });

//查询活动专区属性

export const GetSeckillEditQuery = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/query?activityType=2`,
    data: params,
  });

// 批量根据ID新增专区商品ProductAddListParams

export const GetProductAddList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone-product/add-list`,
    data: params,
  });

//新增单个专区商品/gold-coin/zone-product/addProductAddParams
export const GetProductAdd = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone-product/add`,
    data: params,
  });

export const GetPageSpecialQuery = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/gold-coin/zone-product/page-query?page=${params.page}&size=${params.size}&sort=zone_product_sort,asc`,
    data: params,
  });

//编辑活动商品
export const GetProdEdit = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/activity/prod/edit`,
    data: params,
  });

//编辑秒杀商品
export const GetEditInfo = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/coin/seckill/prod/edit/info`,
    data: params,
  });

  export const activitySort = (params: any) =>
    request<Response<PaginationResponse<shop[]>>>({
      method: "POST",
      path: `/wd-life-app-platform/activity/prod/sort/edit`,
      data: params,
    });

    export const activityHotCakes = (params: any) =>
      request<Response<PaginationResponse<shop[]>>>({
        method: "GET",
        path: `/wd-life-app-platform/activity/prod/setcake?activityProdId=${params.activityProdId}&sellLikeHotCakes=${params.sellLikeHotCakes}`,
        data: params,
      });

      export const GetZoneProdDetail = (params: any) =>
        request<Response<PaginationResponse<shop[]>>>({
          method: "GET",
          path: `/life-platform-dashboard/gold-coin/zone-product/product-detail?zoneProductId=${params.zoneProductId}`,
          data: params,
        });




  
