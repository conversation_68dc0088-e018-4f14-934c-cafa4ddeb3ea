import request from "@/request";

import { Response, PaginationResponse } from "../../common";

/*
  判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
  子应用调用主应用方法获取相应的方法或数据--window.parent.mainAppMethod()
  脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
*/

//列表

export const getPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/share-product/page-search?page=${params.page}&size=${params.size}&sort=create_time,desc`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//导出
export const getExportInterface = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/share-product/export`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};


//营销商品管理列表

export const getProductPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/grouponProduct/page?page=${params.page}&size=${params.size}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//商品分类列表

export const getCategorySub = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/shop-category-v2/list?isAllowEmptySub=${params.isAllowEmptySub}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//营销商品管理注水销量

export const getWaterSoldNum = (params: any) => {
  try {
    return request<Response<any>>({
      method: "PUT",
      path: `/life-platform-dashboard/grouponProduct/water-sold-num/${params.id}?waterSoldNum=${params.num}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//营销商品管理详情
export const getGrouponProduct = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/grouponProduct/get/${params.id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//违规下架

export const getViolationOff = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/grouponProduct/violation-off-sale`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
