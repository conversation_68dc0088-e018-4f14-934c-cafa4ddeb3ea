import request from "@/request";

import { Response, PaginationResponse } from "../../common";

//列表

export const getPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/MarketingActivity/listPage?current=${
        params.current
      }&size=${params.size}${params.name ? `&name=${params.name}` : ""}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//终止活动
export const getUpdateStatus = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/MarketingActivity/updateStatus`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//是否有活动

export const getQueryAddStatus = () => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/MarketingActivity/queryAddStatus`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//新增活动

export const getSetData = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/MarketingActivity/setData`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区查询

export const getQueryMaxRuleById = (id: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/MarketingActivity/queryMaxRuleById?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//平台满减详情列表
export const getSupplierPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/api/v1/platform/supplier/getSupplierPage?current=${
        params.current
      }&size=${params.size}${
        params.shopName ? `&shopName=${params.shopName}` : ""
      }`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区保存

export const getEditMaxRule = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/MarketingActivity/editMaxRule`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//管理商品
export const getListPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/activity/prod/listPage`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//管理商品列表排序

export const getSetSort = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/activity/prod/setSort`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//管理商品添加商品列表

export const getAddListPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/activity/prod/addListPage`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//查询创建人

export const getUserPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-base-user/sysUser/page/list`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//管理商品新增商品

export const getAddProduct = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/promotion/product/add-activity`,
      data: params,
      showMsgError: false,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//管理商品删除

export const getDelProduct = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/activity/prod/delProduct`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
