import request from "@/request";
import { Response, PaginationResponse } from "../../common";

const api = "/life-platform-dashboard";

//分页查询参与乡村振兴活动店铺的信息

export const getSupplierPage = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/rural/revitalization/getSupplierPage`,
    data: params,
  });

//查询乡村振兴积分池信息
export const queryPointsInfo = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/rural/revitalization/pointsPool/query`,
    data: params,
  });

//编辑活动（积分池）信息
export const editPoints = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/rural/revitalization/pointsPool/edit`,
    data: params,
  });

//开/关积分池内店铺
export const switchPointsPool = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/rural/revitalization/pointsPoolShop/switch`,
    data: params,
  });
