import request from "@/request";

import { Response, PaginationResponse } from "../../common";

/*
  判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
  子应用调用主应用方法获取相应的方法或数据--window.parent.mainAppMethod()
  脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
*/

//列表

export const getPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/mktPromoter/list/getMktPromoterPage?current=${params.page}&size=${params.size}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
