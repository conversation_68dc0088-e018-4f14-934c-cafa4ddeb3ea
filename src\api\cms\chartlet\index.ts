import request from '@/request';
import { Response, PaginationResponse } from '../../common';

const api = '/life-platform-dashboard';

// 体贴列表
export const cornerMarkList = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/cornerMark/list`,
    data: params
  });
// 贴图上下架
export const updateStatus = (params: any) =>
  request<Response<any>>({
    method: "PUT",
    path: `${api}/cornerMark/updateStatus`,
    data: params
  });

// 贴图保存
export const saveCornerMark = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/cornerMark/saveInfo`,
    data: params
  });

// 应用类型查询
export const queryUseType = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/cornerMark/useTypeEnum/get`,
    data: params
  });
