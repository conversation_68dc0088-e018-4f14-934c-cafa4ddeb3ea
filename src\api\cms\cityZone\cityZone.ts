import request from '@/request';
import { Response, PaginationResponse } from '../../common';

const api = "/life-platform-dashboard";

// 同城专区列表
export const getSupplierPage = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "GET",
    path: `${api}/api/v1/platform/supplier/getSupplierPage?${new URLSearchParams(
      params
    ).toString()}`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });
// 素材列表查询
export const modifySameCityZone = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `/wd-life-app-platform/api/v1/platform/supplier/modifySameCityZone`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 下单限制查询
export const queryExpand = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `/wd-life-app-platform/api/v1/platform/supplier/queryExpand?${new URLSearchParams(
      params
    ).toString()}`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 下单限制设置
export const editOrderLimit = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `/wd-life-app-platform/api/v1/platform/supplier/editOrderLimit`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 前台分类一级列表
export const getList = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/localArea/getList`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 同城专区分类列表
export const pageList = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/localArea/page?page=${params.page}&size=${params.size}`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 编辑分类
export const update = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/localArea/update`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 添加分类
export const add = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/localArea/add`,
    data: params,
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 删除分类
export const deleteFunc = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/localArea/delete`,
    data: params
    // headers: { 'X-Accept-Version': 'wx1' },
  });

// 绑定页面
export const bindingCmsConfigureBasePage = (params: any) => 
  request<Response<any>>({
    method: 'POST',
    path: `${api}/api/v1/platform/supplier/bindingCmsConfigureBasePage`,
    data: params
  })
