import request from '@/request';
import { Response } from '@/api/common';
import { isEmptyValue } from '@/utils';

const api = "/life-platform-dashboard";


// 获取-浮层广告配置分页列表
export const getFloatPage = (params: any) => {
  return request<Response<any>>({
    method: "POST",
    path: `${api}/floatingAdvertisementPage/page`,
    data: params
  });
};

// 获取-浮层广告配置详情
export const getFloatDetail = (params) => {
  const { page, size, ...reset } = params;
  return request<Response<any>>({
    method: "GET",
    path: `${api}/floatingAdvertisementPage/detail?id=${params.id}`,
    data: reset
  });
};

// 浮层广告配置新增
export const addFloatAdd = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/floatingAdvertisementPage/add`,
    data: params
  });

// 浮层广告配置编辑
export const addFloatEdit = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/floatingAdvertisementPage/edit`,
    data: params
  });

// 浮层广告配置失效
export const expireFloat = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `${api}/floatingAdvertisementPage/expireFloatingAdvertisement/${params.id}`,
    data: params
  });
};

//查询用户抽奖记录
export const getUserPrizeList = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/campaign/user/prize/pageCampaignUserPrizeList`,
    data: params
  });
};

//活动统计
export const getStatisticsList = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/statistics/pageCampaignStatisticsList`,
    data: params
  });
};

//查询活动列表
export const getCampaignList = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/campaign/pageCampaignList`,
    data: params
  });
};

//创建活动
export const addCreate = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/campaign/create`,
    data: params
  });
};

//编辑活动
export const addEdit = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/campaign/edit`,
    data: params
  });
};

//查询活动详情
export const getCampaignDetail = (params) => {
  return request<Response<any>>({
    method: "GET",
    path: `/life-campaign-manager/campaign/campaignDetail?campaignId=${params.campaignId}`,
    data: params
  });
};

//活动状态列表
export const queryCampaignStatus = (params) => {
  return request<Response<any>>({
    method: "GET",
    path: `/life-campaign-manager/campaign/queryCampaignStatuses`,
    data: params
  });
};

//修改活动状态
export const getEditStatus = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/campaign/campaignEditStatus`,
    data: params
  });
};

//奖品类型列表
export const queryPrizeTypes = (params) => {
  return request<Response<any>>({
    method: "GET",
    path: `/life-campaign-manager/prize/queryPrizeTypes`,
    data: params
  });
};

// 快递单号保存&修改
export const editTrackingNumber = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `/life-campaign-manager/campaign/user/prize/editTrackingNumber`,
    data: params
  });
};










