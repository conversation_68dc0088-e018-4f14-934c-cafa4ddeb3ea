import request from "@/request";
import { reactive, toRaw } from "vue";
import { getDefaultTokenProvider } from "@/request/tokenProvider";
import { Response } from "@/api/common";

const api = "/life-platform-dashboard";

// 获取贴图配置设置页面的列表
export const getMarkUseList = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/cornerMark/getMarkUseList`,
    data: params
  });

// 全部删除
export const delAllMarkUse = (id) =>
  request<Response<any>>({
    method: 'DELETE',
    path: `${api}/cornerMark/delAllMarkUse?id=${id}`
  });

// 单个删除
export const delMarkUse = (id) =>
  request<Response<any>>({
    method: 'DELETE',
    path: `${api}/cornerMark/delMarkUse?id=${id}`
  });

// 绑定商品/添加商品
export const markUseBatchSave = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/cornerMark/markUseBatchSave`,
    data: params
  });

// 获取已应用的角标记录列表
export const getUseMap = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/cornerMark/getUseMap`,
    data: params
  });

// 获取已绑定的商品
export const getBindUseList = (id) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/cornerMark/${id}/bindUseList`
  });

// 获取已绑定的供货仓
export const getBindSupplierList = (id) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/cornerMark/${id}/bindSupplierList`
  });
