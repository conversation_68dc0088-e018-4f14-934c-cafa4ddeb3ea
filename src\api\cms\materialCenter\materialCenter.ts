import request from "@/request";
import { reactive, toRaw } from "vue";
import { getDefaultTokenProvider } from "@/request/tokenProvider";
import { Response, PaginationResponse } from "../../common";

const api = "/life-platform-dashboard";

// 查素材树
export const queryGroup = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/material/group/query?flatFlag=${params.flatFlag}`,
    data: params,
    headers: { 'X-Accept-Version': 'wx1' },
  });
// 素材列表查询
export const queryByPage = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/material/page-search`,
    data: params,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 对应素材数量查询
export const countQuery = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/material/group/materialCnt?groupId=${params.groupId}`,
    data: params,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 批量修改素材的所在分组
export const modifyGroup = (params: any) =>
  request<Response<any>>({
    method: "PUT",
    path: `${api}/material/group/bind/batch`,
    data: params,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 素材保存
export const saveMaterial = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/material/save`,
    data: params,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 素材分组保存
export const saveGroup = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/material/group/save`,
    data: params,
    headers: { 'X-Accept-Version': 'wx1' },
  });

// 图片上传
export const qiNiuYunToken = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/qiniuyun-upload/temporary-token`,
    data: {
      bizType: params.bizType,
      resourceType: "image",
      source: "new_life_plat",
    },
  });
export const uploadImg = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `/life-automatic-merchant/shop/image/upload`,
    data: params,
    headers: { clienttype: 'PLATFORM' },
  });
