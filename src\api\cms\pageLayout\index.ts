import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
// import { Response } from '../../common';
import { reactive,toRaw } from 'vue';
interface Response<T> {
  data: {
    records: T[];
    total: number;
  },
  code: number;
}

const api = "/life-platform-dashboard";



// 查询所有用户信息
export const sysUserPage = (params: any) =>{
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/sysUser/page?page=${params.page}&size=${params.size}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 查询页面配置列表
export const basePage = (params: any) =>  {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/basePage/page?page=${params.current}&size=${params.pageSize}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
export const basePage2 = (params: any) => {
  // console.log(tokenProvider,'查询数据')
  // console.log(toRaw(tokenObj),'查询123')
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/basePage/page?page=${params.page}&size=${params.size}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 新建页面配置
export const basePageAdd = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/basePage/add`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询列表详情
export const basePageDetail = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/basePage/detail?id=${params.id}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 编辑页面配置
export const basePageupdate = (params: any) => {
  try {
    return request<Response<any>>({
      method: "PUT",
      path: `${api}/basePage/update`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 复制页面配置
export const basePageCopy = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/basePage/copy`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
