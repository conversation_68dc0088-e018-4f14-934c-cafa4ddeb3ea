import request from '@/request';
import { Response, PaginationResponse } from '../../common';

const api = "/life-platform-dashboard";




// 商品信息列表
export const queryList = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/global/config/search`,
    data: params
  });
// 商品信息详情
export const infoDetail = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/global/config/info?${new URLSearchParams(params).toString()}`,
    data: params
  });

// 商品信息保存
export const saveConfig = (params: any) =>
  request<Response<any>>({
    method: "PUT",
    path: `${api}/global/config/save`,
    data: params
  });
