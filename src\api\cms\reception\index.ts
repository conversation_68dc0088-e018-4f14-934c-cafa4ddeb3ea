import request from "@/request";
import { Response } from "@/api/common";
import { isEmptyValue } from "@/utils";

const api = "/life-platform-dashboard";

// 获取-前台分类列表
export const getFeCategoryPage = (params: any) => {
  const { page, size, ...reset } = params;
  return request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/page?page=${page}&size=${size}`,
    data: reset,
  });
};

// 获取-创建人列表
export const getSysUserPage = (params) => {
  const { page, size, ...reset } = params;
  return request<Response<any>>({
    method: "POST",
    path: `${api}/sysUser/page?page=${page}&size=${size}`,
    data: reset,
  });
};

// 新建分类
export const addFeCategory = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/add`,
    data: params,
  });

// 编辑分类
export const EditFeCategory = (params) =>
  request<Response<any>>({
    method: "PUT",
    path: `${api}/feCategory/update`,
    data: params,
  });

// 复制分类
export const copyFeCategory = (params) => {
  return request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/copy`,
    data: params,
  });
};

// 获取商品分类列表
export const getCommodityList = (categoryId) => {
  let path = "/ms-product/platform/prod/category/list";
  if (!isEmptyValue(categoryId)) {
    path += `?categoryId=${categoryId}`;
  }
  return request<Response<any>>({
    method: "GET",
    path,
  });
};

// 获取管理列表
export const getManageList = (params) => {
  const { page, size, ...reset } = params;
  return request<Response<any>>({
    method: "POST",
    path: `${api}/feCategoryProduct/page?page=${page}&size=${size}`,
    data: reset,
  });
};

// 删除管理列表-选择性删除
export const deleteManage = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategoryProduct/delete`,
    data: params,
  });

// 全部删除
export const deleteAll = (feCategoryId) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/feCategoryProduct/deleteAll?feCategoryId=${feCategoryId}`,
  });

// 修改商品排序
export const modifySort = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategoryProduct/update`,
    data: params,
  });

// 获取添加商品列表
export const getAddList = (params) => {
  const { page, size, ...reset } = params;
  return request<Response<any>>({
    method: "POST",
    path: `${api}/feCategoryProduct/pageProduct/v2?page=${page}&size=${size}`,
    data: reset,
  });
};

// 获取商品品牌列表
export const getBrandList = () =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/productBrand/brandList`,
  });

// 添加商品
export const addProduct = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategoryProduct/addProduct`,
    data: params,
  });

// 获取后台分类tree数据
export const getCategoryTree = () =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/product/categorytree`,
  });

// 获取供货仓列表
export const getSupplierPage = (params) => {
  const { current, size, shopName = "", tel = "" } = params;
  return request<Response<any>>({
    method: "GET",
    path: `${api}/api/v1/platform/supplier/getSupplierPage?current=${current}&size=${size}&shopName=${shopName}&tel=${tel}`,
  });
};

// 绑定商品来源确认
export const bindProdSource = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/prodSource/bind`,
    data: params,
  });

// 获取已绑定信息
export const getBindInfo = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/prodSource/bind/info`,
    data: params,
  });

// 获取批量添加商品列表
export const getBatchList = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/prod/bind/task/page`,
    data: params,
  });

// 批量添加商品新增
export const addBatchProd = (params) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/feCategory/prod/bind/import?taskName=${params.taskName}`,
    data: params.formData,
  });
