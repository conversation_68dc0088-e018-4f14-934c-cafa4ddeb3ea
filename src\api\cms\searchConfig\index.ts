import request from '@/request';
import { Response, PaginationResponse } from '../../common';

const api = "/life-platform-dashboard";


// 搜索页面配置列表
export const searchPageList = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/searchPage/page?page=${params.page}&size=${params.size}`,
    data: params
  });
// 新增版本/复制
export const addVersion = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchPage/add`,
    data: params,
  });

// 搜索页面配置编辑
export const updateVersion = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchPage/updateVersion`,
    data: params
  });

// 搜索页面配置设置详情
export const configDetail = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchPage/detail`,
    data: params
  });

// 搜索页面配置设置
export const updateConfig = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchPage/update`,
    data: params
  });

// 搜索滚动词组相关接口

// 新增滚动词组名
export const addGroup = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/addGroup`,
    data: params
  });

// 编辑滚动词组名
export const updateGroup = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/updateGroup`,
    data: params
  });

// 删除滚动词组名
export const deleteGroupName = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/deleteGroupName`,
    data: params
  });

// 搜索滚动词组列表
export const pageGroupList = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/pageGroup?page=${params.page}&size=${params.size}`,
    data: params
  });

// 编辑搜索词
export const updateRollingWord = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/update`,
    data: params
  });

// 新增搜索词
export const addRollingWord = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/add`,
    data: params
  });

// 搜索滚动词列表
export const searchRollingWordList = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/page?page=${params.page}&size=${params.size}`,
    data: params
  });

// 删除搜索词
export const deleteSearchRollingWords = (params: any) =>
  request<Response<PaginationResponse<any>>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/delete`,
    data: params
  });
