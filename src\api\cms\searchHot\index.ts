import request from "@/request";

import { Response, PaginationResponse } from "../../common";


//热搜词
export const getCoinPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/cmsHostSearch/list`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//热搜词新增
export const getSaveHost = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/cmsHostSearch/save`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//热搜词是否展示
export const getSetSave = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/cmsHostSearch/setShow`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//删除热搜词列表

export const getDelHotList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/cmsHostSearch/del`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
