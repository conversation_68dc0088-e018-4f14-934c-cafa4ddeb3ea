import request from "@/request";

import { Response, PaginationResponse } from "../../common";

//搜索页榜单
export const getRankPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/search/show/rank/page`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//榜单列表

export const getZone = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/cmsSpecialZone/list`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//新增编辑搜索页榜单

export const getAddOrEdit = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/search/show/rank/addOrEdit`,
      data: params,
      showMsgError: false,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//删除

export const getRemoveRank = (id: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/wd-life-app-platform/search/show/rank/remove?searchShowRankId=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
