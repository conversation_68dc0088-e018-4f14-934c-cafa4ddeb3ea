import request from "@/request";
import { shop } from "@/types/shop/shop";
import { Response, PaginationResponse } from "./../../common";

const api = "/life-platform-dashboard";

export const getZone = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/list`,
    data: params,
  });

export const deleteZone = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/del`,
    data: params,
  });

export const updateZoneStatus = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/setStatus`,
    data: params,
  });

export const getZoneCardList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/cardList`,
    data: params,
  });

export const GetCategory = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/wd-life-app-platform/cmsSpecialZone/category/get?zoneId=${params.zoneId}`,
    data: params,
  });

export const saveZone = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/save`,
    data: params,
  });

export const saveZoneCard = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/saveCardList`,
    data: params,
  });

export const GetSearchStatus = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/setSearchStatus`,
    data: params,
  });

//编辑专区属性
export const GetEditZoneAttr = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/cmsSpecialZone/editZoneAttr`,
    data: params,
  });

export const getSpuList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/platform/prod/plat/page`,
    data: params,
  });

  export const getCategoryList = (params: any) =>
    request<Response<PaginationResponse<shop[]>>>({
      method: "GET",
      path: `/ms-product/platform/prod/category/list`,
      data: params,
    });

  
