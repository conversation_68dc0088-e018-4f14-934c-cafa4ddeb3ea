import request from "@/request";

const api = "/life-platform-dashboard";

export interface Response<T> {
  records: any;
  [x: string]: any;
  code: any;
  message: string;
  data: T;
}
export interface Pagination {
  current?: number;
  size: number;
  total?: number;
  pages?: number;
  page?: number;
}
export interface PaginationResponse<T> {
  pages: number;
  total: number;
  records: T;
}

// 获取枚举接口
export const getQueryDictItems = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/plat-dict/items?dictCode=${params.dictCode}`,
  });

// 图片上传
export const qiNiuYunToken = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/qiniuyun-upload/temporary-token`,
    data: {
      bizType: params.bizType,
      resourceType: "image",
      source: "new_life_plat",
    },
  });
export const uploadImg = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `/life-automatic-merchant/shop/image/upload`,
    data: params,
    headers: { clienttype: "PLATFORM" },
  });

export const uploadQiniu = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `https://upload.qiniup.com/`,
    data: params,
    headers: { clienttype: "PLATFORM" },
  });

//供应仓查询

export const GetSupplierPage = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `/life-platform-dashboard/api/v1/platform/supplier/getSupplierPage?current=${params.current}&size=${params.size}`,
  });

export const GetCategoryList = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `/wd-life-app-platform/cmsSpecialZone/category/list?supplyIds=${params.supplyIds}&firstCategory=${params.firstCategory}`,
  });

// 获取字典接口
export const getDictionaries = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `/infra-service/dict/items?dictCode=${params.dictCode}`,
  });
// 获取字典接口（新）
export const getSourceConfigList = () =>
  request<Response<any>>({
    method: "GET",
    path: "/life-platform-dashboard/promotion/product/source-config-list",
  });
// 查询分类
export const queryCategory = () =>
  request<Response<any>>({
    method: "POST",
    path: `/life-platform-dashboard/platform/prod/newCategoryModelAll`,
  });

// 公共服务导出
export const tripTask = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `/life-base-bff/async/task/trip-task`,
    data: params,
  });

// 查京东能不能导出
export const exportCheck = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `/life-platform-dashboard/jd/product/export/check`,
    data: params,
  });

// 省市区List
export const getAreaList = () =>
  request<Response<any>>({
    method: "GET",
    path: `/ms-common/admin/area/list/all`,
    includeCredentials: true,
  });