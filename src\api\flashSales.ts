import request from "@/request";
import {Response} from "@/api/common";


let api = "/life-platform-dashboard"

//查询活动列表
export const getPromotionPage = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/page`,
        data: params,
        showMsgError: false,
    });

//复制活动
export const copyPromotion = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/copy`,
        data: params,
        showMsgError: false,
    });

//创建活动
export const createPromotion = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/create`,
        data: params,
        showMsgError: false,
    });

//创建活动
export const editPromotion = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/edit`,
        data: params,
        showMsgError: false,
    });

//活动状态变更
export const updateStatus = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/flow-status`,
        data: params,
        showMsgError: false,
    });

//店铺分页查询
export const getShopPage = (params: any) =>
    request<Response<any>>({
        method: "GET",
        path: `/life-platform-dashboard/api/v1/platform/shop/getShopPage?current=${params.page}&size=${params.size}&shopName=${params?.shopName ? params?.shopName : ""}`,
        data: params,
        showMsgError: false,
    });

//查询活动列表
export const getSkuPage = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/sku/page`,
        data: params,
        showMsgError: false,
    });

//活动sku流转状态
export const updateSkuStatus = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/sku/flow-status`,
        data: params,
        showMsgError: false,
    });

//预生成活动id
export const getPromotionId = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/id`,
        data: params,
        showMsgError: false,
    });

//预生成活动id
export const getCategoryList = (params: any) =>
    request<Response<any>>({
        method: "GET",
        path: `/ms-product/platform/prod/category/list?${params.categoryId ? 'categoryId=' + params.categoryId : ''}`,
        showMsgError: false,
    });

//活动商品列表
export const getProductSelect = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/product/select`,
        data: params,
        showMsgError: false,
    });

//活动查询商品
export const getProductPage = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/product/page`,
        data: params,
        showMsgError: false,
    });

//活动新增商品
export const addProductData = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/product/add`,
        data: params,
        showMsgError: false,
    });

//活动编辑商品
export const editProductData = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/product/edit`,
        data: params,
        showMsgError: false,
    });

//促销活动日志分页列表
export const getOprLog = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/list/oprLog`,
        data: params,
        showMsgError: false,
    });

//批量导入列表查询
export const getTaskPage = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/list/batch/task`,
        data: params,
        showMsgError: false,
    });

//新增批量任务
export const addTask = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion/add/batch/task`,
        data: params,
        showMsgError: false,
    });

//新增批量任务
export const getPromotionDetails = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/promotion`,
        data: params,
        showMsgError: false,
    });

// 图片上传
export const qiNiuYunTokenFile = (params: any) =>
    request<Response<any>>({
        method: "POST",
        path: `${api}/qiniuyun-upload/temporary-token`,
        data: {
            bizType: params.bizType,
            resourceType: params.resourceType,
            source: "new_life_plat",
        },
    });