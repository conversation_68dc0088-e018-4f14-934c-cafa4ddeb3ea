import { Response, Pagination } from "./../common";
import request from "@/request";

export interface GetShopBrandListParams extends Pagination {
  brandName?: string;
  shopId?: number;
  status?: number;
}

interface CategoryBrandInfo {
  categoryId: number;
  categoryName?: string;
}

export interface ShopBrandListItem {
  brandId: number;
  firstLetter: string;
  imgUrl: string;
  imgUrlId: number;
  isTop: number;
  name: string;
  seq: number;
  shopId: number;
  status: number;
  categoryIds?: number[];
  categoryBrandInfo?: CategoryBrandInfo[];
}

export interface DropdownCategoryList
  extends Pick<
    GetCategoryResponse,
    "categoryId" | "categoryName" | "id" | "thirdCategoryName"
  > {
  children?: DropdownCategoryList[];
  loading?: boolean;
  grade?: number;
}

export interface GetCategoryResponse {
  categoryId: number;
  categoryName: string;
  categoryParentName?: string;
  categoryModels: GetCategoryResponse[];
  deliveryType?: string;
  grade?: number;
  parentId?: number;
  picId: number;
  pic?: string;
  seq: number;
  status: number;
  children?: GetCategoryResponse[];
  cornerMarkImg?: string; // 贴图地址
  diamondShow?: any;
  thirdCategoryName?: string;
  id?: string;
}

export interface ProdCategoryParams {
  name?: string;
  sourceType?: string;
}

export interface ProdCategoryRecords {
  categoryId: string;
  categoryName: string;
}

export interface ProdCategoryResponse {
  data: ProdCategoryRecords[];
}

export interface CategoryPageParams extends Pagination {
  categoryId?: string;
  categoryName?: string;
  status?: number;
}

export interface CategoryRecords {
  categoryId: string;
  categoryName: string;
  supplyChainNames: [];
  brandNum: string;
  productNum: string;
  seq: number;
  createTime: string;
  status: number;
  statusStr: string;
  picId: string;
  picUrl: string;
}
export interface CategoryResponse {
  records: CategoryRecords[];
  total: number;
}

export interface CategorySaveParams {
  categoryId?: string;
  categoryName: string;
  status: number;
  seq: number;
  thirdCategoryIds: string[];
}

export interface CategoryListRecords {
  name: string;
  thirdCategoryId: string;
  categoryId: string;
  categoryName: string;
  brandNum: string;
  productNum: string;
  state: number;
  stateStr: string;
}
export interface CategoryListResponse {
  data: CategoryListRecords[];
}
export interface CategoryManageParams extends Pagination {
  categoryId: string;
  brand?: string;
}

export interface CategoryManageRecords {
  brand: string;
  thirdBrandId: string;
  brandUrl: string;
  thirdCategoryId: string;
  thirdCategoryName: string;
  activityCount: string;
  productCount: string;
  sort: number;
}

export interface CategoryManageResponse {
  records: CategoryManageRecords[];
  total: number;
}

/**
 * 商品中心->电商品牌->品牌列表  列表
 * @param {*} data
 * @returns
 */
export const getShopBrandList = (params: GetShopBrandListParams) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/brand/page`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->电商品牌->品牌列表  列表
 * @param {*} data
 * @returns
 */
export const updateBrandStatus = (
  params: Pick<ShopBrandListItem, "brandId" | "status">
) => {
  try {
    return request<Response<any>>({
      method: "PUT",
      path: `/ms-product/platform/brand/updateBrandStatus`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

export const getCategoryList = (categoryId?: number) => {
  try {
    return request<Response<DropdownCategoryList[]>>({
      method: "GET",
      path: categoryId
        ? `/ms-product/platform/prod/category/list?categoryId=${categoryId}`
        : "/ms-product/platform/prod/category/list",
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->电商品牌->品牌列表  新增
 * @param {*} data
 * @returns
 */
export const addBrand = (params) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/brand`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->电商品牌->品牌列表  编辑
 * @param {*} data
 * @returns
 */
export const updateBrand = (params) => {
  try {
    return request<Response<null>>({
      method: "PUT",
      path: `/ms-product/platform/brand`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->电商品牌->品牌列表  获取编辑回显的数据
 * @param {*} data
 * @returns
 */
export const getBrandById = (brandId: number) => {
  try {
    return request<Response<ShopBrandListItem>>({
      method: "GET",
      path: `/ms-product/platform/brand/info/${brandId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};
/**
 * 商品中心->电商品牌->品牌列表  删除
 * @param {*} data
 * @returns
 */
export const deleteBrand = (id: number) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/ms-product/platform/brand/${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->电商品牌->爱库存品牌分类  品牌分类下拉框数据
 * @param {*} data
 * @returns
 */
export const GetProdCategory = (
  params: ProdCategoryParams,
) =>{
  try {
    return request<Response<ProdCategoryResponse>>({
      method: "POST",
      path: `/ms-product/platform/prod/category/list`,
      data: params,
      includeCredentials: true,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->电商品牌->爱库存品牌分类  列表
 * @param {*} data
 * @returns
 */
  export const GetCategoryPage = (
    params: CategoryPageParams,
  ) => {
    try {
      return request<Response<CategoryResponse>>({
        method: "POST",
        path: `/ms-product/platform/category/page`,
        data: params,
        includeCredentials: true,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  };

/**
 * 商品中心->电商品牌->爱库存品牌分类  新增和编辑弹窗里关联供应链分类下拉数据
 * @param {*} data
 * @returns
 */
  export const GetCategoryList = (
    params?: any,
  ) => {
    try {
      return request<Response<any>>({
        method: "GET",
        path: `/ms-product/platform/ThirdPlatform/akc/category/list?categoryId=${params?.categoryId}`,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  };

  /**
 * 商品中心->电商品牌->爱库存品牌分类  新增分类
 * @param {*} data
 * @returns
 */
  export const GetCategorySave = (
    params: CategorySaveParams,
  ) => {
    try {
      return request<Response<any>>({
        method: "POST",
        path: `/ms-product/platform/category/save`,
        data: params,
        includeCredentials: true,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  };

    /**
 * 商品中心->电商品牌->爱库存品牌分类  编辑分类
 * @param {*} data
 * @returns
 */
  export const GetCategoryEdit = (
    params: CategorySaveParams,
  ) => {
    try {
      return request<Response<any>>({
        method: "POST",
        path: `/ms-product/platform/category/edit`,
        data: params,
        includeCredentials: true,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  };

  /**
 * 商品中心->电商品牌->爱库存品牌分类  删除分类
 * @param {*} data
 * @returns
 */
  export const GetCategoryDelList = (
    categoryId: string,
  ) => {
    try {
      return request<Response<CategoryListResponse>>({
        method: "GET",
        path: `/ms-product/platform/category/del/${categoryId}`,
        includeCredentials: true,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  }
  
    /**
 * 商品中心->电商品牌->爱库存品牌分类  品牌管理页的品牌管理列表
 * @param {*} data
 * @returns
 */
  export const GetCategoryManage = (
    params: CategoryManageParams,
  ) => {
    try {
      return request<Response<CategoryManageResponse>>({
        method: "POST",
        path: `/ms-product/platform/category/manage`,
        data: params,
        includeCredentials: true,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  }

      /**
 * 商品中心->电商品牌->爱库存品牌分类  品牌管理页的修改排序
 * @param {*} data
 * @returns
 */
  export const GetEditSort = (
    categoryId: string,
    thirdBrandId: any,
    sort: any,
  ) => {
    try {
      return request<Response<any>>({
        method: "GET",
        path: `/ms-product/platform/category/brand/edit-sort/${categoryId}/${thirdBrandId}/${sort}`,
        includeCredentials: true,
      });
    } catch (error) {
      // 处理请求失败的情况
      console.error("Request failed:", error);
    }
  }