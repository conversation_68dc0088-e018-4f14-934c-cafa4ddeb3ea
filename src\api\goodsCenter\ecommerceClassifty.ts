import request from "@/request";
import { shop } from "@/types/shop/shop";
import { Response, PaginationResponse, Pagination } from "./../common";

const api = "/life-platform-dashboard";

export interface AkcInfo {
  id: any;
  productCount: number;
  supplyChainCategoryId: string;
  supplyChainCategoryName: string;
}
export interface AkcRecords {
  akcCategoryModelDtos: any[];
  categoryId: string;
  categoryName: string;
  grade: number;
  info: AkcInfo[];
  parentId: number;
  id?: string;
}
export interface AkcCategoryResponse {
  records: AkcRecords[];
  total: number;
}

export interface AkcCategoryParams extends Pagination {
  categoryId?: string;
}
export interface IdDetail {
  id: any;
}

export interface AkcCategoryUpdateParams {
  akcIds: any[];
  categoryId: string;
}

export const getPaginationCategoryList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/platform/prod/category/newPlatCategory`,
    data: params,
  });

// 获取商品分类列表
export const GetCommodityList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: params.categoryId
      ? `/ms-product/platform/prod/category/list?categoryId=${params.categoryId}`
      : `/ms-product/platform/prod/category/list`,
    data: params,
  });

export const saveCategory = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/platform/prod/category`,
    data: params,
  });

export const updateCategory = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "PUT",
    path: `/ms-product/platform/prod/category`,
    data: params,
  });

export const getCategoryInfo = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/platform/prod/category/info/${params.categoryId}`,
    data: params,
  });

export const GetThirdCategory = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/supply-third-category/page?page=${params.page}&size=${params.size}`,
    data: params,
  });

export const GetListByThird = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/third-product-category/list-by-third-type?level=1&thirdType:=XIAO_DIAN`,
    data: params,
  });

export const GetSaveCategoryRef = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/product-service/plat-third-category/save-category-ref`,
    data: params,
  });

//爱库存分类平台的分类
export const GetAkcCategoryPage = (params: AkcCategoryParams) =>
  request<Response<AkcCategoryResponse>>({
    method: "POST",
    path: `/ms-product/platform/prod/akcCategory/akcNewPlatCategory`,
    data: params,
    includeCredentials: true,
  });

//爱库存分类详情
export const GetAkcCategoryDetail = (params: IdDetail) =>
  request<Response<any>>({
    method: "POST",
    path: `/ms-product/platform/prod/akcCategory/detial`,
    data: params,
    includeCredentials: true,
  });

//爱库存分类修改绑定关系
export const GetAkcCategoryUpdate = (params: AkcCategoryUpdateParams) =>
  request<Response<any>>({
    method: "POST",
    path: `/ms-product/platform/prod/akcCategory/update`,
    data: params,
    includeCredentials: true,
  });

//爱库存二级分类接口
export const GetSupplyChainCategoryList = () =>
  request<Response<any>>({
    method: "POST",
    path: `/ms-product/platform/prod/akcCategory/supplyChainCategoryList`,
    includeCredentials: true,
  });
