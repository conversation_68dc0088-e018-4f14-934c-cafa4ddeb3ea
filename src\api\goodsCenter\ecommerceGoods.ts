import request from "@/request";
import { shop } from "@/types/shop/shop";
import { Response, PaginationResponse } from "./../common";

const api = "/life-platform-dashboard";

//列表
export const GetProductPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/page?page=${params.page}&size=${params.size}&sort=${params.sort}`,
    data: params,
  });

export const ExportInterface = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/platform/task/trip-task`,
    data: params,
  });

export const violationOffSale = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/violation-off-sale`,
    data: params,
  });

//违规下架原因ViolationOffSaleParams
export const GetViolationDetail = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/product/violation-details/${params.productId}`,
    data: params,
  });

//新列表
export const GetNewProductPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/page?page=${params.page}&size=${params.size}&sort=${params.sort}`,
    data: params,
  });

//上架，下架，修改列表
export const GetAuditProductPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/page?page=${params.page}&size=${params.size}&sort=${params.sort}`,
    data: params,
  });

//供应仓查询
export const GetSupplierPage = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/api/v1/platform/supplier/getSupplierPage?page=${params.page}&size=${params.size}`,
    data: params,
  });

//供应链查询
export const GetDictItem = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/infra-service/dict/items?dictCode=${params.dictCode}`,
    data: params,
  });

//详情
export const getCommodityInfoById = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/product/details/${params.productId}`,
    data: params,
  });

//新详情
export const getNewCommodityInfoById = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/history/queryProductSnapshotInfo?historyId=${params.id}`,
    data: params,
  });

//详情标红
export const getCommodityInfoByIdSty = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/product/audit/update-details/${params.productId}`,
    data: params,
  });
export const GetSetSeq = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "PUT",
    path: `/life-platform-dashboard/product/seq/${params.productId}/?seq=${params.seq}`,
    data: params,
  });
export const GetWaterSoldNum = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "PUT",
    path: `/life-platform-dashboard/product/water-sold-num/${params.productId}/?waterSoldNum=${params.seq}`,
    data: params,
  });

//违规下架ViolationOffSaleParams
export const GetViolationOffSale = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/violation-off-sale`,
    data: params,
  });

//新违规下架ViolationOffSaleParams
export const GetNewViolationOffSale = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/violation-off-sale`,
    data: params,
  });

//新违规下架原因ViolationOffSaleParams
export const GetNewViolationDetail = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/product/violation-details/history/${params.id}`,
    data: params,
  });

//驳回原因
export const GetAuditDetails = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/product/audit-details/${params.productAuditId}`,
    data: params,
  });

//驳回修改申请
export const GetRejectUpdate = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject/update`,
    data: params,
  });
//全部驳回修改申请
export const GetRejectUpdateByCon = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject/update/by-conditions`,
    data: params,
  });

//驳回上架申请
export const GetRejectPutOn = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject/put-on-shelves/`,
    data: params,
  });

//新驳回上架申请
export const GetNewRejectPutOn = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject`,
    data: params,
  });

//新批量驳回上架申请
export const GetNewBatchReject = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/batchReject`,
    data: params,
  });
//全部驳回上架申请
export const GetRejectPutOnByCon = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject/put-on-shelves/by-conditions`,
    data: params,
  });

//驳回下架申请
export const GetRejectPullOff = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject/pull-off-shelves`,
    data: params,
  });

//全部驳回下架申请
export const GetRejectPullOffByCon = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/reject/pull-off-shelves/by-conditions`,
    data: params,
  });

//驳回原因
export const GetDictCode = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/infra-service/dict/items?dictCode=VIOLATION_CAUSE`,
    data: params,
  });

//新驳回原因
export const GetNewDictCode = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/plat-dict/items?dictCode=${params.dictCode}`,
    data: params,
  });

//通过修改申请
export const GetApprovalUpdate = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval/update`,
    data: params,
  });
//全部通过修改申请
export const GetApprovalUpdateByCon = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval/update/by-conditions`,
    data: params,
  });

//通过上架申请
export const GetApprovalPutOn = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval/put-on-shelves`,
    data: params,
  });

//新通过上架申请
export const GetAuditApproval = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval`,
    data: params,
  });

//新批量通过上架申请
export const GetAuditBatchApproval = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/batchApproval`,
    data: params,
  });

//全部通过上架申请
export const GetApprovalPutOnByCon = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval/put-on-shelves/by-conditions`,
    data: params,
  });
//通过下架申请
export const GetApprovalPullOff = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval/pull-off-shelves`,
    data: params,
  });

//全部通过下架申请
export const GetApprovalPullOffByCon = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/approval/pull-off-shelves/by-conditions`,
    data: params,
  });

//审核状态
export const GetDictItems = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-merchant-dashboard/merchant-dict/items?dictCode=${params.dictCode}`,
    data: params,
  });

//审核历史列表
export const GetAuidHistory = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/life-platform-dashboard/product/audit/history/page?page=${params.page}&size=${params.size}&sort=${params.sort}`,
    data: params,
  });

//查询审核历史操作人下拉列表（支持模糊查询）（审核历史使用）
export const GetOperatorUserList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/life-platform-dashboard/product/audit/history/queryOperatorInfoList`,
    data: params,
  });

//-----规格管理------------------------------------------------------------------------------

export const getStandardPaginationList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/plat/spec/page`,
    data: params,
  });

export const deleteStandardById = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "DELETE",
    path: `/ms-product/plat/spec/${params.id}`,
    data: params,
  });

export const getStandardValueById = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/plat/spec/listSpecValue/${params.specId}`,
    data: params,
  });

export const addStandard = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/plat/spec`,
    data: params,
  });

export const updateStandard = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "PUT",
    path: `/ms-product/plat/spec`,
    data: params,
  });

//--------------商品积分调整------------------------------------------------------------------------
export const getIntegralPaginationList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/platform/prod/pv/page`,
    data: params,
  });

export const addIntegralItem = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/platform/prod/pv`,
    data: params,
  });

export const deleteIntegralItem = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "DELETE",
    path: `/ms-product/platform/prod/pv`,
    data: params,
  });

export const updateIntegralItemPv = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "PUT",
    path: `/ms-product/platform/prod/pv`,
    data: params,
  });
export const setGlobalPv = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "PUT",
    path: `/ms-product/platform/prod/config`,
    data: params,
  });

export const getGlobalPv = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: `/ms-product/platform/prod/config`,
    data: params,
  });

export const getSkuList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/ms-product/platform/prod/pv/sku/page`,
    data: params,
  });

export const GetCommProcessLog = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `/wd-life-app-platform/commProcessLog/page/list`,
    data: params,
  });
// 获取商品分类列表
export const GetCommodityList = (params: any) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "GET",
    path: params.categoryId
      ? `/ms-product/platform/prod/category/list?categoryId=${params.categoryId}`
      : `/ms-product/platform/prod/category/list`,
    data: params,
  });
