import request from "@/request";
import { getDefaultTokenProvider } from "@/request/tokenProvider";

import { Response, PaginationResponse } from "../common";

/*
  判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
  子应用调用主应用方法获取相应的方法或数据--window.parent.mainAppMethod()
  脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
*/

export interface shopParams {
  current: number;
  size: number;
  prodName?: undefined;
  secondPlatCategoryId?: undefined;
  prodCreateTimeStart?: undefined;
  status?: undefined;
  shopId?: undefined;
}

//列表

export const getPage = (params: shopParams) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/gold/prod/page`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//商品分类
export const getCategoryList = () => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/gold/category/list`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//绑定店铺

export const getShopPageList = (params:any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/api/v1/platform/shop/getShopPage`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//下架选择

export const getUpdateStatus = (params: any) => {
  try {
    return request<Response<any>>({
      method: "PUT",
      path: `/ms-product/platform/gold/prod/updateStatus`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//详情

export const getProdInfo = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/gold/prod/info/${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
