import request from "@/request";

import { Response, Pagination } from "./../common";
const api = "/life-platform-dashboard";

export interface PageParams extends Pagination {
  shopName?: string; // 店铺名称
  prodName?: string; // 商品名称
  prodId?: string; // 商品ID
  firstCategoryId?: number; // 商品分类（一级 & 二级）
  secondCategoryId?: number;
  auditStatus?: string; // 状态
  buyStartTime?: string; // 售卖开始 & 结束时间
  buyEndTime?: string;
  useStartTime?: string; // 使用开始 & 结束时间
  useEndTime?: string;
  createStartTime?: string; // 提交开始 & 结束时间
  createEndTime?: string;
  auditStartTime?: string; // 审核开始 & 时间
  auditEndTime?: string;
  activityFlagList?: string[]; // 营销推广
}

export interface PageResponse {
  records: PageRecords[];
  total: number;
}

export interface PageRecords {
  shopId: number; // 店铺ID
  shopName: string; // 店铺名称
  productName: string; // 商品名称
  productId: number; // 商品ID
  stock: number; // 总库存
  limitFlag: number; // 否限售 0：不限售、1：限售
  quantitySold: number; // 售卖数量
  writeOffQuantity: number; // 核销
  waterSoldNum: number; // 注水销量
  productStatus: number; // 商品状态，0:上架、1:下架、2:已下架
  auditStatus: string; // 商品审核状态
  buyStartTime: string; // 购买开始时间
  buyEndTime: string; // 购买结束时间
  sellingPrice: number; // 实际销售价格 - 团购价
  oriPrice: number; // 市场价
  masterPicture: string; // 主图信息
  canUseTimeType: string; // 购买后可以使用时间类型 0: 固定时间 1:几天后
  afterUseDay: number; // 购买后几天可以使用
  useStartDate: string; // 购买后可以使用时间,固定开始时间
  useEndDate: string; // 购买后可以使用时间,固定结束时间
  activityFlag: string; // 营销活动标识
}

export interface ViolationParams {
  productId: string; // 商品ID
  productStatus: number; // 商品状态
  reason: string; // 违规下架原因
}

export interface GetShopCategoryParamsV2 {
  categoryCode?: string;
  categoryName?: string;
  show?: number;
  isAllowEmptySub?: boolean;
  isCheckAllLevel?: boolean;
}

export interface ShopCategoryListItemV2 {
  categoryCode: string;
  categoryName: string;
  level: number;
  pcode: string;
  shopCategoryId: number;
  shopQty: number;
  show: number;
  sort: number;
  subCategoryList: SubCategoryListItem[];
}

export interface SubCategoryListItem {
  categoryCode?: string;
  categoryName?: string;
  level?: number;
  shopCategoryId?: number;
  shopQty?: number;
  show?: number;
  sort?: number;
  subCategoryList?: { [key: string]: any }[];
}

export interface EditGrouponProductCategoryParams {
  firstCategoryId: string;
  productId: number;
  secondCategoryId: string;
  thirdCategoryId: string;
}

export interface platGrouponProductTimeVOProps {
  useTimeStr: string;
}

export interface platGrouponProductUseTimeInfoVOProps {
  canUseTimeType: string;

  platGrouponProductTimeVO: platGrouponProductTimeVOProps;
}

export interface hmGroupProductDetailBaseInfoVOProps {
  tips: string; // 售卖时间
  firstCategory: string; // 一级 & 二级分类
  secondCategory: string;
  shopId: number; // 店铺ID
  productName: string; // 团购标题
  sellingPrice: number; // 团购价格
  oriPrice: number; // 原价
  discount: number; // 折扣
  images: string[]; // 轮播图
  pvRate: number; // 积分比例
  purchasingNotice: string; // 须知
  guarantee: string; // 保障
  expirationDate: string; // 有效期
  introduceImages: string; // 团购介绍图片
  availableDays: string; // 可用日
  availableRanges: string; // 可用时间段
  useRule: string; // 使用规则
  activityFlag: string; // 营销活动标识
  statusDescription: string; // 详情上面的状态标题
  platGrouponProductUseTimeInfoVO: platGrouponProductUseTimeInfoVOProps;
  productId: string;
}

export interface shopLogoProps {
  path: string;
  domain: string;
  url: string; // 图片全路径
}

export interface shopTimeProps {
  timeRange: string;
  allTime: number;
}

export interface shopBaseInfoVOProps {
  shopId: string;
  shopLogo: shopLogoProps;
  distance: number;
  distanceUnit: string;
  shopAddress: string;
  open: boolean;
  shopTime: shopTimeProps;
  mobile: string;
}

export interface hmGrouponProductSkuDTOSProps {
  skuId: number; // 团购套餐ID
  skuName: string; // 明细名称
  price: number; // 价格
  copies: number; // 份数
}

export interface hmGrouponProductGroupInfoDTOS {
  productId: number; // 团购商品ID
  groupName: string; // 分组名称
  hmGrouponProductSkuDTOS: hmGrouponProductSkuDTOSProps[];
}

export interface DetailResponse {
  hmGroupProductDetailBaseInfoVO: hmGroupProductDetailBaseInfoVOProps; // 团购基本信息
  shopBaseInfoVO: shopBaseInfoVOProps; // 店铺基本信息
  hmGrouponProductGroupInfoDTOS: hmGrouponProductGroupInfoDTOS[]; // 团购套餐信息
}

export interface AuditDataParams {
  productIds: number[]; // 商品id
  auditResult: number; // 审核结果 0-失败、1-成功
  auditDesc: string; // 审核描述
  auditImages?: string[]; // 审核图片
}

export interface PageListParams extends Pagination {
  categoryName?: string;
  status?: any;
}

export interface QueryAllResponse {
  name?: string;
  thirdName?: string;
  id?: number;
  thirdId?: number;
}

export interface AddParams {
  third: any;
  categoryName: string;
  status: any;
  seq: any;
  picId: any;
  thirdId: any;
  categoryId?: number;
}

/*
  判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
  子应用调用主应用方法获取相应的方法或数据--window.parent.mainAppMethod()
  脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
*/

//商品中心->本地生活->团购商品管理   团购商品列表
export const GetGrouponProductLists = (
  page: number,
  size: number,
  sort: string,
  params: PageParams
) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/grouponProduct/page?page=${page}&size=${size}&sort=${sort}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

/**
 * 商品中心->本地生活->团购商品管理 修改注水数据量
 * @returns
 */
export const UpdateWaterSoldNum = (productId: string, waterSoldNum: number) => {
  try {
    return request<Response<null>>({
      method: "PUT",
      path: `${api}/grouponProduct/water-sold-num/${productId}?waterSoldNum=${waterSoldNum}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

/**
 * 商品中心->本地生活->团购商品管理 违规下架商品
 * @returns
 */
export const ViolationCommodity = (params: ViolationParams) => {
  try {
    return request<Response<null>>({
      method: "POST",
      path: `${api}/grouponProduct/violation-off-sale`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

// 获取-查询店铺分类列表V2
export const getShopCategoryListV2 = (params: GetShopCategoryParamsV2) => {
  try {
    return request<Response<ShopCategoryListItemV2[]>>({
      method: "POST",
      path: `${api}/shop-category-v2/list?isAllowEmptySub=${params.isAllowEmptySub}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

// 修改团购商品所属分类
export const editGrouponProductCategory = (
  params: EditGrouponProductCategoryParams
) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/grouponProduct/category/edit`,
      data: params,
      showMsgError: false,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->团购商品管理/审核 列表详情查询
 * @returns
 */
export const QueryGroupPurchaseDetail = (productId: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/grouponProduct/get/${productId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->团购商品审核 头部Tab切换的团购审核状态和对应的商品数量
 * @returns
 */
export const GeNumByAuditStatus = () => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/grouponProduct/numByAuditStatus`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->团购商品审核 列表分页查询
 * @param {*} data
 * @returns
 */
export const GetGrouponProductList = (
  page: number,
  size: number,
  params: PageParams,
  sort?: string
) => {
  try {
    return request<Response<PageResponse>>({
      method: "POST",
      path: `${api}/grouponProduct/page-audit?page=${page}&size=${size}&sort=${sort}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->团购商品审核 列表/批量审核
 * @param {*} data
 * @returns
 */
export const ReviewProduct = (params: AuditDataParams) => {
  try {
    return request<Response<null>>({
      method: "POST",
      path: `${api}/grouponProduct/reviewProduct`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->吃喝玩乐套餐分类 列表
 * @param {*} data
 * @returns
 */
export const GetPageList = (params: PageListParams) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/prod/category/tripartite/category/life/pageList`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->吃喝玩乐套餐分类 关联供应商分类
 * @param {*} data
 * @returns
 */
export const GetQueryAll = () => {
  try {
    return request<Response<QueryAllResponse[]>>({
      method: "GET",
      path: `/ms-product/platform/prod/category/tripartite/category/life/queryAll`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

export const GetAdd = (params: AddParams) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/prod/category/tripartite/category/life/add`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->吃喝玩乐套餐分类 编辑套餐分类
 * @param {*} data
 * @returns
 */
export const GetEdit = (params: AddParams) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/ms-product/platform/prod/category/tripartite/category/life/edit`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->吃喝玩乐套餐分类 根据id获取套餐分类
 * @param {*} data
 * @returns
 */
export const getById = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/prod/category/tripartite/category/life/info/byId?categoryId=${params.categoryId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->吃喝玩乐套餐分类 根据id删除套餐分类
 * @param {*} data
 * @returns
 */
export const GetDel = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/prod/category/tripartite/category/life/del?categoryId=${params.categoryId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理 列表
 * @param {*} data
 * @returns
 */
export const queryProjectPageByCondition = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-community-admin/platform/estate/project/queryPageByCondition`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理 上架
 * @param {*} data
 * @returns
 */
export const publishProject= (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-community-admin/platform/estate/project/publish?projectId=${params.projectId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理 下架
 * @param {*} data
 * @returns
 */
export const unPublishProject= (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-community-admin/platform/estate/project/unPublish?projectId=${params.projectId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理 删除
 * @param {*} data
 * @returns
 */
export const deleteProject= (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-community-admin/platform/estate/project/delete?projectId=${params.projectId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理  创建
 * @param {*} data
 * @returns
 */
export const createProject= (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-community-admin/platform/estate/project/create`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理  编辑
 * @param {*} data
 * @returns
 */
export const editProject= (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-community-admin/platform/estate/project/edit`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};

/**
 * 商品中心->本地生活->房产项目管理  详情，用于编辑回显
 * @param {*} data
 * @returns
 */
export const getProjectDetail = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-community-admin/platform/estate/project/queryDetail?projectId=${params.projectId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
  }
};
