import request from "@/request";
import { getDefaultTokenProvider } from "@/request/tokenProvider";

import { Response, PaginationResponse } from "../common";

/*
  判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
  子应用调用主应用方法获取相应的方法或数据--window.parent.mainAppMethod()
  脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
*/

export interface shopParams {
  page: number;
  size: number;
  name?: string;
  endTime?: string;
  startTime?: string;
}

export interface sectionParams {
  name: string;
}
const tokenObj = getDefaultTokenProvider();

//列表

export const getPage = (params: shopParams) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/page-search?page=${
        params.page
      }&size=${params.size}${params.name ? `&name=${params.name}` : ""}${
        params.endTime ? `&endTime=${params.endTime}` : ""
      }${params.startTime ? `&startTime=${params.startTime}` : ""}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//新增

export const getAddSection = (params: sectionParams) => {
  try {
    return request<Response<PaginationResponse<any>>>({
      method: "POST",
      path: `/life-platform-dashboard/section/add`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 专区列表删除

export const getSectionDelete = (id: string) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/life-platform-dashboard/section/delete?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区详情

export const getSectionDetail = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/detail?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区列表

export const getSectionList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/category/get-list-by-section-id?page=${params.page}&size=${params.size}&sectionId=${params.sectionId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区删除

export const getDeleteCategory = (id: string) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/life-platform-dashboard/section/category/delete?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
//排序

export const getEditSort = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/edit-sort?id=${params.id}&sortOrder=${params.sortOrder}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//上下架

export const getUpAndDown = (id: string) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/up-and-down-selves?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区名称

export const getEditSectionName = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/edit`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//专区搜索开关

export const getSwitchSearch = (id: string) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/switch-search?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//设置Banner列表

export const getBannerList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/banner/get-banner-list-vo-by-section-id?page=${params.page}&size=${params.size}&sectionId=${params.sectionId}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑banner轮播属性

export const getSetBannerSection = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/set-banner`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
//banner编辑属性新增

export const getAddBanner = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/banner/add`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//字典选择专区
export const getSelectSection = (name: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/select?dictEnum=${name}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//设置banner排序

export const getEditBannerSort = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/banner/edit-sort?id=${params.id}&sortOrder=${params.sortOrder}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//设置banner上下架

export const getBannerUpAndDown = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/banner/up-and-downselves?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑banner属性详情

export const getBannerDetail = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/banner/detail?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑banner属性保存

export const getUpdateBanner = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/banner/update`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//删除设置Banner列表

export const getDeleteBanner = (id: string) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/life-platform-dashboard/section/banner/delete?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑专区新增分类

export const getAddCategory = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/add`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//获取平台分类

export const getCategoryList = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/ms-product/platform/prod/category/list${
        id ? `?categoryId=${id}` : ""
      }`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//获取三方分类

export const getThCategoryList = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-merchant-dashboard/mktPromoter/product/manager/thirdCategoryList${
        id ? `?categoryId=${id}` : ""
      }`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//获取供应链

export const getSelectSrction = () => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/select?dictEnum=SUPPLY_CHAIN`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//获取供货仓

export const getSupplierPage = () => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/api/v1/platform/supplier/getSupplierPage?page=1&size=10000`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑分类详情

export const getCategoryDetail = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/section/category/detail?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑分类列表
export const getCategoryProductList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/product/get-section-category-product-list?page=${
        params.page
      }&size=${params.size}${params.id ? `&id=${params.id}` : ""}${
        params.prodName ? `&prodName=${params.prodName}` : ""
      }${
        params.platCategoryIdList
          ? `&platCategoryIdList=${params.platCategoryIdList}`
          : ""
      }${
        params.thirdPlatCategoryIdList
          ? `&thirdPlatCategoryIdList=${params.thirdPlatCategoryIdList}`
          : ""
      }${params.prodSource ? `&prodSource=${params.prodSource}` : ""}${
        params.supplierId ? `&supplierId=${params.supplierId}` : ""
      }`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑分类列表排序

export const getCategoryEditSort = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/product/edit-sort?id=${params.id}&sortNum=${params.sortNum}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
//编辑分类列表删除

export const getCategoryDelete = (id: any) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/life-platform-dashboard/section/category/product/delete?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑分类列表全部删除

export const getCategoryAllDelete = (id: any) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/life-platform-dashboard/section/category/product/deleteAll?id=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//编辑分类列表批量删除

export const getBatchDelete = (params: any) => {
  try {
    return request<Response<any>>({
      method: "DELETE",
      path: `/life-platform-dashboard/section/category/product/batch-delete`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//修改外显属性名称

export const getModifyCategory = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/modify`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//选择商品列表

export const getByParams = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/product/get-product-by-params?page=${
        params.page
      }&size=${params.size}${params.id ? `&id=${params.id}` : ""}${
        params.prodName ? `&prodName=${params.prodName}` : ""
      }${
        params.platCategoryIdList
          ? `&platCategoryIdList=${params.platCategoryIdList}`
          : ""
      }${
        params.thirdPlatCategoryIdList
          ? `&thirdPlatCategoryIdList=${params.thirdPlatCategoryIdList}`
          : ""
      }${params.prodSource ? `&prodSource=${params.prodSource}` : ""}${
        params.supplierId ? `&supplierId=${params.supplierId}` : ""
      }${params.thirdProdId ? `&thirdProdId=${params.thirdProdId}` : ""}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//上传全部筛选结果

export const getAllBatch = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/product/batch-insert-all`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//上传多选

export const getBatchInsert = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/section/category/product/batch-insert`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
