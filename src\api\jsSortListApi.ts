import request from "@/request";
import { Response } from "./common";

const api = "/life-platform-dashboard";

// 平台的分类(不分页)
export const listTree = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/platform/prod/category/listTree?categoryId=${params.categoryId}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 平台-京东分类
export const jdplatJd = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/category/jd/plat-jd`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 平台-京东分类下拉查询
export const baseJdList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/category/jd/baseJd/list`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 平台-后台分类绑定京东分类
export const jdPlatBindJd = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/category/jd/platBindJd`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 京东商品列表
export const jsProductList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/jd/product/es/page?page=${params.page}&size=${params.size}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 修改京东商品注水销量
export const waterSoldNum = (params: any) => {
  try {
    return request<Response<any>>({
      method: "PUT",
      path: `${api}/jd/product/waterSoldNum`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 京东商品详情
export const queryDetail = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/jd/product/detail?prodId=${params.prodId}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 商品违规下架原因-实时
export const queryReason = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/product/violation-details/${params.prodId}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 违规下架商品
export const offSale = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/product/violation-off-sale`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
