
import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response } from './common';

export interface LoginRequestParam {
  userName: string;
  password: string;
}

export interface LoginResponseData {
  accessToken: string;
  refreshToken: string;
}

export const accountLogin = (data: LoginRequestParam) =>
  request<Response<LoginResponseData>>({
    method: 'POST',
    path: '/wd-base-user/o/api/plat/login',
    data,
    includeCredentials: true,
  });

export const refreshToken = (tokenProvider = getDefaultTokenProvider()) =>
  request<Response<Pick<LoginResponseData, 'accessToken' | 'refreshToken'>>>({
    method: 'GET',
    path: `/life-platform-dashboard/plat-user/refreshToken?refreshToken=${tokenProvider.refreshToken}`,
    includeCredentials: true,
  });
