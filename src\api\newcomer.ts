import { reactive,toRaw } from 'vue';
import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response } from './common';

const api = '/life-platform-dashboard';

/*
  判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
  子应用调用主应用方法获取相应的方法或数据--window.parent.mainAppMethod()
  脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
*/
const tokenObj = reactive(getDefaultTokenProvider())

// 新人福利订单预警模版-编辑
export const newComerAlarmEdit = (params: any, tokenProvider = toRaw(tokenObj) || {}) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/newcomer-benefits-alarm/edit`,
    data: params,
    tokenProvider,
  });

// 新人福利订单预警-详情
export const newComerAlarmDetail = (tokenProvider = toRaw(tokenObj) || {}) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/newcomer-benefits-alarm/detail`,
    tokenProvider,
  });
