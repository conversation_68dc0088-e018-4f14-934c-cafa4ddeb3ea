import request from "@/request";

import { Response, PaginationResponse } from "../../common";

//获取金币配置
export const getCoinRatio = () => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/wd-life-app-platform/coinRatio/get`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//金币配置保存
export const getUpdateCoin = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/coinRatio/update`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
