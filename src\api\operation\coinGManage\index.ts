import request from "@/request";

import { Response, PaginationResponse } from "../../common";

//金币管理列表
export const getPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/wd-life-app-platform/api/v1/plat/coin/page`,
      data: params,
      showMsgError: false,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
