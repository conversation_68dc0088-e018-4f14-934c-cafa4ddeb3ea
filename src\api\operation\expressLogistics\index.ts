import request from "@/request";

import { Response, PaginationResponse } from "../../common";

//快递物流
export const getPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/plat/transport/groupPage?page=${params.current}&size=${params.size}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//是否开启

export const getGroupStatus = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/plat/transport/updateGroupStatus`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//副本

export const getCopyGroup = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/plat/transport/copyGroup`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//新增运费模板

export const getGroupInfo = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `/life-platform-dashboard/plat/transport/saveGroupInfo`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//查看模板详情

export const getGroupInfoById = (id: string) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `/life-platform-dashboard/plat/transport/getGroupInfo?groupId=${id}`,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
