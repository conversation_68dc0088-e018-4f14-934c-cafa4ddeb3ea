
import request from "@/request";

import { Response, PaginationResponse } from "../common";

//最小下单金额-保存配置
export interface SaveCnfSysValParams {
  keyVl: string; //健  WD_ORDER_MIN_PRICE 我店生活下单最小金额
  val: string; //值 （金额）
}

//最小下单金额-获取配置
export const getStartList = (
  key: string,
) =>
  request<Response<any>>({
    method: "GET",
    path: `/wd-life-app-platform/cnfSys/getCnfSysVal?key=${key}`,
    includeCredentials: true,
  });

  //最小下单金额-保存配置
export const getListPage = (
  params: SaveCnfSysValParams,
) =>
  request<Response<any>>({
    method: "POST",
    path: `/wd-life-app-platform/cnfSys/saveCnfSys`,
    data: params,
    includeCredentials: true,
  });

  //验证下单金额
export interface SaveCnfSysCheckParams {
  val: any;
}
export const getSaveCnfSysCheck = (
  params: SaveCnfSysCheckParams,
) =>
  request<Response<any>>({
    method: "POST",
    path: `/wd-life-app-platform/cnfSys/saveCnfSysCheck`,
    data: params,
    includeCredentials: true,
  });