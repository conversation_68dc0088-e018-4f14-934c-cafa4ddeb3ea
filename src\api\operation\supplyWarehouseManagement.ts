import request from "@/request";

import { Response, PaginationResponse, Pagination } from "../common";
import { appendParamsToUrl } from "@/utils/utils";

export interface ICreateOrUpdataSubsidy {
  id?: number;
  logisticsFlag: number;
  logisticsAmount: number;
  deliveryFlag: number;
  deliveryAmount: number;
}

export interface IGetSubsidyInfo {
  id: number;
  logisticsFlag: number;
  logisticsAmount: number;
  deliveryFlag: number;
  deliveryAmount: number;
}

export interface ISameCityRequestParams extends Pagination {
  createTimeEnd?: string;
  createTimeStart?: string;
  transportName?: string;
  updateTimeEnd?: string;
  updateTimeStart?: string;
}

export interface ISameCityItem {
  createTime: string;
  status: number;
  transportId: number;
  transportName: string;
  updateTime: string;
}

export interface ISameCityDetailResult {
  defaultDistance?: number;
  defaultFreight?: number;
  defaultWeight?: number;
  exceedDistance?: number;
  exceedDistancePrice?: number;
  exceedWeight?: number;
  exceedWeightPrice?: number;
  id?: number;
  isDefault?: number;
  shopId?: string;
  templateName?: string;
}

// 创建更新配置信息
export const createOrUpdataSubsidyervice = (params: ICreateOrUpdataSubsidy) =>
  request<Response<any>>({
    method: "POST",
    path: `/wd-life-app-platform/cnfExpensesSubsidy/updateInfo`,
    data: params,
  });

// 获取配置信息
export const getSubsidyInfoService = () =>
  request<Response<IGetSubsidyInfo>>({
    method: "POST",
    path: `/wd-life-app-platform/cnfExpensesSubsidy/getInfo`,
  });

  // 获取快递列表数据
export const getSameCityListService = (
  params: ISameCityRequestParams,
) =>
  request<Response<PaginationResponse<ISameCityItem[]>>>({
    method: "GET",
    path: appendParamsToUrl(`/life-platform-dashboard/plat/delivery/transport/page`, params),
  });

  // 创建模版
export const createSameCityService = (
  params,
) =>
  request<Response<any>>({
    method: "POST",
    path: `/life-platform-dashboard/plat/delivery/transport`,
    data: params,
  });

  // 模版详情
export const getSameCityDetailService = (
  params,
) =>
  request<Response<ISameCityDetailResult>>({
    method: "GET",
    path: `/life-platform-dashboard/plat/delivery/transport/info/${params.id}`,
  });

  // 修改运费模版
export const updateSameCityService = (
  params,
) =>
  request<Response<any>>({
    method: "PUT",
    path: `/life-platform-dashboard/plat/delivery/transport`,
    data: params,
  });