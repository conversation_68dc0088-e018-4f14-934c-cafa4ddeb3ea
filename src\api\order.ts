
import request from '@/request';
import { Response, PaginationResponse } from './common';
import { order, orderParams, orderExportParams } from '@/types/order/order';

const api = "/life-platform-dashboard";

// 获取-订单列表
export const getOrderListDataService = (params: orderParams) =>
  request<Response<PaginationResponse<order[]>>>({
    method: "POST",
    path: `${api}/channelorder/page?page=${params.page}&size=${params.size}`,
    data: params
  });

// 导出-订单列表
export const exportOrder = (params: orderExportParams) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/channelorder/export`,
    data: params
  });
