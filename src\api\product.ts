import request from '@/request';
import { getDefaultTokenProvider } from '@/request/tokenProvider';
import { Response, PaginationResponse } from './common';

const api = '/life-platform-dashboard';

// 获取列表数据
export const httpExamList = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<PaginationResponse<any[]>>>({
    method: 'POST',
    path: `${api}/product/audit/queryAuditingProductsPage?page=${params.page}&size=${params.size}`,
    data: params,
    tokenProvider,
  });

// 获取供货仓下拉列表
export const httpSupplierList = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/product/audit/querySupplierInfoList?name=${params.name}`,
    tokenProvider,
  });

// 获取审核修改详情
export const httpExamDetail = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/product/audit/update-details/${params.productId}`,
    tokenProvider,
  });
// 获取审核详情
export const httpDetails = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'GET',
    path: `${api}/product/details/${params.productId}`,
    tokenProvider,
  });

// 审核驳回
export const httpReject = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/product/audit/reject`,
    data: params,
    tokenProvider,
  });
// 审核驳回
export const httpBatchReject = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/product/audit/batchReject`,
    data: params,
    tokenProvider,
  });

// 审核通过
export const httpApproval = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/product/audit/approval`,
    data: params,
    tokenProvider,
  });

// 批量审核通过
export const httpBatchApproval = (params: any, tokenProvider = getDefaultTokenProvider()) =>
  request<Response<any>>({
    method: 'POST',
    path: `${api}/product/audit/batchApproval`,
    data: params,
    tokenProvider,
  });
