import request from '@/request';
import { shop, shopParams } from '@/types/shop/shop';
import { lifeShop, lifeShopParams, lifeShopRateParam } from '@/types/shop/lifeShop';
import { logHistory, logHistoryParams } from '@/types/shop/logHistory';
import { Response, PaginationResponse } from './common';

const api = "/life-platform-dashboard";

// 获取渠道店铺列表
export const getShopListDataService = (params: shopParams) =>
  request<Response<PaginationResponse<shop[]>>>({
    method: "POST",
    path: `${api}/channelshop/pageshop?page=${params.page}&size=${params.size}`,
    data: params
  });

// 模糊搜索渠道店铺名称
export const getShopName = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/channelshop/taglist`,
    data: params
  });

// 生活圈店铺列表
export const getLifeShopListDataService = (params: lifeShopParams) =>
  request<Response<PaginationResponse<lifeShop[]>>>({
    method: "POST",
    path: `${api}/channelshop/pagelifeshop?page=${params.page}&size=${params.size}`,
    data: params
  });

// 设置渠道同步签约比例
export const setChannelRate = (params: lifeShopRateParam) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/channelshop/update`,
    data: params
  });

// 获取日志列表
export const getLogListDataService = (params: logHistoryParams) =>
  request<Response<PaginationResponse<logHistory[]>>>({
    method: "POST",
    path: `${api}/channelshop/logpage?page=${params.page}&size=${params.size}`,
    data: params
  });
