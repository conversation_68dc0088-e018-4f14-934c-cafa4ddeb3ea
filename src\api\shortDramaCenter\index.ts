

import request from '@/request';
import { Response, PaginationResponse } from './../common';
import { order } from '@/types/order/order';

const api = "/life-platform-dashboard";

// 查询
export const getSearch = () =>
  request<Response<PaginationResponse<order[]>>>({
    method: "POST",
    path: `${api}/ad-config/csj-context/search`,
  });

// 编辑
export const saveEdit = (params) =>
    request<Response<PaginationResponse<order[]>>>({
      method: "POST",
      path: `${api}/ad-config/csj-context/edit`,
      data: params
    });

