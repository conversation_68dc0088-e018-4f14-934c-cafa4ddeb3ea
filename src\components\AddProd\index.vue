<template>
  <a-modal
    v-model:open="lookVisible"
    placement="center"
    title="选择商品"
    :destroy-on-close="true"
    width="60%"
    @cancel="cancelClick"
  >
    <!-- <a-form
      :model="formState"
      name="horizontal_login"
      layout="inline"
      autocomplete="off"
      class="mt20"
    >
      <a-form-item label="商品名称" name="prodName">
        <a-input
          v-model:value="formState.prodName"
          placeholder="请输入"
        ></a-input>
      </a-form-item>

      <a-form-item label="商品分类" name="secondCategoryId">
        <a-input
          v-model:value="formState.secondCategoryId"
          placeholder="请输入"
        ></a-input>
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="getListApi">查询</a-button>
        <a-button @click="resetClick" class="ml10">重置</a-button>
      </a-form-item>
    </a-form> -->
    <search-antd :form-list="props.formList" @on-search="handerSearch" />
    <div>
      <span style="color: red"
        >使用批量上架时将使用商品的默认售价且不设库存限制</span
      >
      <a-button
        type="primary"
        class="act-btn"
        v-if="props.isActionBtn"
        @click="piAddClick"
        >批量上架</a-button
      >
    </div>

    <a-table
      class="mt20 add-table"
      :columns="props.columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :is-radio="props.isRadio"
      @change="handleTableChange"
      :row-selection="props.isCheckbox ? rowSelection : null"
      :rowKey="(record) => record"
      :scroll="{ x: 1000 }"
    >
      <template #bodyCell="{ column, record }">
        <template
          v-if="column.key === 'prodName' || column.key === 'productName'"
        >
          <div class="proinfo-css">
            <img
              class="img-css"
              :src="
                record.picInfo
                  ? record.picInfo.path
                  : record.picPath ||
                    record.pic ||
                    record.prodImg ||
                    record.productPic
              "
            />
            <div>
              <p class="ml10">{{ record.prodName || record.productName }}</p>
              <p class="ml10" v-if="record.skuNumber">{{ record.skuNumber }}</p>
            </div>
          </div>
        </template>
        <template v-if="column.key === 'goldPrice'">
          <div class="actions-box">
            ¥
            <a-button :disabled="true" class="btn-w">{{
              record.cashPrice || record.price
            }}</a-button>
            +
            <a-button :disabled="true" class="btn-w">{{
              record.goldPrice || record.goldCoin
            }}</a-button>
            <span class="ml10">金币</span>
          </div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button @click="cancelClick">取消</a-button>
      <a-button type="primary" @click="submitDisAgree">{{
        props.btnText
      }}</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, toRef, computed } from "vue";
// import { getSkuList } from "@/api/goodsCenter/ecommerceGoods";
import {
  GetQueryPage,
  getGoldCategoryDrop,
  GetSpecialDetailYZH,
} from "@/api/activityCenter/goldCoin";
import SearchAntd from "@/components/SearchAntd/index.vue";

// import TableListAntd from "@/components/TableListAntd/index.vue";
import { message } from "woody-ui";

import { useRoute } from "vue-router";
const route = useRoute();

const props = defineProps({
  isRadio: {
    type: Boolean,
    default: () => false,
  },
  btnText: {
    type: String,
    default: "确定",
  },
  isActionBtn: {
    type: Boolean,
    default: () => false,
  },
  isRefresh: {
    type: Boolean,
    default: false,
  },
  apiType: {
    type: String,
    default: "query",
  },
  columns: {
    type: Array,
    default: () => [],
  },
  rowKey: {
    type: String,
    default: "skuId",
  },
  isCheckbox: {
    type: Boolean,
    default: true,
  },
  formList: {
    type: Array,
    default: () => [],
  },
});
const classfityOpt = ref([]);
// const formState = ref({
//   prodName: "",
//   secondCategoryId: "",
// });
let formState = {};
const selectedRowKeys = ref([]);
const rowSelection = computed(() => ({
  type: props.isRadio ? "radio" : "checkout",
  selectedRowKeys,
  onChange: onSelectChange,
  getCheckboxProps: (record) => ({
    disabled: record.checked === true, // Column configuration not to be checked
  }),
}));
// 列表复选框 事件
const onSelectChange = (newSelectedRowKeys) => {
  console.log(newSelectedRowKeys, "newSelectedRowKeys");
  selectedRowKeys.value = newSelectedRowKeys;
  const rowId = newSelectedRowKeys.map((v) => {
    return v[props.rowKey];
  });
  emits("select-change", selectedRowKeys.value, rowId);
  // checkCount.value = newSelectedRowKeys.length;
};
// const columns = [
//   {
//     title: "三方商品ID",
//     dataIndex: "thirdProductId",
//     key: "thirdProductId",
//     fixed: "left",
//     width: 180,
//   },
//   {
//     title: "商品信息",
//     dataIndex: "prodName",
//     key: "prodName",
//     width: 260,
//   },
//   {
//     title: "分类",
//     dataIndex: "secondCategoryName",
//     key: "secondCategoryName",
//     width: 150,
//   },
//   {
//     title: "售价",
//     width: 260,
//     customRender: ({ record }) => {
//       return `¥ ${record.price}` + `¥ ${record.goldCoin} 金币`
//     },
//   },
//   {
//     title: "库存",
//     width: 150,
//     customRender: ({ record }) => {
//       if(record.totalStocks != undefined){
//         return record.totalStocks
//       }else{
//         return record.inventory
//       }
//     },
//   },
//   {
//     title: "市场价",
//     dataIndex: "marketPrice",
//     key: "marketPrice",
//     width: 150,
//   },
// ];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const piAddClick = () => {
  if (!selectedRowKeys.value.length) {
    message.error("请勾选商品");
    return;
  }
  emits("action-click", selectedRowKeys.value);
};

const handleTableChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getListApi();
};

const emits = defineEmits([
  "onSucceed",
  "reject-click",
  "close-click",
  "add-click",
  "prod-select",
  "action-click",
  "select-change",
]);
const dataSource = ref([]);
const handerSearch = (data) => {
  formState = data;
  (pagination.value.current = 1), (pagination.value.pageSize = 10);
  getListApi();
};
// 获取列表数据
const getListApi = async () => {
  if (props.apiType === "YZH") {
    const params = {
      page: pagination.value.current,
      size: pagination.value.pageSize,
      zoneId: route.query.zoneId,
      ...formState,
    };
    const res = await GetSpecialDetailYZH(params);
    if (res.code === 0) {
      dataSource.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  } else {
    const params = {
      current: pagination.value.current,
      size: pagination.value.pageSize,
      activityProdFlag: false,
      activityType: 3,
      ...formState,
    };
    const res = await GetQueryPage(params);
    if (res.code === 0) {
      dataSource.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

// const selectChange = (data, data1) => {
//   console.log(data, data1, "7897890");
//   selectData.value = data;
//   emits("prod-select", data);
// };
// const resetClick = () => {
//   pagination.value.current = 1,
//   pagination.value.pageSize = 10
//   getListApi();
// };
const lookVisible = ref(true);

const cancelClick = () => {
  lookVisible.value = false;
  emits("close-click");
};
const open = (code: String, isAudit: Boolean) => {};
const submitDisAgree = () => {
  if (!selectedRowKeys.value.length) {
    message.error("请勾选商品");
    return;
  }
  emits("add-click", selectedRowKeys.value);
};

// 驳回成功
const onSucceed = () => {
  emits("onSucceed");
};

defineExpose({
  open,
});
// 假设这是在 setup 函数内部
const isRefresh = toRef(props, "isRefresh");
watch(isRefresh, (newData) => {
  console.log(newData, "9990000");
  if (newData) {
    getListApi();
  }
});
onMounted(() => {
  console.log(props.columns, "props.columns");
  getClassfity();
  getListApi();
});
</script>

<style lang="less" scoped>
// .dia-box {
//   margin: 16px;
//   .title {
//     font-weight: bold;
//     font-size: 16px;
//     color: #05082c;
//     margin-bottom: 16px;
//   }
//   .sub-title {
//     font-size: 14px;
//     color: #495366;
//     white-space: nowrap;
//   }
// }
// .f-item {
//   .item-name {
//     font-size: 14px;
//     color: #05082c;
//     height: 40px;
//     line-height: 40px;
//     padding-left: 10px;
//     background: rgb(242, 242, 242);
//     .require {
//       font-size: 14px;
//       color: #ff436a;
//       margin-left: 3px;
//     }
//   }
//   p {
//     line-height: 40px;
//     padding: 20px 0;
//   }
// }
.proinfo-css {
  display: flex;
  align-items: center;
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */

  white-space: nowrap;
  // width: 300px; /* 设置容器的宽度，根据需要调整 */
}
.img-css {
  width: 80px;
  height: 80px;
}
.flex-box {
  display: flex;
  align-items: center;
}
.add-table {
  max-height: 600px;
  overflow: auto;
}
.act-btn {
  float: right;
  // margin: 20px 0;
}
.btn-w {
  width: 80px;
}
</style>
<style lang="less">
.t-dialog__header {
  border-bottom: 1px solid var(--td-border-level-1-color);
}
</style>
