<template>
  <a-modal
    v-model:open="lookVisible"
    placement="center"
    title="绑定供货仓"
    :destroy-on-close="true"
    width="60%"
    @cancel="cancelClick"
  >
    <a-tabs v-model:activeKey="activeKey" @change="tabChange">
      <a-tab-pane key="1" tab="供货仓"></a-tab-pane>
      <a-tab-pane key="2" tab="商品分类"></a-tab-pane>
    </a-tabs>
    <div v-if="activeKey === '1'">
      <a-form
        :model="formState"
        name="horizontal_login"
        layout="inline"
        autocomplete="off"
        class="mt20"
      >
        <a-form-item label="店铺名称" name="prodName">
          <a-input
            v-model:value="formState.prodName"
            placeholder="请输入"
          ></a-input>
        </a-form-item>

        <a-form-item label="绑定电活" name="secondCategoryId">
          <a-input
            v-model:value="formState.secondCategoryId"
            placeholder="请输入"
          ></a-input>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="getListApi">查询</a-button>
          <a-button @click="resetClick" class="ml10">重置</a-button>
        </a-form-item>
      </a-form>
      <a-button type="primary" @click="delClick">清楚</a-button>
      <table-list-antd
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        row-key="skuId"
        :is-radio="props.isRadio"
        @update:pagination="handleTableChange"
        @select-change="selectChange"
        class="mt20 add-table"
      >
      </table-list-antd>
    </div>
    <div v-if="activeKey === '2'">
      <a-tree
        :tree-data="treeData"
        :fieldNames="{
          children: 'categoryModels',
          title: 'categoryName',
          key: 'categoryId',
        }"
        v-model:selectedKeys="selectedKeys"
        checkable
      >
      </a-tree>
    </div>
    <template #footer>
      <a-button @click="cancelClick">取消</a-button>
      <a-button type="primary" @click="submitDisAgree">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { GetSupplierPage, GetCategoryList } from "@/api/common";
import { getGoldCategoryDrop } from "@/api/activityCenter/goldCoin";
import TableListAntd from "@/components/TableListAntd/index.vue";
import { message } from "woody-ui";
// import type { TreeProps } from 'woody-ui';

const props = defineProps({
  isRadio: {
    type: Boolean,
    default: () => false,
  },
  supplyId: {
    type: String,
    default: "",
  },
});
const selectData = ref({});
const classfityOpt = ref([]);
const formState = ref({
  prodName: "",
  secondCategoryId: "",
});
const activeKey = ref("1");
const delClick = () => {
  // selectData.value = {};
};
const columns = [
  {
    title: "店铺名称",
    dataIndex: "supplierName",
    key: "supplierName",
    fixed: "left",
    width: 200,
  },
  {
    title: "绑定电话",
    dataIndex: "tel",
    key: "tel",
    width: 200,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const expandedKeys = ref<string[]>([]);
const selectedKeys = ref<string[]>([]);
const checkedKeys = ref<string[]>([]);

const treeData = ref([]);

const handleTableChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getListApi();
};

const tabChange = (key) => {
  if (key === "2") {
    getCategoryApi();
  }
};

const getCategoryApi = async () => {
  const params = {
    supplyIds: props.supplyId,
    firstCategory: "",
  };
  const res = await GetCategoryList(params);
  if (res.code === 0) {
    treeData.value = res.data;
    console.log(treeData.value, "treeData");
  }
};

const emits = defineEmits([
  "onSucceed",
  "reject-click",
  "close-click",
  "add-click",
  "prod-select",
]);
const dataSource = ref([]);
// 获取列表数据
const getListApi = async () => {
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    ...formState.value,
  };
  const res = await GetSupplierPage(params);
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const selectChange = (data, data1) => {
  console.log(data, data1, "7897890");
  selectData.value = data;
  emits("prod-select", data);
};
const resetClick = () => {
  formState.value.prodName = "";
  formState.value.secondCategoryId = "";
  getListApi();
};
const lookVisible = ref(true);

const cancelClick = () => {
  lookVisible.value = false;
  emits("close-click");
};
const open = (code: String, isAudit: Boolean) => {};
const submitDisAgree = () => {
  if (!selectData.value) {
    message.error("请勾选商品");
    return;
  }
  emits("add-click", selectData.value);
};

// 驳回成功
const onSucceed = () => {
  emits("onSucceed");
};

defineExpose({
  open,
});
onMounted(() => {
  getClassfity();
  getListApi();
});
</script>

<style lang="less" scoped>
// .dia-box {
//   margin: 16px;
//   .title {
//     font-weight: bold;
//     font-size: 16px;
//     color: #05082c;
//     margin-bottom: 16px;
//   }
//   .sub-title {
//     font-size: 14px;
//     color: #495366;
//     white-space: nowrap;
//   }
// }
// .f-item {
//   .item-name {
//     font-size: 14px;
//     color: #05082c;
//     height: 40px;
//     line-height: 40px;
//     padding-left: 10px;
//     background: rgb(242, 242, 242);
//     .require {
//       font-size: 14px;
//       color: #ff436a;
//       margin-left: 3px;
//     }
//   }
//   p {
//     line-height: 40px;
//     padding: 20px 0;
//   }
// }
.img-css {
  width: 80px;
  height: 80px;
}
.flex-box {
  display: flex;
  align-items: center;
}
.add-table {
  max-height: 600px;
  overflow: auto;
}
</style>
<style lang="less">
.t-dialog__header {
  border-bottom: 1px solid var(--td-border-level-1-color);
}
</style>
