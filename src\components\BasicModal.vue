<template>
    <a-modal
      :open="visible"
      :title="title"
      :width="width"
      :maskClosable="maskClosable"
      @cancel="handleCancel"
      @ok="handleOk"
    >
      <template #default>
        <slot></slot>
      </template>
      
      <template #footer>
        <slot name="footer">
          <div :style="footerStyle">
            <a-button key="back" @click="handleCancel">取消</a-button>
            <a-button 
              key="submit" 
              type="primary" 
              :loading="confirmLoading"
              @click="handleOk"
            >
              确定
            </a-button>
          </div>
        </slot>
      </template>
    </a-modal>
  </template>
  
  <script setup>
  import { ref, defineProps, defineEmits } from 'vue'
  
  const props = defineProps({
    title: {
      type: String,
      default: '提示'
    },
    width: {
      type: [String, Number],
      default: 520
    },
    maskClosable: {
      type: Boolean,
      default: true
    },
    footerStyle: {
      type: Object,
      default: null
    }
  })
  
  const emits = defineEmits(['update:visible', 'ok', 'cancel'])
  
  const visible = ref(false)
  const confirmLoading = ref(false)
  
  const open = () => {
    visible.value = true
  }
  
  const close = () => {
    visible.value = false
  }
  
  const handleOk = async () => {
    confirmLoading.value = true
    try {
      await emits('ok')
      // close()
    } catch (error) {
      console.error(error)
    } finally {
      confirmLoading.value = false
    }
  }
  
  const handleCancel = () => {
    emits('cancel')
    close()
  }
  
  defineExpose({
    open,
    close
  })
  </script>
  