<template>
    <div class="breadcrumb">
      <span
        v-for="(item, index) in breadcrumbList"
        :key="item.path"
        :disable="true"
      >
        <span
          v-if="index !== breadcrumbList.length - 1"
          class="breadcrumb-item clickable"
          @click="handleClick(item)"
        >
          {{ item.meta?.title }}
        </span>
        <span v-else class="breadcrumb-item">
          {{ item.meta?.title }}
        </span>
        <span v-if="index !== breadcrumbList.length - 1"> / </span>
      </span>
    </div>
  </template>
  
  <script setup lang="ts">
  import { useRoute, useRouter } from "vue-router";
  import { computed } from "vue";
  import { findBreadcrumbs } from "@/utils/breadcrumb";
  
  const route = useRoute();
  const router = useRouter();
  
  const breadcrumbList = computed(() => {
    return findBreadcrumbs([...router.options.routes], route.path);
  });
  
  const handleClick = (item: any) => {
    if (item?.path) {
      router.push({ path: item.path });
    }
  };
  </script>
  
  <style scoped>
  .breadcrumb {
    font-size: 14px;
    margin-bottom: 10px;
  }
  
  .breadcrumb-item.clickable {
    cursor: pointer;
    color: #409EFF;
  }
  
  .breadcrumb-item.clickable:hover {
    text-decoration: underline;
  }
  </style>
  