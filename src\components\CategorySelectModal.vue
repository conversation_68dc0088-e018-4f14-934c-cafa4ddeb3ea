<template>
  <a-modal
    :open="visible"
    title="修改分类"
    cancelText="取消"
    okText="确定"
    @cancel="handleCancel"
    @ok="handleEditCategory"
    :width="500"
    :zIndex="1001"
  >
    <div>
      <a-cascader
        v-model:value="categoryValues"
        :options="cascaderOptions"
        placeholder="请选择类别"
        style="width: 400px"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from "vue";
import { editGrouponProductCategory } from "@/api/goodsCenter/localLife";
import { useShopCategoryListV2 } from "@/hooks/useShopCategoryListV2";
import { message } from "woody-ui";

const emits = defineEmits(["updateSuccess", "ok", "cancel"]);

const visible = ref(false);
const open = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;
};
const props = defineProps({
  isOpenModal: {
    type: Boolean,
    required: true,
  },
  productId: {
    type: Number,
    required: true,
  },
});

const categoryValues = ref<string[]>([]);

watch(props, (newProps) => {
  visible.value = newProps.isOpenModal;
});

const { cascaderOptions } = useShopCategoryListV2();

// 编辑分类
const handleEditCategory = () => {
  const [firstCategoryId, secondCategoryId, thirdCategoryId] =
    categoryValues.value;
  editGrouponProductCategory({
    productId: props.productId,
    firstCategoryId,
    secondCategoryId,
    thirdCategoryId,
  })
    .then((res) => {
      if (res.code === 0) {
        message.success("修改成功");
        emits("updateSuccess", props.productId);
        close();
      } else {
        message.error("修改失败");
      }
    })
    .catch(() => {
      message.error("修改失败");
    });
};

const handleCancel = () => {
  emits("cancel");
  close();
};
</script>

<style scoped></style>
