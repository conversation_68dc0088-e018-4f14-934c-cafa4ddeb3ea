<template>
  <dialog-page
    :visible="visible"
    header="复制"
    :is-enter="true"
    :on-validate="handleValidateFn"
    @on-close="handleClose"
    @on-confirm="handleConfirm"
  >
    <a-form ref="formRef" :model="formData" layout="vertical" :rules="rules">
      <a-form-item label="请输入复制后的名称" name="categoryName">
        <a-input 
          v-model:value="formData.categoryName" 
          :maxlength="maxlength" 
          :show-count="true"
          placeholder="请输入内容" 
        />
      </a-form-item>
    </a-form>
  </dialog-page>
</template>

<script setup>
import { reactive, ref } from 'vue';
import DialogPage from '@/components/DialogPage/index.vue';

const rules = { 
  categoryName: [{ required: true, message: '名称必填' }] 
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  dataObj: {
    type: Object,
    default: () => ({}),
  },
  maxlength: {
    type: Number,
    default: 30,
  },
});

const emits = defineEmits(['onClose', 'onConfirm']);

const formRef = ref(null);

const formData = reactive({
  categoryName: '',
});

// 校验逻辑
const handleValidateFn = (callBack) => {
  formRef.value.validate()
    .then(() => {
      callBack();
    })
    .catch(() => {
      // 验证失败
    });
};

// 关闭弹框
const handleClose = () => {
  formRef.value.resetFields();
  emits('onClose');
};

// 确认逻辑
const handleConfirm = () => {
  const params = {
    ...props.dataObj,
    ...formData,
  };
  emits('onConfirm', params);
  formRef.value.resetFields();
};
</script>

<style lang="less" scoped>
.ant-form {
  .ant-form-item-label {
    line-height: 22px;
    min-height: 22px;
    margin-bottom: 8px;
  }
}
</style>
