<template>
  <a-modal
    :open="visible"
    :width="width"
    :title="header"
    class="custom-dialog_page"
    v-bind="options"
    @cancel="handleClose"
  >
    <template v-if="isEmptyValue(header)" #title>
      <slot name="header" />
    </template>
    <slot />
    <template #footer>
      <div class="dialog-footer">
        <a-button v-if="!isEnter" :disabled="isBtnDisable" :loading="btnLoading" @click="handleConfirm">确定</a-button>
        <confirm-btn v-else text="复制" :is-default="true" :on-confirm="handleConfirm" :on-validate="onValidate">
          <a-button type="primary">确定</a-button>
        </confirm-btn>
        <a-button @click="handleClose">取消</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import ConfirmBtn from '@/components/ConfirmBtn/index.vue';
import { isEmptyValue } from '@/utils';
import './styled.less';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: 480,
  },
  header: {
    type: String,
    default: '',
  },
  isEnter: {
    type: Boolean,
    default: false,
  },
  isBtnDisable: {
    type: Boolean,
    default: false,
  },
  btnLoading: {
    type: Boolean,
    default: false,
  },
  onValidate: {
    type: Function,
  },
  options: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['onClose', 'onConfirm']);

// 关闭弹框
const handleClose = () => {
  emits('onClose');
};

// 确定逻辑
const handleConfirm = () => {
  emits('onConfirm');
};
</script>
