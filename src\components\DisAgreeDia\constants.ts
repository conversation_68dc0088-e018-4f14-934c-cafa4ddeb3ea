export const EXAMINE_COLUMNS = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 30,
  },
  {
    title: '商品ID',
    colKey: 'prodId',
    ellipsis: true,
    width: 200,
  },
  {
    title: '商品来源',
    colKey: 'prodSource',
    ellipsis: true,
    width: 100,
    cell: 'prodSource',
  },
  {
    title: '供货仓',
    colKey: 'supplierName',
    ellipsis: true,
    width: 150,
  },
  {
    title: '商品信息',
    colKey: 'goodsInfo',
    ellipsis: true,
    cell: 'goodsInfo',
    width: 500,
  },
  {
    title: '申请时间',
    colKey: 'applyTime',
    ellipsis: true,
    width: 200,
  },
  {
    title: '审核状态',
    colKey: 'auditStatusName',
    ellipsis: true,
    width: 150,
    cell: 'auditStatusName',
  },
  {
    title: '操作',
    colKey: 'operation',
    // ellipsis: true,
    fixed:"right",
    width: 240,
  },
];

export const SKU_COLUMNS = [
  {
    title: '成本价(元)',
    colKey: 'costPrice',
    ellipsis: true,
    width: 100,
  },
  {
    title: '市场价(元)',
    colKey: 'oriPrice',
    ellipsis: true,
    width: 100,
  },
  {
    title: '销售价(元)',
    colKey: 'price',
    ellipsis: true,
    width: 100,
  },
  {
    title: '库存',
    colKey: 'stocks',
    ellipsis: true,
    width: 100,
  },
  {
    title: '规格',
    colKey: 'prodName',
    ellipsis: true,
    width: 100,
    cell: 'prodName',
  },
  {
    title: '商品体积(m³)',
    colKey: 'volume',
    ellipsis: true,
    width: 100,
  },
  {
    title: '商品重量(kg)',
    colKey: 'weight',
    ellipsis: true,
    width: 100,
  },
  {
    title: '国标码',
    colKey: 'skuNumber',
    ellipsis: true,
    width: 100,
  },
  {
    title: '单位',
    colKey: 'unit',
    ellipsis: true,
    width: 100,
  },
];

export const prodSourceArr = [
  {
    label: '我店生活',
    value: 0,
  },
  {
    label: '我店优选',
    value: 7,
  },
];

export const auditTypes = [
  {
    label: '系统',
    name: 'system',
    value: 1,
  },
  {
    label: '营销',
    name: 'marketing',
    value: 2,
  },
  {
    label: '风控',
    name: 'risk',
    value: 3,
  },
  {
    label: '运营',
    name: 'operation',
    value: 4,
  },
];

// 驳回原因
export const causeOfRejection = {
  // 营销
  marketerRejectCause: 'MARKETER_REJECT_CAUSE',
  // 风控
  riskControllerRejectCause: 'RISK_CONTROLLER_REJECT_CAUSE',
};
// 审核传参
export interface auditListTs {
  // 审核id
  auditId: String;
  // 审核节点id
  nodeId: String;
}
