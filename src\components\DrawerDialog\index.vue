<template>
  <a-drawer
    :open="visible"
    :width="size"
    :title="title"
    v-bind="options"
    class="customer-drawer-dialog"
    @close="handleClose"
    :footer-style="{ textAlign: 'right' }"
  >
    <slot />
    <template #footer>
      <a-button v-if="!hideConfirm" :disabled="confirmDisabled" :loading="btnLoading" type="primary" @click="handleConfirm">确定</a-button>
      <a-button style="margin-left: 8px;" @click="handleClose">取消</a-button>
      <slot name="customBtn" />
    </template>
  </a-drawer>
</template>

<script setup>
import './styled.less';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  size: {
    type: [String, Number],
    default: 400,
  },
  title: {
    type: String,
    default: '',
  },
  confirmDisabled: {
    type: Boolean,
    default: false,
  },
  hideConfirm: {
    type: Boolean,
    default: false,
  },
  btnLoading: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => ({
      closable: true,
    }),
  },
});

const emits = defineEmits(['onClose', 'onConfirm']);

// 关闭逻辑
const handleClose = () => {
  emits('onClose');
};

// 确认逻辑
const handleConfirm = () => {
  emits('onConfirm');
};
</script>