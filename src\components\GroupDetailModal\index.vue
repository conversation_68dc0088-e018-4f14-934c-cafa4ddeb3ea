<template>
  <a-modal
    :open="visible"
    :width="600"
    :title="'查看详情'"
    @cancel="handleCancel"
    :footer="null"
    destroyOnClose
    centered
    :zIndex="1000"
  >
    <div class="modal-container">
      <div class="sale-date">{{ hmBaseInfo?.tips || "-" }}</div>
      <div class="head-level">
        <div>
          <span v-if="hmBaseInfo?.firstCategory">
            {{ formatCategory(hmBaseInfo) }}
          </span>
          <a-button
            class="btn-css"
            v-if="!hmBaseInfo?.isCategoryValid"
            type="link"
            @click="
              () => {
                isOpenModal = true;
              }
            "
          >
            修改
          </a-button>
        </div>
        <div v-if="!hmBaseInfo?.isCategoryValid" class="tips">
          当前分类不存在，修改分类
        </div>
      </div>
      <div class="tag-scroll">
        <!-- 营销活动 -->
        <div class="marketing-promotion">
          <div class="stus">
            <div class="icons">
              <img
                width="90"
                height="20"
                src="@/assets/images/mark-pro_logo.png"
              />
            </div>
            <div
              :class="[
                hmBaseInfo?.activityFlag === 'GROUPON_SHARE'
                  ? 'join'
                  : 'not-join',
              ]"
            >
              {{
                hmBaseInfo?.activityFlag === "GROUPON_SHARE" ? "参加" : "未参加"
              }}
            </div>
          </div>
          <div class="equity-dec">
            <template v-if="hmBaseInfo?.activityFlag === 'GROUPON_SHARE'">
              <div className="div-b">
                <div :class="!isShowMoreBtn ? 'overfow_ellipsis' : ''">
                  <div style="margin-bottom: 8px">享受权益：</div>
                  <div style="white-space: pre-wrap">
                    {{ hmBaseInfo?.activityFlagRemark.replace(/\\n/g, "\n") }}
                  </div>
                </div>
                <div class="put-away" @click="toggleShowMore">
                  <div v-if="isShowMoreBtn">收起 <UpOutlined /></div>
                  <div v-else>展开 <DownOutlined /></div>
                </div>
              </div>
            </template>
            <template v-else> 目前商品曝光不足，建议参加营销活动哦！ </template>
          </div>
        </div>
        <div class="h5">
          <div class="commodity-box">
            <div class="commodity-info">
              <!-- <div class="title">{{ hmBaseInfo?.productName || "-" }}</div> -->
              <div class="lb">
                <a-carousel
                  :autoplay="true"
                  :autoplay-speed="2000"
                  :dots="false"
                >
                  <template v-for="(it, idx) in imageUrls" :key="idx">
                    <div class="rounded-box">
                      <img
                        class="rounded-border"
                        :src="it ? it : 'error'"
                        alt=""
                      />
                    </div>
                  </template>
                </a-carousel>
                <div class="pos-back">
                  <div class="left">
                    <div class="price">
                      ¥{{ hmBaseInfo?.sellingPrice || 0 }}
                    </div>
                    <div class="discount">
                      {{ hmBaseInfo?.discount || 0 }}折
                    </div>
                    <div class="line-price">
                      ¥{{ hmBaseInfo?.oriPrice || 0 }}
                    </div>
                  </div>
                  <div class="pv-rate">
                    预计获得积分{{ hmBaseInfo?.pvRate || 0 }}%
                  </div>
                </div>
                <div class="title">{{ hmBaseInfo?.productName || "-" }}</div>
                <div class="description">
                  <div class="txt-bd">
                    <div class="left">优质联盟商家精选</div>
                    <div class="right">超值低价</div>
                  </div>
                  <div class="buy">
                    {{ hmBaseInfo?.totalSoldNum ?? 0 }}人抢购
                  </div>
                </div>
              </div>
            </div>

            <div class="commodity-info">
              <div class="notice">
                <div class="notice-item mt-10 mb-10">
                  <span class="fi">限购：</span>
                  <span class="la">
                    商品每人每日限购{{ hmBaseInfo?.todayMaxBuyCount ?? 0 }}张
                  </span>
                </div>
                <div class="notice-item">
                  <span class="fi">须知：</span>
                  <span class="la">
                    {{
                      hmBaseInfo?.platGrouponProductUseTimeInfoVO
                        ?.platGrouponProductTimeVO?.useDayStr
                        ? hmBaseInfo?.platGrouponProductUseTimeInfoVO
                            ?.platGrouponProductTimeVO?.useDayStr + " · "
                        : null
                    }}
                    {{
                      hmBaseInfo?.platGrouponProductUseTimeInfoVO
                        ?.appointmentType === 0
                        ? "不需要预约"
                        : `需提前${hmBaseInfo?.platGrouponProductUseTimeInfoVO?.appointmentDay}天预约`
                    }}
                  </span>
                </div>
                <div class="notice-item mt-10 mb-10">
                  <span class="fi">保障：</span>
                  <span class="la">随时退 · 过期自动退</span>
                </div>
                <div class="notice-item">
                  <span class="fi">效期：</span>
                  <span class="la">{{
                    hmBaseInfo?.expirationDate || "-"
                  }}</span>
                </div>
              </div>
            </div>

            <div class="commodity-info">
              <div class="head-cont">
                <div class="line"></div>
                <div class="txt">适用商户</div>
              </div>
              <div class="merchant-info">
                <div class="imgs">
                  <img
                    width="50"
                    height="50"
                    :src="hmShopBaseInfo?.shopLogo?.url"
                    alt=""
                  />
                </div>
                <div class="dec-title">
                  {{ hmShopBaseInfo?.shopName || "-" }}
                </div>
              </div>
              <div class="address">
                <div v-if="hmShopBaseInfo?.shopAddress">
                  <EnvironmentOutlined />
                  <span class="ml">{{ hmShopBaseInfo?.shopAddress }}</span>
                </div>
                <div v-if="hmShopBaseInfo?.open">
                  <div class="call">
                    <ClockCircleOutlined />
                    <span class="ml">
                      {{
                        hmShopBaseInfo?.shopTime.allTime === 1
                          ? "全天营业"
                          : hmShopBaseInfo?.shopTime?.timeRange
                      }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="commodity-info">
              <div class="head-cont">
                <div class="line"></div>
                <div class="txt">团购详情</div>
              </div>
              <div
                v-for="(it, idx) in hmGroupInfo"
                v-if="hmGroupInfo && hmGroupInfo.length > 0"
                :key="idx"
              >
                <div class="set-meal">
                  <div class="top">{{ it?.groupName }}</div>
                  <div class="cont">
                    <div
                      v-for="(i, dx) in it?.hmGrouponProductSkuDTOS"
                      :key="dx"
                      class="goods-item"
                    >
                      <span>{{ i?.skuName }}</span>
                      <div>
                        <span class="fen">{{
                          i?.copies && `(${i?.copies}份)`
                        }}</span>
                        <span class="price">{{
                          i?.price && `¥${i?.price}`
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="commodity-info">
              <div class="head-cont">
                <div class="line"></div>
                <div class="txt">购买须知</div>
              </div>
              <div class="knows">
                <div class="wa">
                  <div class="cir mins">使用日期：</div>
                  <div class="content">
                    {{ hmBaseInfo?.expirationDate || "-" }}
                  </div>
                </div>
                <div class="wa">
                  <div class="cir mins">不可用时间：</div>
                  <div class="content">
                    {{
                      hmBaseInfo?.platGrouponProductUseTimeInfoVO
                        ?.useTimeContent || "-"
                    }}
                  </div>
                </div>
                <div class="wa">
                  <div class="cir mins">使用时间：</div>
                  <div class="content">
                    {{
                      hmBaseInfo?.platGrouponProductUseTimeInfoVO
                        ?.platGrouponProductTimeVO?.useTimeStr || "-"
                    }}
                  </div>
                </div>
                <div class="wa">
                  <div class="cir mins">使用规则：</div>
                  <div class="content">{{ hmBaseInfo?.useRule ?? null }}</div>
                </div>
              </div>
            </div>

            <div class="commodity-info">
              <div class="head-cont">
                <div class="line"></div>
                <div class="txt">团购介绍</div>
              </div>
              <div class="img-box">
                <img
                  v-for="(it, idx) in hmBaseInfo?.introduceImages"
                  :key="idx"
                  class="imgs"
                  :src="it"
                  alt=""
                />
              </div>
            </div>
          </div>
          <div class="footer-btns">
              <div
                :class="hmBaseInfo?.auditStatus === '10' ? 'btn-close' : 'btn-big-close'"
                @click="
                  () => {
                    isShowMoreBtn = false;
                    visible = false;
                  }
                "
              >
                关闭
              </div>
              <div
                v-if="hmBaseInfo?.auditStatus === '10' && currentRecord"
                className="btn-confirm"
                @click="() => {
                  emits('handlePublicModal', 'single', currentRecord)
                }"
              >
                审核
              </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
  <!-- 分类选择模态框 -->
  <CategorySelectModal
    :isOpenModal="isOpenModal"
    @cancel="isOpenModal = false"
    :productId="hmBaseInfo?.productId"
    @updateSuccess="
      () => {
        emits('refreshGroupDetailData', hmBaseInfo?.productId);
      }
    "
  />
</template>

<script setup lang="ts">
import "./styled.less";

import { ref, computed, watch } from "vue";
import {
  ClockCircleOutlined,
  DownOutlined,
  EnvironmentOutlined,
  UpOutlined,
} from "@ant-design/icons-vue";
import CategorySelectModal from "@/components/CategorySelectModal.vue";

interface GroupDetailModalData {
  hmGroupProductDetailBaseInfoVO: any;
  shopBaseInfoVO: any;
  hmGrouponProductGroupInfoDTOS: any;
}

const props = defineProps<{
  isOpenGroupDetailModal: boolean;
  groupDetailModalData: GroupDetailModalData;
  currentRecord?: any;
}>();

const emits = defineEmits(["refreshGroupDetailData", "handlePublicModal"]);

const isShowMoreBtn = ref(false);
const isOpenModal = ref(false);

const visible = ref(false);

watch(props, (newProps) => {
  visible.value = newProps.isOpenGroupDetailModal;
});

const hmBaseInfo = computed(
  () => props.groupDetailModalData?.hmGroupProductDetailBaseInfoVO || {}
);
const hmShopBaseInfo = computed(
  () => props.groupDetailModalData?.shopBaseInfoVO || {}
);
const imageUrls = computed(() => hmBaseInfo.value.images?.slice(1) || []);
const hmGroupInfo = computed(
  () => props.groupDetailModalData?.hmGrouponProductGroupInfoDTOS || {}
);

const handleCancel = () => {
  visible.value = false;
  isShowMoreBtn.value = false;
};

const toggleShowMore = () => {
  isShowMoreBtn.value = !isShowMoreBtn.value;
};

const formatCategory = (info: any) => {
  const { firstCategory, secondCategory, thirdCategoryName } = info;
  return `${firstCategory || ""}${
    secondCategory
      ? ` > ${secondCategory}${
          thirdCategoryName ? ` > ${thirdCategoryName}` : ""
        }`
      : ""
  }`;
};
</script>

<style scoped></style>
