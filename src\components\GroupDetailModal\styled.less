.modal-container{
  display: flex;
  flex-direction: column;
  height: 700px;
  .sale-date {
    background: #ccc;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    .tit {
      font-weight: 500;
    }
    .symbol {
      font-size: 12px;
    }
  }
  .head-level {
    margin-bottom: 7px;
    color: #333333;
    font-weight: 500;
    font-size: 18px;
    padding: 15px 0 0 40px;
    .tips {
      font-size: 14px;
      color: red;
    }
  }
  .tag-scroll {
    overflow: auto;
    .marketing-promotion {
      width: 375px;
      height: auto;
      box-shadow: 0px 2px 10px 0px #d2d2d2;
      margin: 15px auto 5px auto;
      border-radius: 2px;
      padding: 10px;
      position: relative;
      .stus {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .join {
          color: #6eb49a;
          background: #e8f7ef;
          border-radius: 5px;
          padding: 0 9px;
        }
        .not-join {
          color: red;
          background: #fceced;
          border-radius: 5px;
          padding: 0 8px;
        }
      }
      .equity-dec {
        background: #fef9eb;
        padding: 20px;
        color: #817452;
        font-weight: 500;
        font-size: 13px;
        .div-b {
          .overfow_ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            margin-bottom: 10px;
          }
          .put-away {
            cursor: pointer;
            font-weight: 500;
            text-align: center;
            margin-top: 10px;
            &.put-away:hover {
              color: #1890ff;
            }
          }
        }
      }
    }
    .h5 {
      flex: 1;
      overflow: auto;
      width: 375px;
      margin: 0px auto 10px auto;
      .commodity-box {
        background: #eee;
        padding: 10px;
        .commodity-info {
          background: #fff;
          border-radius: 10px;
          padding: 10px;
          margin-bottom: 15px;
          .title {
            font-size: 18px;
            margin-bottom: 5px;
            color: #333333;
            font-weight: 500;
          }
          .lb {
            margin-bottom: 10px;
            .rounded-box {
              width: 100%;
              height: 200px;
              margin-bottom: 13px;
              .rounded-border {
                height: 100%;
                border-radius: 10px;
                width: 100%;
                display: block;
              }
            }
            .pos-back {
              height: 113px;
              background: url('https://img.zsjming.com/2024/12/cbf9d80aa7e14cf597ed07627b49f2da.png');
              background-size: 100% 100%;
              padding: 10px 30px;
              margin-bottom: 5px;
              .left {
                display: flex;
                align-items: center;
                justify-items: center;
                margin-bottom: 2px;
                .price {
                  color: #ffffff;
                  font-size: 21px;
                  font-weight: blod;
                  margin-right: 10px;
                }
                .discount {
                  width: 42px;
                  height: 18px;
                  line-height: 18px;
                  text-align: center;
                  font-size: 12px;
                  color: #ffeded;
                  border: 1px solid #d9d9d9;
                  border-radius: 5px;
                  margin-right: 10px;
                }
                .line-price {
                  color: #ffbbbb;
                  text-decoration: line-through;
                }
              }
              .pv-rate {
                width: 120px;
                height: 18px;
                line-height: 18px;
                text-align: center;
                background: #fff0ed;
                font-size: 12px;
                border-radius: 10px;
                color: #f43b3b;
                font-weight: 400;
              }
            }
            .title {
              margin-bottom: 15px;
              padding: 0 10px;
            }
            .description {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              padding: 0 10px;
              .txt-bd {
                display: flex;
                .left {
                  margin-right: 10px;
                  color: #ff634f;
                  border: 1px solid #ff634f;
                  border-radius: 3px;
                  height: 18px;
                  line-height: 18px;
                  padding: 0 8px;
                  font-weight: 500;
                }
                .right {
                  color: #ff634f;
                  border: 1px solid #ff634f;
                  border-radius: 3px;
                  height: 18px;
                  line-height: 18px;
                  padding: 0 8px;
                  font-weight: 500;
                }
              }
              .buy {
                color: #616a66;
              }
            }
          }
          .notice {
            font-size: 13px;
            font-weight: 400;
            .notice-item {
              padding-left: 10px;
            }
            .mb-1 {
              margin-bottom: 1px;
            }
            .la {
              color: #888888;
            }
            .fi {
              font-weight: bold;
            }
          }
          .head-cont {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            .line {
              width: 5px;
              height: 25px;
              border-radius: 10px;
              background-image: linear-gradient(to top, #ade599, #16c046);
              margin-right: 10px;
            }
            .txt {
              padding-top: 5px;
              font-weight: 500;
              font-size: 18px;
              margin-bottom: 5px;
            }
          }
          .merchant-info {
            display: flex;
            margin-bottom: 30px;
            padding: 0 15px;
            .imgs {
              width: 50px;
              height: 50px;
              margin-right: 10px;
              border-radius: 8px;
              img {
                border-radius: 8px;
              }
            }
            .dec-title {
              font-size: 15px;
              font-weight: 500;
            }
          }
          .address {
            color: #666666;
            font-size: 13px;
            line-height: 30px;
            padding: 0 15px;
            .ml {
              margin-left: 5px;
            }
          }
          .set-meal {
            padding: 0 15px;
            color: #333333;
            .top {
              font-size: 14px;
              margin-bottom: 10px;
              font-weight: 500;
            }
            &.set-meal:last-child {
              .cont {
                border-bottom: none;
                margin-bottom: 0;
              }
            }
            .cont {
              padding: 0 10px 25px 0;
              font-size: 13px;
              line-height: 25px;
              border-bottom: 1px dashed #eee;
              margin-bottom: 25px;
              .goods-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-left: 10px;
                font-size: 12px;
                .fen {
                  color: #999999;
                }
                .price {
                  color: #666666;
                }
              }
            }
          }
          .knows {
            padding: 0 15px;
            font-size: 13px;
            line-height: 30px;
            .wa {
              display: flex;
              .cir {
                min-width: 100px;
                color: #333333;
                &.mins {
                  min-width: 80px;
                }
              }
              .cir::before {
                content: "";
                display: inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50px;
                background: #000;
                margin-right: 3px;
                margin-bottom: 1px;
              }
              .content {
                max-width: 200px;
                flex-wrap: wrap;
                color: #666666;
              }
            }
          }
          .img-box {
            padding: 0 15px;
            .imgs {
              width: 100%;
              border-radius: 10px;
              margin-bottom: 15px;
            }
          }
        }
      }
      .footer-btns {
        display: flex;
        .btn-close {
          width: 40%;
          background: #eee;
          margin: 20px auto 0;
          border-radius: 3px;
          text-align: center;
          height: 45px;
          line-height: 45px;
          cursor: pointer;
        }
        .btn-big-close {
          width: 60%;
          background: #eee;
          margin: 20px auto 0;
          border-radius: 3px;
          text-align: center;
          height: 45px;
          line-height: 45px;
          cursor: pointer;
        }
        .btn-confirm {
          width: 40%;
          background: #1890ff;
          margin: 20px auto 0;
          border-radius: 3px;
          text-align: center;
          height: 45px;
          line-height: 45px;
          cursor: pointer;
          color: #fff;
        }
      }
    }
  }
  .ant-carousel {
    .slick-prev,
    .slick-prev:hover,
    .slick-prev:focus {
      height: 28px;
      line-height: 28px;
      text-align: center;
      width: 28px;
      border-radius: 50%;
      background: rgb(0, 0, 0, 0.5);
      color: #fff;
      font-size: 15px;
      left: 5px;
      z-index: 2;
      opacity: 1;
      &::before {
        content: "";
      }
      &.slick-prev:hover {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-weight: 500;
        background: rgb(0, 0, 0, 0.6);
      }
    }
    .slick-next,
    .slick-next:hover,
    .slick-next:focus {
      height: 28px;
      line-height: 28px;
      text-align: center;
      width: 28px;
      border-radius: 50%;
      background: rgb(0, 0, 0, 0.5);
      color: #fff;
      font-size: 15px;
      right: 5px;
      z-index: 2;
      color: #fff;
      opacity: 1;
      &::before {
        content: "";
      }
      &.slick-next:hover {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-weight: 500;
        background: rgb(0, 0, 0, 0.6);
      }
    }
  }
}
