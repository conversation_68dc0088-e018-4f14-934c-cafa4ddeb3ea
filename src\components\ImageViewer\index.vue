<template>
  <div class="flex" style="flex-wrap: wrap">
    <div
      v-for="(item, index) in props.images"
      :key="index"
      class="tdesign-demo-image-viewer__base"
    >
      <a-image-preview-group>
        <a-image
          v-for="(item, index) in props.images"
          :key="index"
          :src="item"
          alt="test"
          class="tdesign-demo-image-viewer__ui-image--img"
          style="width: 100px; height: 100px; margin: 8px"
        >
          <template #placeholder>
            <div>加载中...</div>
          </template>
          <template #previewMask>
            <div class="tdesign-demo-image-viewer__ui-image--hover">
              <span><browse-icon style="margin-right: 4px" />预览</span>
            </div>
          </template>
        </a-image>
      </a-image-preview-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from "vue";

const props = defineProps({
  images: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});
</script>

<style lang="less" scoped>
.tdesign-demo-image-viewer__ui-image--hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.tdesign-demo-image-viewer__base {
  width: 110px;
  height: 110px;
  background: #f1f6f8;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 16px;
  margin-right: 16px;
}
</style>
