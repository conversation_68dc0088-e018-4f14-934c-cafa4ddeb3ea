import { queryCategory } from "@/api/common";

async function fetchCategory() {
  let res = await queryCategory();
  if (res.data) {
    res.data = dealWithAll(res.data);
    res.data.forEach((item: any) => {
      if (item.newCategoryModelDtos?.length) {
        item.newCategoryModelDtos = dealWithAll(item.newCategoryModelDtos);
        item.newCategoryModelDtos.forEach((items: any) => {
          if (items.newCategoryModelDtos?.length) {
            items.newCategoryModelDtos = dealWithAll(
              items.newCategoryModelDtos
            );
          }
        });
      }
    });
  }
  return res.data;
}

function dealWithAll(arr: any) {
  arr.unshift({
    categoryId: "",
    categoryName: "全部",
  });
  return arr;
}

export default fetchCategory;
