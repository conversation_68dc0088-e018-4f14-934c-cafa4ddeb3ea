.material-center-container {
  background: #fff;
  border-radius: 16px;
  height: calc(~'100vh - 118px');
  display: flex;
  .left-part {
    width: 240px;
    height: 100%;
    border-right: 1px solid #f2f5f9;
    box-sizing: border-box;
    padding: 28px 0 28px 15px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 8px;
      > div {
        &:first-child {
          color: #05082c;
          font-size: 18px;
          font-weight: 600;
          line-height: 24px;
          font-family: PingFang SC, PingFang SC;
        }
        &:last-child {
          font-size: 12px;
          padding: 0 8px;
          background: #f2f5f9;
          border-radius: 3px 3px 3px 3px;
          cursor: pointer;
          &:hover {
            background-color: var(--td-brand-color-active);
            > span {
              color: #fff;
            }
          }
          > span {
            height: 24px;
            display: flex;
            align-items: center;
            color: #05082c;
          }
        }
      }
    }
    .tree-container {
      height: calc(100% - 80px);
      margin-top: 32px;
      overflow-y: auto;
      .un-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-right: 16px;
        padding-right: 34px;
        height: 48px;
        padding-left: 15px;
        margin-bottom: 2px;
        cursor: pointer;
        > div {
          font-size: 14px;
          &:first-child {
            color: #05082c;
            line-height: 22px;
          }
          &:last-child {
            color: #818999;
          }
        }
        &:hover {
          background-color: #f1f6f8;
        }
      }
      .active {
        background-color: #f0f9ff;
        border-right-color: var(--td-brand-color);
      }
      :deep(.t-tree__item) {
        padding: 0;
      }
      :deep(.t-is-active .t-tree__label) {
        background-color: #f0f9ff;
        border-right-color: var(--td-brand-color);
        .label {
          color: #1a7af8;
        }
      }
      :deep(.t-folder-icon) {
        position: relative;
        top: -2px;
      }
      :deep(.t-tree__label) {
        padding: 13px 0;
        padding-left: 13px;
        margin-bottom: 5px;
        color: #495366;
        border-right-width: 3px;
        border-right-style: solid;
        border-right-color: transparent;
        border-radius: 0;
      }
      .real-tree {
        // margin-right: 16px;
        padding-left: 15px;
        .label-info {
          width: 100%;
          display: flex;
          justify-content: space-between;
          line-height: 40px;
          .edit-icon {
            display: none;
          }
          position: relative;
          &:hover {
            .edit-icon {
              display: inline-block;
              position: absolute;
              top: -2px;
              right: 24px;
            }
          }
          > span:nth-child(1) {
            max-width: 90px;
            overflow: hidden;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .t-tree__label > span {
          display: block;
        }
        .edit-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .t-input__wrap {
            width: 75%;
          }
          // .check {
          //   cursor: pointer;
          //   top: 3px;
          // }
        }
        :deep(.ant-tree-switcher){
          line-height: 40px;
        }
      }
    }
  }
  .right-part {
    flex: 1;
    padding: 24px;
    position: relative;
    display: flex;
    flex-direction: column;
    .title {
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      .upload-search {
        display: flex;
        .t-upload {
          width: 112px;
          height: 32px;
          line-height: 32px;
          border-radius: 3px 3px 3px 3px;
          margin-right: 24px;
        }
        .t-input__wrap {
          width: 340px;
        }
      }
    }
    .operate-group {
      display: flex;
      margin-bottom: 25px;
      > span {
        height: 32px;
        line-height: 32px;
        text-align: center;
        padding: 0 16px;
        border-radius: 3px;
        background-color: #f2f5f9;
        color: #05082c;
        font-size: 14px;
        margin-right: 16px;
        cursor: pointer;
        &:hover {
          background-color: var(--td-brand-color-active);
          color: #fff;
        }
      }
    }
    .pic-container {
      flex: 1;
      overflow-y: auto;
      .pic-list {
        width: 153px;
        .img-container {
          width: 100%;
          height: 153px;
          border-radius: 6px;
          display: flex;
          box-sizing: border-box;
          justify-content: center;
          align-items: center;
          background: #f6f6f6;
          position: relative;
          overflow: hidden;
          .bg-hover {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
          }
          &:hover {
            .bg-hover {
              display: block;
              background-color: rgba(29, 36, 38, 0.5);
              border-radius: 6px;
              display: flex;
              justify-content: center;
              align-items: center;
              > span {
                width: 40px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                border-radius: 3px;
                background-color: #fff;
                color: #05082c;
                margin: 0 8px;
                font-size: 12px;
                cursor: pointer;
                &:first-child {
                  background-color: #1a7af8;
                  color: #fff;
                }
              }
            }
          }
          img {
            width: 112px;
            height: auto;
            // max-height: 112px;
            // padding-top: 10px;
            // padding-bottom: 10px;
          }
        }
        :deep(.t-checkbox) {
          width: 100%;
        }
        :deep(.t-checkbox__label) {
          width: 84%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .page-container {
      // margin-top: 50px;
      height: 64px;
      width: 97%;
      display: flex;
      align-items: center;
      padding: 0 5px;
      background-color: #fff;
    }
  }
}
