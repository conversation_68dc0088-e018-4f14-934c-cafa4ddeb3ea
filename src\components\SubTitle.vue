<template>
  <div :id="id ? id : undefined" class="sub-title">
    <div v-if="showPreBlock" class="block" />
    <span class="title" :style="{ marginLeft: showPreBlock ? '8px' : '0' }">{{ title }}</span>
    <span class="tip">{{ tip }}</span>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  tip: {
    type: String,
    default: '',
  },
  showPreBlock: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.sub-title {
  display: flex;
  align-items: center;
  .block {
    width: 4px;
    height: 16px;
    background: #1a7af8;
    border-radius: 4px 4px 4px 4px;
  }
  .title {
    margin-left: 8px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #05082c;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .tip {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #636d7e;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-left: 8px;
  }
}
</style>
