<template>
  <a-table
    :columns="props.columns"
    :data-source="props.dataSource"
    :loading="props.loading"
    :pagination="props.pagination"
    @change="handleTableChange"
    :row-selection="props.isCheckbox ? rowSelection : null"
    :rowKey="(record) => record"
    :scroll="{ x: 1000 }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'orderStatus'">
        <span>{{ orderStatus[record.orderStatus] }}</span>
      </template>
      <template
        v-if="column.key === 'prodName' || column.key === 'productName'"
      >
        <div class="proinfo-css">
          <img
            class="con-style"
            :src="
              record.picInfo
                ? record.picInfo.path
                : record.picPath ||
                  record.pic ||
                  record.prodImg ||
                  record.productPic
            "
          />
          <div>
            <p class="ml10">{{ record.prodName || record.productName }}</p>
            <p class="ml10" v-if="record.skuNumber">{{ record.skuNumber }}</p>
          </div>
        </div>
      </template>
      <template v-if="column.key === 'platProductSnapshotInfoVO'">
        <div class="proinfo-css">
          <img
            class="con-style"
            :src="
              record.platProductSnapshotInfoVO
                ? record.platProductSnapshotInfoVO.picUrl
                : ''
            "
          />
          <span class="ml10">{{
            record.platProductSnapshotInfoVO.prodName
          }}</span>
        </div>
      </template>
      <template v-if="column.key === 'skuName'">
        <div class="proinfo-css">
          <img class="con-style" :src="record.picPath" />
          <div>
            <p class="ml10">{{ record.skuName }}</p>
            <p class="ml10" v-if="record.skuNumber">{{ record.skuNumber }}</p>
          </div>
        </div>
      </template>
      <template v-if="column.key === 'platParentCategoryName'">
        <span>{{ record.topPlatCategoryName }}</span>
        <span class="ml10">{{ record.platParentCategoryName }}</span>
        <span class="ml10">{{ record.platCategoryName }}</span>
      </template>
      <template v-if="column.key === 'status'">
        <span>{{ goodsListStatus[record.status] }}</span>
      </template>
      <template v-if="column.key === 'cashPrice'">
        <span>{{ record.status }}+{{ record.goldPrice }}</span>
      </template>
      <template v-if="column.key === 'prodPropValues'">
        <span v-for="(item, index) in record.prodPropValues" :key="index">{{
          item.propValue
        }}</span>
      </template>
      <template v-if="column.key === 'bizDesc'">
        <p class="desc-css">{{ record.bizDesc }}</p>
      </template>
      <template v-if="column.key === 'addType'">
        <p class="desc-css">
          {{ record.addType === 0 ? "自动添加" : "手动添加" }}
        </p>
      </template>

      <template v-if="column.key === 'actions'">
        <div
          v-for="item in column.actionType"
          :key="item.type"
          class="actions-box"
        >
          <a-popconfirm
            v-if="item.isPop"
            ok-text="确定"
            cancel-text="取消"
            title="确定执行此操作？"
            class="btn-css"
            @confirm="actionsClick(item.type, record)"
          >
            <a-button type="link" class="btn-css">{{ item.title }}</a-button>
          </a-popconfirm>
          <a-button type="link" class="btn-css"
            v-else
            :danger="buttonColor[item.type]"
            @click="actionsClick(item.type, record)"
            >{{ item.title }}</a-button
          >
        </div>
      </template>
      <template v-if="column.key === 'dynamicsAct'">
        <div class="actions-box">
          <a-button type="link"
            v-if="props.isLook"
            class="btn-css"
            @click="actionsClick('look', record)"
            >查看</a-button
          >
          <a-popconfirm
            ok-text="确定"
            cancel-text="取消"
            title="确定执行此操作？"
            class="btn-css"
            @confirm="actionsClick('off', record)"
          >
            <a-button type="link" class="btn-css">{{
              record.status === 1 || record.showType === "Y" ? "下架" : "上架"
            }}</a-button>
          </a-popconfirm>
          
          <a-button type="link" class="btn-css" @click="actionsClick('edit', record)"
            >编辑</a-button
          >
          <a-dropdown>
            <a-button type="link" class="btn-css" @click.prevent>
              更多
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a-popconfirm
                    ok-text="确定"
                    cancel-text="取消"
                    title="确定执行此操作？"
                    class="btn-css"
                    @confirm="actionsClick('delete', record)"
                  >
                    <a-button type="link" class="btn-css">删除</a-button>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
      <template v-if="column.key === 'zoneSort'">
        <a-input-number
          v-model:value="record.zoneSort"
          @blur="sortBlur(record.zoneSort, record)"
        />
      </template>
      <template v-if="column.key === 'sort'">
        <a-input-number
          v-model:value="record.sort"
          @blur="sortBlur(record.sort, record)"
        />
      </template>
      <template v-if="column.key === 'zoneProductSort'">
        <a-input-number
          v-model:value="record.zoneProductSort"
          @blur="sortBlur(record.zoneProductSort, record)"
        />
      </template>
      <template v-if="column.key === 'waterSoldNum'">
        <a-input-number
          v-model:value="record.waterSoldNum"
          @blur="sortBlur(record.productId, record.waterSoldNum)"
        />
      </template>
      <template v-if="column.key === 'sellLikeHotCakes'">
        <a-input-number
          v-model:value="record.sellLikeHotCakes"
          @blur="hotCakesBlur(record.sellLikeHotCakes, record)"
        />
      </template>

      <template v-if="column.key === 'seckillAct'">
        <div class="actions-box">
          <a-popconfirm
            ok-text="确定"
            cancel-text="取消"
            title="确定执行此操作？"
            
            @confirm="actionsClick('off', record)"
          >
            <a-button type="link" class="btn-css">{{
              record.status === 2 ? "下架" : "上架"
            }}</a-button>
          </a-popconfirm>
          <a-popconfirm
            ok-text="确定"
            cancel-text="取消"
            title="确定执行此操作？"
            class="btn-css"
            @confirm="actionsClick('delete', record)"
          >
            <a-button type="link" class="btn-css">删除</a-button>
          </a-popconfirm>
          <a-button type="link" class="btn-css"
            @click="actionsClick('edit', record)"
            >编辑</a-button
          >
        </div>
      </template>
      <template v-if="column.key === 'platformFullAct'">
        <div class="actions-box">
          <a-button type="link" class="btn-css" @click="actionsClick('look', record)"
            >查看</a-button
          >
          <a-button type="link" class="btn-css"
            v-if="record.status === 'NO_START' || record.status === 'START'"
            @click="actionsClick('edit', record)"
            >修改</a-button
          >
          <a-popconfirm
            ok-text="确定"
            cancel-text="取消"
            title="确定执行此操作？"
            class="btn-css"
            @confirm="actionsClick('delete', record)"
          >
            <a-button type="link" class="btn-css">终止</a-button>
          </a-popconfirm>
        </div>
      </template>
      <template v-if="column.key === 'reviewHistory'">
        <div class="actions-box">
          <a-button type="link" class="btn-css" @click="actionsClick('look', record)"
            >查看</a-button
          >
          
          <div v-if="record.auditStatus === 3">
            <a-button type="link" class="btn-css" @click="actionsClick('bohui', record)"
              >驳回理由</a-button
            >
          </div>
          <div v-if="record.operationReason === 'VIOLATION_OFF_SALE'">
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiRes', record)"
              >违规原因</a-button
            >
          </div>
        </div>
      </template>
      <template v-if="column.key === 'GMV'">
        <span>
          {{ record.totalCashPrice ? `￥${record.totalCashPrice}` : 0 }} +
          {{ record.totalCoinPrice ? record.totalCoinPrice : 0 }}
        </span>
      </template>
      <template v-if="column.key === 'npGoodsList'">
        <div class="actions-box">
          <a-button type="link" class="btn-css" @click="actionsClick('look', record)">查看</a-button>
          <a-button type="link" class="btn-css" v-if="
              record.status === 0 &&
              record.auditType === 1 &&
              record.auditStatus === 1

            " @click="actionsClick('shenhe', record)">审核历史</a-button>
          <span
            v-if="
              record.status === 0 &&
              record.auditType === 1 &&
              record.auditStatus === 3
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('bohui', record)"
              >驳回理由</a-button
            >
          </span>
          <span
            v-if="
              record.status === 0 &&
              record.auditType === 0 &&
              record.auditStatus === 0
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
          </span>
          <span
            v-if="
              record.status === 2 &&
              record.auditType === 1 &&
              record.auditStatus === 1
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiRes', record)"
              >违规原因</a-button
            >
          </span>
          <span
            v-if="
              record.status === 2 &&
              record.auditType === 1 &&
              record.auditStatus === 3
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiRes', record)"
              >违规原因</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('bohui', record)"
              >驳回理由</a-button
            >
          </span>
          <span
            v-if="
              record.status === 2 &&
              record.auditType === 0 &&
              record.auditStatus === 0
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiRes', record)"
              >违规原因</a-button
            >
          </span>
          <span
            v-if="
              record.status === 1 &&
              record.auditType === 1 &&
              record.auditStatus === 2
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiOff', record)"
              >违规下架</a-button
            >
          </span>
          <span
            v-if="
              record.status === 1 &&
              record.auditType === 2 &&
              record.auditStatus === 1
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiOff', record)"
              >违规下架</a-button
            >
          </span>
          <span
            v-if="
              record.status === 1 &&
              record.auditType === 2 &&
              record.auditStatus === 3
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('bohui', record)"
              >驳回理由</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiOff', record)"
              >违规下架</a-button
            >
          </span>
          <span
            v-if="
              record.status === 1 &&
              record.auditType === 0 &&
              record.auditStatus === 0
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiOff', record)"
              >违规下架</a-button
            >
          </span>
          <span
            v-if="
              record.status === 0 &&
              record.auditType === 2 &&
              record.auditStatus === 2
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
          </span>
          <span
            v-if="
              record.status === 1 &&
              record.auditType === 3 &&
              record.auditStatus === 1
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiOff', record)"
              >违规下架</a-button
            >
          </span>
          <span
            v-if="
              record.status === 1 &&
              record.auditType === 3 &&
              record.auditStatus === 2
            "
          >
            <a-button type="link" class="btn-css" @click="actionsClick('shenhe', record)"
              >审核历史</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('weiguiOff', record)"
              >违规下架</a-button
            >
          </span>
        </div>
      </template>
    </template>
    <template v-for="(_, slotName) in $slots" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps"></slot>
    </template>
  </a-table>
</template>
<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch, computed } from "vue";
import { DownOutlined } from '@ant-design/icons-vue';
const orderStatus = {
  WAIT_PAY: "待支付",
  WAIT_USE: "待使用",
  RECHARGING: "充值中",
  PAY_SUCCESS: "待拣货",
  WAIT_DELIVER: "待发货",
  WAIT_RECEIVE: "待收货",
  COMPLETE: "已完成",
  CANCEL: "已取消",
  GOLD_CREATE_FAILED: "订单失败",
  CLOSE: "交易关闭",
};
const goodsListStatus = {
  1: "上架",
  0: "下架",
  2: "违规下架",
};
const buttonColor = {
  edit: false,
  delete: true,
};
const goodsListType = ref([]);
// Props
const props = defineProps({
  isLook: {
    type: Array,
    required: false,
  },
  columns: {
    type: Array,
    required: true,
  },
  dataSource: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
    }),
  },
  rowKey: {
    type: String,
    default: "id",
  },
  isCheckbox: {
    type: Boolean,
    default: true,
  },
  isRadio: {
    type: Boolean,
    default: false,
  },
});

const selectedRowKeys = ref([]);

// Emits
const emit = defineEmits([
  "update:pagination",
  "fetchData",
  "actions-click",
  "select-change",
  "blur-change",
  "hotCakes-change",
]);

const handleTableChange = (newPagination, filters, sorter) => {
  emit("update:pagination", newPagination);
  emit("fetchData", { pagination: newPagination, filters, sorter });
};

const sortBlur = (sort, data) => {
  emit("blur-change", sort, data);
};
const hotCakesBlur = (hotCakes, data) => {
  emit("hotCakes-change", hotCakes, data);
};

// 列表复选框 事件
const onSelectChange = (newSelectedRowKeys) => {
  console.log(newSelectedRowKeys, "newSelectedRowKeys");
  selectedRowKeys.value = newSelectedRowKeys;
  const rowId = newSelectedRowKeys.map((v) => {
    return v[props.rowKey];
  });
  emit("select-change", selectedRowKeys.value, rowId);
  // checkCount.value = newSelectedRowKeys.length;
};

const rowSelection = computed(() => ({
  type: props.isRadio ? "radio" : "checkout",
  selectedRowKeys,
  onChange: onSelectChange,
  getCheckboxProps: (record) => ({
    disabled: record.checked === true, // Column configuration not to be checked
  }),
}));

//操作
const actionsClick = (type, record) => {
  emit("actions-click", type, record);
};
</script>
<style lang="less" scoped>
.con-style {
  width: 80px;
  height: 80px;
}
.actions-box {
  float: left;
  
}
.proinfo-css {
  display: flex;
  align-items: center;
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */

  white-space: nowrap;
  // width: 300px; /* 设置容器的宽度，根据需要调整 */
}
.desc-css {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 限制显示的行数 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */
  width: 400px; /* 设置容器的宽度，根据需要调整 */
}
.btn-w {
  width: 80px;
}
</style>
