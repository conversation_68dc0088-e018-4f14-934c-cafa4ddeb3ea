<template>
  <a-table
    :columns="columns"
    :data-source="tableData"
    :row-key="rowKey"
    :scroll="scrollObj"
    :row-selection="rowSelection"
    :pagination="paginationSelf"
    :transform-cell-text="transformCellText"
    :loading="loading"
    @change="handleChange"
  >
    <template #bodyCell="{ column, text, record }">
      <slot name="bodyCell" :column="column" :record="record" :text="text" />
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { isEmptyValue, isObj } from "@/utils";

const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  rowKey: {
    type: [Number, String],
    default: "id",
  },
  rowSelection: {
    type: [null, Object],
    default: null,
  },
  scrollObj: {
    type: Object,
    default: () => ({}),
  },
  pagination: {
    type: [Object, Boolean],
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["onChange"]);

const paginationSelf = ref<object | boolean>({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 项数据`,
});

// 处理展示内容
const transformCellText = ({ text, column, record }) => {
  if (
    !isObj(text[0]) &&
    isObj(column) &&
    !isEmptyValue(column.dataIndex) &&
    column.dataIndex !== "operation" &&
    column.dataIndex !== "row-select"
  ) {
    if (!isEmptyValue(record[column.dataIndex])) {
      return text;
    }
    return "-";
  }
  return text;
};

// 分页、排序、筛选变化时触发
const handleChange = (pagination) => {
  emits("onChange", pagination);
};

watch(
  () => props.pagination,
  (newValue) => {
    if (isObj(newValue)) {
      Object.keys(newValue).forEach((key) => {
        paginationSelf.value[key] = newValue[key];
      });
    } else {
      paginationSelf.value = false;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
