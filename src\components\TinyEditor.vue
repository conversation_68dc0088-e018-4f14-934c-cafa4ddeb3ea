<!-- components/TinyEditor.vue -->
<template>
    <Editor
      v-model="content"
      :init="editorInit"
      :disabled="false"
    />
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import Editor from '@tinymce/tinymce-vue'
  
  // 本地引入 TinyMCE 核心和插件
  import 'tinymce/tinymce'
  import 'tinymce/themes/silver/theme'
  import 'tinymce/icons/default/icons'
  import 'tinymce/plugins/link'
  import 'tinymce/plugins/image'
  import 'tinymce/plugins/code'
  import 'tinymce/plugins/lists'
  
  // 样式
  import 'tinymce/skins/ui/oxide/skin.min.css'
  
  const content = ref('<p>Hello from TinyMCE (本地版)</p>')
  
  const editorInit = {
    height: 400,
    menubar: false,
    plugins: 'link image code lists',
    toolbar: 'undo redo | bold italic underline | bullist numlist | link image | code',
    branding: false,
    language: 'zh_CN'
  }
  </script>
  