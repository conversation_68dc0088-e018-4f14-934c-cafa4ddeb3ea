<template>
  <!-- :custom-request="customUpload" -->
  <a-upload
    accept="image/*"
    :disabled="props.disabled"
    name="file"
    :headers="header"
    list-type="picture-card"
    :data="formatRequest"
    :file-list="props.fileList"
    :action="props.action"
    class="avatar-uploader"
    :multiple="props.multiple"
    :max-count="props.maxCount"
    :before-upload="beforeUpload"
    @change="handleChange"
    @preview="handlePreview"
    @remove="handleDelete"
    @success="handleSuccess"
  >
    <div v-if="props.fileList.length < props.maxCount">
      <loading-outlined v-if="loading"></loading-outlined>
      <plus-outlined v-else></plus-outlined>
      <div class="ant-upload-text">{{ props.btnText }}</div>
    </div>
  </a-upload>

  <a-modal
    :open="previewVisible"
    :title="previewTitle"
    :footer="null"
    centered
    :z-index="10000"
    @cancel="handleCancel"
  >
    <img alt="example" style="width: 100%" :src="previewImage" />
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { PlusOutlined, LoadingOutlined } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import type { UploadChangeParam, UploadProps } from "woody-ui";
import axios from "axios";
import { qiNiuYunToken } from "@/api/common";

const emits = defineEmits(["getUrlList", "successChange"]);

const props = defineProps({
  resourceType: {
    type: String,
    default: "image",
  },
  bizType: {
    type: String,
    default: "",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  action: {
    type: String,
    default: "",
  },
  formData: {
    type: Object,
    default: null,
  },
  fileList: {
    type: Array,
    default: () => [],
  },
  requestType: {
    type: String,
    default: "",
  },
  btnText: {
    type: String,
    default: "上传",
  },
  fileSize: {
    type: Number,
    default: 2,
  },
  maxCount: {
    type: Number,
    default: 100,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// 计算属性：根据是否传入 customRequest 来设置上传参数
const uploadProps = computed(() => ({
  action: !props.requestType ? props.action : undefined,
  customRequest: props.requestType || undefined,
  // headers: props.headers,
  // multiple: props.multiple,
  // fileList: props.fileList,
  // data: props.data,
}));

const header = {
  Authorization: JSON.parse(localStorage.getItem("user-auth")).accessToken,
  clientType: "PLATFORM",
};

const formatRequest = (requestData) => {
  console.log(props.formData, "formData");
  return { ...requestData, ...props.formData };
};

function getBase64(img: Blob, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener("load", () => callback(reader.result as string));
  reader.readAsDataURL(img);
}
function getBase642(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

const fileList = ref([]);
const loading = ref<boolean>(false);

const tokenData = ref({
  agreement: "",
  domain: "",
  path: "",
  token: "",
});

const customUpload: UploadProps["customRequest"] = (e) => {
  qiNiuYunToken({
    bizType: props.bizType,
    resourceType: props.resourceType,
  }).then((res) => {
    if (res.code === 0) {
      const { token, path, agreement, domain } = res.data;
      tokenData.value = {
        token,
        path,
        agreement,
        domain,
      };
      e.onSuccess(e);
    }
  });
};
const beforeUpload = (file) => {
  if (file.size > 2 * 1024 * 1024) {
    return false;
  }
};

const handleSuccess = (e) => {
  if (e.code !== 0) {
    message.error(e.message || "系统异常");
    return;
  }
  emits("successChange", e.data);
};

const handleChange = (info: UploadChangeParam) => {
  console.log(info, "info567");
  // 检查文件大小
  const maxSizeInMB = props.fileSize;
  if (info.file.size > maxSizeInMB * 1024 * 1024) {
    message.error(`文件大小不能超过 ${maxSizeInMB}MB`);
    return;
  }
  if (info.file.status === "uploading") {
    loading.value = true;
    return;
  }
  if (info.file.status === "done") {
    getBase64(info.file.originFileObj, () => {
      loading.value = false;
    });

    const formData = new FormData();
    const { token, path } = tokenData.value;
    const key = `${path}${
      info.file.name.split(".")[0]
    }_${new Date().getTime()}.${info.file.name.split(".")[1]}`;
    formData.append("key", key);
    formData.append("token", token);
    formData.append("file", info.file.originFileObj);
    formData.append("fname", info.file.name);
    const { agreement, domain } = tokenData.value;

    axios
      .post("https://upload.qiniup.com/", formData, {
        headers: {
          "content-type": "multipart/form-data",
        },
      })
      .then((response) => {
        if (response.data.key) {
          fileList.value = [
            ...fileList.value,
            {
              url: `${agreement}://${domain}/${response.data.key}`,
              name: info.file.name,
              uid: info.file.uid,
            },
          ];
          // fileList.value = [...new Set(fileList.value)];

          emits("getUrlList", fileList.value);
          emits("successChange", fileList.value);
        }
      })
      .catch((error) => {
        console.log("上传失败", error);
      });
  }
  if (info.file.status === "error") {
    loading.value = false;
    message.error("upload error");
  }
};

const previewVisible = ref(false);
const previewImage = ref("");
const previewTitle = ref("");
const handlePreview = async (file: UploadProps["fileList"][number]) => {
  if (!file.url && !file.preview) {
    file.preview = (await getBase642(file.originFileObj)) as string;
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value =
    file.name || file.url.substring(file.url.lastIndexOf("/") + 1);
};
const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = "";
};
const handleDelete = (file: UploadProps["fileList"][number]) => {
  console.log(file, "filefilefilefile");
  const index = fileList.value.indexOf(file.url);
  fileList.value.splice(index, 1);
  loading.value = false;
  if (fileList.value.length === 0) {
    fileList.value = [];
  }
  console.log(fileList.value, "fileList.valuefileList.value");
  emits("getUrlList", fileList.value);
  emits("successChange", fileList.value);
};
</script>
<style scoped>
.avatar-uploader > .ant-upload {
  width: 138px;
  height: 138px;
  overflow: hidden;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
