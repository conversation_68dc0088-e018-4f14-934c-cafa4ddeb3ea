<template>
  <div>
    <a-modal
      title="导出订单数据"
      :open="visible"
      :footer="null"
      :width="500"
      @cancel="closeFunc"
    >
      <a-form ref="formRef" :model="formData" @finish="onSubmit">
        <a-form-item
          label="电子邮箱"
          name="emailAddress"
          :rules="[
            { required: true, message: '邮箱必填' },
            { type: 'email', message: '邮箱格式不正确！' },
          ]"
          extra="导出完成文件会发送至邮箱，请勿多次发送，请确保邮箱正确"
        >
          <a-input
            v-model:value="formData.emailAddress"
            placeholder="请输入内容"
          ></a-input>
        </a-form-item>
        <a-form-item :status-icon="false" style="text-align: center">
          <a-space size="small">
            <a-button style="margin-right: 16px" @click="reset" type="default"
              >重置</a-button
            >
            <a-button htmlType="submit" type="primary">导出</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "woody-ui";
import { tripTask, exportCheck } from "@/api/common.ts";

const formRef = ref(null);
const formData = reactive({
  emailAddress: "",
});
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  info: {
    type: Object,
  },
  taskName: {
    type: String,
  },
});
const emits = defineEmits(["downloadBack"]);

const onSubmit = async () => {
  // let res = await exportCheck(props.info);
  // if (res.code === 0) {
  tripTask({
    inform: JSON.stringify({ emailAddress: [formData.emailAddress] }),
    informWay: "email",
    taskName: props.taskName,
    taskParam: JSON.stringify({
      ...props.info,
      emailAddress: formData.emailAddress,
    }),
  })
    .then((res) => {
      if (res.code === 0) {
        message.success("已成功导出至该邮箱！");
        emits("downloadBack");
        reset();
      }
    })
    .catch((err) => {
      console.log(err);
    });
  // }
};
const closeFunc = () => {
  reset();
  emits("downloadBack");
};
const reset = () => {
  formRef.value.resetFields();
};
</script>
