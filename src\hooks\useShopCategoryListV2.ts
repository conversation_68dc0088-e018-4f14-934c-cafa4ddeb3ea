/**
 * 分类下拉数据V2（支持 3 级分类）
 */
import { ref, watch, onMounted } from "vue";
import {
  getShopCategoryListV2,
  SubCategoryListItem,
} from "@/api/goodsCenter/localLife";
import { message } from "woody-ui";

export function useShopCategoryListV2(
  isCheckAllLevel?: boolean,
  show?: number
) {
  const cascaderOptions = ref<Array<any>>([]);

  // 递归处理分类数据为级联选择器格式
  const mapCategories = (categories: SubCategoryListItem[]): any => {
    return categories.map((category: SubCategoryListItem) => {
      const { subCategoryList = [], ...rest } = category;
      return {
        value: category.shopCategoryId,
        label: category.categoryName,
        children: mapCategories(subCategoryList || []), // 递归处理子类别
        ...rest,
      };
    });
  };

  // 查询商品分类列表
  const fetchCategoryList = () => {
    getShopCategoryListV2({
      isAllowEmptySub: false,
      isCheckAllLevel,
      show,
    })
      .then((res) => {
        if (res.code !== 0) {
          return message.error(res.message || "请求失败!");
        }
        cascaderOptions.value = mapCategories(res.data);
      })
      .catch((err) => {
        message.error(err?.message || "请求异常");
      });
  };

  watch(
    [() => isCheckAllLevel, () => show],
    () => {
      fetchCategoryList();
    },
    { immediate: true }
  );

  onMounted(() => {
    fetchCategoryList();
  });

  return {
    cascaderOptions,
  };
}
