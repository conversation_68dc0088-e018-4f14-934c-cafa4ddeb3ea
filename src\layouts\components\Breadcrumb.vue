<template>
  <div class="breadcrumb-box">
    <a-breadcrumb>
      <a-breadcrumb-item
        v-for="item in crumbs"
        :key="item.to"
      >
        <router-link
          v-if="!item.disabled"
          :to="item.to"
          style="max-width: 150px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
        >
          {{ item.title }}
        </router-link>
        <span
          v-else
          style="max-width: 150px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
        >
          {{ item.title }}
        </span>
      </a-breadcrumb-item>
    </a-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const crumbs = computed(() => {
  const pathArray = route.path.split('/');
  pathArray.shift();

  const breadcrumbs = pathArray.reduce((breadcrumbArray, path, idx) => {
    // 如果路由下有hiddenBreadcrumb或当前遍历到参数则隐藏
    if (route.matched[idx]?.meta?.hiddenBreadcrumb || Object.values(route.params).includes(path)) {
      return breadcrumbArray;
    }

    breadcrumbArray.push({
      path,
      to: setRoutePath(breadcrumbArray, path),
      title: route.matched[idx]?.meta?.title ?? path,
    });
    breadcrumbArray[0].disabled = true;
    return breadcrumbArray;
  }, []);
  return breadcrumbs;
});

// 设置路由路径地址
const setRoutePath = (routeArray, path) => {
  let resultPath = '';
  for (let i = 0; i <= routeArray.length; i++) {
    if (i === routeArray.length) {
      resultPath += `/${path}`;
    } else {
      resultPath += `/${routeArray[i].path}`;
    }
  }
  return resultPath;
};
</script>
<style scoped>
.breadcrumb-box {
  background-color: #fff;
  padding: 10px 20px;
}
</style>
