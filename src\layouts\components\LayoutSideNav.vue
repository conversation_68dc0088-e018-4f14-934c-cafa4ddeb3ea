<template>
  <l-side-nav :menu="sideMenu" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { usePermissionStore, useSettingStore } from "@/store";
import LSideNav from "./SideNav.vue";

const route = useRoute();
const permissionStore = usePermissionStore();
const settingStore = useSettingStore();
const { routers: menuRouters } = storeToRefs(permissionStore);
// console.log(storeToRefs(permissionStore),'9090909090')
const sideMenu = computed(() => {
  const { layout, splitMenu } = settingStore;
  let newMenuRouters = menuRouters.value;
  if (layout === "mix" && splitMenu) {
    newMenuRouters.forEach((menu) => {
      if (route.path.indexOf(menu.path) === 0) {
        newMenuRouters = menu.children.map((subMenu) => ({
          ...subMenu,
          path: `${menu.path}/${subMenu.path}`,
        }));
      }
    });
  }
  // 判断是否在微环境中
  if (window.__MICRO_APP_ENVIRONMENT__) {
    const menuAuthList = JSON.parse(localStorage.getItem("menuList"));
    let navArr = []; // 用来存储二级菜单
    if (Array.isArray(menuAuthList)) {
      for (const i of menuAuthList) {
        // 遍历 tabArr 中的每个元素
        if (i.nameEn === "npMarket") {
          navArr = i.list;
        }
      }
      // 输出二级菜单列表
      console.log(navArr, "权限菜单");
    }
    console.log(menuRouters.value, "总菜单");
    const arr = [];
    for (const v of navArr) {
      for (const s of menuRouters.value) {
        if (v.nameEn === s.nameEn) {
          arr.push(s);
        }
      }
    }
    // 调用函数
    const nameEnArray = getAllNameEn(navArr);
    // 调用函数，得到筛选后的数据
    const filteredData = filterDataByNameEn(arr, nameEnArray);
    console.log(filteredData, "最终的数据");
    return filteredData;
  }
  return newMenuRouters;
});
// 递归函数：获取所有nameEn
const getAllNameEn = (data) => {
  let result = [];
  data.forEach((item) => {
    // 添加当前项的 nameEn
    if (item.nameEn) result.push(item.nameEn);
    // 如果有子节点，递归遍历子节点
    if (Array.isArray(item.list) && item.list.length > 0) {
      result = result.concat(getAllNameEn(item.list)); // 合并子节点的结果
    }
  });
  return result;
};
// 递归筛选节点
const filterDataByNameEn = (data, selectedNamesEn) => {
  return data
    .map((item) => {
      // 过滤出当前节点符合 nameEn 的项
      if (selectedNamesEn.includes(item.nameEn)) {
        // 如果有子节点，也需要递归处理
        if (item.children && item.children.length > 0) {
          item.children = filterDataByNameEn(item.children, selectedNamesEn);
        }
        return item; // 保留符合条件的节点
      }
      return null; // 不符合条件的节点删除
    })
    .filter((item) => item !== null); // 删除所有 null 值
};
</script>
