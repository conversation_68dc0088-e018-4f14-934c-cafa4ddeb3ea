<template>
  <a-menu
    class="wd-menu"
    mode="inline"
    :openKeys="defaultExpanded"
    :selectedKeys="[active]"
    @openChange="menuExpand"
    @click="menuClick"
  >
    <menu-content :nav-data="list" :iconArr="iconArr" />
  </a-menu>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import type { PropType } from "vue";
import type { MenuRoute } from "@/types/interface";
import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import MenuContent from "./MenuContent.vue";
import { usePermissionStore } from "@/store";

const props = defineProps({
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: () => [],
  },
});

const route = useRoute();
const router = useRouter();
const iconArr = ref<Array<string>>([]);
const permissionStore = usePermissionStore();
const { routers: menuRouters } = storeToRefs(permissionStore);
type ListItemType = MenuRoute & { icon?: string };

const active = computed(() => {
  let path: any = null;
  let pathUrl = route.path;
  route.matched.forEach((item: any) => {
    if (item.path == pathUrl) {
      if (item?.meta && item?.meta?.replace) {
        let lastSlashIndex = pathUrl.lastIndexOf("/");
        path = pathUrl.slice(0, lastSlashIndex) + `/${item.meta.replace}`;
      } else {
        path = pathUrl;
      }
    }
  });
  console.log(path, "path");
  // sessionStorage.setItem('menuActive', path);
  return path;
});

// export const changeMenuActive = (actPath) => {
//   active();
// };

const defaultExpanded = computed(() => {
  const path = route.path;
  console.log(route,'path00000')
  try {
    const parts = path.split("/");
    console.log(parts, "parts");
    const result = parts.slice(1).reduce((acc, part, index) => {
      const path = `/${parts.slice(1, index + 2).join("/")}`;
      return acc.concat(path);
    }, []);
    iconArr.value = result;
    console.log(result,active, "result0000000");
    return result;
  } catch (error) {
    iconArr.value = [];
    return [];
  }
});

const list = computed(() => {
  // props.menu
  return getMenuList(props.menu);
});
const getMenuList = (list: MenuRoute[], basePath?: string): ListItemType[] => {
  if (!list) return [];
  // 如果meta中有orderNo则按照从小到大排序
  list.sort((a, b) => {
    return (a.meta?.orderNo || 0) - (b.meta?.orderNo || 0);
  });
  return list
    .map((item) => {
      const path =
        basePath && !item.path.includes(basePath)
          ? `${basePath}/${item.path}`
          : item.path;
      return {
        path,
        title: item.meta?.title,
        icon: item.meta?.icon || "",
        activeIcon: item.meta?.activeIcon || "",
        children: getMenuList(item.children, path),
        meta: item.meta,
      };
    })
    .filter((item) => item.meta && item.meta.hidden !== true);
};

// 展开的菜单项发生变化时触发
const menuExpand = (openKeys) => {
  iconArr.value = openKeys;
};

const menuClick = ({ item, key, keyPath }) => {
  console.log(item, key, keyPath);
  router.push({ path: key });
};
</script>

<style lang="less" scoped>
.wd-menu {
  margin: 15px;
  width: 232px;
  height: 90% !important;
  border-radius: 12px;
  background: linear-gradient(to bottom, #e5f4ff, #f2effd, #f6fbfe);
  overflow-y: auto;
  overflow-x: hidden;
  border: none !important;
  margin-right: 0px;
  padding-top: 16px;
}

:deep(.t-menu) {
  border-right: 0 !important;
}

:deep(.ant-menu-inline) {
  background: none !important;
}

:deep(.ant-menu-item-selected) {
  background: #ffffff !important;
  // border-radius: 4px !important;
  font-weight: bold;
}

// 菜单样式修改
:deep(.ant-menu-submenu) {
  color: #495366 !important;
  .ant-menu-submenu-title {
    height: 48px !important;
    margin-inline: 16px;
    padding-left: 16px !important;
    padding-right: 16px !important;
    width: calc(100% - 32px);
    .ant-menu-title-content {
      margin-left: 8px !important;
    }
    &:hover {
      background-color: rgba(255, 255, 255, 0.6) !important;
    }
  }
  .ant-menu-item {
    padding-left: 16px !important;
    height: 48px !important;
    margin-inline: 16px;
    width: calc(100% - 32px);
    &:not(.ant-menu-item-selected):hover  {
      background-color: rgba(255, 255, 255, 0.6);
    }
  }
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      padding-left: 16px !important;
    }
    .ant-menu-item {
      padding-left: 41px !important;
      height: 48px !important;
      margin-inline: 16px;
      width: calc(100% - 32px);
      &:not(.ant-menu-item-selected):hover  {
        background-color: rgba(255, 255, 255, 0.6);
      }
    }
  }
  .ant-menu-item-icon {
    margin-right: 0;
  }
}

:deep(.ant-menu-submenu-selected) {
  > .ant-menu-submenu-title  {
    color: #1677ff !important;
    font-weight: bold;
  }
}

// 滚动条整体样式
.ant-menu::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}
/* 滚动条轨道样式 */
.ant-menu::-webkit-scrollbar-track {
  // background-color: #f1f6f8; /* 轨道背景色 */
  border-radius: 3px; /* 圆角 */
}

/* 滚动条滑块样式 */
.ant-menu::-webkit-scrollbar-thumb {
  background-color: #e0e8ed; /* 滑块颜色 */
  border-radius: 3px; /* 圆角 */
}
</style>
