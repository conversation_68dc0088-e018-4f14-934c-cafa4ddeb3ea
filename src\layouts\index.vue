<template>
  <div style="background: linear-gradient(to bottom, #d5efff, #ece4fc, #edf7fd)">
    <a-layout style="display: flex; width: 100%">
      <a-layout-sider class="sider-css"><layout-side-nav /></a-layout-sider>
      <a-layout
        style="background: linear-gradient(to bottom, #d5efff, #ece4fc, #edf7fd); flex: 1"
        class="a-layout-css"
      >
        <a-layout-content>
          <noPage v-if="path === '/product'" />
          <router-view v-else></router-view>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, watch, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { useSettingStore, useTabsRouterStore } from '@/store';
  import LBreadcrumb from './components/Breadcrumb.vue';
  import LayoutSideNav from './components/LayoutSideNav.vue';
  import noPage from '@/components/noPage.vue';

  import '@/style/layout.less';
  import router from '@/router';

  const route = useRoute();
  const path = ref('');

  const settingStore = useSettingStore();
  const tabsRouterStore = useTabsRouterStore();
  const setting = storeToRefs(settingStore);

  const mainLayoutCls = computed(() => [
    {
      't-layout--with-sider': settingStore.showSidebar,
    },
  ]);

  const appendNewRoute = () => {
    const {
      path,
      query,
      meta: { title },
      name,
    } = route;
    tabsRouterStore.appendTabRouterList({
      path,
      query,
      title: title as string,
      name,
      isAlive: true,
      meta: route.meta,
    });
  };

  onMounted(() => {
    appendNewRoute();
  });
  watch(
    route, // 监听 route 对象
    (newRoute, oldRoute) => {
      path.value = router.options.history.location;
    },
    { immediate: true, deep: true }, // 初始化时立即执行一次
  );

  watch(
    () => route.path,
    () => {
      appendNewRoute();
      // document.querySelector(`.${prefix}-layout`).scrollTo({ top: 0, behavior: 'smooth' });
    },
  );
</script>

<style lang="less" scoped>
  .a-layout-css {
    height: 89vh;
    margin: 16px;
    overflow-y: auto;
  }
  .sider-css {
    height: 100vh;
    overflow-y: auto;
  }
</style>
