import microApp from '@micro-zoe/micro-app';
import { createApp } from 'vue';
import Vue3ColorPicker from 'vue3-colorpicker'; // 注册组件
import 'vue3-colorpicker/style.css'; // 引入样式文件
import { tokenStore } from '@/request/tokenProvider';
import router from './router';
import { store } from './store';
import '@/style/index.less';
import '@/style/woody-style.less';
import './permission';
import PageWrap from './components/PageWrap/index.vue';
import ConfirmBtn from './components/ConfirmBtn/index.vue';
import App from './App.vue';
import '@/style/table.less';
import '@/style/modal.less';
import 'dayjs/locale/zh-cn';
import 'woody-ui/dist/reset.css';
import '@/style/index.less';
import './permission';
import {
  Upload,
  Modal,
  Select,
  Button,
  Table,
  Drawer,
  Row,
  Col,
  Input,
  Form,
  DatePicker,
  InputNumber,
  Space,
  Image,
  Tooltip,
  Popconfirm,
  Switch,
  Radio,
  Carousel,
  Cascader,
  Tabs,
  TimePicker,
  Tree,
  Menu,
  Pagination,
  ConfigProvider,
  Descriptions,
  Checkbox,
  Popover,
  Dropdown,
  Tag,
  Breadcrumb,
  Divider,
  Spin,
  TreeSelect,
  Slider,
  Rate
} from 'woody-ui';

const userAuth = JSON.parse(localStorage.getItem('user-auth')) || '';
const { accessToken, refreshToken } = userAuth;
tokenStore.setToken(accessToken);
tokenStore.setRefreshToken(refreshToken);
const app = createApp(App);
microApp.start();
app.use(Drawer);
app.use(Upload);
app.use(Cascader);
app.use(Table);
app.use(Menu);
app.use(Tag);
app.use(Button);
app.use(Modal);
app.use(Select);
app.use(Form);
app.use(Row);
app.use(Col);
app.use(Breadcrumb);
app.use(ConfigProvider);
app.use(store);
app.use(router);
app.use(Vue3ColorPicker);

app.component('PageWrap', PageWrap);
app.component('ConfirmBtn', ConfirmBtn);
app
  .use(Upload)
  .use(Modal)
  .use(Select)
  .use(Button)
  .use(Table)
  .use(Drawer)
  .use(Row)
  .use(Col)
  .use(Input)
  .use(Form)
  .use(InputNumber)
  .use(Space)
  .use(Image)
  .use(Tooltip)
  .use(Radio)
  .use(Popconfirm)
  .use(DatePicker)
  .use(Switch)
  .use(Carousel)
  .use(Cascader)
  .use(TimePicker)
  .use(Tree)
  .use(Tabs)
  .use(Menu)
  .use(Pagination)
  .use(ConfigProvider)
  .use(Descriptions)
  .use(Checkbox)
  .use(Popover)
  .use(Dropdown)
  .use(Divider)
  .use(TreeSelect)
  .use(Slider)
  .use(Rate)
  .use(Spin);
app.mount('#app');
