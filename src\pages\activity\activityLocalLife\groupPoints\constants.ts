export const COLUMNS = [
  {
    title: "分享单号",
    dataIndex: "shareNumber",
    key: "shareNumber",
    width: 200,
    fixed: "left",
    align: "left",
    ellipsis: true,
  },
  {
    title: "订单号",
    dataIndex: "orderNumber",
    key: "orderNumber",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "商品名称",
    dataIndex: "productName",
    key: "productName",
    width: 220,
    ellipsis: true,
    align: "left",
  },
  {
    title: "店铺名称",
    dataIndex: "shopName",
    key: "shopName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "分享者昵称",
    dataIndex: "shareUserName",
    key: "shareUserName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "购买者昵称",
    dataIndex: "orderUserName",
    key: "orderUserName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "分享时间",
    dataIndex: "shareTime",
    key: "shareTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "下单时间",
    dataIndex: "orderTime",
    key: "orderTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "支付时间",
    dataIndex: "payTime",
    key: "payTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "核销时间",
    dataIndex: "finishTime",
    key: "finishTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "店铺等级",
    dataIndex: "shopLevel",
    key: "shopLevel",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "订单状态",
    dataIndex: "orderStatus",
    key: "orderStatus",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "商品单价",
    dataIndex: "productPrice",
    key: "productPrice",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预计获得积分",
    dataIndex: "preUserPv",
    key: "preUserPv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "实际订单金额",
    dataIndex: "accOrderAmount",
    key: "accOrderAmount",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "实际获得积分",
    dataIndex: "accUserPv",
    key: "accUserPv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "售后积分",
    dataIndex: "refundPv",
    key: "refundPv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "运营",
    dataIndex: "operatePv",
    key: "operatePv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "平台",
    dataIndex: "platPv",
    key: "platPv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "分享者",
    dataIndex: "shareAccountPv",
    key: "shareAccountPv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "商家",
    dataIndex: "accShopPv",
    key: "accShopPv",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "新用户",
    dataIndex: "newUser",
    key: "newUser",
    width: 200,
    ellipsis: true,
    fixed: "right",
    align: "left",
  },
];

export const FROM_DATA = {
  shareNumber: undefined,
  orderNumber: undefined,
  shopName: undefined,
  productName: undefined,
  shareUserMobile: undefined,
  orderUserMobile: undefined,
  shareUserName: undefined,
  orderUserName: undefined,
  shareTime: undefined,
  shareEndTime: undefined,
  shareStartTime: undefined,
  orderTime: undefined,
  orderEndTime: undefined,
  orderStartTime: undefined,
  payTime: undefined,
  finishTime: undefined,
  payEndTime: undefined,
  payStartTime: undefined,
  verificationTime: undefined,
  finishEndTime: undefined,
  finishStartTime: undefined,
  shopLevel: undefined,
  orderStatus: undefined,
  productPrice: undefined,
  preUserPv: undefined,
  accOrderAmount: undefined,
  accUserPv: undefined,
  refundPv: undefined,
  operatePv: undefined,
  newUser: undefined,
  productId: undefined,
  shopId: undefined,
};
