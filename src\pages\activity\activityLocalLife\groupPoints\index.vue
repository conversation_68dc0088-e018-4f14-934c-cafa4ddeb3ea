<template>
  <div v-if="!isDetail">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-columns mt10">
      <a-button class="mb10" type="primary" @click="handleExport"
        >导出</a-button
      >
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 分享单号 -->
          <template v-if="column.key == 'shareNumber'">
            {{ record.shareNumber ? record.shareNumber : "——" }}
          </template>
          <!-- 订单号 -->
          <template v-if="column.key == 'orderNumber'">
            {{ record.orderNumber ? record.orderNumber : "——" }}
          </template>
          <!-- 商品名称 -->
          <template v-if="column.key == 'productName'">
            {{ record.productName ? record.productName : "——" }}
          </template>
          <!-- 店铺名称 -->
          <template v-if="column.key == 'shopName'">
            {{ record.shopName ? record.shopName : "——" }}
          </template>
          <!-- 分享者昵称 -->
          <template v-if="column.key == 'shareUserName'">
            {{ record.shareUserName ? record.shareUserName : "——" }}
          </template>
          <!-- 购买者昵称 -->
          <template v-if="column.key == 'orderUserName'">
            {{ record.orderUserName ? record.orderUserName : "——" }}
          </template>
          <!-- 分享时间 -->
          <template v-if="column.key == 'shareTime'">
            {{ record.shareTime ? record.shareTime : "——" }}
          </template>
          <!-- 下单时间 -->
          <template v-if="column.key == 'orderTime'">
            {{ record.orderTime ? record.orderTime : "——" }}
          </template>
          <!-- 支付时间 -->
          <template v-if="column.key == 'payTime'">
            {{ record.payTime ? record.payTime : "——" }}
          </template>
          <!-- 核销时间 -->
          <template v-if="column.key == 'finishTime'">
            {{ record.finishTime ? record.finishTime : "——" }}
          </template>
          <!-- 店铺等级 -->
          <template v-if="column.key == 'shopLevel'">
            {{
              record.shopLevel === "GOLD"
                ? "金牌商家"
                : record.shopLevel === "SILVER"
                ? "银牌商家"
                : record.shopLevel === "BRONZE"
                ? "铜牌商家"
                : record.shopLevel === "COMMON"
                ? "普通商家"
                : ""
            }}
          </template>
          <!-- 订单状态 -->
          <template v-if="column.key == 'orderStatus'">
            {{ record.orderStatus ? record.orderStatus : "——" }}
          </template>
          <!-- 商品单价 -->
          <template v-if="column.key == 'productPrice'">
            {{ record.productPrice ? record.productPrice : "——" }}
          </template>
          <!-- 预计获得积分 -->
          <template v-if="column.key == 'preUserPv'">
            {{ record.preUserPv ? record.preUserPv : "——" }}
          </template>
          <!-- 实际订单金额 -->
          <template v-if="column.key == 'accOrderAmount'">
            {{ record.accOrderAmount ? record.accOrderAmount : "——" }}
          </template>
          <!-- 实际获得积分 -->
          <template v-if="column.key == 'accUserPv'">
            {{ record.accUserPv ? record.accUserPv : "——" }}
          </template>
          <!-- 售后积分 -->
          <template v-if="column.key == 'refundPv'">
            {{ record.refundPv ? record.refundPv : "——" }}
          </template>
          <!-- 运营 -->
          <template v-if="column.key == 'operatePv'">
            {{ record.operatePv ? record.operatePv : "——" }}
          </template>
          <!-- 平台 -->
          <template v-if="column.key == 'platPv'">
            {{ record.platPv ? record.platPv : "——" }}
          </template>
          <!-- 分享者 -->
          <template v-if="column.key == 'shareAccountPv'">
            {{ record.shareAccountPv ? record.shareAccountPv : "——" }}
          </template>
          <!-- 商家 -->
          <template v-if="column.key == 'accShopPv'">
            {{ record.accShopPv.toString() ? record.accShopPv : "——" }}
          </template>
          <!-- 新用户 -->
          <template v-if="column.key == 'newUser'">
            {{
              record.newUser === "YES"
                ? "是"
                : record.newUser === "NO"
                ? "否"
                : ""
            }}
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { DownOutlined, UpOutlined } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getPage, getExportInterface } from "@/api/activityCenter/groupPoints";
import dayjs from "dayjs";

const expand = ref(false);
const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
let formData = {};
const formList = [
  {
    label: "分享单号",
    name: "shareNumber",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "订单号",
    name: "orderNumber",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "店铺名称",
    name: "shopName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品名称",
    name: "productName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "分享者手机号",
    name: "shareUserMobile",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "购买者手机号",
    name: "orderUserMobile",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "分享者昵称",
    name: "shareUserName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "购买者昵称",
    name: "orderUserName",
    type: "input", // 输入框
    span: 6,
  },
  {
    type: "rangePicker",
    label: "分享时间",
    name: "shareTime",
    // showTime: true,
    span: 6,
  },
  {
    type: "rangePicker",
    label: "下单时间",
    name: "orderTime",
    // showTime: true,
    span: 6,
  },
  {
    type: "rangePicker",
    label: "支付时间",
    name: "payTime",
    // showTime: true,
    span: 6,
  },
  {
    type: "rangePicker",
    label: "核销时间",
    name: "verificationTime",
    // showTime: true,
    span: 6,
  },
  {
    label: "店铺等级",
    name: "shopLevel",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: [
      {
        label: "全部",
        value: "",
      },
      {
        label: "金牌商家",
        value: "GOLD",
      },
      {
        label: "银牌商家",
        value: "SILVER",
      },
      {
        label: "铜牌商家",
        value: "BRONZE",
      },
      {
        label: "普通商家",
        value: "COMMON",
      },
    ],
    showSearch: true,
    needFilter: true,
    span: 6,
  },
  {
    label: "订单状态",
    name: "orderStatus",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: [
      {
        label: "全部",
        value: "",
      },
      {
        label: "待支付",
        value: "WAIT_PAY",
      },
      {
        label: "待使用",
        value: "WAIT_USE",
      },
      {
        label: "已完成",
        value: "COMPLETE",
      },
      {
        label: "交易关闭",
        value: "CLOSE",
      },
      {
        label: "已取消",
        value: "CANCEL",
      },
    ],
    showSearch: true,
    needFilter: true,
    span: 6,
  },
  {
    label: "新用户",
    name: "newUser",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: [
      {
        label: "全部",
        value: "",
      },
      {
        label: "是",
        value: "YES",
      },
      {
        label: "否",
        value: "NO",
      },
    ],
    span: 6,
  },
  {
    label: "商品ID",
    name: "productId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "店铺ID",
    name: "shopId",
    type: "input", // 输入框
    span: 6,
  },
];
// const formData = reactive({
//   shareNumber: undefined,
//   orderNumber: undefined,
//   shopName: undefined,
//   productName: undefined,
//   shareUserMobile: undefined,
//   orderUserMobile: undefined,
//   shareUserName: undefined,
//   orderUserName: undefined,
//   shareTime: undefined,
//   shareEndTime: undefined,
//   shareStartTime: undefined,
//   orderTime: undefined,
//   orderEndTime: undefined,
//   orderStartTime: undefined,
//   payTime: undefined,
//   finishTime: undefined,
//   payEndTime: undefined,
//   payStartTime: undefined,
//   verificationTime: undefined,
//   finishEndTime: undefined,
//   finishStartTime: undefined,
//   shopLevel: undefined,
//   orderStatus: undefined,
//   productPrice: undefined,
//   preUserPv: undefined,
//   accOrderAmount: undefined,
//   accUserPv: undefined,
//   refundPv: undefined,
//   operatePv: undefined,
//   newUser: undefined,
//   productId: undefined,
//   shopId: undefined,
// });
const formTable = ref({
  shareNumber: undefined,
  orderNumber: undefined,
  shopName: undefined,
  productName: undefined,
  shareUserMobile: undefined,
  orderUserMobile: undefined,
  shareUserName: undefined,
  orderUserName: undefined,
  shareTime: undefined,
  shareEndTime: undefined,
  shareStartTime: undefined,
  orderTime: undefined,
  orderEndTime: undefined,
  orderStartTime: undefined,
  payTime: undefined,
  finishTime: undefined,
  payEndTime: undefined,
  payStartTime: undefined,
  verificationTime: undefined,
  finishEndTime: undefined,
  finishStartTime: undefined,
  shopLevel: undefined,
  orderStatus: undefined,
  productPrice: undefined,
  preUserPv: undefined,
  accOrderAmount: undefined,
  accUserPv: undefined,
  refundPv: undefined,
  operatePv: undefined,
  newUser: undefined,
  productId: undefined,
  shopId: undefined,
});
const isDetail = ref(false);
onMounted(async () => {
  getPageList();
});

// 获取表格板数据
// const onSubmit = () => {
//   formTable.value = {
//     shareNumber: undefined,
//     orderNumber: undefined,
//     shopName: undefined,
//     productName: undefined,
//     shareUserMobile: undefined,
//     orderUserMobile: undefined,
//     shareUserName: undefined,
//     orderUserName: undefined,
//     shareTime: undefined,
//     shareEndTime: undefined,
//     shareStartTime: undefined,
//     orderTime: undefined,
//     orderEndTime: undefined,
//     orderStartTime: undefined,
//     payTime: undefined,
//     finishTime: undefined,
//     payEndTime: undefined,
//     payStartTime: undefined,
//     verificationTime: undefined,
//     finishEndTime: undefined,
//     finishStartTime: undefined,
//     shopLevel: undefined,
//     orderStatus: undefined,
//     productPrice: undefined,
//     preUserPv: undefined,
//     accOrderAmount: undefined,
//     accUserPv: undefined,
//     refundPv: undefined,
//     operatePv: undefined,
//     newUser: undefined,
//     productId: undefined,
//     shopId: undefined,
//   };
//   formTable.value = formData;
//   pagination.current = 1;
//   getPageList();
// };

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

const handleSearch = (param) => {
  console.log(param, "pram");
  const { shareTime, orderTime, payTime, verificationTime } = param;
  formData = param;
  formData["shareStartTime"] = Array.isArray(shareTime)
    ? shareTime[0] + " 00:00:00"
    : null;
  formData["shareEndTime"] = Array.isArray(shareTime)
    ? shareTime[1] + " 23:59:59"
    : null;
  formData["orderStartTime"] = Array.isArray(orderTime)
    ? orderTime[0] + " 00:00:00"
    : null;
  formData["orderEndTime"] = Array.isArray(orderTime)
    ? orderTime[1] + " 23:59:59"
    : null;
  formData["payStartTime"] = Array.isArray(payTime)
    ? payTime[0] + " 00:00:00"
    : null;
  formData["payEndTime"] = Array.isArray(payTime)
    ? payTime[1] + " 23:59:59"
    : null;
  formData["finishStartTime"] = Array.isArray(verificationTime)
    ? verificationTime[0] + " 00:00:00"
    : null;
  formData["finishEndTime"] = Array.isArray(verificationTime)
    ? verificationTime[1] + " 23:59:59"
    : null;
  // pagination.value.current = 1;
  // pagination.value.pageSize = 10;
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      ...formData,
    };
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
//导出
const handleExport = async () => {
  const params = {
    // shareStartTime: formData.shareTime ? formData.shareTime[0] : null,
    // shareEndTime: formData.shareTime ? formData.shareTime[1] : null,
    // orderStartTime: formData.orderTime ? formData.orderTime[0] : null,
    // orderEndTime: formData.orderTime ? formData.orderTime[1] : null,
    // payStartTime: formData.payTime ? formData.payTime[0] : null,
    // payEndTime: formData.payTime ? formData.payTime[1] : null,
    // finishStartTime: formData.verificationTime
    //   ? formData.verificationTime[0]
    //   : null,
    // finishEndTime: formData.verificationTime
    //   ? formData.verificationTime[1]
    //   : null,
    ...formData,
  };
  if (pagination.total > 10000) {
    message.error("超过一万条，请修改时间范围重新导出");
    return;
  }
  try {
    const res = await getExportInterface(params);
    console.log(res, "查询res");
    message.success("导出成功");
    const blob = new Blob([res as unknown as BlobPart]);
    const elink = document.createElement("a"); // 创建a标签节点
    let nowdate = new Date();
    let year = nowdate.getFullYear();
    let month = nowdate.getMonth() + 1;
    let date = nowdate.getDate();
    let h = nowdate.getHours();
    let m = nowdate.getMinutes();
    let s = nowdate.getSeconds();
    elink.download = `团购推广积分${
      year + "-" + month + "-" + date + " " + h + ":" + m + ":" + s
    }.xlsx`; // 下载出来的名字
    elink.style.display = "none";
    elink.href = window.URL.createObjectURL(blob); // 创建好的url 放入a 标签 href中
    document.body.appendChild(elink); // 追加到body中
    elink.click(); // 执行a标签的点击
    window.URL.revokeObjectURL(elink.href); // 浏览器打开excel文件
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
.ant-btn-primary:disabled {
  color: rgba(0, 0, 0, 0.25) !important;
}
:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}
</style>
