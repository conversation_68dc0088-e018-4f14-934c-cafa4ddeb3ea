export const COLUMNS = [
  {
    title: "店铺名称",
    dataIndex: "shopName",
    key: "shopName",
    width: 200,
    fixed: "left",
    align: "left",
    ellipsis: true,
  },
  {
    title: "商品信息",
    dataIndex: "productName",
    key: "productName",
    width: 400,
    ellipsis: true,
    align: "left",
  },
  {
    title: "市场价",
    dataIndex: "oriPrice",
    key: "oriPrice",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "团购价",
    dataIndex: "sellingPrice",
    key: "sellingPrice",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "售卖数量",
    dataIndex: "quantitySold",
    key: "quantitySold",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "售卖时间",
    dataIndex: "quantityTime",
    key: "quantityTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "使用时间",
    dataIndex: "useTime",
    key: "useTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "状态",
    dataIndex: "productStatus",
    key: "productStatus",
    width: 150,
    ellipsis: true,
    align: "left",
  },
  {
    title: "绑定分类",
    dataIndex: "isCategoryValid",
    key: "isCategoryValid",
    width: 150,
    ellipsis: true,
    align: "left",
  },
  {
    title: "注水量",
    dataIndex: "waterSoldNum",
    key: "waterSoldNum",
    width: 150,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    width: 200,
    fixed: "right",
    ellipsis: true,
    align: "left",
  },
];

export const FROM_DATA = {
  shopName: undefined,
  prodName: undefined,
  prodId: undefined,
  categoryId: undefined,
  status: undefined,
  isCategoryValid: undefined,
  buyTime: undefined,
  useTime: undefined,
};
