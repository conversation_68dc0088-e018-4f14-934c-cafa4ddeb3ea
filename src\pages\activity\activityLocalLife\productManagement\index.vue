<template>
  <div v-if="!isDetail">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-columns mt10">
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 商品信息 -->
          <template v-if="column.key == 'productName'">
            <a-space>
              <div v-if="record?.masterPicture.includes('https')">
                <a-image
                  :src="record?.masterPicture"
                  width="80px"
                  height="80px"
                />
              </div>
              <div v-else>
                <a-image
                  :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${record?.masterPicture}`"
                  width="50px"
                  height="50px"
                />
              </div>
              <a-space direction="vertical">
                <a-tooltip :title="record?.productName">
                  <div
                    style="
                      width: 270px;
                      font-weight: 500;
                      cursor: pointer;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                    ellipsis
                  >
                    {{ record?.productName }}
                  </div>
                </a-tooltip>
                <div style="text-align: left; color: #999">
                  {{ record.productId }}
                </div>
                <div
                  class="tui"
                  v-if="record?.activityFlag === 'GROUPON_SHARE'"
                >
                  推
                </div>
              </a-space>
            </a-space>
          </template>
          <!-- 市场价 -->
          <template v-if="column.key == 'oriPrice'">
            {{ record.oriPrice ? "￥" + record.oriPrice : "" }}
          </template>
          <!-- 团购价 -->
          <template v-if="column.key == 'sellingPrice'">
            {{ record.sellingPrice ? "￥" + record.sellingPrice : "" }}
          </template>
          <!-- 售卖数量 -->
          <template v-if="column.key == 'quantitySold'">
            {{ record.quantitySold === -1 ? "不限库存" : record.quantitySold }}
          </template>
          <!-- 售卖时间 -->
          <template v-if="column.key == 'quantityTime'">
            <div>{{ record.buyStartTime }}</div>
            <div>{{ record.buyEndTime }}</div>
          </template>
          <!-- 使用时间 -->
          <template v-if="column.key == 'useTime'">
            <div>{{ record.useStartDate }}</div>
            <div>{{ record.useEndDate }}</div>
            <div
              v-if="record.afterUseDay"
              style="color: red; padding-top: 10px"
            >
              购买后{{ record?.afterUseDay }}天可使用
            </div>
          </template>
          <!-- 状态 -->
          <template v-if="column.key == 'productStatus'">
            {{
              record.productStatus === 30
                ? "已上架"
                : record.productStatus === 40
                ? "未上架"
                : record.productStatus === 50
                ? "已下架"
                : record.productStatus === 70
                ? "封禁中"
                : ""
            }}
          </template>
          <!-- 绑定分类 -->
          <template v-if="column.key == 'isCategoryValid'">
            {{ record.isCategoryValid ? "是" : "否" }}
          </template>
          <!-- 注水量 -->
          <template v-if="column.key == 'waterSoldNum'">
            {{ record.waterSoldNum
            }}<EditOutlined
              style="cursor: pointer; margin-left: 15px"
              @click="() => handleSold(record)"
            />
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css"
              @click="handleOpenGroupDetailModal(record.productId)"
            >
              查看
            </a-button>
            <a-button
              v-if="record.productStatus !== 70"
              @click="handleViol(record)"
              type="link"
              class="btn-css"
            >
              违规下架
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
    <sold-modal
      :open="isOpen"
      :data="isData"
      @is-open="handleModalOpen"
    ></sold-modal>
    <!-- 团购详情弹窗 -->
    <GroupDetailModal
      :isOpenGroupDetailModal="isOpenGroupDetailModal"
      :groupDetailModalData="groupDetailModalData"
      @refreshGroupDetailData="handleOpenGroupDetailModal"
    />
    <viol-modal
      :open="isViolOpen"
      :data="isViolData"
      @is-open="handleViolOpen"
    ></viol-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { EditOutlined } from "@ant-design/icons-vue";
import {
  getProductPage,
  getCategorySub,
} from "@/api/activityCenter/groupPoints";
import soldModal from "./soldModal/index.vue";
import violModal from "./violationModal/index.vue";
import GroupDetailModal from "@/components/GroupDetailModal/index.vue";
import { QueryGroupPurchaseDetail } from "@/api/goodsCenter/localLife";
import SearchAntd from "@/components/SearchAntd/index.vue";
import dayjs from "dayjs";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const categoryIdOptions = ref([]);
let formData = {};
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const formList = [
  {
    label: "店铺名称",
    name: "shopName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品ID",
    name: "prodId",
    type: "input", // 输入框
    span: 6,
  },
  {
    type: "cascader",
    label: "商品分类",
    name: "categoryId",
    span: 6,
    isLoad: true,
    labelKey: "categoryName",
    valueKey: "shopCategoryId",
    childrenKey: "subCategoryList",
    changeOnSelect: true,
    multiple: false,
    searchFn: async () => {
      let res = await getCategorySubList();
      return res.data;
    },
  },
  {
    label: "状态",
    name: "status",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: [
      {
        label: "全部",
        value: "",
      },
      {
        label: "已上架",
        value: "30",
      },
      {
        label: "未上架",
        value: "40",
      },
      {
        label: "已下架",
        value: "50",
      },
      {
        label: "封禁中",
        value: "70",
      },
    ],
    span: 6,
  },
  {
    label: "绑定分类",
    name: "isCategoryValid",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: [
      {
        label: "全部",
        value: "",
      },
      {
        label: "是",
        value: true,
      },
      {
        label: "否",
        value: false,
      },
    ],
    span: 6,
  },
  {
    type: "rangePicker",
    label: "售卖时间",
    name: "buyTime",
    // showTime: true,
    span: 6,
  },
  {
    type: "rangePicker",
    label: "使用时间",
    name: "useTime",
    // showTime: true,
    span: 6,
  },
];
const tableData = ref([]);
// const formData = reactive({
//   shopName: undefined,
//   prodName: undefined,
//   prodId: undefined,
//   categoryId: undefined,
//   status: undefined,
//   isCategoryValid: undefined,
//   buyTime: undefined,
//   useTime: undefined,
// });
const isDetail = ref(false);

const isOpenGroupDetailModal = ref(false);
const groupDetailModalData = ref(undefined);
onMounted(async () => {
  getPageList();
  // getCategorySubList();
});
const formTable = ref({
  shopName: undefined,
  prodName: undefined,
  prodId: undefined,
  categoryId: undefined,
  status: undefined,
  isCategoryValid: undefined,
  buyTime: undefined,
  useTime: undefined,
});

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};
const handleSearch = (param) => {
  const { shareTime, orderTime, payTime, verificationTime } = param;
  formData = param;
  formData["shareStartTime"] = Array.isArray(shareTime)
    ? shareTime[0] + " 00:00:00"
    : null;
  formData["shareEndTime"] = Array.isArray(shareTime)
    ? shareTime[1] + " 23:59:59"
    : null;
  formData["orderStartTime"] = Array.isArray(orderTime)
    ? orderTime[0] + " 00:00:00"
    : null;
  formData["orderEndTime"] = Array.isArray(orderTime)
    ? orderTime[1] + " 23:59:59"
    : null;
  formData["payStartTime"] = Array.isArray(payTime)
    ? payTime[0] + " 00:00:00"
    : null;
  formData["payEndTime"] = Array.isArray(payTime)
    ? payTime[1] + " 23:59:59"
    : null;
  formData["finishStartTime"] = Array.isArray(verificationTime)
    ? verificationTime[0] + " 00:00:00"
    : null;
  formData["finishEndTime"] = Array.isArray(verificationTime)
    ? verificationTime[1] + " 23:59:59"
    : null;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      ...formTable.value,
      activityFlagList: ["GROUPON_SHARE"],
      buyStartTime: formTable.value.buyTime ? formTable.value.buyTime[0] : null,
      buyEndTime: formTable.value.buyTime ? formTable.value.buyTime[1] : null,
      useStartTime: formTable.value.useTime ? formTable.value.useTime[0] : null,
      useEndTime: formTable.value.useTime ? formTable.value.useTime[1] : null,
      firstCategoryId: formTable.value.categoryId
        ? formTable.value.categoryId[0]
        : null,
      secondCategoryId: formTable.value.categoryId
        ? formTable.value.categoryId[1]
        : null,
      thirdCategoryId: formTable.value.categoryId
        ? formTable.value.categoryId[2]
        : null,
    };
    delete params.buyTime;
    delete params.useTime;
    delete params.categoryId;
    const res = await getProductPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
//商品分类
const getCategorySubList = async () => {
  const params = {
    isAllowEmptySub: false,
  };
  try {
    const res = await getCategorySub(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    return res;
  } catch (error) {
    message.error(error.message);
  }
};

//注水销量
const isOpen = ref(false);
const isData = ref();
const handleSold = (data) => {
  isData.value = data;
  isOpen.value = !isOpen.value;
};
const handleModalOpen = (e) => {
  isOpen.value = e;
  getPageList();
};
// 打开查看详情弹窗
const handleOpenGroupDetailModal = async (productId) => {
  try {
    const res = await QueryGroupPurchaseDetail(productId);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    groupDetailModalData.value = res.data;
    isOpenGroupDetailModal.value = true;
    return;
  } catch (error) {
    return message.error(error.message);
  }
};

//违规下架
const isViolOpen = ref(false);
const isViolData = ref();
const handleViol = (data) => {
  isViolData.value = data;
  isViolOpen.value = !isViolOpen.value;
};
const handleViolOpen = (e) => {
  isViolOpen.value = e;
  getPageList();
};
</script>
<style lang="less">
.tui {
  text-align: left;
  color: red;
  width: 20px;
  padding: 0px 4px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: rgb(255, 255, 255);
  background: rgb(222, 65, 65);
  font-size: 12px;
  border-radius: 3px;
  margin-right: 3px;
}
.ant-btn-primary:disabled {
  color: rgba(0, 0, 0, 0.25) !important;
}

@import url("@/style/plat.less");
</style>
