<template>
  <a-modal
    v-model:open="isOpen"
    width="500px"
    title="注水销量设置"
    :destroy-on-close="true"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item>
            <a-space>
              <a-image
                width="80px"
                height="80px"
                :src="isData?.masterPicture"
              ></a-image>
              <a-space direction="vertical">
                <div style="text-align: left">{{ isData.productName }}</div>
                <div style="text-align: left">{{ isData.productId }}</div>
              </a-space></a-space
            >
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="注水销量" name="waterSoldNum">
            <a-input-number
              min="0"
              max="99999999"
              maxlength="8"
              :precision="0"
              placeholder="注水销量"
              v-model:value="formData.waterSoldNum"
              allow-clear
              style="width: 100%"
            ></a-input-number>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button key="back" @click="handleCancel">取消</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="handleOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { onMounted, ref, defineProps, watch, defineEmits, reactive } from "vue";
import { message } from "woody-ui";
import { getWaterSoldNum } from "@/api/activityCenter/groupPoints";

const loading = ref<boolean>(false);
const formData = reactive<any>({
  transGroupId: 0,
  deliveryId: 0,
  supplierId: "",
});
const formRef = ref(null);
const isOpen = ref(false);
const isData = ref();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: {},
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  isData.value = newProps.data;
  Object.assign(formData, newProps.data);
});
onMounted(() => {});

//修改注水销量
const getPageList = async () => {
  const params = {
    id: formData.productId,
    num: formData.waterSoldNum,
  };
  try {
    const res = await getWaterSoldNum(params as any);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("注水销量修改成功！");
    isOpen.value = false;
    emit("isOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const emit = defineEmits(["isOpen"]);
const handleOk = () => {
  getPageList();
};
const handleCancel = () => {
  isOpen.value = false;
  emit("isOpen", false);
};
</script>
<style lang="less" scoped></style>
