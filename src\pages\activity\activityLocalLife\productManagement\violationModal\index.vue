<template>
  <a-modal
    v-model:open="isOpen"
    width="500px"
    title="违规下架"
    :destroy-on-close="true"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <p style="margin: 20px 0">确定违规下架商品？下架后将停止售卖</p>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item>
            <a-space>
              <a-image
                width="80px"
                height="80px"
                :src="isData?.masterPicture"
              ></a-image>
              <a-space direction="vertical">
                <div style="text-align: left">{{ isData.productName }}</div>
                <div style="text-align: left">{{ isData.productId }}</div>
              </a-space></a-space
            >
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24" style="position: relative">
          <a-form-item label="下架原因:" name="reason">
            <a-textarea
              v-model:value="formData.reason"
              type="input"
              autocomplete="off"
              show-count
              rows="6"
              :maxlength="200"
            />
          </a-form-item>
          <span style="position: absolute; top: 0; left: 90px; color: red"
            >下架原因必填</span
          >
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button key="back" @click="handleCancel">取消</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="handleOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { onMounted, ref, defineProps, watch, defineEmits, reactive } from "vue";
import { message } from "woody-ui";
import { getViolationOff } from "@/api/activityCenter/groupPoints";

const loading = ref<boolean>(false);
const formData = reactive<any>({
  transGroupId: 0,
  deliveryId: 0,
  supplierId: "",
});
const formRef = ref(null);
const isOpen = ref(false);
const isData = ref();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: {},
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  isData.value = newProps.data;
  Object.assign(formData, newProps.data);
});
onMounted(() => {});

//修改注水销量
const getPageList = async () => {
  const params = {
    productId: formData.productId,
    productStatus: formData.productStatus,
    reason: formData.reason,
  };
  try {
    const res = await getViolationOff(params as any);
    if (res.code !== 0) {
      formRef.value.resetFields();
      return message.error(res.message);
    }
    formData.reason = "";
    message.success("违规下架成功!");
    isOpen.value = false;
    emit("isOpen", false);
    formRef.value.resetFields();
  } catch (error) {
    message.error(error.message);
  }
};

const emit = defineEmits(["isOpen"]);
const handleOk = () => {
  if (formData.reason && formData.reason.length > 0) {
    getPageList();
  } else {
    message.error("下架原因不能为空！");
  }
};
const handleCancel = () => {
  isOpen.value = false;
  emit("isOpen", false);
};
</script>
<style lang="less" scoped></style>
