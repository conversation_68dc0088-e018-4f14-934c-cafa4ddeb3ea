<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      @change="pageChange"
      :loading="loading"
    >
    </a-table>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { getStatisticsList } from "@/api/cms/floatLayer/index";
import { useRoute } from "vue-router";
const route = useRoute();
let formData = {};
const formList = [
  {
    label: "统计时间",
    name: "time",
    maxlength: 30,
    type: "rangePicker", // 输入框
    span: 6,
    showTime:true,
  }
];

const handleSearch = (param) => {
  if (param.time && param.time.length) {
    param.startTime = param.time[0];
    param.endTime = param.time[1];
  }
  formData = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};


//table表头数据
const columns = [
  {
    title: "统计时间",
    dataIndex: "createTime",
    key: "createTime",
    fixed: true,
    align: "left",
    width: 150,
  },
  {
    title: "累计参与次数",
    dataIndex: "totalParticipationCount",
    key: "totalParticipationCount",
    align: "left",
    width: 150,
  },
  {
    title: "累计参与人数",
    dataIndex: "totalParticipantCount",
    key: "totalParticipantCount",
    align: "left",
    width: 150,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.value.current = current;
  pagination.value.pageSize = pageSize;
  getList();
};
const getList = async () => {
  loading.value = true;
  const params = {
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
    campaignId: route.query.campaignId,
    ...formData,
  };
  const res = await getStatisticsList(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

onMounted(() => {
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
}
.add-form-css {
  padding: 30px 0;
}
</style>
