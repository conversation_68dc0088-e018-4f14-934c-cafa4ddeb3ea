<template>
    <search-antd :form-list="formList" @on-search="handleSearch"/>
    <div class="table-css">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        @change="pageChange"
        :scroll="{ x: 1500 }"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'actions'">
            <a-button v-if="record.prizeType === 2" type="link" class="btn-css" @click="actionsClick(record)"
              >{{statusFunc(record)}}</a-button
            >
            <span v-else>-</span>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:open="addVisible"
      placement="center"
      :title="'运单号'"
      :destroy-on-close="true"
      width="35%"
      @cancel="closeClick"
    >
      <a-form
        ref="formRef"
        name="custom-validation"
        :model="formState"
        v-bind="layout"
        class="add-form-css"
      >
        <a-form-item
          has-feedback
          label="运单号"
          name="trackingNumber"
          :rules="[{ required: true, message: '请输入运单号' }]"
        >
          <a-input
            v-model:value="formState.trackingNumber"
            autocomplete="off"
            placeholder="请输入运单号"
            style="width: 100%"
            :maxlength="30"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="addVisible = false">取消</a-button>
        <a-button type="primary" @click="submitClick">确定</a-button>
      </template>
    </a-modal>
  </template>
  
  <script setup lang="tsx">
  import { ref, onMounted, watch, reactive, } from "vue";
  import SearchAntd from "@/components/SearchAntd/index.vue";
  
  import { getUserPrizeList,queryPrizeTypes,editTrackingNumber } from "@/api/cms/floatLayer/index";
  import { useRouter } from "vue-router";
  import { message } from "woody-ui";
  const addVisible = ref(false);
  import type { FormInstance } from "woody-ui";
  const formRef = ref<FormInstance>();
  const router = useRouter();
  const prizeTypesList = ref([]);
  const formState = reactive({
    trackingNumber: "",
    lotteryId: "",
  });
  let formData = {};
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 14 },
  };
  const formList = [
    {
      label: "抽奖ID",
      name: "lotteryId",
      maxlength: 30,
      type: "input", // 输入框
      span: 6,
    },
    {
      label: "抽奖人手机号",
      name: "passengerPhone",
      maxlength: 11,
      type: "input", // 输入框
      span: 6,
    },
    {
      label: "活动ID",
      name: "campaignId",
      maxlength: 30,
      type: "input", // 输入框
      span: 6,
    },
    
    
    {
      label: "发货状态",
      name: "sendPrizeStatus",
      type: "select", // 输入框
      span: 6,
      options:[
        {
            label:'未发放',
            value:0
        },
        {
            label:'已发放',
            value:1
        },
        {
            label:'待填写收货地址',
            value:2
        },
        {
            label:'超时未填写',
            value:3
        },
      ]
    },
    {
      label: "奖品类型",
      name: "prizeType",
      type: "select", // 输入框
      span: 6,
      options:prizeTypesList
    },
    {
      label: "收货人手机号",
      name: "recipientPhone",
      type: "input", // 输入框
      maxlength: 11,
      span: 6,
    },
    {
      label: "快递单号",
      name: "trackingNumber",
      type: "input", // 输入框
      maxlength: 30,
      span: 6
    },
  ];

  const getPrizeTypes = async () => {
    const res = await queryPrizeTypes({})
    
    prizeTypesList.value = res.data.map(item => ({
      label: item.description,
      value: item.code
    }))
  }

  const statusFunc = (record) =>{
    if(record.sendPrizeStatus === 1){
      return '修改运单号'
    }else if(record.sendPrizeStatus === 0 && !record.shippingAddress){
      return '-'
    }else if(record.sendPrizeStatus === 0 && !record.shippingAddress){
      return '-'
    }else if(record.sendPrizeStatus === 0 && record.shippingAddress && !record.trackingNumber){
      return '填写运单号'
    }else if(record.sendPrizeStatus === 1 && record.trackingNumber){
      return '修改运单号'
    }else if(record.sendPrizeStatus === 2 && record.prizeType === 2 && !record.shippingAddress){
      return '-'
    }else if(record.sendPrizeStatus === 2 && record.prizeType === 2 && record.shippingAddress){
      return '修改运单号'
    }else if(record.sendPrizeStatus === 3){
      return '-'
    }else if(record.sendPrizeStatus === 2 && record.prizeType === 2 && record.shippingAddress){
      return '修改运单号'
    }
  };
  
  const handleSearch = (param) => {
    if (param.time && param.time.length) {
      param.startTime = param.time[0] + " 00:00:00";
      param.endTime = param.time[1] + " 23:59:59";
    }
    formData = param;
    pagination.value.current = 1;
    pagination.value.pageSize = 10;
  
    getList();
  };
  
  const closeClick = () => {
    formState.trackingNumber = "";
    formState.lotteryId = "";
    addVisible.value = false;
  };
  
  const submitClick = () => {
    formRef.value.validate().then(async() => {
      const res = await editTrackingNumber({ ...formState });
      if (res.code === 0) {
        message.success("已填写");
        addVisible.value = false;
        getList();
        formState.trackingNumber = "";
        formState.lotteryId = "";
      }
    })
    .catch(error => {
      console.log('error', error);
    });
    
  };
  
  //table表头数据
  const columns = [
    {
      title: "抽奖ID",
      dataIndex: "lotteryId",
      key: "lotteryId",
      fixed: 'left',
      align: "left",
      width: 150,
    },
    {
      title: "抽奖时间",
      dataIndex: "createTime",
      key: "createTime",
      align: "left",
      width: 200,
    },
    {
      title: "活动ID",
      dataIndex: "campaignId",
      key: "campaignId",
      align: "left",
      width: 150,
    },
    {
      title: "抽奖人手机号",
      dataIndex: "passengerPhone",
      key: "passengerPhone",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.passengerPhone) return '-'
      },
    },
    {
      title: "发货状态",
      dataIndex: "sendPrizeStatusDescription",
      key: "sendPrizeStatusDescription",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(record.prizeType === 3) {
          return '-'
        }else{
          return record.sendPrizeStatusDescription || '-'
        }
      },
    },
    {
      title: "奖品名称",
      dataIndex: "prizeName",
      key: "prizeName",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.prizeName) return '-'
      },
    },
    {
      title: "奖品类型",
      dataIndex: "prizeTypeDescription",
      key: "prizeTypeDescription",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.prizeTypeDescription) return '-'
      },
    },
    {
      title: "收货人姓名",
      dataIndex: "recipientName",
      key: "recipientName",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.recipientName) return '-'
      },
    },
    {
      title: "收货地址",
      dataIndex: "shippingAddress",
      key: "shippingAddress",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.shippingAddress) return '-'
      },
    },
    {
      title: "收货人手机号",
      dataIndex: "recipientPhone",
      key: "recipientPhone",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.recipientPhone) return '-'
      },
    },
    {
      title: "运单号",
      dataIndex: "trackingNumber",
      key: "trackingNumber",
      align: "left",
      width: 150,
      customRender: ({ record }) => {
        if(!record.trackingNumber) return '-'
      },
    },
    {
      title: "操作",
      key: "actions",
      fixed: "right",
      width: 150,
    },
  ];
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `共${total}条数据`,
  });
  
  const dataSource = ref([]);
  const loading = ref(false);
  const getList = async () => {
    loading.value = true;
    const params = {
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...formData,
    };
    const res = await getUserPrizeList(params);
    loading.value = false;
    if (res.code === 0) {
      dataSource.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  };
  
  // 操作回调
  const actionsClick = async ( data) => {
    console.log(data, "data")
    if(data.sendPrizeStatus === 2 && data.prizeType === 2){
      return
    }
    if(data.sendPrizeStatus === 3){
      return
    }
    formState.lotteryId = data.lotteryId;
    formState.trackingNumber = data.trackingNumber;
    addVisible.value = true;
  };
  
  // 分页变化
  const pageChange = (newPagination) => {
    console.log(newPagination, "newPagination");
    pagination.value = { ...pagination.value, ...newPagination };
    getList();
  };
  
  onMounted(() => {
    getList();
    getPrizeTypes();
  });
  </script>
  <style lang="less" scoped>
  .table-css {
    padding: 20px;
    background: #ffffff;
    margin-top: 16px;
    border-radius: 16px;
    margin-bottom: 16px;
    .btn-box {
      float: right;
      height: 40px;
      margin-bottom: 10px;
    }
  }
  .add-form-css {
    padding: 30px 0;
  }
  </style>
  