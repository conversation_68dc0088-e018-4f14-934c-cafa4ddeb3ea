<template>
  <div class="config-form">
    <div class="search-form" style="height: 100%">
      <div class="flexl">
        <div class="line"></div>
        <div class="font">活动基本信息</div>
      </div>
      <div class="alarm-item">
        <a-form ref="formRef" :model="alarmData" layout="vertical">
          <a-form-item
            label="活动名称"
            name="campaignName"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-input
              v-model:value="alarmData.campaignName"
              show-count
              :maxlength="8"
              placeholder="请输入"
              class="input-width"
            ></a-input>
          </a-form-item>
          <a-form-item
            label="活动参与时间"
            name="participationTime"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-range-picker
              v-model:value="alarmData.participationTime"
              allow-clear
              show-time
              class="input-width"
              @change="participationChange"
            />
            <p class="text-css">活动参与时间内可进行分享、助力、抽奖</p>
          </a-form-item>
          <a-form-item
            label="页面失效时间"
            name="campaignEndTime"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-date-picker
              v-model:value="alarmData.campaignEndTime"
              allow-clear
              show-time
              class="input-width"
            />
            <p class="text-css">页面失效后，活动页将无法访问</p>
          </a-form-item>

          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">参与规则</div>
          </div>
          <a-form-item
            label="助力（?）次后"
            name="helpCount"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-input-number
              :disabled="isEdit"
              max="99"
              min="1"
              :rules="[{ required: true, message: '必填项' }]"
              v-model:value="alarmData.helpCount"
              class="input-width"
            />
          </a-form-item>
          <a-form-item
            label="获得（?）次抽奖机会"
            name="drawCount"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-input-number
              :disabled="isEdit"
              max="99"
              min="1"
              :rules="[{ required: true, message: '必填项' }]"
              v-model:value="alarmData.drawCount"
              class="input-width"
            />
          </a-form-item>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">抽奖、助力次数限制</div>
          </div>
          <a-form-item
            label=""
            name="selectPrizeType"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.selectPrizeType" :disabled="isEdit">
              <a-radio :value="1">按中奖次数</a-radio>
              <a-radio :value="2">按抽奖次数</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            :label="`活动期间每个用户最多${alarmData.selectPrizeType === 1 ? '中奖次数' : '抽奖次数'}`"
            name="maxPrizeCount"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-input-number
              :disabled="isEdit"
              max="9999"
              min="1"
              :rules="[{ required: true, message: '必填项' }]"
              v-model:value="alarmData.maxPrizeCount"
              class="input-width"
            />
          </a-form-item>
          <a-form-item
            label="活动期间每个用户最多助力次数"
            name="maxHelpCount"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-input-number
              :disabled="isEdit"
              max="9999"
              min="1"
              :rules="[{ required: true, message: '必填项' }]"
              v-model:value="alarmData.maxHelpCount"
              class="input-width"
            />
          </a-form-item>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">配置奖品概率</div>
          </div>
          <add-award
            :prizeTypesList="prizeTypesList"
            @get-form-data="getFormData"
            :prizeData="prizeData"
            :isEdit="isEdit"
          ></add-award>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">指定兜底奖品</div>
          </div>
          <a-form-item
            label="奖品类型"
            name="douType"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-select
              :disabled="isEdit"
              v-model:value="alarmData.douType"
              :options="prizeTypeOpt"
              placeholder="请选择"
              @change="prizeTypeChange"
            >
            </a-select>
          </a-form-item>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">发货日期</div>
          </div>
          <a-form-item
            label="收货地址锁定日期"
            name="deliveryEndTime"
          >
            <a-date-picker
              v-model:value="alarmData.deliveryEndTime"
              show-time
              allow-clear
              class="input-width"
            />
            <p class="text-css">晚于该日期时，用户将无法填写或修改收货地址</p>
          </a-form-item>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">规则说明</div>
          </div>
          <a-form-item
            label="规则说明"
            name="regulation"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-textarea
              v-model:value="alarmData.regulation"
              show-count
              :maxlength="800"
              :auto-size="{ minRows: 6, maxRows: 20 }"
            ></a-textarea>
            <!-- <Editor
              v-model="alarmData.regulation"
              api-key="7bpaun0t38351yqr12fyhyosh2wb3umrb94s8g16z0eu2eol"
              :init="editorInit"
            /> -->
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
  <div class="btn-box flex">
    <a-button class="mr10" @click="back">返回</a-button>
    <div class="flex">
      <a-button type="primary" @click="handleSubmit">保存</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { message } from "woody-ui";
import { onMounted, ref } from "vue";
import {
  queryPrizeTypes,
  addCreate,
  addEdit,
  getCampaignDetail,
} from "@/api/cms/floatLayer/index";
import { useRouter, useRoute } from "vue-router";
import addAward from "./components/addAward.vue";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/zh-cn";
import Editor from '@tinymce/tinymce-vue'
const route = useRoute();
const isEdit = ref(false);
const alarmData = ref({
  helpCount: "",
  campaignName: "",
  campaignEndTime: null,
  playStartTime: "",
  playEndTime: "",
  selectPrizeType: 1,
  drawCount: "",
  maxPrizeCount: "",
  maxHelpCount: "",
  regulation: "",
  deliveryEndTime: null,
  prizeReqList: [],
  participationTime: [],
  prizeRespList:[],
  accessTime: '',
  sendTime: null,
  douType: '',
});
const diffInDays = ref(0);
const prizeTypesList = ref([]);
const prizeData = ref([]);
const router = useRouter();
const prizeTypeOpt = ref([]);

const editorInit = {
  height: 400,
  menubar: false,
  plugins: 'lists link image table code',
  toolbar: 'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code',
  branding: false,
  tinymceScriptSrc: 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js', // 使用 CDN 加载核心文件
}
const getData = async () => {
  const params = {
    campaignId: route.query.campaignId,
  };
  const res = await getCampaignDetail(params);
  if (res.code === 0) {
    alarmData.value = res.data;
    prizeData.value = res.data.prizeRespList;
    for(let j of prizeData.value){
      j['fileList'] = [
        {
          name:'',
          url:j.prizeUrl
        }
      ]
    }
    const {
      campaignEndTime,
      playStartTime,
      playEndTime,
      deliveryEndTime,
    } = res.data;
    alarmData.value.participationTime = [
      dayjs(playStartTime),
      dayjs(playEndTime),
    ];
    alarmData.value.campaignEndTime = dayjs(campaignEndTime);
    alarmData.value.deliveryEndTime = deliveryEndTime ? dayjs(deliveryEndTime) : null;
    prizeTypeOpt.value = prizeData.value.map((v, i) => {
      return {
        label: `奖品${i + 1}`,
        value: i.toString(),
      };
    });
    for(const [index, i] of prizeData.value.entries()){
      if(i.catchAllFlag == 1){
        alarmData.value.douType = index.toString();
      }
    }
    console.log(typeof alarmData.value.douType,'alarmData.value.douType')
  }
};

const prizeTypeChange = (value) => {
  console.log(alarmData.value,'alarmData.value')
  if(route.query.campaignId){
    for(let i of alarmData.value.prizeRespList){
      i.catchAllFlag = 0;
    }
    alarmData.value.prizeRespList[value].catchAllFlag = 1;
  }else{
    for(let i of alarmData.value.prizeReqList){
      i.catchAllFlag = 0;
    }
    alarmData.value.prizeReqList[value].catchAllFlag = 1;
  }
  
};

const getFormData = (data) => {
  alarmData.value.prizeReqList = data.sights;
  prizeTypeOpt.value = data.sights.map((v, i) => {
    return {
      label: `奖品${i + 1}`,
      value: i,
    };
  });
};

const back = () => {
  router.back();
};

const getPrizeTypes = async () => {
  const res = await queryPrizeTypes({});
  console.log(res, "res");
  prizeTypesList.value = res.data.map((v) => {
    return {
      label: v.description,
      value: v.code,
    };
  });
};

onMounted(() => {
  if (route.query.campaignId) {
    getData();
    if(route.query.campaignStatus === '2'){
      isEdit.value = true;
    }
  }
  getPrizeTypes();
});
const formRef = ref();

const participationChange = (date) => {
  alarmData.value.playStartTime = dayjs(date[0]).format(
    "YYYY-MM-DD HH:mm:ss"
  );
  alarmData.value.playEndTime = dayjs(date[1]).format(
    "YYYY-MM-DD HH:mm:ss"
  );
};

const handleSubmit = () => {
  const copy = JSON.parse(JSON.stringify(alarmData.value));
  console.log(alarmData.value,'alarmData.value')
  const {
    helpCount,
    drawCount,
    maxPrizeCount,
    maxHelpCount,
    regulation,
    selectPrizeType,
    campaignName,
    prizeReqList,
    prizeRespList,
    playStartTime,
    playEndTime,
  } = copy;
  const params = {
    drawCount,
    helpCount,
    maxHelpCount,
    maxPrizeCount,
    regulation,
    selectPrizeType,
    campaignName,
    prizeReqList,
    campaignEndTime:alarmData.value.campaignEndTime?.format(
      "YYYY-MM-DD HH:mm:ss"
    ),
    playStartTime,
    playEndTime,
    deliveryEndTime: alarmData.value.deliveryEndTime?.format(
      "YYYY-MM-DD HH:mm:ss"
    ),
  };
  console.log(params,'params')
  formRef.value.validate().then(async () => {
    
    if (route.query.campaignId) {
      params['campaignId'] = route.query.campaignId;
      params['prizeReqList'] = prizeRespList;
      for(let v of params.prizeReqList){
        if(v.prizeType === 2 && !alarmData.value.deliveryEndTime){
          message.error("有实物奖品，收货地址锁定日期必填");
          return
        }
      }
      const res = await addEdit(params);
      if (res.code === 0) {
        message.success("编辑成功");
        router.back();
      }
    }else{
      for(let v of params.prizeReqList){
        delete v.fileList;
        if(v.prizeType === 2 && !alarmData.value.deliveryEndTime){
          message.error("有实物奖品，收货地址锁定日期必填");
          return
        }
      }
      const res = await addCreate(params);
      if (res.code === 0) {
        message.success("保存成功");
        router.back();
      }
    }
    
  });
};
</script>

<style lang="less" scoped>
@import url("@/style/plat.less");
.flexl {
  display: flex;
  margin: 0 0 40px 0;
  .line {
    width: 4px;
    height: 17px;
    background: #1a7af8;
    border-radius: 4px 4px 4px 4px;
    margin: 2px 10px 0 0;
  }
  .font {
    font-weight: 600;
    font-size: 20px;
  }
}
.input-width {
  width: 80%;
}
.text-css {
  font-family: SF Pro, SF Pro;
  font-weight: 400;
  font-size: 14px;
  color: #636d7e;
  line-height: 22px;
  margin-top: 5px;
}
.config-form {
  position: relative;
}

.title {
  font-size: 14px;
  color: #495366;
}
.alarm-item {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  width: 550px;
  .name {
    margin-bottom: 8px;
    font-size: 14px;
    color: #495366;
  }
  .value {
    width: 450px;
    margin-right: 10px;
  }
}

.btn-box {
  width: 100%;
  height: 56px;
  border-top: 1px solid #f2f5f9;
  background: #ffffff;
  margin-top: 16px;
  padding: 10px 0;
  //   position: absolute;
  //   bottom: 0;
  //   right: 0;
  justify-content: center;
}
</style>
