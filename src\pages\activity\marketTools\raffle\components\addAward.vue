<template>
  <a-form ref="formRef" :model="awardForm" layout="vertical">
    <div v-for="(sight, index) in awardForm.sights" :key="index">
      <h2>
        奖品{{ index + 1 }}
        <a-popconfirm
          ok-text="确定"
          cancel-text="取消"
          title="确定执行此操作？"
          @confirm="removeSight(sight)"
          v-if="!isEdit"
        >
        <div style="float: right">
          <DeleteOutlined class="del-css" />
          <span class="del-css">删除</span>
        </div>
        </a-popconfirm>
        
      </h2>
      <a-form-item
        label="奖品类型"
        :name="['sights', index, 'prizeType']"
        :rules="[{ required: true, message: '必填项' }]"
      >
        <a-select
          :disabled="isEdit"
          v-model:value="sight.prizeType"
          :options="props.prizeTypesList"
          placeholder="请选择"
          @change="changeClick"
        >
        </a-select>
      </a-form-item>
      <!-- v-if="sight.prizeType != 1" -->
      <a-form-item
        
        label="奖品名称"
        :name="['sights', index, 'prizeName']"
        :rules="[{ required: true, message: '必填项',trigger: ['blur', 'change'] }]"
      >
        <a-input
          :disabled="isEdit"
          v-model:value="sight.prizeName"
          show-count
          :maxlength="5"
          placeholder="请输入"
          class="input-width"
          @blur="changeClick"
        ></a-input>
      </a-form-item>
      <a-form-item
        v-if="sight.prizeType === 1"
        label="积分数量"
        :name="['sights', index, 'sendNumber']"
        :rules="[{ required: true, message: '必填项' }]"
      >
        <a-input-number
          :disabled="isEdit"
          v-model:value="sight.sendNumber"
          :min="1"
          :max="9999"
          placeholder="请输入"
          style="width: 100%"
          @blur="changeClick"
        ></a-input-number>
      </a-form-item>
      <a-form-item
        label="每日库存"
        :name="['sights', index, 'inventoryCount']"
        :rules="[{ required: true, message: '必填项' }]"
      >
        <a-input-number
          :disabled="isEdit"
          v-model:value="sight.inventoryCount"
          :min="1"
          :max="9999"
          placeholder="请输入"
          style="width: 100%"
          @blur="changeClick"
        ></a-input-number>
      </a-form-item>
      <a-form-item
        v-if="sight.prizeType != 3"
        label="奖品图片"
        :name="['sights', index, 'prizeUrl']"
        :rules="[{ required: true, message: '必填项',trigger: 'change' }]"
      >
        <div class="upload-flex">
          <wd-upload
            biz-type="in_coming"
            :max-count="1"
            :disabled="props.isEdit"
            @get-url-list="afferentUrlChange($event,index)"
            :file-list="sight.fileList"
          />
          <p>
            支持jpg、jpeg、png、gif格式，图片尺寸400*400px，大小不超过1M，仅限上传1张
          </p>
        </div>
      </a-form-item>
      <a-form-item
        v-if="sight.prizeType === 2"
        label="奖品描述"
        :name="['sights', index, 'remark']"
      >
        <a-textarea :disabled="isEdit" v-model:value="sight.remark" @blur="changeClick" show-count :maxlength="50"></a-textarea>
      </a-form-item>
    </div>
  </a-form>
  <a-button type="dashed" block @click="addSight" v-if="!isEdit">
    <PlusOutlined />
    添加奖品{{ awardForm.sights.length }}/8
  </a-button>
</template>

<script lang="ts" setup>
import { message } from "woody-ui";
import { onMounted, ref, reactive,watch } from "vue";
import WdUpload from "@/components/WdUpload/index.vue";
import type { Dayjs } from "dayjs";
import "dayjs/locale/zh-cn";

const emit = defineEmits(["get-form-data"]);

const props = defineProps({
  prizeTypesList: {
    type: Array,
    default: [],
  },
  prizeData:{
    type: Array,
    default: [],
  },
  diffInDays:{
    type: Number,
    default: 0,
  },
  isEdit:{
    type: Boolean,
    default: false,
  }
});
const awardForm = reactive({
  sights: [
    {
      prizeType: 1,
      prizeName: "",
      inventoryCount: "",
      sendNumber: "",
      remark: "",
      prizeUrl:'',
      fileList:[],
      catchAllFlag:0
    },
    {
      prizeType: 1,
      prizeName: "",
      inventoryCount: "",
      sendNumber: '',
      remark: "",
      prizeUrl:'',
      fileList:[],
      catchAllFlag:0
    },
    {
      prizeType: 1,
      prizeName: "",
      inventoryCount: "",
      sendNumber: '',
      remark: "",
      prizeUrl:'',
      fileList:[],
      catchAllFlag:0
    },
    {
      prizeType: 1,
      prizeName: "",
      inventoryCount: "",
      sendNumber: '',
      remark: "",
      prizeUrl:'',
      fileList:[],
      catchAllFlag:0
    },
    {
      prizeType: 1,
      prizeName: "",
      inventoryCount: "",
      sendNumber: '',
      remark: "",
      prizeUrl:'',
      fileList:[],
      catchAllFlag:0
    },
    {
      prizeType: 1,
      prizeName: "",
      inventoryCount: "",
      sendNumber: '',
      remark: "",
      prizeUrl:'',
      fileList:[],
      catchAllFlag:0
    },
  ],
});

const removeSight = (item) => {
  if (awardForm.sights.length <= 6) {
    message.error("至少保留6个奖品");
    return;
  }
  const index = awardForm.sights.indexOf(item);
  if (index !== -1) {
    awardForm.sights.splice(index, 1);
  }
};

const addSight = () => {
  if (awardForm.sights.length >= 8) {
    message.error("最多添加8个奖品");
    return;
  }
  awardForm.sights.push({
    prizeType: 1,
    prizeName: "",
    inventoryCount: "",
    sendNumber: "",
    remark: "",
    prizeUrl: "",
    fileList:[],
    catchAllFlag:0,
  });
};

const changeClick = () =>{
  emit('get-form-data', awardForm)
};
const afferentUrlChange = (data,index) => {
  if (data && data.length) {
    awardForm.sights[index].prizeUrl = data[0].url;
    awardForm.sights[index].fileList = [{ name: "", url: data[0].url }];
    changeClick()
  } 
};
const formRef = ref();

onMounted(() => {
  console.log(props.prizeData,'查看')
})

watch(
  () => props.prizeData,
  (newVal: Array<{ prizeUrl: string }>, oldVal) => {
    awardForm.sights = newVal as typeof awardForm.sights;
    console.log(awardForm.sights,'awardForm.sights')
  },
  {
    deep: true,
  }
);

</script>

<style lang="less" scoped>
.del-css {
  font-family: SF Pro, SF Pro;
  font-weight: 400;
  font-size: 14px;
  color: #05082c;
  line-height: 22px;
  margin-left: 10px;
  cursor: pointer;
}
.ku-text {
  margin-top: 20px;
}
</style>
