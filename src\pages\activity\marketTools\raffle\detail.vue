<template>
    <div class="config-form">
      <div class="search-form" style="height: 100%">
        <div class="flexl">
          <div class="line"></div>
          <div class="font">基础信息</div>
        </div>
        <div class="alarm-item">
          <a-row :gutter="24">
            <a-col  :span="8">
              <div class="label-css">活动名称</div>
              <div class="mt10 value-css">{{detailData.campaignName}}</div>
            </a-col>
            <a-col  :span="8">
              <div class="label-css">活动时间</div>
              <div class="mt10 value-css">
                {{detailData.playStartTime}}
                <span style="margin:0 10px;">至</span>
                {{detailData.playEndTime}}
              </div>
            </a-col>
          </a-row>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 3px"></div>
            <div class="font">参与规则</div>
          </div>
          <a-row :gutter="24">
            <a-col  :span="8">
              <div class="label-css">助力（？）次后</div>
              <div class="mt10 value-css">{{detailData.helpCount}}</div>
            </a-col>
            <a-col  :span="8">
              <div class="label-css">获得（？）次抽奖机会</div>
              <div class="mt10 value-css">{{detailData.drawCount}}</div>
            </a-col>
          </a-row>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 3px"></div>
            <div class="font">抽奖、助力次数限制</div>
          </div>
          <a-row :gutter="24">
            <a-col  :span="8">
              <div class="label-css">限制类型</div>
              <div class="mt10 value-css">{{detailData.selectPrizeType === 1 ? '按中奖次数' : '按抽奖次数'}}</div>
            </a-col>
            <a-col  :span="8">
              <div class="label-css">活动期间每个用户最多{{detailData.selectPrizeType === 1 ? '中奖' : '抽奖'}}次数</div>
              <div class="mt10 value-css">{{detailData.maxPrizeCount}}</div>
            </a-col>
            <a-col  :span="8">
              <div class="label-css">活动期间每个用户最多助力次数</div>
              <div class="mt10 value-css">{{detailData.maxHelpCount}}</div>
            </a-col>
          </a-row>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 3px"></div>
            <div class="font">配置奖品概率</div>
          </div>
          <a-row :gutter="24" v-for="item in detailData.prizeRespList" :key="item.prizeId" class="mb20">
            <a-col  :span="24" v-if="item.prizeType != 3">
              <div class="label-css">奖品图片</div>
              <div class="mt10 value-css mb20">
                <a-image
                  :width="100"
                  :height="100"
                  :src="item.prizeUrl"
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3PTWBSGcbGzM6GCKqlIBRV0dHRJFarQ0eUT8LH4BnRU0NHR0UEFVdIlFRV7TzRksomPY8uykTk/zewQfKw/9znv4yvJynLv4uLiV2dBoDiBf4qP3/ARuCRABEFAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghgg0Aj8i0JO4OzsrPv69Wv+hi2qPHr0qNvf39+iI97soRIh4f3z58/u7du3SXX7Xt7Z2enevHmzfQe+oSN2apSAPj09TSrb+XKI/f379+08+A0cNRE2ANkupk+ACNPvkSPcAAEibACyXUyfABGm3yNHuAECRNgAZLuYPgEirKlHu7u7XdyytGwHAd8jjNyng4OD7vnz51dbPT8/7z58+NB9+/bt6jU/TI+AGWHEnrx48eJ/EsSmHzx40L18+fLyzxF3ZVMjEyDCiEDjMYZZS5wiPXnyZFbJaxMhQIQRGzHvWR7XCyOCXsOmiDAi1HmPMMQjDpbpEiDCiL358eNHurW/5SnWdIBbXiDCiA38/Pnzrce2YyZ4//59F3ePLNMl4PbpiL2J0L979+7yDtHDhw8vtzzvdGnEXdvUigSIsCLAWavHp/+qM0BcXMd/q25n1vF57TYBp0a3mUzilePj4+7k5KSLb6gt6ydAhPUzXnoPR0dHl79WGTNCfBnn1uvSCJdegQhLI1vvCk+fPu2ePXt2tZOYEV6/fn31dz+shwAR1sP1cqvLntbEN9MxA9xcYjsxS1jWR4AIa2Ibzx0tc44fYX/16lV6NDFLXH+YL32jwiACRBiEbf5KcXoTIsQSpzXx4N28Ja4BQoK7rgXiydbHjx/P25TaQAJEGAguWy0+2Q8PD6/Ki4R8EVl+bzBOnZY95fq9rj9zAkTI2SxdidBHqG9+skdw43borCXO/ZcJdraPWdv22uIEiLA4q7nvvCug8WTqzQveOH26fodo7g6uFe/a17W3+nFBAkRYENRdb1vkkz1CH9cPsVy/jrhr27PqMYvENYNlHAIesRiBYwRy0V+8iXP8+/fvX11Mr7L7ECueb/r48eMqm7FuI2BGWDEG8cm+7G3NEOfmdcTQw4h9/55lhm7DekRYKQPZF2ArbXTAyu4kDYB2YxUzwg0gi/41ztHnfQG26HbGel/crVrm7tNY+/1btkOEAZ2M05r4FB7r9GbAIdxaZYrHdOsgJ/wCEQY0J74TmOKnbxxT9n3FgGGWWsVdowHtjt9Nnvf7yQM2aZU/TIAIAxrw6dOnAWtZZcoEnBpNuTuObWMEiLAx1HY0ZQJEmHJ3HNvGCBBhY6jtaMoEiJB0Z29vL6ls58vxPcO8/zfrdo5qvKO+d3Fx8Wu8zf1dW4p/cPzLly/dtv9Ts/EbcvGAHhHyfBIhZ6NSiIBTo0LNNtScABFyNiqFCBChULMNNSdAhJyNSiECRCjUbEPNCRAhZ6NSiAARCjXbUHMCRMjZqBQiQIRCzTbUnAARcjYqhQgQoVCzDTUnQIScjUohAkQo1GxDzQkQIWejUogAEQo121BzAkTI2agUIkCEQs021JwAEXI2KoUIEKFQsw01J0CEnI1KIQJEKNRsQ80JECFno1KIABEKNdtQcwJEyNmoFCJAhELNNtScABFyNiqFCBChULMNNSdAhJyNSiECRCjUbEPNCRAhZ6NSiAARCjXbUHMCRMjZqBQiQIRCzTbUnAARcjYqhQgQoVCzDTUnQIScjUohAkQo1GxDzQkQIWejUogAEQo121BzAkTI2agUIkCEQs021JwAEXI2KoUIEKFQsw01J0CEnI1KIQJEKNRsQ80JECFno1KIABEKNdtQcwJEyNmoFCJAhELNNtScABFyNiqFCBChULMNNSdAhJyNSiECRCjUbEPNCRAhZ6NSiAARCjXbUHMCRMjZqBQiQIRCzTbUnAARcjYqhQgQoVCzDTUnQIScjUohAkQo1GxDzQkQIWejUogAEQo121BzAkTI2agUIkCEQs021JwAEXI2KoUIEKFQsw01J0CEnI1KIQJEKNRsQ80JECFno1KIABEKNdtQcwJEyNmoFCJAhELNNtScABFyNiqFCBChULMNNSdAhJyNSiEC/wGgKKC4YMA4TAAAAABJRU5ErkJggg=="
                />
              </div>
            </a-col>
            <a-col  :span="5">
              <div class="label-css">奖品类型</div>
              <div class="mt10 value-css">
                {{item.prizeTypeDescription}}
              </div>
            </a-col>
            <a-col  :span="5">
              <div class="label-css">奖品名称</div>
              <div class="mt10 value-css">
                {{item.prizeName}}
              </div>
            </a-col>
            <a-col  :span="5">
              <div class="label-css">每日库存</div>
              <div class="mt10 value-css">
                {{item.inventoryCount}}
              </div>
            </a-col>
            <a-col  :span="5">
              <div class="label-css">总库存</div>
              <div class="mt10">
                {{item.totalStock}}
              </div>
            </a-col>
            <a-col  :span="4">
              <div class="label-css">今日剩余</div>
              <div class="mt10">
                {{item.todayStockRemaining }}
              </div>
            </a-col>
            
            
            <a-col  :span="24" v-if="item.remark">
              <div class="label-css">奖品描述</div>
              <div class="mt10">
                {{item.remark }}
              </div>
            </a-col>
          </a-row>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 3px"></div>
            <div class="font">指定兜底奖品</div>
          </div>
          <a-row :gutter="24">
            <a-col  :span="24">
              <div class="label-css">兜底奖品</div>
              <div class="mt10 value-css">{{douName}}</div>
            </a-col>
          </a-row>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 3px"></div>
            <div class="font">发货日期</div>
          </div>
          <a-row :gutter="24">
            <a-col  :span="8">
              <div class="label-css">收货地址锁定日期</div>
              <div class="mt10 value-css">{{detailData.deliveryEndTime}}</div>
            </a-col>
          </a-row>
          <div class="flexl" style="margin-top: 40px">
            <div class="line" style="margin-top: 3px"></div>
            <div class="font">规则说明</div>
          </div>
          <a-row :gutter="24">
            <a-col  :span="24">
              <div class="label-css">规则说明</div>
              <div class="mt10 value-css">{{detailData.regulation}}</div>
            </a-col>
          </a-row>
        </div>
      </div>
  
      <div class="btn-box">
        <a-button class="mt10" @click="back">返回</a-button>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { onMounted, ref } from "vue";
  import { getCampaignDetail } from "@/api/cms/floatLayer/index";
  import { useRouter,useRoute } from "vue-router";
  
  const route = useRoute();
  const detailData = ref({
    campaignName:'',
    playEndTime:'',
    playStartTime:'',
    helpCount:'',
    drawCount:"",
    regulation:'',
    selectPrizeType:1,
    maxPrizeCount:0,
    maxHelpCount:0,
    deliveryEndTime:'',
    prizeRespList:[],
    campaignStatus:'',
    campaignType:'',
  });
  const router = useRouter();
  const douName = ref('');
  const getData = async() => {
    const params = {
      campaignId:route.query.campaignId
    };
    const res = await getCampaignDetail(params);
    if (res.code === 0) {
      detailData.value = res.data;
      for(let i of res.data.prizeRespList){
      if(i.catchAllFlag === 1){
        douName.value = i.prizeName
      }
    }
    }
  };

  const douFunc = (arr) => {
    
  }
  
  const back = () => {
    router.back();
  };
  
  onMounted(() => {
    getData();
  });
  </script>
  
  <style lang="less" scoped>
  @import url("@/style/plat.less");
  .flexl {
    display: flex;
    margin: 0 0 40px 0;
    .line {
      width: 4px;
      height: 17px;
      background: #1a7af8;
      border-radius: 4px 4px 4px 4px;
      margin: 2px 10px 0 0;
    }
    .font {
      font-weight: 600;
      font-size: 20px;
    }
  }
  .config-form {
    position: relative;
  }
  .title-box {
    font-weight: bold;
    font-size: 20px;
    color: #05082c;
    margin-bottom: 32px;
    .line {
      width: 4px;
      height: 16px;
      background: #1a7af8;
      border-radius: 4px 4px 4px 4px;
      margin-right: 12px;
    }
  }
  .title {
    font-size: 14px;
    color: #495366;
  }
  .alarm-item {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    .name {
      margin-bottom: 8px;
      font-size: 14px;
      color: #495366;
    }
    .value {
      width: 450px;
      margin-right: 10px;
    }
    .label-css{
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #636D7E;
    }
    .value-css{
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #05082C;
    }
  }
  .rule-css {
    margin-left: 8px;
    font-size: 14px;
    color: #495366;
    font-weight: 400;
  }
  .input-css {
    width: 260px;
    
    .span-css{
      background: #f2f5f9;
      line-height: 32px;
      padding: 0 10px;
      border-radius: 4px 0 0 4px;
    }
  }
  
  .btn-box {
    width: 100%;
    height: 66px;
    border-top: 1px solid #f2f5f9;
    background: #fff;
    // position: absolute;
    // bottom: 0;
    // right: 0;
    text-align: center;
  }
  </style>
  