<template>
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-css">
      <div class="btn-box">
        <a-button type="primary" @click="addClick" class="ml10"
          >新增</a-button
        >
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        @change="pageChange"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'actions'">
            <a-popconfirm
              ok-text="确定"
              cancel-text="取消"
              title="确定执行此操作？"
              @confirm="actionsClick('publish', record)"
            >
              <a-button type="link" v-if="record.campaignStatus === 1" class="btn-css"
                >发布</a-button
              >
            </a-popconfirm>
            <a-popconfirm
              ok-text="确定"
              cancel-text="取消"
              title="确定执行此操作？"
              @confirm="actionsClick('down', record)"
            >
            <a-button type="link" v-if="record.campaignStatus === 2 || record.campaignStatus === 3" class="btn-css"
              >下线</a-button
            >
            </a-popconfirm>
            <a-popconfirm
              ok-text="确定"
              cancel-text="取消"
              title="确定执行此操作？"
              @confirm="actionsClick('delete', record)"
            >
              <a-button type="link" v-if="record.campaignStatus === 1" class="btn-css"
                >删除</a-button
              >
            </a-popconfirm>
            <a-button type="link" class="btn-css" v-if="[1,2].includes(record.campaignStatus)" @click="actionsClick('edit', record)"
              >编辑</a-button
            >
            <a-button type="link" class="btn-css" v-if="[5,4,3].includes(record.campaignStatus)" @click="actionsClick('look', record)"
              >查看</a-button
            >
            <a-button type="link" class="btn-css" @click="actionsClick('copy', record)"
              >复制链接</a-button
            >
            <a-button type="link" v-if="[3,4,5].includes(record.campaignStatus)" class="btn-css" @click="actionsClick('dataSum', record)"
              >数据统计</a-button
            >
          </template>
        </template>
      </a-table>
    </div>
    
  </template>
  
  <script setup lang="tsx">
  import { ref, onMounted} from "vue";
  import SearchAntd from "@/components/SearchAntd/index.vue";
  import useClipboard from 'vue-clipboard3';
  import { getCampaignList,queryCampaignStatus,getEditStatus } from "@/api/cms/floatLayer/index";
  import { useRouter } from "vue-router";
  import { message } from "woody-ui";
  const router = useRouter();
  const { toClipboard } = useClipboard();
  let formData = {};
  const statusList = ref([]);
  const formList = [
    {
      label: "活动ID",
      name: "campaignId",
      maxlength: 30,
      type: "input", // 输入框
      span: 6,
    },
    {
      label: "活动名称",
      name: "campaignName",
      type: "input", // 输入框
      span: 6,
      
    },
    {
      label: "活动状态",
      name: "campaignStatus",
      type: "select", // 输入框
      span: 6,
      options:statusList,
    },
  ];
  
  const handleSearch = (param) => {
    if (param.time && param.time.length) {
      param.startTime = param.time[0] + " 00:00:00";
      param.endTime = param.time[1] + " 23:59:59";
    }
    formData = param;
    pagination.value.current = 1;
    pagination.value.pageSize = 10;
  
    getList();
  };
  const getStatusList = async () => {
    const res = await queryCampaignStatus({})
    
    statusList.value = res.data.map(item => ({
      label: item.description,
      value: item.code
    }))
    console.log(statusList.value, 'statusList')
  }
  //table表头数据
  const columns = [
    {
      title: "活动ID",
      dataIndex: "campaignId",
      key: "campaignId",
      fixed: true,
      align: "left",
      width: 150,
    },
    {
      title: "活动名称",
      dataIndex: "campaignName",
      key: "campaignName",
      align: "left",
      width: 200,
    },
    {
      title: "活动开始时间",
      dataIndex: "playStartTime",
      key: "playStartTime",
      align: "left",
      width: 150,
    },
    {
      title: "活动结束时间",
      dataIndex: "playEndTime",
      key: "playEndTime",
      align: "left",
      width: 150,
    },
    {
      title: "活动状态",
      dataIndex: "campaignStatusDescription",
      key: "campaignStatusDescription",
      align: "left",
      width: 100,
    },
    {
      title: "操作",
      key: "actions",
      fixed: "right",
      width: 260,
    },
  ];
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `共${total}条数据`,
  });
  
  const dataSource = ref([]);
  const loading = ref(false);
  const categoryIdOptions = ref([]);
  const getList = async () => {
    loading.value = true;
    const params = {
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...formData,
    };
    const res = await getCampaignList(params);
    loading.value = false;
    if (res.code === 0) {
      dataSource.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  };
  
  const addClick = () => {
    router.push('/activity/marketTools/addActivity');
  };
  const statusArr = ref({
    delete:{
      msg:'已删除',
      code:6
    },
    down:{
      msg:'已下线',
      code:4
    },
    publish:{
      msg:'已发布',
      code:2
    }
  });
  // 操作回调
  const actionsClick = async (type, data) => {
    console.log(data, "data")
    const {campaignId,campaignStatus} = data;
    if(type === 'look'){
      router.push(`/activity/marketTools/raffleDetail?campaignId=${campaignId}`);
    }else if (type === "edit") {
      router.push(`/activity/marketTools/addActivity?campaignId=${campaignId}&campaignStatus=${campaignStatus}`);
    }else if(type === 'copy'){
      const urlToCopy = `wx://page/packageMain/pages/lotteryDraw/lotteryDraw?campaignId=${campaignId}`;
      toClipboard(urlToCopy); // 复制到剪贴板
      message.success('已复制');
    }else if (type === "delete" || type === "publish" || type === "down") {
      const params = {
        campaignId,
        campaignStatus:statusArr.value[type].code
      }
      const res = await getEditStatus(params);
      if (res.code === 0) {
        message.success(`${statusArr.value[type].msg}`);
        getList();
      }
    }else if (type === "dataSum") {
      router.push(`/activity/marketTools/dataStatistics?campaignId=${campaignId}`);
    }
  };
  
  // 分页变化
  const pageChange = (newPagination) => {
    console.log(newPagination, "newPagination");
    pagination.value = { ...pagination.value, ...newPagination };
    getList();
  };
  
  onMounted(() => {
    getList();
    getStatusList()
  });
  </script>
  <style lang="less" scoped>
  .table-css {
    padding: 20px;
    background: #ffffff;
    margin-top: 16px;
    border-radius: 16px;
    margin-bottom: 16px;
    .btn-box {
      float: right;
      height: 40px;
      margin-bottom: 10px;
    }
  }
  .add-form-css {
    padding: 30px 0;
  }
  </style>
  