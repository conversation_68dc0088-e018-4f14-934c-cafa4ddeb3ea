<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button type="primary" @click="editClick" class="ml10"
        >修改时间</a-button
      >
      <a-button type="primary" @click="addClick" class="ml10"
        >添加商品</a-button
      >
    </div>
    <p class="time-css">{{ startTime }} 至 {{ endTime }}</p>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      :is-checkbox="false"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      class="mt10"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="addVisible"
    placement="center"
    title="场次时间"
    :destroy-on-close="true"
    width="50%"
    @cancel="addVisible = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="formState"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item
        label="开始时间"
        name="startTime"
        :rules="[{ required: true, message: '请选择开始时间' }]"
      >
        <a-time-picker
          placeholder="请选择时间"
          v-model:value="formState.startTime"
          style="width: 200px"
        />
      </a-form-item>
      <a-form-item
        label="结束时间"
        name="endTime"
        :rules="[{ required: true, message: '请选择结束时间' }]"
      >
        <a-time-picker
          placeholder="请选择时间"
          format="HH:mm:ss"
          v-model:value="formState.endTime"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
  <add-prod
    v-if="addShow"
    :columns="prodColumns"
    :form-list="propdFormList"
    api-type="query"
    @add-click="addSubmitClick"
    @close-click="addShow = false"
  ></add-prod>
  <a-modal
    v-model:open="addProdShow"
    placement="center"
    :title="'编辑商品'"
    :destroy-on-close="true"
    width="50%"
    @cancel="addProdShow = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="proDetail"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item label="商品主题">
        <img :src="proDetail.prodImg" class="pic-css" />
      </a-form-item>
      <a-form-item label="商品名称" name="prodName">
        <a-input v-model:value="proDetail.prodName" :disabled="true" />
      </a-form-item>
      <a-form-item label="供货价" name="costPrice">
        <a-input v-model:value="proDetail.costPrice" :disabled="true" />
      </a-form-item>
      <a-form-item label="市场价" name="marketPrice">
        <a-input :value="proDetail.marketPrice" :disabled="true" />
      </a-form-item>
      <a-form-item label="现金价格" name="price">
        <a-input
          v-model:value="proDetail.price"
          placeholder="属性值"
          :disabled="true"
        />
      </a-form-item>
      <a-form-item label="金币价格" name="goldCoin">
        <a-input v-model:value="proDetail.goldCoin" :disabled="true" />
      </a-form-item>
      <a-form-item label="库存" name="seckillInvent">
        <a-input v-model:value="proDetail.seckillInvent" />
      </a-form-item>
      <a-form-item label="云仓库存" name="totalInitInvent">
        <a-input v-model:value="proDetail.totalInitInvent" :disabled="true" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addProdShow = false">取消</a-button>
      <a-button type="primary" @click="editProdClick">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import addProd from "@/components/AddProd/index.vue";
import {
  GetProdList,
  getGoldCategoryDrop,
  GetEditInfo,
  GetProdDel,
  GetAddOrEdit,
  GetProdSave,
} from "@/api/activityCenter/goldCoin";
import { message } from "woody-ui";
import router from "@/router";
import dayjs, { Dayjs } from "dayjs";
import { useRoute } from "vue-router";

const route = useRoute();
const addVisible = ref(false);
const formData = ref({});
const addShow = ref(false);
const proDetail = ref(null);
const addProdShow = ref(false);
const classfityOpt = ref([]);
import type { FormInstance } from "woody-ui";
const formRef = ref<FormInstance>();
const formState = reactive({
  startTime: null,
  endTime: null,
});
const startTime = ref(route.query.startTime);
const endTime = ref(route.query.endTime);

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const editClick = () => {
  const endTime = route.query.endTime.toString();
  const startTime = route.query.startTime.toString();
  formState.startTime = ref<Dayjs>(dayjs(startTime, "HH:mm:ss"));
  formState.endTime = ref<Dayjs>(dayjs(endTime, "HH:mm:ss"));
  addVisible.value = true;
};
const addSubmitClick = async (data) => {
  const ids = data.map((v) => {
    return v.prodId;
  });
  const params = {
    prodIdList: ids,
    seckillId: route.query.coinSeckillId,
  };
  const res = await GetProdSave(params);
  if (res.code === 0) {
    addShow.value = false;
    getList();
  }
};

const editProdClick = async () => {
  const res = await GetEditInfo(proDetail.value);
  if (res.code === 0) {
    message.success("编辑成功");
    addProdShow.value = false;
    getList();
  }
};

const formList = [
  {
    label: "商品ID",
    name: "prodId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品信息",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "状态",
    name: "status",
    type: "select", // 输入框
    options: [
      {
        value: "TO_BE_EDIT",
        label: "待编辑",
      },
      {
        value: "NOEMAL",
        label: "正常",
      },
      {
        value: "ABNORMAL",
        label: "异常",
      },
      {
        value: "OUT",
        label: "已抢光",
      },
    ],
    span: 6,
  },
];

const propdFormList = [
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品分类",
    name: "secondCategoryId",
    type: "select", // 输入框
    options: classfityOpt,
    labelKey: "categoryName",
    valueKey: "categoryId",
    span: 6,
  },
];

const prodColumns = [
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    fixed: "left",
    width: 260,
  },
  {
    title: "分类",
    dataIndex: "secondCategoryName",
    key: "secondCategoryName",
    width: 150,
  },
  {
    title: "售价",
    dataIndex: "goldPrice",
    key: "goldPrice",
    width: 260,
  },
  {
    dataIndex: "inventory",
    key: "inventory",
    title: "库存",
    width: 150,
  },
  {
    title: "市场价",
    dataIndex: "marketPrice",
    key: "marketPrice",
    width: 150,
  },
];

const handleSearch = (param) => {
  formData.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  getList();
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const submitClick = () => {
  formRef.value
    .validate()
    .then(async () => {
      const params = {
        startTime: dayjs(formState.startTime).format("HH:mm:ss"),
        endTime: dayjs(formState.endTime).format("HH:mm:ss"),
        coinSeckillId: route.query.coinSeckillId,
      };
      const res = await GetAddOrEdit(params);
      if (res.code === 0) {
        addVisible.value = false;
        startTime.value = params.startTime;
        endTime.value = params.endTime;
        message.success("修改时间成功");
        getList();
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

//table表头数据
const columns = [
  {
    title: "商品ID",
    dataIndex: "prodId",
    key: "prodId",
    fixed: true,
    align: "left",
    width: 200,
  },
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    align: "left",
    width: 320,
  },
  {
    title: "库存数量",
    align: "left",
    dataIndex: "seckillInvent",
    key: "seckillInvent",
    width: 150,
  },
  {
    title: "云仓库存",
    dataIndex: "totalInitInvent",
    key: "totalInitInvent",
    align: "left",
    width: 150,
  },
  {
    title: "状态",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      if (record.status === "TO_BE_EDIT") {
        return "待编辑";
      } else if (record.status === "NOEMAL") {
        return "正常";
      } else if (record.status === "ABNORMAL") {
        return "异常";
      } else if (record.status === "OUT") {
        return "已抢光";
      }
    },
  },
  {
    title: "价格",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      return "¥" + record.price + "+" + record.goldCoin;
    },
  },
  {
    title: "今日详情浏览量",
    dataIndex: "detailView",
    key: "detailView",
    align: "left",
    width: 150,
  },
  {
    title: "今日已购人数",
    dataIndex: "buyCount",
    key: "buyCount",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      return record.buyCount ? record.buyCount : 0;
    },
  },
  {
    title: "今日销售额",
    dataIndex: "amountSale",
    key: "amountSale",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      return `￥${record.amountSale ? record.amountSale : 0}+
      ${record.coinSale ? record.coinSale : 0}`;
    },
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    width: 240,
    actionType: [
      {
        type: "edit",
        title: "编辑",
        isPop: false,
      },
      {
        type: "delete",
        title: "删除",
        color: "",
        isPop: true,
      },
    ],
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const zoneId = ref("");
const getList = async () => {
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    coinSeckillId: route.query.coinSeckillId,
    ...formData.value,
  };
  const res = await GetProdList(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const addClick = () => {
  zoneId.value = "";
  addShow.value = true;
  getClassfity();
};

// 操作回调
const actionsClick = (type, data) => {
  zoneId.value = data.zoneId;
  proDetail.value = data;
  if (type === "delete") {
    deleteApi(data.id);
  } else if (type === "edit") {
    addProdShow.value = true;
  }
};

const deleteApi = async (id) => {
  const res = await GetProdDel({ id });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
};

onMounted(() => {
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
.time-css {
  font-size: 20px;
  font-weight: bold;
  line-height: 50px;
}
.pic-css {
  width: 100px;
  height: 100px;
}
</style>
