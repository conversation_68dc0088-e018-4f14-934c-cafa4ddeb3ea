<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button @click="editClick" class="ml10">编辑专区属性</a-button>
      <a-button type="primary" @click="addClick" class="ml10"
        >增加场次</a-button
      >
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      :is-checkbox="false"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      class="mt10"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="addVisible"
    placement="center"
    title="场次时间"
    :destroy-on-close="true"
    width="30%"
    @cancel="addVisible = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="formState"
      v-bind="layout"
      layout="vertical"
      class="add-form-css"
    >
      <a-form-item
        label="开始时间"
        name="startTime"
        :rules="[{ required: true, message: '请选择开始时间' }]"
      >
        <a-time-picker
          placeholder="请选择时间"
          format="HH:mm:ss"
          v-model:value="formState.startTime"
          style="width: 200px"
        />
      </a-form-item>
      <a-form-item
        label="结束时间"
        name="endTime"
        :rules="[{ required: true, message: '请选择结束时间' }]"
      >
        <a-time-picker
          placeholder="请选择时间"
          format="HH:mm:ss"
          v-model:value="formState.endTime"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="editVisible"
    placement="center"
    title="编辑专区属性"
    :destroy-on-close="true"
    width="50%"
    @cancel="addVisible = false"
  >
    <a-form
      ref="formRefEdit"
      name="custom-validation"
      :model="formEditState"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item label="专区开关">
        <a-switch
          :checked="formEditState.valid === 1 ? true : false"
          @change="handleSwitchChange"
        />
      </a-form-item>
      <a-form-item
        label="专区名称"
        name="activityName"
        :rules="[{ required: true, message: '请输入专区名称' }]"
      >
        <a-input
          v-model:value="formEditState.activityName"
          type="input"
          autocomplete="off"
          placeholder="请输入专区名称"
        />
      </a-form-item>
      <a-form-item
        label="规则说明"
        name="ruleExplain"
        :rules="[{ required: true, message: '请输入规则说明' }]"
      >
        <a-textarea
          v-model:value="formEditState.ruleExplain"
          type="input"
          placeholder="请输入规则说明"
        />
      </a-form-item>
      <a-form-item
        label="Banner图"
        name="bannerImage"
        :rules="[
          { required: true, message: '请选择Banner图', trigger: 'change' },
        ]"
      >
        <wd-upload
          biz-type="cms"
          :file-list="fileList"
          @get-url-list="afferentUrlChange"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="editSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import {
  GetSeckillQuery,
  getGoldCategoryDrop,
  GetZoneDelete,
  GetSpecialOff,
  GetStatusChange,
  GetSeckillRemove,
  GetAddOrEdit,
  GetAddEdit,
  GetSeckillEditQuery,
} from "@/api/activityCenter/goldCoin";
import WdUpload from "@/components/WdUpload/index.vue";
import { message } from "woody-ui";
import router from "@/router";
import dayjs, { Dayjs } from "dayjs";
const addVisible = ref(false);
const formData = ref({});
const editVisible = ref(false);
const fileList = ref([]);
const classfityOpt = ref([]);

import type { Rule } from "woody-ui/es/form";
import type { FormInstance } from "woody-ui";
// interface FormState {
//   propName: string;
//   prodPropValues: Array;
// }
const formRef = ref<FormInstance>();
const formRefEdit = ref<FormInstance>();
const formState = reactive({
  endTime: "",
  startTime: "",
});
const formEditState = ref({
  activityName: "",
  ruleExplain: "",
  valid: 0,
  bannerImage: "",
});
// const startTime = ref<Dayjs>();
// const endTime = ref<Dayjs>();
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const rules: Record<string, Rule[]> = {
  propName: [{ required: true, trigger: "blur", message: "请输入规格中文名" }],
};
const handleSwitchChange = (checked) => {
  formEditState.value.valid = checked ? 1 : 0;
};
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const editClick = async () => {
  const res = await GetSeckillEditQuery({});
  if (res.code === 0) {
    formEditState.value = res.data;
    fileList.value = [
      {
        name: "",
        url: res.data.bannerImage,
      },
    ];
    editVisible.value = true;
  }
};
// 图片上传
const afferentUrlChange = (list) => {
  console.log(list, "list");

  formEditState.value.bannerImage = list && list[0].url;
  // formData.value.fileList = list;
  // picInfoList.value = list;
};

const formList = [
  {
    label: "场次编号",
    name: "coinSeckillNo",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "状态",
    name: "status",
    type: "select", // 输入框
    options: [
      {
        label: "未上架",
        value: 0,
      },
      {
        label: "未开始",
        value: 2,
      },
      {
        label: "进行中",
        value: 3,
      },
    ],
    span: 6,
  },
  {
    label: "商品异常场次",
    name: "isExistException",
    type: "select", // 输入框
    options: [
      {
        label: "异常",
        value: true,
      },
      {
        label: "正常",
        value: false,
      },
    ],
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  formData.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};
const editSubmit = () => {
  formRefEdit.value
    .validate()
    .then(async () => {
      console.log("values", formEditState);
      const res = await GetAddEdit(formEditState.value);
      if (res.code === 0) {
        editVisible.value = false;
        message.success("操作成功");
        getList();
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};
const submitClick = () => {
  formRef.value
    .validate()
    .then(async () => {
      console.log("values", formState);
      const params = {
        startTime: dayjs(formState.startTime).format("HH:mm:ss"),
        endTime: dayjs(formState.endTime).format("HH:mm:ss"),
      };
      const res = await GetAddOrEdit(params);
      if (res.code === 0) {
        addVisible.value = false;
        getList();
        message.success("新增成功");
        router.push({
          path: "/activity/activityOnlineShop/goldcoinCommodity",
          query: {
            coinSeckillId: res.data,
            startTime: params.startTime,
            endTime: params.endTime,
          },
        });
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

//table表头数据
const columns = [
  {
    title: "场次编号",
    dataIndex: "coinSeckillNo",
    key: "coinSeckillNo",
    fixed: true,
    align: "left",
    width: 240,
  },
  {
    title: "时间",
    dataIndex: "zoneName",
    key: "zoneName",
    align: "left",
    width: 220,
    customRender: ({ record }) => {
      return record.startTime + "-" + record.endTime;
    },
  },
  {
    title: "状态",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      if (record.status === 2) {
        return "未开始";
      } else if (record.status === 0) {
        return "未上架";
      } else if (record.status === 3) {
        return "进行中";
      }
    },
  },
  {
    title: "商品数量",
    dataIndex: "prodNum",
    key: "prodNum",
    align: "left",
    width: 150,
  },
  {
    title: "异常商品数",
    dataIndex: "exceptionProdNum",
    key: "exceptionProdNum",
    align: "left",
    width: 150,
  },
  {
    title: "当日浏览量",
    dataIndex: "browseNum",
    key: "browseNum",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      return record.browseNum + "万";
    },
  },
  {
    title: "当日GMV",
    dataIndex: "GMV",
    key: "GMV",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "seckillAct",
    fixed: "right",
    width: 240,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const zoneId = ref("");
const getList = async () => {
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    ...formData.value,
  };
  const res = await GetSeckillQuery(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const addClick = () => {
  zoneId.value = "";
  addVisible.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  zoneId.value = data.zoneId;
  if (type === "delete") {
    deleteApi(data.coinSeckillId);
  } else if (type === "edit") {
    router.push({
      path: "/activity/activityOnlineShop/goldcoinCommodity",
      query: {
        coinSeckillId: data.coinSeckillId,
        startTime: data.startTime,
        endTime: data.endTime,
      },
    });
  } else if (type === "off") {
    offApi(data);
  }
};

const offApi = async (data) => {
  const params = {
    coinSeckillId: data.coinSeckillId,
    status: data.status === 0 ? 1 : 0,
  };
  const res = await GetStatusChange(params);
  if (res.code === 0) {
    message.success("操作成功");
    getList();
  } else {
    message.error(res.message);
  }
};

const deleteApi = async (id) => {
  const res = await GetSeckillRemove({ coinSeckillId: id });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
};

onMounted(() => {
  getClassfity();
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
