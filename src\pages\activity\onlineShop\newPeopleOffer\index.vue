<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button @click="editClick" class="ml10">编辑专区属性</a-button>
      <a-button type="primary" @click="addClick" class="ml10"
        >新增商品</a-button
      >
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      @hotCakes-change="hotCakesChange"
      @blur-change="blurChange"
      class="mt10"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="addProdShow"
    placement="center"
    :title="isEdit ? '编辑商品' : '新增商品'"
    :destroy-on-close="true"
    width="40%"
    @cancel="addProdShow = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="proDetail"
      layout="vertical"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item label="商品主题">
        <img
          :src="isEdit ? proDetail.pic.path : proDetail.pic"
          class="pic-css"
        />
      </a-form-item>
      <a-form-item label="商品名称" name="prodName">
        <a-input v-model:value="proDetail.prodName" :disabled="true" />
      </a-form-item>
      <a-form-item v-if="isEdit" label="供货价" name="marketPrice">
        <a-input v-model:value="proDetail.costPrice" :disabled="true" />
      </a-form-item>
      <a-form-item label="市场价">
        <a-input
          :value="proDetail.marketPrice || proDetail.oriPrice"
          :disabled="true"
        />
      </a-form-item>
      <a-form-item label="现金价格" name="propName">
        <a-input defaultValue="0.01" placeholder="属性值" :disabled="true" />
      </a-form-item>
      <a-form-item label="金币价格" name="propName">
        <a-input defaultValue="100" :disabled="true" />
      </a-form-item>
      <a-form-item label="库存" name="stocks">
        <a-input v-model:value="proDetail.stocks" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addProdShow = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="editShow"
    placement="center"
    :title="'编辑专区属性'"
    :destroy-on-close="true"
    width="50%"
    @cancel="editShow = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="editDetail"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item label="启用专区">
        <a-switch v-model:checked="editDetail.valid" />
      </a-form-item>
      <a-form-item label="专区名称" name="activityName">
        <a-input v-model:value="editDetail.activityName" :disabled="true" />
      </a-form-item>
      <a-form-item label="活动限时" name="marketPrice">
        <div>
          <a-input
            v-model:value="editDetail.hour"
            style="width: 80px"
            class="mr10"
          />时
          <a-input
            v-model:value="editDetail.min"
            style="width: 80px"
            class="mr10"
          />分
          <a-input
            v-model:value="editDetail.sec"
            style="width: 80px"
            class="mr10"
          />秒
        </div>
      </a-form-item>
      <a-form-item label="规则说明" name="activityName">
        <a-textarea v-model:value="editDetail.ruleExplain" />
      </a-form-item>
      <!-- <a-form-item label="页面主题色" name="color"> </a-form-item> -->
    </a-form>
    <template #footer>
      <a-button @click="editShow = false">取消</a-button>
      <a-button type="primary" @click="editSubmit">确定</a-button>
    </template>
  </a-modal>
  <add-prod
    v-if="addVisible"
    @add-click="selectChange"
    :is-radio="true"
    @prod-select="prodSelect"
    @close-click="addVisible = false"
  />
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, toRaw } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import {
  GetQueryPage,
  getGoldCategoryDrop,
  GetRemove,
  GetAddPage,
  GetEdit,
  addOrEdit,
  GetActivityQuery,
  GetProdEdit,
  activitySort,
  activityHotCakes,
} from "@/api/activityCenter/goldCoin";
import { message } from "woody-ui";
import addProd from "./addProd.vue";

const addVisible = ref(false);
const formData = ref({});
const addProdShow = ref(false);
const classfityOpt = ref([]);
const proDetail = ref(null);
const itemData = ref(null);
const editDetail = ref(null);
const editShow = ref(false);
const isEdit = ref(false);
import type { FormInstance } from "woody-ui";
const formRef = ref<FormInstance>();
const selectChange = (data) => {
  console.log(data, "data123");
};
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const detailInfoApi = async () => {
  const res = await GetActivityQuery({});
  if (res.code === 0) {
    editDetail.value = res.data;
    res.data.valid === 1
      ? (editDetail.value.valid = true)
      : (editDetail.value.valid = false);
    editShow.value = true;
    // 剩余时间总的毫秒数 除以 1000 变为总秒数（时间戳为13位 需要除以1000，为10位 则不需要）
    let dec = res.data?.activityAttr?.duration;
    if (dec <= 0) {
      dec = 0;
    }
    // 得到小时 格式化成前缀加零的样式
    let h = Math.trunc(dec / 3600);
    // 得到分钟 格式化成前缀加零的样式
    let m = Math.trunc((dec % 3600) / 60);
    // 得到秒 格式化成前缀加零的样式
    let s = Math.trunc((dec % 3600) % 60);

    editDetail.value.hour = h < 10 ? "0" + h : h;
    editDetail.value.min = m < 10 ? "0" + m : m;
    editDetail.value.sec = s < 10 ? "0" + s : s;
  }
};
const editClick = () => {
  detailInfoApi();
};
const prodSelect = (data) => {
  console.log(data);
  proDetail.value = data && data[0];
  console.log(proDetail.value, "proDetail.value");
  addProdShow.value = true;
};
const formList = [
  {
    label: "商品ID",
    name: "prodId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品分类",
    name: "secondCategoryId",
    type: "select", // 输入框
    labelKey: "categoryName",
    valueKey: "categoryId",
    options: classfityOpt,
    span: 6,
  },
];

const handleSearch = (param) => {
  formData.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const hotCakesChange = async (data, row) => {
  const params = {
    activityProdId: row.activityProdId,
    sellLikeHotCakes: data,
  };
  const res = await activityHotCakes(params);
  if (res.code === 0) {
    message.success("修改成功");
    getList();
  }
};
const blurChange = async (data, row) => {
  const params = {
    activityProdId: row.activityProdId,
    sort: data,
  };
  const res = await activitySort(params);
  if (res.code === 0) {
    message.success("修改成功");
    getList();
  }
};
const editSubmit = async () => {
  let activityAttr = {
    duration:
      parseInt(editDetail.value.hour) * 60 * 60 +
      parseInt(editDetail.value.min) * 60 +
      parseInt(editDetail.value.sec),
  };
  if (activityAttr.duration === 0) {
    return message.error("活动限时不能为0");
  }

  let editParams = {
    activityId: editDetail.value.activityId, // 活动id
    activityType: 3, // 活动类型code，详情见枚举，必传
    activityName: editDetail.value.activityName, // 活动名称，必传
    activityLimitBuyNum: editDetail.value.activityLimitBuyNum, // 活动限购数量
    prodLimitBuyNum: editDetail.value.prodLimitBuyNum, // 商品限购数量
    ruleExplain: editDetail.value.ruleExplain, // 规则说明
    bannerImage: editDetail.value.pic?.url, // banner图
    activityAttr: activityAttr, //有效期/分钟
    valid:
      editDetail.value.valid === 1
        ? 1
        : editDetail.value.valid === true
        ? 1
        : 0, // 是否启用（0未启用、1已启用）
  };
  const res = await addOrEdit(editParams);
  if (res.code === 0) {
    message.success(res.message);
    editShow.value = false;
    getList();
  }
};

const submitClick = async () => {
  proDetail.value["activityType"] = 3;
  const rawData = toRaw(proDetail.value);
  const { inventory, prodId, skuId, stocks, price, goldCoin } = rawData;
  console.log(rawData, "rawData");
  const params = {
    activityType: 3,
    cashPrice: "0.01",
    goldPrice: "100",
    inventory: Number(inventory),
    prodId,
    skuId,
  };
  const params1 = {
    activityProdId: itemData.value?.activityProdId,
    cashPrice: price,
    goldPrice: goldCoin,
    inventory: stocks,
    prodId: prodId,
  };
  console.log(rawData, params, "params");
  const res = isEdit.value
    ? await GetProdEdit(params1)
    : await GetAddPage(params);
  if (res.code === 0) {
    addProdShow.value = false;
    message.success("操作成功");
    getList();
  } else {
    message.error(res.message);
  }
};

//table表头数据
const columns = [
  {
    title: "商品ID",
    dataIndex: "prodId",
    key: "prodId",
    fixed: true,
    align: "left",
    width: 200,
  },
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    align: "left",
    width: 320,
  },
  {
    title: "商品分类",
    dataIndex: "secondCategoryName",
    key: "secondCategoryName",
    align: "left",
    width: 150,
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
    key: "updateTime",
    align: "left",
    width: 150,
  },
  {
    title: "销售价（元）",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      return `${record.cashPrice} + ${record.goldPrice}`;
    },
  },
  {
    title: "市场价（元）",
    dataIndex: "marketPrice",
    key: "marketPrice",
    align: "left",
    width: 150,
  },
  {
    title: "库存",
    dataIndex: "inventory",
    key: "inventory",
    align: "left",
    width: 150,
  },
  {
    title: "热销",
    dataIndex: "sellLikeHotCakes",
    key: "sellLikeHotCakes",
    align: "left",
    width: 150,
  },
  {
    title: "排序",
    dataIndex: "sort",
    key: "sort",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    width: 240,
    actionType: [
      {
        type: "edit",
        title: "编辑",
        isPop: false,
      },
      {
        type: "delete",
        title: "删除",
        isPop: true,
      },
    ],
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const getList = async () => {
  const params = {
    activityProdFlag: true,
    activityType: 3,
    current: pagination.value.current,
    size: pagination.value.pageSize,
    ...formData.value,
  };
  const res = await GetQueryPage(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const addClick = () => {
  isEdit.value = false;
  addVisible.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  itemData.value = data;
  if (type === "delete") {
    deleteApi(data.prodId);
  } else if (type === "edit") {
    isEdit.value = true;
    detailApi(data.prodId);
  }
};

const detailApi = async (prodId) => {
  const res = await GetEdit({ prodId });
  if (res.code === 0) {
    proDetail.value = res.data.skuList && res.data.skuList[0];
    addProdShow.value = true;
  }
};

const deleteApi = async (id) => {
  const res = await GetRemove({ prodId: id, activityType: 3 });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  console.log(newPagination, "newPagination");
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
  console.log(pagination, "pagination");
};

onMounted(() => {
  getClassfity();
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
.pic-css {
  width: 100px;
  height: 100px;
}
</style>
