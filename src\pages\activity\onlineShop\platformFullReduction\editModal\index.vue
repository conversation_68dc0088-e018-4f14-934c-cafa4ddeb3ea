<template>
  <a-modal
    class="modal-form-style"
    v-model:open="isOpen"
    width="520px"
    title="编辑活动"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="活动名称"
            name="name"
            :rules="[{ required: true, message: '请输入活动名称' }]"
          >
            <a-input
              v-model:value="formModalData.name"
              allow-clear
              show-count
              :maxlength="30"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="活动周期"
            name="time"
            :rules="[{ required: true, message: '请输入活动周期' }]"
          >
            <a-range-picker
              v-model:value="formModalData.time"
              :locale="locale"
              :disabledDate="disabledDate"
              :show-time="{
                hideDisabledOptions: true,
                defaultValue: [
                  dayjs('00:00:00', 'HH:mm:ss'),
                  dayjs('23:59:59', 'HH:mm:ss'),
                ],
              }"
              allow-clear
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              :placeholder="['开始时间', '结束时间']"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <p style="margin: 0 0 10px 0"><i style="color: red">*</i>满减配置</p>
          <a-space
            v-for="(sight, index) in formModalData.configInfoList"
            :key="index"
            style="display: flex; margin-bottom: 8px; align-items: baseline"
          >
            <a-form-item
              :name="['configInfoList', index, 'max']"
              :rules="{
                required: true,
                message: '请输入满减金额',
              }"
            >
              满<a-input
                allow-clear
                v-model:value="sight.max"
                style="width: 120px; margin: 0 5px"
              />元
            </a-form-item>
            <a-form-item
              :name="['configInfoList', index, 'out']"
              :rules="{
                required: true,
                message: '请输入优惠金额',
              }"
            >
              减<a-input
                allow-clear
                v-model:value="sight.out"
                style="width: 120px; margin: 0 5px"
              />元
            </a-form-item>
            <a-button
              v-show="formModalData.configInfoList.length > 1"
              type="link"
              class="btn-css"
              danger
              @click="removeSight(sight)"
            >
              删除
            </a-button>
          </a-space>
          <a-form-item>
            <a-button
              v-show="formModalData.configInfoList.length < 5"
              type="link"
              class="btn-css"
              @click="addSight"
            >
              添加梯度
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import { getSetData } from "@/api/activityCenter/platformFullReduction";
import dayjs from "dayjs";

const isOpen = ref(false);
const formModalData = reactive({
  name: "",
  id: "",
  time: undefined,
  configInfoList: [{ max: "", out: "" }],
  beginDate: undefined,
  stopDate: undefined,
});
const loading = ref(false);
const formModalRef = ref(null);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: {},
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  Object.assign(formModalData, newProps.data);
  formModalData.time = [formModalData.beginDate, formModalData.stopDate];
});

const disabledDate = (current) => {
  return current && current < dayjs().startOf("days");
};
const removeSight = (item) => {
  const index = formModalData.configInfoList.indexOf(item);
  if (index !== -1) {
    formModalData.configInfoList.splice(index, 1);
  }
};
const addSight = () => {
  formModalData.configInfoList.push({
    out: undefined,
    max: undefined,
  });
};

const emit = defineEmits(["isModalOpen"]);

// 新增
const getAdd = async () => {
  for (let i = 0; i < formModalData.configInfoList.length; i++) {
    if (
      Number(formModalData.configInfoList[i].out) >
      Number(formModalData.configInfoList[i].max)
    ) {
      message.error(`满减配置第${i + 1}行优惠金额大于满减金额，请重新填写`);
      return;
    }
  }
  const params = {
    id: formModalData.id,
    ...formModalData,
    beginDate: formModalData.time ? formModalData.time[0] : null,
    stopDate: formModalData.time ? formModalData.time[1] : null,
  };
  delete params.time;
  try {
    const res = await getSetData(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isOpen.value = false;
    emit("isModalOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getAdd();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
