<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="6">
            <a-form-item label="活动名称" name="name">
              <a-input
                v-model:value="formData.name"
                allow-clear
                style="width: 100%"
                placeholder="请输入活动名称"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <div class="table-operate-box">
        <a-button
          style="margin: 0 15px 0 0"
          type="primary"
          @click="() => handleCreate()"
        >
          <plus-circle-outlined />
          新增活动
        </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
        :customRow="customRow"
      >
        <template #bodyCell="{ column, record }">
          <!-- 活动周期 -->
          <template v-if="column.key == 'time'">
            {{ record.beginDate }}--{{ record.stopDate }}
          </template>
          <!-- 状态 -->
          <template v-if="column.key == 'status'">
            {{
              record.status === "STOP"
                ? "已终止"
                : record.status === "NO_START"
                ? "未开始"
                : record.status === "START"
                ? "已开始"
                : record.status === "END"
                ? "已结束"
                : ""
            }}
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css" @click="handleChatDetail(record.id)"
              >查看</a-button
            >
            <a-button
              v-if="record.status === 'NO_START' || record.status === 'START'"
              @click="handleEdit(record)"
              type="link"
              class="btn-css"
              >修改</a-button
            >
            <a-button
              @click="handleStop(record.id)"
              v-if="record.status === 'NO_START' || record.status === 'START'"
              type="link"
              class="btn-css"
              >终止</a-button
            >
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:open="isOpen"
      width="520px"
      title="提示"
      :destroy-on-close="true"
      @cancel="handleCancel"
    >
      <div
        style="color: rgb(25, 144, 255); text-align: center; margin: 10px 0 0 0"
      >
        当前已有未开始或已开始的活动，请等待活动结束后再新增
      </div>
      <template #footer>
        <a-button type="primary" @click="handleCancel">知道了</a-button>
      </template>
    </a-modal>
    <a-modal
      v-model:open="isStopOpen"
      width="520px"
      title="终止"
      :destroy-on-close="true"
      @cancel="handleStopCancel"
    >
      <div style="text-align: left; margin: 10px 0 0 0">
        确定要终止该活动吗？此操作不可逆
      </div>
      <template #footer>
        <a-button style="margin-right: 8px" @click="handleStopCancel"
          >取消</a-button
        >
        <a-button type="primary" @click="handleStopOk">确定</a-button>
      </template>
    </a-modal>
    <add-modal :open="isModalOpen" @is-modal-open="handleModalOk"></add-modal>
    <edit-modal
      :open="isEditOpen"
      :data="isInfo"
      @is-modal-open="handleEditModalOk"
    ></edit-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive, watch, onBeforeUnmount } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getPage,
  getQueryAddStatus,
  getUpdateStatus,
} from "@/api/activityCenter/platformFullReduction";
import { PlusCircleOutlined } from "@ant-design/icons-vue";
import addModal from "./addModal/index.vue";
import editModal from "./editModal/index.vue";
import router from "@/router";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const customRow = () => ({
  style: {
    height: "46px",
  },
});
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({ name: "" });
const isData = ref();
const isDetail = ref(false);
const isOpen = ref(false);
onMounted(async () => {
  getPageList();
});

watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (
      newVal.path ===
        "/activity/activityOnlineShop/platformFullReduction/platformFullReductionDetail" ||
      newVal.path ===
        "/activity/activityOnlineShop/platformFullReduction/platformFullReductionDetail/platformProductManage"
    ) {
      isDetail.value = true;
    } else {
      isDetail.value = false;
    }
  },
  {
    immediate: true,
  }
);

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
    };
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isData.value = res.data;
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
//查看
const handleChatDetail = (e) => {
  router.push({
    path: "/activity/activityOnlineShop/platformFullReduction/platformFullReductionDetail",
    query: { id: e },
  });
};
//终止
const isStopOpen = ref(false);
const isId = ref();
const handleStop = (e) => {
  isId.value = e;
  isStopOpen.value = true;
};
const handleStopOk = async () => {
  try {
    const params = {
      id: isId.value,
      status: "STOP",
    };
    const res = await getUpdateStatus(params as any);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isStopOpen.value = false;
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
const handleStopCancel = () => {
  isStopOpen.value = false;
};
//新增活动
const getStatus = async () => {
  try {
    const res = await getQueryAddStatus();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    if (!res.data) {
      isOpen.value = true;
    } else {
      isModalOpen.value = true;
    }
  } catch (error) {
    message.error(error.message);
  }
};

const isModalOpen = ref(false);

const handleModalOk = (e) => {
  isModalOpen.value = e;
  getPageList();
};
const handleCancel = () => {
  isOpen.value = false;
};
const handleCreate = () => {
  getStatus();
};
//编辑
const isInfo = ref();
const isEditOpen = ref(false);
const handleEdit = (data) => {
  isInfo.value = data;
  isEditOpen.value = !isEditOpen.value;
};
const handleEditModalOk = (e) => {
  isEditOpen.value = e;
  getPageList();
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
