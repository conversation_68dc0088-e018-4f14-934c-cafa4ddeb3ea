<template>
  <a-modal
    v-model:open="isOpen"
    width="520px"
    title="编辑专区属性"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item label="Banner图" name="bannerImage">
            <wd-upload
              biz-type="in_coming"
              :max-count="1"
              @get-url-list="afferentUrlChange"
              :file-list="fileList"
          /></a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="规则说明" name="ruleExplain">
            <a-textarea
              v-model:value="formModalData.ruleExplain"
              type="input"
              autocomplete="off"
              show-count
              rows="6"
              :maxlength="400"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import {
  getQueryMaxRuleById,
  getEditMaxRule,
} from "@/api/activityCenter/platformFullReduction";
import { useRoute } from "vue-router";
import WdUpload from "@/components/WdUpload/index.vue";

const isOpen = ref(false);
const formModalData = reactive({
  name: "",
  id: "",
  time: undefined,
  configInfoList: [{ max: "", out: "" }],
  beginDate: undefined,
  stopDate: undefined,
  bannerImage: undefined,
  ruleExplain: undefined,
});
const loading = ref(false);
const formModalRef = ref(null);
const fileList = ref([]);
const route = useRoute();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: {},
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  getDetail(route.query.id);
});

// 图片上传
const afferentUrlChange = (data) => {
  if (data && data.length) {
    formModalData.bannerImage = data[0].url;
    fileList.value = [{ name: "", url: data[0].url }];
  } else {
    formModalData.bannerImage = [];
    fileList.value = [];
  }
};

const emit = defineEmits(["isModalOpen"]);
//详情

const getDetail = async (e) => {
  try {
    const res = await getQueryMaxRuleById(e);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    Object.assign(formModalData, res.data);
    formModalData.bannerImage = res.data.bannerImage
      ? res.data.bannerImage
      : "";
    fileList.value = res.data.bannerImage
      ? [{ name: "", url: res.data.bannerImage }]
      : [];
  } catch (error) {
    message.error(error.message);
  }
};

// 新增
const getAdd = async () => {
  const params = {
    id: route.query.id,
    ruleExplain: formModalData.ruleExplain,
    bannerImage: formModalData.bannerImage,
  };
  try {
    const res = await getEditMaxRule(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("编辑成功");
    isOpen.value = false;
    emit("isModalOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  getAdd();
};
</script>
<style lang="less" scoped></style>
