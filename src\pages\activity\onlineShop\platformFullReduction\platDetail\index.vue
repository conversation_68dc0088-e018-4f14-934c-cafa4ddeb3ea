<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
        <a-col class="gutter-row"> 活动名称：{{ isData?.name }} </a-col>
        <a-col class="gutter-row">
          活动周期：{{ isData?.beginDate }}--{{ isData?.stopDate }}
        </a-col>
        <a-col class="gutter-row"> 创建时间：{{ isData?.createTime }}</a-col>
        <a-col class="gutter-row">
          状态：
          {{
            isData?.status === "STOP"
              ? "已终止"
              : isData?.status === "NO_START"
              ? "未开始"
              : isData?.status === "START"
              ? "已开始"
              : isData?.status === "END"
              ? "已结束"
              : null
          }}</a-col
        >
      </a-row>
    </div>
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="6">
            <a-form-item label="供货仓名称" name="shopName">
              <a-input
                v-model:value="formData.shopName"
                allow-clear
                style="width: 100%"
                placeholder="请输入供货仓名称"
              />
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <div class="table-operate-box">
        <a-button @click="handleModalOpen"> 编辑属性专区 </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :pagination="pagination"
        :customRow="customRow"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 状态 -->
          <template v-if="column.key == 'enable'">
            {{ record.enable ? "启用" : "禁用" }}
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button
              @click="handleProductManage(record.shopName, record.supplierId)"
              type="link"
              class="btn-css"
              >管理商品</a-button
            >
          </template>
        </template>
      </a-table>
    </div>
    <edit-modal
      :open="isEditOpen"
      :data="isEditData"
      @is-modal-open="handleModalOk"
    ></edit-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive, watch } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getSupplierPage,
  getQueryMaxRuleById,
} from "@/api/activityCenter/platformFullReduction";
import { useRoute } from "vue-router";
import editModal from "./editZoneModal/index.vue";
import router from "@/router";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const customRow = () => ({
  style: {
    height: "46px",
  },
});
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({ shopName: "" });
const isDetail = ref(false);
const route = useRoute();
const isId = ref();
const isData = ref();
onMounted(async () => {
  if (route.query.id) {
    isId.value = route.query.id;
    getDetail(isId.value);
  }
  getPageList();
});

watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (
      newVal.path ===
      "/activity/activityOnlineShop/platformFullReduction/platformFullReductionDetail/platformProductManage"
    ) {
      isDetail.value = true;
    } else {
      isDetail.value = false;
    }
  },
  {
    immediate: true,
  }
);
// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

//获取编辑专区详情

const getDetail = async (e) => {
  try {
    isLoading.value = true;
    const res = await getQueryMaxRuleById(e);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isData.value = res.data;
  } catch (error) {
    message.error(error.message);
  }
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
    };
    const res = await getSupplierPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

//编辑专区属性

const isEditOpen = ref(false);
const isEditData = ref();
const handleModalOpen = () => {
  isEditOpen.value = !isEditOpen.value;
};
const handleModalOk = (e) => {
  isEditOpen.value = e;
};

//管理商品

const handleProductManage = (name, id) => {
  const params = {
    shopName: name,
    supplierId: id,
    ...isData.value,
  };
  router.push({
    path: "/activity/activityOnlineShop/platformFullReduction/platformFullReductionDetail/platformProductManage",
    query: { data: encodeURIComponent(JSON.stringify(params)) },
  });
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
.opt_text {
  color: #167fff;
  cursor: pointer;
  margin-right: 16px;
}
</style>
