<template>
  <a-drawer
    v-model:open="isOpen"
    width="80%"
    title="添加商品"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    @close="handleCancel"
  >
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="6">
          <a-form-item label="商品名称" name="prodName">
            <a-input
              v-model:value="formData.prodName"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品名称"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="后台分类" name="secondPlatCategoryIds">
            <a-cascader
              v-model:value="formData.secondPlatCategoryIds"
              change-on-select
              :options="platCategoryIdOptions"
              :field-names="{
                label: 'categoryName',
                value: 'categoryId',
                children: 'newCategoryModelDtos',
              }"
              placeholder="请选择商品分类"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="创建人" name="createUserList">
            <a-select
              v-model:value="formData.createUserList"
              show-search
              option-filter-prop="username"
              placeholder="请选择创建人"
              :options="userOptions"
              :field-names="{ label: 'username', value: 'userId' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item
            :wrapper-col="{ span: 14, offset: 0 }"
            style="align-self: flex-end; text-align: left"
          >
            <a-button type="primary" @click="onSubmit">搜索</a-button>
            <a-button style="margin-left: 10px" @click="reset">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="table-columns">
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :pagination="pagination"
        rowKey="skuId"
        :row-selection="rowSelection"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 商品信息 -->
          <template v-if="column.key == 'prodName'">
            <a-space>
              <div v-if="record && record.picPath?.includes('https')">
                <a-image :src="record?.picPath" width="60px" height="60px" />
              </div>
              <div v-else>
                <a-image
                  :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${record?.picPath}`"
                  width="50px"
                  height="50px"
                />
              </div>
              <a-space direction="vertical">
                <div style="text-align: left">{{ record.prodName }}</div>
                <div style="text-align: left">
                  {{ record.prodNumber }}
                </div>
              </a-space>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button
        style="margin-right: 8px"
        :loading="loading"
        type="primary"
        @click="handleAllOk"
        ghost
        >添加当前全部搜索结果</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleOk"
        >确定</a-button
      >
    </template>
    <a-modal
      v-model:open="isAOpen"
      width="520px"
      title="提示"
      :destroy-on-close="true"
      @cancel="handleOpenCancel"
    >
      <div
        style="color: rgb(25, 144, 255); text-align: center; margin: 10px 0 0 0"
      >
        点击确定会将选择的商品进行添加
      </div>
      <template #footer>
        <a-button @click="handleOpenCancel">取消</a-button>
        <a-button type="primary" @click="handleOpenOk">确定</a-button>
      </template>
    </a-modal>
  </a-drawer>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getCategoryList } from "@/api/goodsCenter/newZone";
import {
  getAddListPage,
  getUserPage,
  getAddProduct,
} from "@/api/activityCenter/platformFullReduction";
// import { queryCategory } from "@/api/common";
import { fetchCategory } from "@/utils";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const formData = reactive({
  prodName: undefined,
  secondPlatCategoryIds: undefined,
  createUserList: undefined,
});
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const isOpen = ref(false);
const loading = ref(false);
const platCategoryIdOptions = ref([{ value: "", label: "", children: [] }]);
const userOptions = ref();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  if (isOpen.value && newProps.data.supplierId) {
    getPageList();
    getCategory();
    getUserPageList();
  }
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params: any = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
      createUserList: formData.createUserList
        ? [formData.createUserList]
        : undefined,
      supplierId: props.data.supplierId,
      activityFlag: props.data.activityFlag,
      activityId: props.data.id,
    };
    if (
      params.secondPlatCategoryIds &&
      params.secondPlatCategoryIds.length == 0
    ) {
      delete params.secondPlatCategoryIds;
    }
    if (params.secondPlatCategoryIds) {
      params.firstPlatCategoryId = formData.secondPlatCategoryIds[0];
      params.secondPlatCategoryId = formData.secondPlatCategoryIds[1];
      params.threePlatCategoryIds = formData.secondPlatCategoryIds[2]
        ? [formData.secondPlatCategoryIds[2]]
        : [];
      delete params.secondPlatCategoryIds;
    }
    const res = await getAddListPage(params as any);
    console.log(res, "res123");
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

//获取平台分类
const getCategory = async () => {
  platCategoryIdOptions.value = await fetchCategory();
  // fetchCategory().then((res) => {
  //   if (res.data) {
  //     platCategoryIdOptions.value = res.data;
  //   }
  // });
};
//获取二级分类
// const loadSelectData = (selectedOptions: any) => {
//   const targetOption = selectedOptions[selectedOptions.length - 1];
//   targetOption.loading = true;
//   getCategoryList(targetOption.categoryId)
//     .then((res) => {
//       targetOption.children = res.data;
//     })
//     .finally(() => {
//       targetOption.loading = false;
//     });
// };
//创建人

const getUserPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: 1,
      size: 10000,
    };
    const res = await getUserPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    userOptions.value = res.data.records;
  } catch (error) {
    message.error(error.message);
  }
};
//新增商品getAddProduct

const getAddProductList = async () => {
  try {
    const params = {
      ...formData,
      supplierId: props.data.supplierId,
      activityFlag: props.data.activityFlag,
      activityId: props.data.id,
      skuIds: isSelectRowKey.value,
      type: isSelectRowKey.value ? "PART" : "ALL",
      createUserList: formData.createUserList
        ? [formData.createUserList]
        : undefined,
    };
    const res = await getAddProduct(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("新增成功");
    isOpen.value = false;
    isAOpen.value = false;
    isSelectRowKey.value = undefined;
    formData.prodName = undefined;
    formData.secondPlatCategoryIds = undefined;
    emit("isModalOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};
//添加当前全部搜索结果
const isAOpen = ref(false);
const handleAllOk = () => {
  isAOpen.value = true;
};
const handleOpenOk = () => {
  getAddProductList();
};
const handleOpenCancel = () => {
  isAOpen.value = false;
};
//批量删除
const isSelectRowKey = ref();
const rowSelection = {
  onChange: (selectedRowKeys: string[], selectedRows: []) => {
    isSelectRowKey.value = selectedRowKeys;
  },
  getCheckboxProps: (record: any) => ({
    disabled: record.exist === true,
    name: record.name,
  }),
};
const emit = defineEmits(["isModalOpen"]);
const handleCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleOk = () => {
  getAddProductList();
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
