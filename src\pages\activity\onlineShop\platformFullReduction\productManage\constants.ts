import { reactive } from "vue";
export const COLUMNS = [
  {
    title: "商品信息",
    dataIndex: "productName",
    key: "productName",
    width: 320,
    align: "left",
    ellipsis: true,
  },
  {
    title: "后台分类",
    dataIndex: "firstPlatCategoryName",
    key: "firstPlatCategoryName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "售价",
    dataIndex: "price",
    key: "price",
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "商品状态",
    dataIndex: "productStatus",
    key: "productStatus",
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "创建人",
    dataIndex: "createUserName",
    key: "createUserName",
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "商品排序",
    dataIndex: "sort",
    key: "sort",
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    key: "operate",
    fixed: "right",
    width: 120,
    ellipsis: true,
    align: "left",
  },
];

export const FROM_DATA = reactive({
  productName: undefined,
  secondPlatCategoryIds: undefined,
});
