<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-row :gutter="24" style="line-height: 40px; margin-top: -16px">
        <a-col class="gutter-row"> 活动名称：{{ isData?.name }} </a-col>
        <a-col class="gutter-row">
          活动周期：{{ isData?.beginDate }}--{{ isData?.stopDate }}
        </a-col>
        <a-col class="gutter-row"> 创建时间：{{ isData?.createTime }}</a-col>
        <a-col class="gutter-row">
          状态：
          {{
            isData?.status === "STOP"
              ? "已终止"
              : isData?.status === "NO_START"
              ? "未开始"
              : isData?.status === "START"
              ? "已开始"
              : isData?.status === "END"
              ? "已结束"
              : null
          }}</a-col
        >
        <a-col class="gutter-row"> 供应仓名称：{{ isData?.shopName }}</a-col>
      </a-row>
    </div>
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row">
            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleSelectDetele()"
            >
              <a-button type="link" class="btn-css" style="margin: 30px 0 0 0"
                >删除选择</a-button
              >
            </a-popconfirm>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="商品名称" name="productName">
              <a-input
                v-model:value="formData.productName"
                allow-clear
                style="width: 100%"
                placeholder="请输入商品名称"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="后台分类" name="secondPlatCategoryIds">
              <a-cascader
                v-model:value="formData.secondPlatCategoryIds"
                change-on-select
                :options="platCategoryIdOptions"
                :field-names="{
                  label: 'categoryName',
                  value: 'categoryId',
                  children: 'newCategoryModelDtos',
                }"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
              <a-button
                style="margin-left: 10px"
                danger
                @click="handleAllDelete"
                >全部删除</a-button
              >
              <a-button
                type="primary"
                style="margin-left: 10px"
                @click="handleOpen"
                >新增商品</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        :row-selection="rowSelection"
        rowKey="id"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 商品信息 -->
          <template v-if="column.key == 'productName'">
            <a-space>
              <div v-if="record?.picPath.includes('https')">
                <a-image :src="record?.picPath" width="60px" height="60px" />
              </div>
              <div v-else>
                <a-image
                  :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${record?.picPath}`"
                  width="50px"
                  height="50px"
                />
              </div>
              <a-space direction="vertical">
                <div style="text-align: left">{{ record.productName }}</div>
                <div style="text-align: left" v-if="record.skuId">
                  {{ record.prodNumber }}
                </div>
              </a-space>
            </a-space>
          </template>
          <!-- 分类 -->
          <template v-if="column.key == 'firstPlatCategoryName'">
            {{ record.firstPlatCategoryName }}-{{
              record.secondPlatCategoryName
            }}--{{ record.threePlatCategoryName }}
          </template>
          <!-- 商品状态 -->
          <template v-if="column.key == 'productStatus'">
            {{
              record.productStatus === "0"
                ? "下架"
                : record.productStatus === "1"
                ? "上架"
                : record.productStatus === "2"
                ? "违规下架"
                : record.productStatus === "4"
                ? "异常"
                : ""
            }}
          </template>
          <!-- 排序 -->
          <template v-if="column.key == 'sort'">
            <a-input-number
              v-if="isInp === record.id"
              @blur="handleSort"
              v-model:value="record.sort"
              :max="999999999"
              placeholder="请输入排序"
            />
            <div v-else>
              <a-input-number
                :disabled="true"
                v-model:value="record.sort"
              ></a-input-number>
              <edit-filled
                @click="handleEditSort(record.id)"
                style="color: #5599ff; cursor: pointer; margin: 0 0 0 10px"
              />
            </div>
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDetele(record.id)"
            >
              <a-button type="link" class="btn-css"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:open="isAllDelOpen"
      width="520px"
      title="提示"
      :destroy-on-close="true"
      @cancel="handleAllDelCancel"
    >
      <div
        style="color: rgb(25, 144, 255); text-align: center; margin: 10px 0 0 0"
      >
        是否确认删除全部商品？
      </div>
      <template #footer>
        <a-button @click="handleAllDelCancel">取消</a-button>
        <a-button type="primary" @click="handleAllDelnOk">确定</a-button>
      </template>
    </a-modal>
    <add-modal
      :open="isOpen"
      :data="isData"
      @is-modal-open="handleOpenOk"
    ></add-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getListPage,
  getSetSort,
  getDelProduct,
} from "@/api/activityCenter/platformFullReduction";
// import { queryCategory } from "@/api/common";
import { fetchCategory } from "@/utils";
import { getCategoryList } from "@/api/goodsCenter/newZone";
import { useRoute } from "vue-router";
import { EditFilled } from "@ant-design/icons-vue";
import addModal from "./addModal/index.vue";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({
  productName: undefined,
  secondPlatCategoryIds: undefined,
  firstPlatCategoryId: undefined,
});
const isDetail = ref(false);
const route = useRoute();
const isId = ref();
const isData = ref();
const platCategoryIdOptions = ref([{ value: "", label: "", children: [] }]);
onMounted(async () => {
  if (route.query.data) {
    isData.value = JSON.parse(decodeURIComponent(route.query.data as any));
  }
  isId.value = route.query.id;
  getCategory();
  getPageList();
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

//获取商品分类
const getCategory = async () => {
  platCategoryIdOptions.value = await fetchCategory();
  // fetchCategory().then((res) => {
  //   if (res.data) {
  //     platCategoryIdOptions.value = res.data;
  //   }
  // });
};
//获取商品二级分类
// const loadSelectData = (selectedOptions: any) => {
//   const targetOption = selectedOptions[selectedOptions.length - 1];
//   targetOption.loading = true;
//   getCategoryList(targetOption.categoryId)
//     .then((res) => {
//       targetOption.children = res.data;
//     })
//     .finally(() => {
//       targetOption.loading = false;
//     });
// };

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params: any = {
      supplierId: isData.value.supplierId,
      activityFlag: isData.value.activityFlag,
      activityId: isData.value.id,
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
    };

    if (
      params.secondPlatCategoryIds &&
      params.secondPlatCategoryIds.length == 0
    ) {
      delete params.secondPlatCategoryIds;
    }
    if (params.secondPlatCategoryIds) {
      const [firstPlatCategoryId, secondPlatCategoryId, threePlatCategoryIds] =
        params.secondPlatCategoryIds;
      params.firstPlatCategoryId = firstPlatCategoryId;
      params.secondPlatCategoryId = secondPlatCategoryId;
      params.threePlatCategoryIds = threePlatCategoryIds
        ? [threePlatCategoryIds]
        : [];
      delete params.secondPlatCategoryIds;
    }
    const res = await getListPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

//删除

const isSelectRowKey = ref();
const rowSelection = {
  onChange: (selectedRowKeys: string[], selectedRows: []) => {
    isSelectRowKey.value = selectedRowKeys;
  },
};

const getDel = async (id) => {
  try {
    const params = {
      delIds: id === "all" ? undefined : id,
      supplierId: isData.value.supplierId,
      activityFlag: isData.value.activityFlag,
      activityId: isData.value.id,
      type: id === "all" ? "ALL" : "PART",
    };
    const res = await getDelProduct(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("删除成功");
    isAllDelOpen.value = false;
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

const handleDetele = async (id) => {
  getDel([id]);
};

//删除选择
const handleSelectDetele = () => {
  if (!isSelectRowKey.value) {
    message.error("请选择要删除的商品");
    return;
  }
  if (
    isSelectRowKey.value.length === 10 ||
    isSelectRowKey.value.length === 20 ||
    isSelectRowKey.value.length === 50 ||
    isSelectRowKey.value.length === 100
  ) {
    pagination.current = 1;
  }
  getDel(isSelectRowKey.value);
};
//删除全部
const isAllDelOpen = ref(false);
const handleAllDelete = async () => {
  isAllDelOpen.value = true;
};
const handleAllDelnOk = () => {
  getDel("all");
};
const handleAllDelCancel = () => {
  isAllDelOpen.value = false;
};

//排序

const getSort = async () => {
  try {
    const params = {
      id: isSortId.value,
      sort: isSortText.value,
    };
    const res = await getSetSort(params as any);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

const isInp = ref();
const isSortText = ref();
const isSortId = ref();
const handleSort = (e) => {
  isSortText.value = e.target.value;
  isInp.value = "-1";
  getSort();
};
const handleEditSort = (e) => {
  isInp.value = e;
  isSortId.value = e;
};

//新增商品
const isOpen = ref(false);
const handleOpen = () => {
  isOpen.value = !isOpen.value;
};

const handleOpenOk = (e) => {
  isOpen.value = e;
  getPageList();
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
