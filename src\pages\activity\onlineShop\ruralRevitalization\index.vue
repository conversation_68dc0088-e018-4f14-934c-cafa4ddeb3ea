<template>
  <div class="ruralRevitalization-container">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-container">
      <div class="title">
        <div>
          <a-button @click="showModalFunc">编辑活动</a-button>
          <span style="margin-left: 8px"
            >消费者奖励积分比例：商家获得积分的{{
              detailInfo.pointsRate
            }}%</span
          >
        </div>
      </div>
      <wd-table
        :columns="columns"
        :table-data="tableData"
        :pagination="pagination"
        @onChange="pageChange"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.key === 'operate'">
            <a-switch
              :checked="record.ruralSwitch"
              @change="statusChange(record)"
            />
          </template>
        </template>
      </wd-table>
    </div>
    <a-modal
      v-model:open="showModal"
      title="编辑活动"
      @ok="handleOk"
      @cancel="cancelFunc"
      getContainer=".ruralRevitalization-container"
    >
      <a-form :form="form" layout="vertical" :rules="rules">
        <a-form-item label="开启活动" name="enable">
          <a-switch v-model:checked="formState.enable" />
        </a-form-item>
        <a-form-item label="规则说明" name="ruleExplanation">
          <a-textarea
            v-model:value="formState.ruleExplanation"
            :maxlength="200"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onMounted, watch, reactive } from "vue";
import { PlusCircleOutlined } from "@ant-design/icons-vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { message } from "woody-ui";
import WdTable from "@/components/TableList/tableComp.vue";
import {
  getSupplierPage,
  queryPointsInfo,
  editPoints,
  switchPointsPool,
} from "@/api/activityCenter/ruralRevitalization";

const showModal = ref(false);
const queryParam = ref({});
const formState = reactive({
  enable: false,
  ruleExplanation: "",
});
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});
const formList = [
  {
    label: "店铺名称",
    name: "shopName",
    type: "input",
    maxlength: 30,
  },
  {
    label: "绑定电话",
    name: "tel",
    type: "input",
    maxlength: 11,
  },
];
const columns = [
  {
    title: "店铺名称",
    dataIndex: "shopName",
    key: "shopName",
  },
  {
    title: "绑定电话",
    dataIndex: "tel",
    key: "tel",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    width: 100,
  },
];

const tableData = ref([]);
const detailInfo = ref({});

onMounted(() => {
  querypointsPool();
});

// 获取乡村振兴积分信息
const querypointsPool = () => {
  queryPointsInfo({ promotionPointsType: "RURAL_REVITALIZATION" }).then(
    (res) => {
      if (res.code === 0) {
        detailInfo.value = res.data;
        formState.enable = res.data.enable;
        formState.ruleExplanation = res.data.ruleExplanation;
        if (res.data.promotionPointsId) {
          queryList({ promotionPointsId: res.data.promotionPointsId });
        }
      }
    }
  );
};

// 查表格数据
const queryList = (param = {}) => {
  getSupplierPage({
    current: 1,
    size: 10,
    promotionPointsId: detailInfo.value.promotionPointsId,
    ...param,
    ...queryParam.value,
  }).then((res) => {
    if (res.code === 0) {
      tableData.value = res.data.records;
      pagination.total = res.data.total;
    }
  });
};
// 分页
const pageChange = ({ current, pageSize }) => {
  pagination.current = current;
  pagination.pageSize = pageSize;
  queryList({
    current,
    size: pageSize,
    ...queryParam.value,
  });
};
// 搜索
const handleSearch = (data) => {
  queryParam.value = data;
  if (!Object.keys(data).length) {
    pagination.current = 1;
    pagination.pageSize = 10;
  }
  queryList(data);
};

// 状态切换
const statusChange = (item) => {
  const { ruralSwitch, shopId } = item;
  switchPointsPool({
    shopId,
    enable: !ruralSwitch,
    promotionPointsId: detailInfo.value.promotionPointsId,
  }).then((res) => {
    if (res.code === 0) {
      message.success("操作成功");
      queryList({ ...pagination, size: pagination.pageSize });
    } else {
      message.error(res.msg);
    }
  });
};
// 编辑
const showModalFunc = () => {
  showModal.value = true;
};

// 弹框确认
const handleOk = () => {
  const { enable, ruleExplanation } = formState;
  editPoints({
    promotionPointsId: detailInfo.value.promotionPointsId,
    enable,
    ruleExplanation,
  })
    .then((res) => {
      if (res.code === 0) {
        message.success("编辑成功");
        showModal.value = false;
        querypointsPool();
      }
    })
    .catch((err) => {
      console.log(err, "err");
    });
};
// 弹框取消
const cancelFunc = () => {
  querypointsPool();
};
</script>
<style lang="less" scoped>
.ruralRevitalization-container {
  .table-container {
    margin-top: 16px;
    padding: 32px;
    border-radius: 16px;
    background-color: #fff;
  }
  .title {
    margin-bottom: 20px;
  }
  :deep(.ant-modal-content) {
    padding-left: 0;
    padding-right: 0;
    padding-top: 24px;
    padding-bottom: 24px;
  }
  :deep(.ant-modal-body) {
    border-top: 1px solid #f2f5f9;
    padding-top: 24px;
    padding-left: 32px;
    padding-right: 32px;
  }
  :deep(.ant-modal-header) {
    padding-bottom: 24px;
    padding-left: 32px;
    margin-bottom: 0;
  }
  :deep(.ant-modal-footer) {
    padding-right: 32px;
    padding-top: 24px;
    border-top: 1px solid #f2f5f9;
    margin-top: 48px;
  }
  :deep(.ant-modal-close) {
    top: 24px !important;
    right: 32px !important;
  }
}
</style>
