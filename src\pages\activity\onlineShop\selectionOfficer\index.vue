<template>
  <div v-if="!isDetail">
    <div class="table-columns">
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1000 }"
        :pagination="pagination"
        :customRow="customRow"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }"
          ><!-- 状态 -->
          <template v-if="column.key === 'enable'">
            {{ record.status ? "启用" : "禁用" }}
          </template></template
        >
      </a-table>
    </div>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getPage } from "@/api/activityCenter/selectionOfficer/index";

const isLoading = ref(true);
const formRef = ref(null);
const customRow = () => ({
  style: {
    height: "46px",
  },
});
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({ name: undefined, time: undefined });
const isDetail = ref(false);
onMounted(async () => {
  getPageList();
});

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
    };
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
.table-columns {
  height: calc(100vh - 100px);
}
</style>
