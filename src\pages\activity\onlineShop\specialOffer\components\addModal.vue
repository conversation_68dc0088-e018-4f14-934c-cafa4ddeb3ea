<template>
  <a-modal
    v-model:open="addVisible"
    placement="center"
    :title="props.isEdit ? '编辑' : '新增'"
    :destroy-on-close="true"
    width="30%"
    @cancel="closeClick(addVisible)"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="detailObj"
      v-bind="layout"
      layout="vertical"
      :label-col="{ span: 6 }"
      class="add-form-css mt20"
    >
      <a-form-item has-feedback label="启用专区" name="propName">
        <a-switch
          :checked="detailObj.status === 1 ? true : false"
          @change="handleStatusChange"
        />
      </a-form-item>
      <a-form-item
        has-feedback
        label="专区名称"
        name="zoneName"
        :rules="[{ required: true, message: '请输入专区名称' }]"
      >
        <a-input
          v-model:value="detailObj.zoneName"
          type="input"
          autocomplete="off"
          placeholder="请输入专区名称"
        />
      </a-form-item>
      <a-form-item
        has-feedback
        label="专区描述"
        name="zoneIntro"
        :rules="[{ required: true, message: '请输入专区描述' }]"
      >
        <a-input
          v-model:value="detailObj.zoneIntro"
          type="input"
          autocomplete="off"
          placeholder="请输入专区描述"
        />
      </a-form-item>
      <a-form-item has-feedback label="自动填充商品规则">
        <a-switch
          :checked="detailObj.fillerProduct === 1 ? true : false"
          @change="handleFillerChange"
        />
      </a-form-item>
      <a-form-item
        v-if="detailObj.fillerProduct"
        has-feedback
        label="最低价"
        name="minPrice"
        :rules="[{ required: true, message: '请输入最高价' }]"
      >
        <a-input-number
          style="width: 200px"
          id="inputNumber1"
          v-model:value="detailObj.minPrice"
          :min="0"
          :max="100000000"
        />
      </a-form-item>
      <a-form-item
        v-if="detailObj.fillerProduct"
        has-feedback
        label="最高价"
        name="maxPrice"
        :rules="[{ required: true, message: '请输入最高价' }]"
      >
        <a-input-number
          style="width: 200px"
          id="inputNumber2"
          v-model:value="detailObj.maxPrice"
          :min="0"
          :max="100000000"
        />
      </a-form-item>
      <div v-if="detailObj.fillerProduct === 1">
        <p style="color: red">注：价格以售价的现金部分计算。</p>
        <p style="color: red">
          关闭自动填充后需要手动清除已有商品，请勿频繁修改规则
        </p>
      </div>
    </a-form>
    <template #footer>
      <a-button @click="closeClick(addVisible)">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="addSuccessShow"
    title="自动填充商品"
    cancelText="取消"
    okText="确认"
    @cancel="closeClick(addSuccessShow)"
    @ok="handleSucessOk"
  >
    <p>
      在{{ detailObj.minPrice }}~{{ detailObj.maxPrice }}区间共检测到{{
        detailObj.total
      }}个商品符合要求，是否确认添加
    </p>
  </a-modal>
  <a-modal
    v-model:open="addSuccess"
    title="数据处理中"
    @ok="addSuccessClick"
    @cancel="closeClick(addSuccess)"
  >
    <p>任务处理中，预计1~2分钟后完成</p>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import {
  GetPageQuery,
  getGoldCategoryDrop,
  GetZoneDelete,
  GetSpecialOff,
  GetSpecialDetail,
  GetAddProductCount,
  GetZoneAdd,
  GetSpecialUpdate,
} from "@/api/activityCenter/goldCoin";
import { message } from "woody-ui";
import { useRoute } from "vue-router";
const route = useRoute();

const emits = defineEmits(["close-click", "get-list"]);
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
});
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const addVisible = ref(true);
const addSuccessShow = ref(false);
const addSuccess = ref(false);
const formRef = ref(null);
const detailObj = ref({
  maxPrice: "",
  minPrice: "",
  zoneName: "",
  zoneIntro: "",
  status: 0,
  fillerProduct: 0,
  total: null,
});

const closeClick = (data) => {
  data = false;
  emits("close-click");
};
const addSuccessClick = () => {
  addSuccess.value = false;
  emits("close-click");
};
const handleFillerChange = (checked) => {
  detailObj.value.fillerProduct = checked ? 1 : 0;
};
const handleStatusChange = (checked) => {
  detailObj.value.status = checked ? 1 : 0;
};
const submitClick = () => {
  formRef.value
    .validate()
    .then(async () => {
      const { maxPrice, minPrice } = detailObj.value;
      console.log(props.isEdit, "props.isEdit");
      if (props.isEdit) {
        // 编辑
        // if (!detailObj.value.fillerProduct) {
        //   editApi();
        // } else {
        //   // 开关打开
        //   editOpenApi();
        // }
        editApi();
      } else {
        // 新增且开关不打开
        if (!detailObj.value.fillerProduct) {
          addApi();
        } else {
          // 开关打开
          editOpenApi();
        }
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

const editApi = async () => {
  const { status, fillerProduct, maxPrice, minPrice, zoneIntro, zoneName } =
    detailObj.value;
  const params1 = {
    zoneId: route.query.zoneId,
    zoneIntro,
    zoneName,
    status: status ? 1 : 0,
    fillerProduct,
  };
  if (fillerProduct === 1) {
    params1["maxPrice"] = maxPrice;
    params1["minPrice"] = minPrice;
  }
  const res = await GetSpecialUpdate(params1);
  if (res.code === 0) {
    console.log(typeof fillerProduct, "fillerProduct");
    emits("close-click");
    message.success("操作成功");
    addVisible.value = false;
    emits("get-list");
  } else {
    message.error(res.message);
    emits("close-click");
  }
};
const editOpenApi = async () => {
  const { status, fillerProduct, maxPrice, minPrice, zoneIntro, zoneName } =
    detailObj.value;
  const params = {
    maxPrice,
    minPrice,
  };
  const res = await GetAddProductCount(params);
  if (res.code === 0) {
    detailObj.value.total = res.data;
    addVisible.value = false;
    addSuccessShow.value = true;
    message.success("操作成功");
    emits("get-list");
    // if (res.data != 0 || res.data != "0") {
    //   addSuccessShow.value = true;
    // }

    return;
  }
  message.error(res.message);
};

const addApi = async () => {
  const { status, fillerProduct, maxPrice, minPrice, zoneIntro, zoneName } =
    detailObj.value;
  const params = {
    fillerProduct,
    status,
    zoneIntro,
    zoneName,
    maxPrice,
    minPrice,
  };
  const res = await GetZoneAdd(params);
  if (res.code === 0) {
    message.success("操作成功");
    addVisible.value = false;
    if (fillerProduct === 1) {
      addSuccess.value = true;
    }
    emits("get-list");
  } else {
    message.error(res.message);
    emits("close-click");
  }
};
const handleSucessOk = () => {
  props.isEdit ? editApi() : addApi();
};

const getDetail = async () => {
  const res = await GetSpecialDetail({ zoneId: route.query.zoneId });
  console.log(res, "res");
  if (res.code === 0) {
    detailObj.value = res.data;
    //   detailObj.value.status === 1 ? detailObj.value.status = true : detailObj.value.status = false;
    //   detailObj.value.fillerProduct === 1 ? true : false;
    console.log(detailObj.value, "detailObj.value");
  }
};
onMounted(() => {
  if (props.isEdit) getDetail();
});
</script>
<style scoped lang="scss"></style>
