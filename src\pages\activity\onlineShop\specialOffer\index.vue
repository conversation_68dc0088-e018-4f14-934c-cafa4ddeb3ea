<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button type="primary" @click="addClick" class="ml10"
        >新增专区</a-button
      >
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      :is-checkbox="false"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      @blur-change="blurChange"
    >
    </table-list-antd>
  </div>
  <addModal
    v-if="addShow"
    :is-edit="false"
    @get-list="getList"
    @close-click="addShow = false"
  />
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import {
  GetPageQuery,
  getGoldCategoryDrop,
  GetZoneDelete,
  GetSpecialOff,
  GetAddProductCount,
  GetZoneAdd,
  GetSpecialUpdate,
} from "@/api/activityCenter/goldCoin";
import { message } from "woody-ui";
import { useRouter } from "vue-router";
import addModal from "./components/addModal.vue";

const addShow = ref(false);
const addSuccessShow = ref(false);
const formData = ref({});
const router = useRouter();
const classfityOpt = ref([]);

import type { Rule } from "woody-ui/es/form";
import type { FormInstance } from "woody-ui";
// interface FormState {
//   propName: string;
//   prodPropValues: Array;
// }
const formRef = ref<FormInstance>();
const formState = reactive({
  maxPrice: "",
  minPrice: "",
  zoneName: "",
  zoneIntro: "",
  status: false,
  fillerProduct: false,
  total: null,
});

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const blurChange = async (sort, data) => {
  console.log(sort, data);
  const params = {
    zoneId: data.zoneId,
    zoneSort: sort,
  };
  const res = await GetSpecialUpdate(params);
  if (res.code === 0) {
    message.success("编辑成功");
    getList();
  }
};
const formList = [
  {
    label: "专区ID",
    name: "zoneId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "专区名称",
    name: "zoneName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "专区状态",
    name: "status",
    type: "select", // 输入框
    options: [
      {
        label: "上架",
        value: 1,
      },
      {
        label: "下架",
        value: 0,
      },
    ],
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  formData.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

// const submitClick = () => {
//   formRef.value
//     .validate()
//     .then(async () => {
//       const { maxPrice, minPrice } = formState;
//       if (!formState.fillerProduct) {
//         addApi();
//       } else {
//         const params = {
//           maxPrice,
//           minPrice,
//         };
//         const res = await GetAddProductCount(params);
//         if (res.code === 0) {
//           formState.total = res.data;
//           addVisible.value = false;
//           addSuccessShow.value = true;
//         }
//       }
//     })
//     .catch((error) => {
//       console.log("error", error);
//     });
// };

// const addApi = async () => {
//   const { status, fillerProduct } = formState;
//   const params = {
//     ...formState,
//     status: status ? 1 : 0,
//     fillerProduct: fillerProduct ? 1 : 0,
//   };
//   const res = await GetZoneAdd(params);
//   if (res.code === 0) {
//     message.success("新增成功");
//     addVisible.value = true;
//     getList();
//   }
// };

// const handleSucessOk = async () => {
//   const { status, fillerProduct } = formState;
//   const params = {
//     ...formState,
//     status: status ? 1 : 0,
//     fillerProduct: status ? 1 : 0,
//   };
//   const res = await GetZoneAdd(params);
//   if (res.code === 0) {
//     addSuccessShow.value = false;
//     getList();
//   }else{
//     message.success(res.message);
//   }
// };

//table表头数据
const columns = [
  {
    title: "专区ID",
    dataIndex: "zoneId",
    key: "zoneId",
    fixed: true,
    align: "left",
    width: 220,
  },
  {
    title: "专区名称",
    dataIndex: "zoneName",
    key: "zoneName",
    align: "left",
    width: 120,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 200,
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
    key: "updateTime",
    align: "left",
    width: 200,
  },
  {
    title: "商品数量",
    dataIndex: "productCount",
    key: "productCount",
    align: "left",
    width: 150,
  },
  {
    title: "自动填充商品",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      if (record.minPrice) {
        return record.minPrice + "-" + record.maxPrice;
      }
      return "不开启";
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    align: "left",
    width: 150,
  },
  {
    title: "排序",
    dataIndex: "zoneSort",
    key: "zoneSort",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "dynamicsAct",
    fixed: "right",
    width: 300,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});
const childKey = ref(Math.random());

const dataSource = ref([]);
const loading = ref(false);
const isModalOpen = ref(false);
const zoneId = ref("");
const shopName = ref("");
const getList = async () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    ...formData.value,
  };
  const res = await GetPageQuery(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const addClick = () => {
  zoneId.value = "";
  addShow.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  zoneId.value = data.zoneId;
  if (type === "delete") {
    deleteApi(data.zoneId);
  } else if (type === "edit") {
    router.push({
      path: "/activity/activityOnlineShop/specialDetail",
      query: { zoneId: data.zoneId },
    });
  } else if (type === "off") {
    offApi(data);
  }
};

const offApi = async (data) => {
  const params = {
    zoneId: data.zoneId,
    status: data.status === 0 ? 1 : 0,
  };
  const res = await GetSpecialOff(params);
  if (res.code === 0) {
    message.success("操作成功");
    getList();
  }
};

const deleteApi = async (id) => {
  const res = await GetZoneDelete({ zoneId: id });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  } else {
    message.success(res.message);
  }
};

// 获取表格数据
// const fetchTableData = ({ pagination, filters, sorter }) => {
//   loading.value = true;
//   getList();
// };

// 分页变化
const onPaginationChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
};

// 编辑记录
const editRecord = (record) => {
  console.log("编辑记录", record);
};

// 删除记录
const deleteRecord = (record) => {
  console.log("删除记录", record);
};

onMounted(() => {
  // getSupplyList();
  // getSource();
  // getStatus();
  //   getShopListFunc();
  getClassfity();
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  height: 100%;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
