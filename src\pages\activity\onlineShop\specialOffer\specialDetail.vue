<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button @click="editClick" class="ml10">编辑专区属性</a-button>

      <a-button type="primary" @click="addClick" class="ml10"
        >新增商品</a-button
      >
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      :is-checkbox="false"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      @blur-change="blurChange"
      class="mt10"
    >
    </table-list-antd>
  </div>
  <addModal
    v-if="addShow"
    :is-edit="isEdit"
    @close-click="addShow = false"
    @get-list="getList"
  />
  <a-modal
    v-model:open="addProdVisible"
    placement="center"
    :title="!isEdit ? '新增商品' : '编辑商品'"
    :destroy-on-close="true"
    width="40%"
    @cancel="addProdShow = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="proDetail"
      v-bind="layout"
      layout="vertical"
      :rules="proRules"
      class="add-form-css"
    >
      <a-form-item label="商品主题">
        <img
          :src="!isEdit ? proDetail.pic : proDetail.productPic"
          class="pic-css"
        />
      </a-form-item>
      <a-form-item label="商品名称" name="prodName">
        <a-input
          v-if="isEdit"
          v-model:value="proDetail.productName"
          :disabled="true"
        />
        <a-input v-else v-model:value="proDetail.prodName" :disabled="true" />
      </a-form-item>
      <a-form-item v-if="isEdit" label="供货价" name="marketPrice">
        <a-input v-model:value="proDetail.costPrice" :disabled="true" />
      </a-form-item>
      <a-form-item label="市场价">
        <a-input v-model:value="proDetail.oriPrice" :disabled="true" />
      </a-form-item>
      <a-form-item label="现金价格" :name="!isEdit ? 'price' : 'amountPrice'">
        <a-input
          v-if="!isEdit"
          v-model:value="proDetail.price"
          :disabled="isEdit && addType === 0"
        />
        <a-input
          v-else
          v-model:value="proDetail.amountPrice"
          :disabled="isEdit && addType === 0"
        />
      </a-form-item>
      <a-form-item label="金币价格" :name="!isEdit ? 'goldCoin' : 'coinPrice'">
        <a-input
          v-if="!isEdit"
          v-model:value="proDetail.goldCoin"
          :disabled="isEdit && addType === 0"
        />
        <a-input
          v-else
          v-model:value="proDetail.coinPrice"
          :disabled="isEdit && addType === 0"
        />
      </a-form-item>
      <!-- <a-form-item label="库存" name="inventory">
        <a-input :value="proDetail.inventory || proDetail.stocks" />
      </a-form-item> -->
    </a-form>
    <template #footer>
      <a-button @click="addProdVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="upShow"
    title="确认批量上架？"
    cancelText="取消"
    okText="确认"
    @cancel="closeUpClick"
    @ok="handleUpOk"
  >
    <p>
      使用批量上架时将使用商品的默认售价且不设库存限制，确认上架？本次选中{{
        selectData.length
      }}个商品
    </p>
  </a-modal>
  <add-prod
    v-if="addProdShow"
    :columns="prodColumns"
    :form-list="propdFormList"
    @add-click="addSubmitClick"
    @close-click="addProdShow = false"
    btn-text="上架"
    :is-action-btn="true"
    @action-click="actionClick"
    :is-Refresh="isRefresh"
    api-type="YZH"
  ></add-prod>
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import {
  GetSpecialDetailYZH,
  getGoldCategoryDrop,
  GetZoneProdDelete,
  GetProdUpdate,
  GetProductAddList,
  GetPageSpecialQuery,
  GetProductAdd,
  GetZoneProdDetail,
} from "@/api/activityCenter/goldCoin";
import { message } from "woody-ui";
import { useRoute } from "vue-router";
import addModal from "./components/addModal.vue";
import addProd from "@/components/AddProd/index.vue";

const addShow = ref(false);
const proDetail = ref(null);
const formData = ref({});
const route = useRoute();
const classfityOpt = ref([]);
const addProdShow = ref(false);
const addProdVisible = ref(false);
const selectData = ref([]);
const upShow = ref(false);
const isRefresh = ref(false);

import type { Rule } from "woody-ui/es/form";
import type { FormInstance } from "woody-ui";
// interface FormState {
//   propName: string;
//   prodPropValues: Array;
// }
const formRef = ref<FormInstance>();
const formState = reactive({
  propName: "",
});
const proRules = {
  goldCoin: [{ required: true, message: "请输入金币价格", trigger: "blur" }],
  price: [{ required: true, message: "请输入现金", trigger: "blur" }],
  amountPrice: [{ required: true, message: "请输入现金", trigger: "blur" }],
  coinPrice: [{ required: true, message: "请输入金币价格", trigger: "blur" }],
};
const propdFormList = [
  {
    label: "商品名称",
    name: "productName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "三方商品ID",
    name: "thirdProductId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "售价区间",
    name: "minSellingPrice",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "至",
    name: "maxSellingPrice",
    type: "input", // 输入框
    span: 6,
  },
];
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const handleFinish = (values) => {
  console.log(values, formState);
};
const handleFinishFailed = (errors) => {
  console.log(errors);
};
const blurChange = async (data, row) => {
  const params = {
    zoneProductId: row.zoneProductId,
    zoneProductSort: data,
  };
  const res = await GetProdUpdate(params);
  if (res.code === 0) {
    message.success("修改成功");
    getList();
    return;
  }
};
const handleValidate = (...args) => {
  console.log(args);
};
const prodColumns = [
  {
    title: "三方商品ID",
    dataIndex: "thirdProductId",
    key: "thirdProductId",
    fixed: "left",
    width: 180,
  },
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    width: 260,
  },
  {
    title: "售价",
    dataIndex: "goldPrice",
    key: "goldPrice",
    width: 260,
    // customRender: ({ record }) => {
    //   return `¥ ${record.price} + ¥ ${record.goldCoin} 金币 `
    // },
  },
  {
    title: "库存",
    width: 150,
    customRender: ({ record }) => {
      if (record.totalStocks != undefined) {
        return record.totalStocks;
      } else {
        return record.inventory;
      }
    },
  },
];
const formList = [
  {
    label: "商品ID",
    name: "productId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "三方商品ID",
    name: "thirdProductId",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品名称",
    name: "productName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "状态",
    name: "status",
    type: "select", // 输入框
    span: 6,
    options: [
      {
        label: "下架",
        value: 0,
      },
      {
        label: "上架",
        value: 1,
      },
    ],
  },
  {
    label: "添加方式",
    name: "addType",
    type: "select", // 输入框
    span: 6,
    options: [
      {
        label: "自动添加",
        value: 0,
      },
      {
        label: "手动添加",
        value: 1,
      },
    ],
  },
];

const handleSearch = (param) => {
  formData.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const actionClick = (data) => {
  selectData.value = data;
  upShow.value = true;
};

const handleUpOk = async () => {
  const ids = selectData.value.map((v) => {
    return v.prodId;
  });
  const params = {
    productIds: ids,
    zoneId: route.query.zoneId,
  };
  const res = await GetProductAddList(params);
  if (res.code === 0) {
    message.success("操作成功");
    upShow.value = false;
    isRefresh.value = true;
    getList();
  }
};

const closeUpClick = () => {
  upShow.value = false;
};

const addSubmitClick = async (data) => {
  console.log(data, "data123");
  if (data.length > 1) {
    message.error("只能选择一个商品");
    return;
  }
  proDetail.value = data[0];
  selectData.value = data;
  addProdVisible.value = true;
  // const ids = data.map((v)=>{
  //   return v.prodId
  // })
  // const params = {
  //   prodIdList:ids,
  //   seckillId:route.query.coinSeckillId
  // }
  // const res = await GetProdSave(params);
  // if(res.code === 0){
  //   addShow.value = false;
  //   getList();
  // }
};

const submitClick = () => {
  formRef.value
    .validate()
    .then(async () => {
      console.log(proDetail.value, "proDetail.value");
      const {
        goldCoin,
        prodId,
        productName,
        productPic,
        costPrice,
        oriPrice,
        zoneProductId,
        price,
        amountPrice,
        coinPrice,
      } = proDetail.value;
      let params = {};
      if (isEdit.value) {
        params["amountPrice"] = Number(amountPrice);
        params["coinPrice"] = Number(coinPrice);
        params["costPrice"] = Number(costPrice);
        params["oriPrice"] = Number(oriPrice);
        params["productPic"] = productPic;
        params["zoneProductId"] = zoneProductId;
        params["productName"] = productName;
      } else {
        params["productId"] = prodId;
        params["zoneId"] = route.query.zoneId;
        params["coinPrice"] = Number(goldCoin);
        params["amountPrice"] = Number(price);
      }
      const res = isEdit.value
        ? await GetProdUpdate(params)
        : await GetProductAdd(params);
      if (res.code === 0) {
        addProdVisible.value = false;
        message.success("操作成功");
        if (isEdit.value) {
          getList();
        } else {
          isRefresh.value = true;
        }
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

//table表头数据
const columns = [
  {
    title: "商品ID",
    dataIndex: "productId",
    key: "productId",
    fixed: true,
    align: "center",
    width: 220,
  },
  {
    title: "三方商品ID",
    dataIndex: "thirdProductId",
    key: "thirdProductId",
    align: "center",
    width: 200,
  },
  {
    title: "商品信息",
    dataIndex: "productName",
    key: "productName",
    align: "center",
    width: 320,
  },
  {
    title: "状态",
    align: "center",
    width: 150,
    customRender: ({ record }) => {
      if (record.status === 1) {
        return "上架";
      } else {
        return "下架";
      }
    },
  },
  {
    title: "添加方式",
    align: "center",
    width: 150,
    customRender: ({ record }) => {
      if (record.addType === 0) {
        return "自动添加";
      } else {
        return "手动添加";
      }
    },
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
    key: "updateTime",
    align: "center",
    width: 150,
  },
  {
    title: "销售价",
    align: "center",
    width: 150,
    customRender: ({ record }) => {
      return `¥${record.amountPrice}`;
    },
  },
  {
    title: "结算价",
    align: "center",
    width: 150,
    customRender: ({ record }) => {
      return `¥${record.costPrice}`;
    },
  },
  {
    title: "市场价",
    align: "center",
    width: 150,
    customRender: ({ record }) => {
      return `¥${record.oriPrice}`;
    },
  },
  {
    title: "参与活动",
    dataIndex: "activityDesc",
    key: "activityDesc",
    align: "center",
    width: 150,
  },
  {
    title: "三方库存",
    dataIndex: "stock",
    key: "stock",
    align: "center",
    width: 150,
  },
  {
    title: "排序",
    dataIndex: "zoneProductSort",
    key: "zoneProductSort",
    align: "center",
    width: 150,
  },
  {
    title: "操作",
    key: "dynamicsAct",
    fixed: "right",
    width: 300,
    // actionType: [
    //   {
    //     type: "off",
    //     title: "下架",
    //     isPop: true,
    //   },
    //   {
    //     type: "edit",
    //     title: "编辑",
    //     isPop: false,
    //   },
    //   {
    //     type: "delete",
    //     title: "删除",
    //     isPop: true,
    //   },
    // ],
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const isEdit = ref(false);
const addType = ref(1);
const getList = async () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    zoneId: route.query.zoneId,
    ...formData.value,
  };
  const res = await GetPageSpecialQuery(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const addClick = () => {
  isEdit.value = false;
  addProdShow.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  console.log(data, "data123");
  addType.value = data.addType;
  if (type === "delete") {
    deleteApi(data.zoneProductId);
  } else if (type === "edit") {
    isEdit.value = true;
    getDetailApi(data.zoneProductId);
  } else if (type === "off") {
    getUpdateApi(data);
  }
};

const getDetailApi = async (zoneProductId) => {
  const res = await GetZoneProdDetail({ zoneProductId });
  if (res.code === 0) {
    proDetail.value = res.data;
    addProdVisible.value = true;
  }
};

const getUpdateApi = async (data) => {
  const params = {
    status: data.status === 0 ? 1 : 0,
    zoneProductId: data.zoneProductId,
  };
  const res = await GetProdUpdate(params);
  if (res.code === 0) {
    message.success("操作成功");
    getList();
    return;
  }
};

const editClick = async () => {
  isEdit.value = true;
  addShow.value = true;
};

const deleteApi = async (id) => {
  const res = await GetZoneProdDelete({ zoneProductId: id });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
    return;
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  console.log(newPagination, "newPagination");
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
  console.log(pagination, "pagination");
};

onMounted(() => {
  getClassfity();
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.pic-css {
  width: 100px;
  height: 100px;
}
.add-form-css {
  padding: 30px 0;
}
</style>
