export const FILTER_CONFIG = [
  {
    type: 'input',
    label: '页面标题',
    prop: 'pageName',
  },
  {
    type: 'select',
    label: '创建人',
    prop: 'createUserId',
    keys: {
      label: 'username',
      value: 'userId',
    },
    filterable: true,
    options: [],
  },
  {
    type: 'timePicker',
    label: '创建日期',
    prop: 'createTime',
  },
  {
    type: 'timeRangePicker',
    label: '更新日期',
    prop: 'updateTime',
  },
  {
    type: 'select',
    label: '页面类型',
    prop: 'pageType',
    filterable: true,
    options: [],
  },
  {
    type: 'select',
    label: '模板',
    prop: 'pageSubTypeList',
    filterable: true,
    options: [],
  },
  {
    type: 'custom',
    label: '自定义筛选项-1',
    prop: 'shopName',
  },
  {
    type: 'custom',
    label: '自定义筛选项-2',
    prop: 'shopName',
  },
];

export const ORDER_COLUMNS = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 30,
  },
  {
    title: '页面标题',
    colKey: 'pageName',
  },
  {
    title: '创建人',
    colKey: 'createUserName',
  },
  {
    title: '创建时间',
    colKey: 'createTime',
    width: 146,
  },
  {
    title: '更新时间',
    colKey: 'updateTime',
    width: 146,
  },
  {
    title: '类型',
    colKey: 'pageTypeName',
  },
  {
    title: '模板',
    colKey: 'pageSubTypeName',
  },
  {
    title: '页面状态',
    colKey: 'pageStatus',
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 160,
    fixed: 'right',
    align: 'center',
  },
];
