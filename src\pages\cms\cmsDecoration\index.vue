<template>
  <search-table
    ref="searchTableRef"
    :filters="filters"
    :columns="columns"
    :api="api"
    :tableSlots="tableSlots"
    @get-selected-rows="getSelectedRows"
  >
    <template #filter="{ formData, prop }">
      <a-input v-model:value="formData[prop]" />
    </template>
    <template #filterBtn>
      <a-button>自定义按钮-1</a-button>
      <a-button>自定义按钮-2</a-button>
    </template>
    <template #tableBtn="{ row }">
      <a-button>自定义按钮-1</a-button>
      <a-button>自定义按钮-2</a-button>
    </template>
    <template #table="{ row, prop }">
      <div v-if="prop === 'pageStatus'">
        <a-tag v-if="row.pageStatus === 'PUBLISHED'">{{ row.pageStatus }}</a-tag>
        <a-tag v-else>{{ row.pageStatus }}</a-tag>
      </div>
      <div v-if="prop === 'operate'">
        <a-button type="primary" @click="handleEdit(row)">编辑</a-button>
        <a-button type="danger" @click="handleDelete(row)">删除</a-button>
      </div>
    </template>
  </search-table>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { FILTER_CONFIG, ORDER_COLUMNS } from './config'; // 引入配置文件
import { basePage2 } from '@/api/cms/pageLayout/index'; // 引入列表查询接口

const filters = ref(FILTER_CONFIG); // 筛选区域配置
const columns = ORDER_COLUMNS; // table_columns 配置
const api = basePage2; // 接口
const tableSlots = ['operate', 'pageStatus'] // table需要自定义的column

// 动态获取select下拉菜单
const getCreaterArr = () => {
  // mock接口 动态获取创建人筛选项的下拉列表
  var mockArr = [ { username: 'zhangsan', userId: '1' }, { username: 'lisi', userId: '2' }]  
  filters.value.find((item) => item.prop === 'createUserId').options = mockArr
}
onMounted(() => {
  getCreaterArr()
})

// 获取已选中的行
const selectedRows = ref([])
const getSelectedRows = (val) => {
  console.log('已选中的行：', val.selectedIds, val.selectedRows);
  selectedRows.value = val.selectedIds // 获取已选中的行id
}

// 获取组件实例
const searchTableRef = ref(null)

const handleEdit = (row) => {
  console.log('获取到当前行的数据', row);

  // 调用组件实例event，刷新列表
  // searchTableRef.value.refreshTable()
}
const handleDelete = (row) => { 
  console.log('删除的数据id', row.id);
  
  // 调用组件实例event，刷新列表
  // searchTableRef.value.refreshTable()
}
</script>

<style scoped lang="less">
</style>
