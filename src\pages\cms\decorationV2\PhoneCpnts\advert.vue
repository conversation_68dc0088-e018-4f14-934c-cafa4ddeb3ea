<template>
  <div class="aduert-Image">
    <template
      v-if="
        info.list.length == 0 || (info.list.length == 1 && !info.list[0].imgUrl)
      "
    >
      <div class="aduert-Image-default">
        <img
          :src="`${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/13/loop_1749781776665.png`"
        />
      </div>
    </template>
    <template v-else>
      <template v-if="info.list.length > 1">
        <a-carousel
          autoplay
          :dots="false"
          :class="scene !== 'firstScreen' ? 'aduert-Image-swiper-hd' : 'aduert-Image-swiper'"
        >
          <div
            v-for="(item, index) in info.list"
            :key="index"
            :style="{ background: info.bkChange ? item.bgColor : '' }"
          >
            <img v-if="item.imgUrl" :src="item.imgUrl" />
          </div>
        </a-carousel>
      </template>
      <template v-if="info.list.length === 1">
        <div
          :style="{ background: info.bkChange ? info.list[0].bgColor : '' }"
          :class="
            scene != 'firstScreen' ? 'aduert-Image-hd' : 'aduert-Image-sy'
          "
        >
          <img :src="info.list[0].imgUrl" />
        </div>
      </template>
    </template>
  </div>
</template>
<script setup lang="ts">
const { VITE_API_IMG_CMS } = import.meta.env;
defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  scene: {
    type: String,
    default: "",
  },
});
</script>
<style lang="less" scoped>
@import "../css/advertPhone.less";
</style>
