<template>
  <div class="brand-container">
    <template v-if="brandList.length != 0">
      <div
        v-for="(item, index) in brandList"
        :key="index"
        class="container-center"
      >
        <div class="brand-card-cell-header">
          <div class="brand-icon-box">
            <img :src="item.brandUrl" />
          </div>
          <div class="brand-right-box">
            <div class="brand-enter-box">
              <div class="brand-shop-name">
                <span>{{ item.brand }}</span>
                <img
                  :src="`${VITE_API_IMG}/2024/10/d40fdcf5f6bb4557be476d4c17721bf0.png`"
                />
              </div>
              <div class="brand-distance-day">
                <span class="day-one">距结束</span>
                <template v-if="distanceDay(item.endDate)?.days">
                  <div class="day-num">
                    {{ distanceDay(item.endDate)?.days }}
                  </div>
                </template>
                <template v-if="distanceDay(item.endDate)?.hours">
                  <div class="day-num">
                    {{ distanceDay(item.endDate)?.hours }}
                  </div>
                </template>
                <span class="day-t">{{
                  distanceDay(item.endDate)?.days ? "天" : "时"
                }}</span>
                <template v-if="distanceDay(item.endDate)?.minutes">
                  <div class="day-num">
                    {{ distanceDay(item.endDate)?.minutes }}
                  </div>
                </template>
                <span v-if="distanceDay(item.endDate)?.minutes" class="day-t"
                  >分</span
                >
              </div>
            </div>
            <div class="brand-distance-end-tips">
              <div class="brand-distance-label">
                <template v-for="(sup, supIndex) in item.promoTypeList" :key="supIndex">
                  <template v-if="sup == '特卖价'">
                    <img
                      :src="`${VITE_API_IMG}/2024/08/26cbba6ca17845ab94dcefabfb81d389.png`"
                    />
                  </template>
                  <template v-if="sup == '秒杀'">
                    <img
                      :src="`${VITE_API_IMG}/2024/08/cc05f06cf0fc4171ac1aba38d727c7de.png`"
                    />
                  </template>
                  <template v-if="sup == '一口价'">
                    <img
                      :src="`${VITE_API_IMG}/2024/08/c9264f162a1c4e87aec56b849116953b.png`"
                      style="width: 56px"
                    />
                  </template>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div class="prod-card-list-body">
          <div
            v-for="(it, idx) in item.productList"
            :key="idx"
            class="card-item-box"
          >
            <img class="prod-image" :src="it.pic" />
            <div class="prod-card-info">
              <div class="prod-card-price">
                <div class="price-one">¥</div>
                <div class="price-two">
                  {{ String(it.price).split(".")[0] }}
                </div>
                <div class="price-one">
                  .{{
                    String(it.price).split(".")[1]
                      ? String(it.price).split(".")[1]
                      : "00"
                  }}
                </div>
              </div>
              <div class="price-lineation">¥{{ it.oriPrice }}</div>
            </div>
          </div>
          <view class="card-item-box">
            <img
              :src="`${VITE_API_IMG}/2024/09/1076dd8d3e744cbb98b0ffbeae82a2a7.png`"
              class="prod-image"
              style="margin-top: -24px"
            />
          </view>
        </div>
      </div>
    </template>
    <!-- 缺省图 -->
    <template v-if="brandList.length === 0">
      <div class="brand-Image-default">
        <img
          :src="`${VITE_API_IMG}/2024/08/26af98c38b3f4ae68c2efa3c25eb001f.png`"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, watchEffect, computed } from "vue";
import dayjs from "dayjs";
import { akcPageBrandList } from "@/api/cms/decoration/index";

// 定义props
const props = defineProps({
  brandInfo: {
    type: Object,
    default: () => ({})
  }
});

// 品牌列表
const brandList = ref<any[]>([]);

// 计算品牌配置
const brandConfig = computed(() => {
  if (props.brandInfo) {
    return {
      type: props.brandInfo.type,
      platCategoryId: props.brandInfo.platCategoryId,
      platCategoryName: props.brandInfo.platCategoryName,
      brands: props.brandInfo.brands || [],
      page: 1,
      size: 50
    };
  }
  return null;
});

// 修改时间
const distanceDay = (Date) => {
  const endDate = dayjs(Date);
  const days = endDate.diff(dayjs(), "day");
  if (days < 1) {
    const diffInHours = endDate.diff(dayjs(), "hour");
    const diffInMinutes = endDate.diff(dayjs(), "minute") - diffInHours * 60;
    const hours =
      diffInHours > 0 ? diffInHours.toString().padStart(2, "0") : "00";
    const minutes =
      diffInMinutes > 0 ? diffInMinutes.toString().padStart(2, "0") : "00";
    return { hours, minutes };
  }
  return { days: days.toString().padStart(2, "0") };
};

// 查询品牌列表
const httpAkcPageBrandList = async () => {
  try {
    if (!brandConfig.value) {
      brandList.value = [];
      return;
    }
    
    const res = await akcPageBrandList(brandConfig.value);
    brandList.value = res.data.records;
  } catch (error) {
    brandList.value = [];
  }
};

watchEffect(() => {
  if (brandConfig.value?.platCategoryId || brandConfig.value?.brands?.length > 0) {
    httpAkcPageBrandList().catch(() => {
      brandList.value = [];
    });
  } else {
    brandList.value = [];
  }
});
</script>

<style lang="less" scoped>
@import "../../css/brandPhone.less";
</style>
