<template>
  <div class="goods-component-preview">
    <template v-if="goodsList.length != 0">
      <div class="goods-content">
        <div class="goods-nav-tap">
          <div class="nav-tap-zh">综合</div>
          <div class="nav-tap-price">
            <span>价格</span>
            <img :src="priceSortPng" />
          </div>
          <div class="nav-tap-new">上新</div>
        </div>
      <!-- 一行一个 -->
      <template v-if="goodsConfig?.style == 1">
        <template v-for="(item, index) in goodsList" :key="index">
          <div v-if="item.type === 0" class="goods-rowOne">
            <div class="goods-rowOne-left">
              <img class="rowOne-left-Img" :src="item.productCard.pic" />
            </div>
            <div class="goods-rowOne-right">
              <div class="goods-rowOne-top">
                <div class="goods-title-line">
                  <div class="goods-name">{{ item.productCard.prodName }}</div>
                  <div
                    v-if="item.productCard.buyingPointTags"
                    class="goods-title"
                  >
                    {{
                      item.productCard.buyingPointTags.split(",").join(" | ")
                    }}
                  </div>
                </div>
                <div class="goods-price">
                  <div class="goods-price-left">
                    <div class="price-unit">¥</div>
                    <div class="price-unit-num">
                      {{ String(item.productCard.price).split(".")[0] }}
                    </div>
                    <div class="price-unit">
                      .{{
                        String(item.productCard.price).split(".")[1]
                          ? String(item.productCard.price).split(".")[1]
                          : "00"
                      }}
                    </div>
                    <div v-if="item.productCard.unit" class="price-tag">
                      /{{ item.productCard.unit }}
                    </div>
                    <div class="price-Scribing">
                      ¥{{ item.productCard.oriPrice }}
                    </div>
                  </div>
                  <div class="goods-price-right">
                    <img
                      src="https://img.zsjming.com/2023/11/6f7296ba797344d6b9cae2efb2586a4b.png"
                    />
                  </div>
                </div>
              </div>
              <div class="goods-shop">
                <img :src="item.productCard.shopImgUrl" />
                <span>
                  {{
                    item.productCard.shopName
                      ? item.productCard.shopName
                      : "-------------------"
                  }}</span
                >
              </div>
            </div>
          </div>
        </template>
      </template>
      <!-- 一行两个 -->
      <template v-if="goodsConfig?.style == 2">
        <div class="goods-container">
          <div class="goods-container-left">
            <template v-for="(item, index) in goodsLeftList" :key="index">
              <!-- 图片配置 -->
              <template v-if="item.type == 1">
                <div class="goods-item-box">
                  <a-image
                    :src="item.imgCard.imgs[0].imgUrl"
                    :lazy="true"
                    :fit="'fill'"
                    :position="'center'"
                    :style="{
                      width: '100%',
                      height: 'auto',
                      background: '#Ffffff',
                    }"
                  />
                </div>
              </template>
              <!-- 商品配置 -->
              <template v-if="item.type == 0">
                <div class="goods-column-container">
                  <div class="goods-item-box-content">
                  <div class="goods-item-box-wd">
                    <a-image
                      :src="item.productCard.pic"
                      :lazy="true"
                      :fit="'contain'"
                      :position="'center'"
                      :style="{
                        width: '100%',
                        aspectRatio: '1',
                        background: '#Ffffff',
                      }"
                    />
                  </div>
                  <div class="goods-item-box-details">
                    <div class="goods-name-box">
                      {{ item.productCard.prodName }}
                    </div>
                    <template v-if="item.productCard.buyingPointTags">
                      <div class="prod-buy-point-tags">
                        {{
                          item.productCard.buyingPointTags
                            .split(",")
                            .join(" | ")
                        }}
                      </div>
                    </template>
                    <div class="goods-price-box">
                      <div class="goods-price-left">
                        <div class="goods-price-unit-group">
                          <div class="goods-price-unit">¥</div>
                          <div class="goods-price-text">
                            {{ String(item.productCard.price).split(".")[0] }}
                          </div>
                          <div class="goods-price-unit">
                            .{{
                              String(item.productCard.price).split(".")[1]
                                ? String(item.productCard.price).split(".")[1]
                                : "00"
                            }}
                          </div>
                        </div>
                        <div class="crossedPrice">
                          ¥{{ item.productCard.oriPrice }}
                        </div>
                      </div>
                      <div class="shop-car-fix">
                        <img
                          src="https://img.zsjming.com/2023/11/6f7296ba797344d6b9cae2efb2586a4b.png"
                        />
                      </div>
                    </div>
                    <div class="supply-shop-box">
                      <img :src="item.productCard.shopImgUrl" />
                      <span>
                        {{
                          item.productCard.shopName
                            ? item.productCard.shopName
                            : "-------------------"
                        }}</span
                      >
                    </div>
                  </div>
                  </div>
                </div>
              </template>
            </template>
          </div>
          <div class="goods-container-right">
            <template v-for="(item, index) in goodsRightList" :key="index">
              <!-- 图片配置 -->
              <template v-if="item.type == 1">
                <div class="goods-item-box">
                  <a-image
                    :src="item.imgCard.imgs[0].imgUrl"
                    :lazy="true"
                    :fit="'fill'"
                    :position="'center'"
                    :style="{
                      width: '100%',
                      height: 'auto',
                      background: '#Ffffff',
                    }"
                  />
                </div>
              </template>
              <!-- 商品配置 -->
              <template v-if="item.type == 0">
                <div class="goods-column-container">
                  <div class="goods-item-box-content">
                  <div class="goods-item-box-wd">
                    <a-image
                      :src="item.productCard.pic"
                      :lazy="true"
                      :fit="'contain'"
                      :position="'center'"
                      :style="{
                        width: '100%',
                        aspectRatio: '1',
                        background: '#Ffffff',
                      }"
                    />
                  </div>
                  <div class="goods-item-box-details">
                    <div class="goods-name-box">
                      {{ item.productCard.prodName }}
                    </div>
                    <template v-if="item.productCard.buyingPointTags">
                      <div class="prod-buy-point-tags">
                        {{
                          item.productCard.buyingPointTags
                            .split(",")
                            .join(" | ")
                        }}
                      </div>
                    </template>
                    <div class="goods-price-box">
                      <div class="goods-price-left">
                        <div class="goods-price-unit-group">
                          <div class="goods-price-unit">¥</div>
                          <div class="goods-price-text">
                            {{ String(item.productCard.price).split(".")[0] }}
                          </div>
                          <div class="goods-price-unit">
                            .{{
                              String(item.productCard.price).split(".")[1]
                                ? String(item.productCard.price).split(".")[1]
                                : "00"
                            }}
                          </div>
                        </div>
                        <div class="crossedPrice">
                          ¥{{ item.productCard.oriPrice }}
                        </div>
                      </div>
                      <div class="shop-car-fix">
                        <img
                          src="https://img.zsjming.com/2023/11/6f7296ba797344d6b9cae2efb2586a4b.png"
                        />
                      </div>
                    </div>
                    <div class="supply-shop-box">
                      <img :src="item.productCard.shopImgUrl" />
                      <span>
                        {{
                          item.productCard.shopName
                            ? item.productCard.shopName
                            : "-------------------"
                        }}</span
                      >
                    </div>
                  </div>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </template>
      </div>
    </template>
    <template v-if="goodsList.length === 0">
      <div class="goods-Image-default">
        <img
          :src="`${VITE_API_IMG}/2024/09/c4260f883cda4ed7ad72569e2c807da7.png`"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { feCategorySearchPage } from "@/api/cms/decoration/index";
import priceSortPng from "@/assets/images/price_sort.png";

const { VITE_API_IMG } = import.meta.env;

const props = defineProps({
  categoryInfo: {
    type: Object,
    required: true,
  },
});

// 不再需要decorationStore

// 商品列表
const goodsList = ref<any[]>([]);
const goodsLeftList = ref<any[]>([]);
const goodsRightList = ref<any[]>([]);

// 获取商品组件配置
const goodsConfig = computed(() => {
  // 直接使用传递的分类信息
  if (props.categoryInfo?.feCategoryId) {
    const style = props.categoryInfo.style || 2; // 从传递的参数获取样式，默认一行两个
    return {
      feCategoryId: props.categoryInfo.feCategoryId,
      feCategoryName: props.categoryInfo.feCategoryName,
      // 使用传递的样式配置
      sortType: 'DEFAULT',
      showType: style === 1 ? 'ONE_ROW' : 'TWO_ROW',
      showPrice: true,
      showName: true,
      style: style, // 使用传递的样式
    };
  }

  return null;
});

// 记录上一次的feCategoryId，避免重复请求
const lastFeCategoryId = ref<string | null>(null);

// 获取商品接口
const httpFeCategorySearchPage = async () => {
  try {
    if (!goodsConfig.value?.feCategoryId) {
      goodsList.value = [];

      // 即使没有商品分类，也要显示图片组
      const allItems = [];
      if (props.categoryInfo?.imgGroup && Array.isArray(props.categoryInfo.imgGroup)) {
        props.categoryInfo.imgGroup.forEach((imgGroup) => {
          if (imgGroup.imgs && imgGroup.imgs.length > 0) {
            const validImgs = imgGroup.imgs.filter(img => img.imgUrl);
            if (validImgs.length > 0) {
              allItems.push({
                type: 1, // 图片类型
                imgCard: {
                  imgs: validImgs
                }
              });
            }
          }
        });
      }

      goodsLeftList.value = allItems.filter((_item, index) => index % 2 === 0);
      goodsRightList.value = allItems.filter((_item, index) => index % 2 !== 0);
      return;
    }

    const params = {
      ...goodsConfig.value,
      page: 1,
      size: 50,
    };

    const res = await feCategorySearchPage(params);

    goodsList.value = res.records || [];

    // 合并图片组数据
    const allItems = [...goodsList.value];

    // 如果有图片组数据，添加到列表中
    if (props.categoryInfo?.imgGroup && Array.isArray(props.categoryInfo.imgGroup)) {
      props.categoryInfo.imgGroup.forEach((imgGroup, index) => {
        if (imgGroup.imgs && imgGroup.imgs.length > 0) {
          // 检查是否有有效的图片URL
          const validImgs = imgGroup.imgs.filter(img => img.imgUrl);
          if (validImgs.length > 0) {
            allItems.splice(index, 0, {
              type: 1, // 图片类型
              imgCard: {
                imgs: validImgs
              }
            });
          }
        }
      });
    }

    goodsLeftList.value = allItems.filter((_item, index) => index % 2 === 0);
    goodsRightList.value = allItems.filter((_item, index) => index % 2 !== 0);
  } catch (error) {
    goodsLeftList.value = [];
    goodsRightList.value = [];
    goodsList.value = [];
    console.error("Error in httpFeCategorySearchPage:", error);
  }
};

// 使用watch替代watchEffect，并添加防抖逻辑
// 监听props.categoryInfo变化，只有当feCategoryId变化时才重新获取数据
watch(() => props.categoryInfo?.feCategoryId, (newFeCategoryId, oldFeCategoryId) => {
  if (newFeCategoryId && newFeCategoryId !== lastFeCategoryId.value) {
    lastFeCategoryId.value = newFeCategoryId;
    httpFeCategorySearchPage();
  } else if (!newFeCategoryId) {
    // 处理没有feCategoryId的情况
    goodsList.value = [];
    
    // 即使没有商品分类，也要显示图片组
    const allItems = [];
    if (props.categoryInfo?.imgGroup && Array.isArray(props.categoryInfo.imgGroup)) {
      props.categoryInfo.imgGroup.forEach((imgGroup) => {
        if (imgGroup.imgs && imgGroup.imgs.length > 0) {
          const validImgs = imgGroup.imgs.filter(img => img.imgUrl);
          if (validImgs.length > 0) {
            allItems.push({
              type: 1, // 图片类型
              imgCard: {
                imgs: validImgs
              }
            });
          }
        }
      });
    }

    goodsLeftList.value = allItems.filter((_item, index) => index % 2 === 0);
    goodsRightList.value = allItems.filter((_item, index) => index % 2 !== 0);
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
@import "../../css/goodsPhone.less";

.goods-component-preview {
  background-color: #ffffff;
  margin-top: 8px;
  width: 100%;
  max-width: 375px;
  overflow: hidden;
  box-sizing: border-box;

  // 重写商品组件的样式以适应预览容器
  :deep(.goods-content) {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 8px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  // 修复一行两个商品的布局
  :deep(.goods-rowTwo) {
    width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    box-sizing: border-box !important;
    gap: 8px !important;
  }

  :deep(.goods-rowTwo-left),
  :deep(.goods-rowTwo-right) {
    width: calc(50% - 4px) !important;
    max-width: calc(50% - 4px) !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
  }

  // 修复商品项的样式 - 模仿标准商品卡片
  :deep(.goods-item-box-content) {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin-bottom: 8px !important;
    background: #ffffff !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    border: 1px solid #f0f0f0 !important;
  }

  :deep(.goods-item-box-wd) {
    width: 100% !important;
    aspect-ratio: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #ffffff !important;

    img, .a-image {
      max-width: 100% !important;
      width: 100% !important;
      height: 100% !important;
      object-fit: cover !important;
    }
  }

  // 修复商品详情的样式
  :deep(.goods-item-box-details) {
    padding: 10px !important;
    box-sizing: border-box !important;
    background: #ffffff !important;
  }

  :deep(.goods-name-box) {
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    color: #1d2129 !important;
    line-height: 1.4 !important;
    margin-bottom: 8px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    min-height: 40px !important;
  }

  :deep(.prod-buy-point-tags) {
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 12px !important;
    color: #00A473 !important;
    margin-bottom: 8px !important;
  }

  :deep(.goods-price-box) {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    height: 34px !important;
  }

  :deep(.goods-price-box-left) {
    display: flex !important;
    align-items: baseline !important;
    flex-wrap: wrap !important;
  }

  :deep(.price-unit) {
    font-family: OPlusSans 30 !important;
    font-weight: bold !important;
    font-size: 14px !important;
    color: #f43b3b !important;
  }

  :deep(.price-unit-num) {
    font-family: OPlusSans 30 !important;
    font-weight: bold !important;
    font-size: 20px !important;
    color: #f43b3b !important;
  }

  :deep(.goods-price-Scribing) {
    font-family: Source Han Sans CN !important;
    font-weight: 400 !important;
    font-size: 12px !important;
    color: #D9D9D9 !important;
    text-decoration: line-through !important;
    margin-left: 8px !important;
  }

  // 修复底部详情区域
  :deep(.goods-item-box-details-bottom) {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-top: 8px !important;
    padding-top: 8px !important;
    border-top: 1px solid #f5f7f7 !important;
  }

  :deep(.goods-item-box-details-bottom-left) {
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
  }

  :deep(.goods-item-box-details-bottom-left-top) {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  :deep(.goods-item-box-details-bottom-left-top-left),
  :deep(.goods-item-box-details-bottom-left-top-right) {
    display: flex !important;
    align-items: center !important;
    gap: 2px !important;

    img {
      width: 12px !important;
      height: 12px !important;
    }

    span {
      font-size: 10px !important;
      color: #999 !important;
    }
  }

  :deep(.supply-shop-box) {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;

    img {
      width: 16px !important;
      height: 16px !important;
      border-radius: 2px !important;
    }

    p {
      font-size: 10px !important;
      color: #666 !important;
      margin: 0 !important;
      max-width: 60px !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
  }

  :deep(.goods-item-box-details-bottom-right) {
    img {
      width: 24px !important;
      height: 24px !important;
      background: #00A473 !important;
      border-radius: 50% !important;
      padding: 4px !important;
    }
  }

  // 修复一行一个商品的布局
  :deep(.goods-rowOne) {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  :deep(.goods-rowOne-left) {
    flex-shrink: 0 !important;
  }

  :deep(.goods-rowOne-right) {
    flex: 1 !important;
    min-width: 0 !important;
  }

  // 修复导航栏样式
  :deep(.goods-nav-tap) {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  // 一行一个商品样式 - 与标准goods组件保持一致
  :deep(.goods-rowOne) {
    border-radius: 8px !important;
    background: #ffffff !important;
    padding-top: 8px !important;
    display: flex !important;
    align-items: flex-start !important;
    padding-bottom: 8px !important;
    margin-bottom: 10px !important;
  }

  :deep(.goods-rowOne-left) {
    width: 142px !important;
    height: 142px !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    margin-left: 8px !important;

    .rowOne-left-Img {
      width: 142px !important;
      height: 142px !important;
      border-radius: 6px !important;
    }
  }

  :deep(.goods-rowOne-right) {
    width: 52% !important;
    height: 142px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    margin-left: 10px !important;
  }

  :deep(.goods-rowOne-top) {
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    height: 112px !important;
  }

  :deep(.goods-title-line) {
    .goods-name {
      font-family: PingFang SC !important;
      font-weight: 400 !important;
      font-size: 14px !important;
      color: #000000 !important;
      display: -webkit-box !important;
      -webkit-box-orient: vertical !important;
      -webkit-line-clamp: 2 !important;
      overflow: hidden !important;
    }

    .goods-title {
      font-family: PingFang SC !important;
      font-weight: 400 !important;
      font-size: 12px !important;
      color: #00a473 !important;
      padding-top: 5px !important;
    }
  }

  :deep(.goods-price) {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding-top: 4px !important;
    padding-bottom: 5px !important;

    .goods-price-left {
      display: flex !important;
      align-items: baseline !important;

      .price-unit {
        font-family: OPlusSans 30 !important;
        font-weight: bold !important;
        font-size: 14px !important;
        color: #f43b3b !important;
      }

      .price-unit-num {
        font-family: OPlusSans 30 !important;
        font-weight: bold !important;
        font-size: 20px !important;
        color: #f43b3b !important;
      }

      .price-tag {
        font-family: OPlusSans 30 !important;
        font-weight: 400 !important;
        font-size: 12px !important;
        color: #86909c !important;
      }

      .price-Scribing {
        font-family: Source Han Sans CN !important;
        font-weight: 400 !important;
        font-size: 12px !important;
        color: #d9d9d9 !important;
        text-decoration: line-through !important;
        margin-left: 4px !important;
      }
    }

    .goods-price-right {
      width: 24px !important;
      height: 24px !important;

      img {
        width: 20px !important;
        height: 20px !important;
      }
    }
  }

  :deep(.goods-shop) {
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    border-top: 1px solid #f5f7f7 !important;

    img {
      width: 19px !important;
      height: 19px !important;
      border: 1px solid #e0e8ed !important;
      border-radius: 4px !important;
    }

    span {
      font-family: Source Han Sans CN !important;
      font-weight: 400 !important;
      font-size: 12px !important;
      color: #616a66 !important;
      margin-left: 4px !important;
    }
  }

  // 一行两个商品样式 - 与标准goods组件保持一致
  :deep(.goods-container) {
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
  }

  :deep(.goods-container-left),
  :deep(.goods-container-right) {
    .goods-column-container {
      width: 172px !important;
      height: auto !important;
      border-radius: 6px !important;
      margin-bottom: 10px !important;
      overflow: hidden !important;
    }

    .goods-item-box-content {
      width: 100% !important;

      .goods-item-box-wd {
        width: 172px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        .t-image {
          width: 100% !important;
          height: auto !important;
          display: block !important;
        }
      }

      .goods-item-box-details {
        background: #ffffff !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
        padding-top: 10px !important;

        .goods-name-box {
          font-family: PingFang SC !important;
          font-weight: 400 !important;
          font-size: 14px !important;
          color: #1d2129 !important;
          display: -webkit-box !important;
          -webkit-box-orient: vertical !important;
          -webkit-line-clamp: 2 !important;
          overflow: hidden !important;
        }

        .prod-buy-point-tags {
          font-family: PingFang SC !important;
          font-weight: 400 !important;
          font-size: 12px !important;
          color: #00A473 !important;
        }

        .goods-price-box {
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
          height: 34px !important;

          .goods-price-left {
            display: flex !important;
            align-items: baseline !important;
            justify-content: space-between !important;

            .goods-price-unit-group {
              display: flex !important;
              align-items: baseline !important;

              .goods-price-unit {
                font-family: OPlusSans 30 !important;
                font-weight: bold !important;
                font-size: 14px !important;
                color: #f43b3b !important;
              }

              .goods-price-text {
                font-family: OPlusSans 30 !important;
                font-weight: bold !important;
                font-size: 20px !important;
                color: #f43b3b !important;
              }
            }

            .crossedPrice {
              font-family: Source Han Sans CN !important;
              font-weight: 400 !important;
              font-size: 12px !important;
              color: #D9D9D9 !important;
              text-align: center !important;
              margin-left: 5px !important;
              text-decoration: line-through !important;
            }
          }

          .shop-car-fix {
            width: 24px !important;
            height: 24px !important;

            img {
              width: 24px !important;
              height: 24px !important;
            }
          }
        }

        .supply-shop-box {
          display: flex !important;
          align-items: center !important;
          height: 35px !important;
          border-top: 1px solid #e0e8ed !important;
          border-radius: 4px !important;

          img {
            width: 19px !important;
            height: 19px !important;
            border: 1px solid #e0e8ed !important;
          }

          span {
            font-family: Source Han Sans CN !important;
            font-weight: 400 !important;
            font-size: 12px !important;
            color: #616a66 !important;
            margin-left: 5px !important;
            width: 85% !important;
            overflow: hidden !important;
            white-space: nowrap !important;
            text-overflow: ellipsis !important;
          }
        }
      }
    }
  }
}
</style>
