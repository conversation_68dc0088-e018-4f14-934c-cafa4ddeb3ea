<template>
  <div>
    <a-row class="nav-tab">
      <a-col :span="8">
        全部分类
        <img src="@/assets/images/icon_2.png" />
      </a-col>
      <a-col :span="8">
        附近
        <img src="@/assets/images/icon_2.png" />
      </a-col>
      <a-col :span="8">
        排序方式
        <img src="@/assets/images/icon_2.png" />
      </a-col>
    </a-row>
    <div class="main-card">
      <div class="flex-box" v-for="item in listData" :key="item.productId">
        <div class="img-css">
          <img :src="item.masterPicture" alt="" />
        </div>

        <div class="title-box">
          <!-- <img src="@/assets/images/icon_1.png" /> -->
          <div class="title">{{item.productName}}</div>
          <div class="desc">
            <span class="span1">{{item.shopAddress}}</span>
            <span class="address">24.5km</span>
          </div>
          <div class="price-div">
            <div class="money">
              <span>¥25.01</span>
              <span class="small">¥50.00</span>
            </div>
            <div class="money">低至5.9折</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch,toRaw } from 'vue';
  const value = ref<number>(2);
  import { feCategorySearchProduct } from '@/api/cms/decoration/index';

  // 默认经纬度常量(默认取绿地中心坐标)
  const DEFAULT_LONGITUDE = 121.12475440394053; // 经度
  const DEFAULT_LATITUDE = 31.161143842538333; // 纬度

  const props = defineProps({
    groupInfo: {
      type: Object,
      required: true,
    },
  });

  const longitude = ref(DEFAULT_LONGITUDE)// 经度
  const latitude = ref(DEFAULT_LATITUDE)// 纬度

  const listData = ref([]);
  const httpFeCategorySearchProduct = async () => {
    const { uriList } = props.groupInfo;
    console.log(toRaw(uriList),'uriList')
    const params = {
      latitude: latitude.value,
      longitude: longitude.value,
      firstCategoryId:toRaw(uriList) && toRaw(uriList).length > 0 ? toRaw(uriList)[0] : null,
      secondCategoryId:toRaw(uriList) && toRaw(uriList).length > 1 ? toRaw(uriList)[1] : null,
      thirdCategoryId:toRaw(uriList) && toRaw(uriList).length > 2 ? toRaw(uriList)[2] : null,
      size: 100,
      page: 1,
    };
    const res = await feCategorySearchProduct(params);
    if(res.code === 0){
      listData.value = res.data.records;
    }
  };
  watch(
    () => props.groupInfo,
    (newFeCategoryId, oldFeCategoryId) => {
      console.log(props.groupInfo, '查看数据');
      httpFeCategorySearchProduct();
    },
    { immediate: true },
  );
</script>
<style scoped lang="less">
  .nav-tab {
    text-align: center;
    img {
      height: 14px;
    }
  }
  .main-card {
    .flex-box {
      display: flex;
      flex-direction: row;
      margin: 12px 12px;
      .img-css {
        width: 104px;
        height: 104px;
        border-radius: 0px 0px 0px 0px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title-box {
        width: 220px;
        margin-left: 12px;
        .title {
          line-height: 25px;
          padding-bottom: 0;
          width: 95%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .desc {
          line-height: 20px;
          color: #666666;
          font-size: 12px;
          margin-top: 5px;
          .span1 {
            width: 80%;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            display: inline-block;
          }
          .address {
            width: 20%;
            color: #333333;
            font-size: 12px;
            float: right;
          }
        }
        .price-div {
          border-radius: 40px;
          height: 45px;
          background: url('https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/14/qianggouBg_1752496356983.png?wdISI={%22imageWidth%22:427,%22imageHeight%22:80}')
            no-repeat;
          background-size: 100% 100%;
          width: 220px;
          position: relative;
          .money {
            color: #f43b3b;
            font-size: 13px;
            line-height: 18px;
            font-weight: 500;
            margin-left: 20px;
            padding-top: 3px;
            .small {
              margin-left: 10px;
              font-size: 12px;
              text-decoration: line-through;
            }
          }
        }
      }
    }
  }
</style>
