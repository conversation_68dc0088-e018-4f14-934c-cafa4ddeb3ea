<template>
  <div>
    <a-row class="nav-tab">
      <a-col :span="8">
        全部分类
        <img src="@/assets/images/icon_2.png" />
      </a-col>
      <a-col :span="8">
        附近
        <img src="@/assets/images/icon_2.png" />
      </a-col>
      <a-col :span="8">
        排序方式
        <img src="@/assets/images/icon_2.png" />
      </a-col>
    </a-row>
    <div class="main-card">
      <div class="flex-box" v-for="item in listData" :key="item.shopId">
        <div class="img-css">
          <img :src="item.shopLogo" alt="" />
        </div>
        <div class="title-box">
          <div class="title">{{ item.shopName }}</div>
          <div class="rate-container">
            <span v-for="(star, index) in getStarArray(item.evaluateScore)" :key="index" class="star-item">
              <img :src="star" />
            </span>
            <span class="score-text">{{ formatScore(item.evaluateScore) }}</span>
          </div>
          <div class="desc">{{ item.shopAddress }}</div>
          <div class="point-box" v-if="item.shopPaymentRatios && item.shopPaymentRatios.length > 0">
            <span class="point-css" v-for="(val, idx) in item.shopPaymentRatios" :key="idx">
              <span class="lab">积</span>
              <span class="val">{{ val.ratio }}%</span>
            </span>
          </div>
        </div>
        <div class="right-box">
          <div class="title">{{ item.shopStatus === 0 ? '停业中' : '营业中' }}</div>
          <div class="address">{{ item.distance }}{{ item.distanceUnit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { feCategorySearchShop } from '@/api/cms/decoration/index';

  // 默认经纬度常量(默认取绿地中心坐标)
  const DEFAULT_LONGITUDE = 121.12475440394053; // 经度
  const DEFAULT_LATITUDE = 31.161143842538333; // 纬度

  const props = defineProps({
    shopInfo: {
      type: Object,
      required: true,
    },
  });

  const longitude = ref(DEFAULT_LONGITUDE); // 经度
  const latitude = ref(DEFAULT_LATITUDE); // 纬度
  const listData = ref<any[]>([]);

  // 格式化评分，保留一位小数
  const formatScore = (score: number | undefined): string => {
    if (score === undefined || score === null) return '0.0';
    return score.toFixed(1);
  };

  const scoreActived =
    'https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/14/scoreActived_1752497338194.png?wdISI={%22imageWidth%22:32,%22imageHeight%22:32}';
  const scoreNoActived =
    'https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/14/scoreNoActived_1752497338192.png?wdISI={%22imageWidth%22:32,%22imageHeight%22:32}';
  const scoreHalf =
    'https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/14/scoreHalf_1752497338195.png?wdISI={%22imageWidth%22:32,%22imageHeight%22:32}';

  const getStarArray = (scoreVal: number | undefined) => {
    const result: string[] = [];
    const max = 5;
    const score = Math.max(0, Math.min(scoreVal || 0, max));
    const fullStars = Math.floor(score);
    const hasHalfStar = score - fullStars >= 0.5;

    for (let i = 0; i < max; i++) {
      if (i < fullStars) {
        result.push(scoreActived);
      } else if (i === fullStars && hasHalfStar) {
        result.push(scoreHalf);
      } else {
        result.push(scoreNoActived);
      }
    }
    return result;
  };

  const httpFeCategorySearchShop = async () => {
    const { id } = props.shopInfo;
    const params = {
      latitude: latitude.value,
      longitude: longitude.value,
      categoryId: id,
      size: 100,
      page: 1,
    };
    const res = await feCategorySearchShop(params);
    if (res.code === 0) {
      listData.value = res.data.records;
    }
  };
  watch(
    () => props.shopInfo,
    () => {
      httpFeCategorySearchShop();
    },
    { immediate: true, deep: true },
  );
</script>
<style scoped lang="less">
  .nav-tab {
    text-align: center;
    img {
      height: 14px;
    }
  }
  .main-card {
    margin-top: 10px;
    .flex-box {
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #eee;
      padding: 15px 10px;
      .img-css {
        width: 90px;
        height: 90px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title-box {
        flex: 1;
        margin-left: 12px;
        .title {
          line-height: 25px;
          padding-bottom: 0;
        }
        .rate-container {
          display: flex;
          align-items: center;
          line-height: 1;
          margin: 5px 0;
          .star-item {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 2px;
            img {
              width: 100%;
              height: 100%;
              display: block;
            }
          }
          .score-text {
            margin-left: 5px;
            color: #ff4d4f;
            font-size: 14px;
          }
        }
        .desc {
          line-height: 25px;
          color: #666666;
          font-size: 12px;
          width: 160px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          display: inline-block;
        }
        .point-box {
          height: 18px;
          display: flex;
          .point-css {
            display: inline-block;
            margin-right: 5px;
            .lab {
              color: #145c38;
              padding: 3px;
              font-size: 11px;
              background: linear-gradient(139deg, #ceffbc 0%, #7be392 100%);
            }
            .val {
              color: #438b4e;
              font-size: 11px;
              padding: 3px;
              background: #dfffe4;
            }
          }
        }
      }
      .right-box {
        width: 16%;
        .title {
          color: #d47e28;
          margin-top: 25px;
        }
        .address {
          color: #333333;
          margin-top: 10px;
          font-size: 12px;
        }
      }
    }
  }
</style>
