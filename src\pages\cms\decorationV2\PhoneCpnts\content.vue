<template>
  <div v-if="computedPhoneComponentArr && computedPhoneComponentArr.length">
    <div v-for="(item, index) in computedPhoneComponentArr" :key="index">
      <component :is="item.component" :info="item.info" :scene="scene" />
    </div>
  </div>
  <img
    v-else
    :src="`${VITE_API_IMG}/2024/09/3fa106d347324e1294f71a37bdf37106.png`"
    alt=""
    class="content-empty"
  />
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { storeToRefs } from "pinia";
import { ref, watch, watchEffect } from "vue";
import { phoneComponentArr } from "../config/phoneCfg";
import { getDecorationStore } from "@/store";
import { getDecorationDetail } from "@/api/decoration";

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);

const props = defineProps({
  ctId: {
    type: String,
    default: "",
  },
});

const computedPhoneComponentArr = ref<any>();
const scene = ref<string | null>(null);
let contentDetailData = null;

const contentId = ref("");

watch(contentId, () => {
  getDecorationInfo();
});
watchEffect(() => {
  if (!props.ctId && decorationInfo.value.components) {
    // 内容页
    contentId.value = decorationInfo.value.components.find(
      (item) => item.templateId === "content"
    ).info.id;
  } else {
    // 导航内容
    getDecorationInfo();
  }
});

function getDecorationInfo() {
  let id = "";
  if (props.ctId) {
    id = props.ctId;
  } else {
    id = contentId.value;
  }
  if (!id) return;
  getDecorationDetail({ id }).then((res) => {
    if (res.code === 0) {
      contentDetailData = res.data;
      decorationInfo.value.components.forEach((item) => {
        if (item.templateId === "content") {
          item.info = res.data;
        }
      });
      renderContent();
    }
  });
}
function renderContent() {
  // phoneComponentArr 重新排序
  const targetPhoneArr = computeTargetPhoneComponent();
  function computeTargetPhoneComponent() {
    const targetPhoneComponentArr = [];
    scene.value = contentDetailData.scene;
    contentDetailData.components.forEach((item: any, index: number) => {
      item.flagId = item.templateId + index;
      phoneComponentArr.forEach((itm: any) => {
        if (item.templateId === itm.templateId) {
          targetPhoneComponentArr.push({ ...itm, flagId: item.flagId });
        }
      });
    });
    return targetPhoneComponentArr;
  }
  targetPhoneArr.forEach((item: any) => {
    contentDetailData.components.forEach((itm: any) => {
      if (item.flagId === itm.flagId) {
        // 将toolNavs数据同步到computedPhoneComponentArr，info字段动态传入<component />
        item.info = itm.info;
      }
    });
  });
  computedPhoneComponentArr.value = targetPhoneArr;
}
</script>

<style lang="less" scoped>
.content-empty {
  width: 354px;
  display: block;
  margin: 8px auto;
}
</style>
