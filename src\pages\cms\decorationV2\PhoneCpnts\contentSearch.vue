<template>
  <div class="search-container">
    <div class="search-cpnt-box flex">
      <div style="align-items: center" class="flex">
        <img
          :src="`${VITE_API_IMG}/2024/08/52326ba530b54688b610b09bcd9198cd.png`"
          alt=""
          class="icon"
        />
        <div class="input">请输入您要搜索的商品</div>
      </div>
      <div class="btn">搜索</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
</script>

<style lang="less" scoped>
.search-container {
  padding: 6px 8px;
  user-select: none;
}
.search-cpnt-box {
  justify-content: space-between;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  :deep(.t-input) {
    border: none !important;
    box-shadow: none;
  }
  :deep(.t-input--focused) {
    border: none !important;
    box-shadow: none;
  }
  .icon {
    width: 16px;
    height: 16px;
    margin: 0 4px 0 10px;
  }
  .input {
    margin: 6px 0;
    color: #939596;
  }
  .btn {
    width: 60px;
    font-weight: 500;
    font-size: 14px;
    color: #00a473;
    text-align: center;
    border-left: 1px solid #e5ebeb;
  }
}
</style>
