<template>
  <div
    class="cube-content"
    :style="{ background: !info.isBglucency ? info.bkColor : '' }"
  >
    <!-- 一行两个 -->
    <template v-if="info.iconType == 'TWO_ROW'">
      <div v-for="(item, index) in info.list" :key="index" class="cube-row-two">
        <img v-if="item.imgUrl" :src="showImg(item.imgUrl)" />
      </div>
    </template>
    <!-- 一行三个 -->
    <template v-if="info.iconType == 'THREE_ROW'">
      <div
        v-for="(item, index) in info.list"
        :key="index"
        class="cube-row-three"
      >
        <img v-if="item.imgUrl" :src="showImg(item.imgUrl)" />
      </div>
    </template>
    <!-- 一行四个 -->
    <template v-if="info.iconType == 'FOUR_ROW'">
      <div
        v-for="(item, index) in info.list"
        :key="index"
        class="cube-row-four"
      >
        <img v-if="item.imgUrl" :src="showImg(item.imgUrl)" />
      </div>
    </template>
    <!-- 一行五个 -->
    <template v-if="info.iconType == 'FIVE_ROW'">
      <div
        v-for="(item, index) in info.list"
        :key="index"
        class="cube-row-five"
      >
        <img v-if="item.imgUrl" :src="showImg(item.imgUrl)" />
      </div>
    </template>
    <!-- 二行五个 -->
    <template v-if="info.iconType == 'TWO_FIVE_ROW'">
      <div class="two-five-fow-list">
        <div
          v-for="(item, index) in info.list"
          :key="index"
          class="two-five-fow"
        >
          <img v-if="item.imgUrl" :src="showImg(item.imgUrl)" />
        </div>
      </div>
    </template>
    <!-- 四宫格 -->
    <template v-if="info.iconType == 'FOUR_GRID'">
      <div class="cube-four-grid">
        <div class="cube-four-grid-list">
          <div
            v-for="(item, index) in info.list"
            :key="index"
            class="four-grid-list-img"
          >
            <img v-if="item.imgUrl" :src="showImg(item.imgUrl)" />
          </div>
        </div>
      </div>
    </template>
    <!-- 一左两右 -->
    <template v-if="info.iconType == 'ONE_LEFT_TWO_RIGHT'">
      <div class="cube-row-one">
        <div class="cube-row-one-list">
          <img v-if="info.list[0].imgUrl" :src="showImg(info.list[0].imgUrl)" />
        </div>
        <div class="cube-row-one-list">
          <div class="row-one-list-left">
            <div class="row-one-img">
              <img
                v-if="info.list[1].imgUrl"
                :src="showImg(info.list[1].imgUrl)"
              />
            </div>
            <div class="row-one-img">
              <img
                v-if="info.list[2].imgUrl"
                :src="showImg(info.list[2].imgUrl)"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- 一上两下 -->
    <template v-if="info.iconType == 'ONE_UP_TWO_DOWN'">
      <div class="cube-row-topButtom">
        <div class="topButtom-list">
          <img v-if="info.list[0].imgUrl" :src="showImg(info.list[0].imgUrl)" />
        </div>
        <div class="topButtom-list-tow">
          <div class="topButtom-list-img">
            <img
              v-if="info.list[1].imgUrl"
              :src="showImg(info.list[1].imgUrl)"
            />
          </div>
          <div class="topButtom-list-img">
            <img
              v-if="info.list[2].imgUrl"
              :src="showImg(info.list[2].imgUrl)"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
  <div v-if="info.list[0].imgUrl == null" class="cube-Image-default">
    <img
      :src="`${VITE_API_IMG}/2024/09/83553f2c6b1241f39100b80dbf9011ed.png`"
    />
  </div>
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { showImg } from "@/utils";

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
</script>
<style lang="less" scoped>
@import "../css/cubePhone.less";
</style>
