<template>
  <div class="king-kong-preview" v-if="detailData?.info">
    <div class="list-container" ref="listContainerRef" @scroll.passive="handleScroll">
      <div class="page" v-for="(page, pageIndex) in paginatedList" :key="pageIndex">
        <div class="item" v-for="item in page" :key="item.id">
          <div class="image-wrapper">
            <img :src="item.imgUrl || placeholderImageUrl" class="item-image" alt="金刚位图标"/>
          </div>
          <p class="item-name">{{ item.name || '示例名称' }}</p>
        </div>
      </div>
    </div>
    <div class="pagination-dots" v-if="paginatedList.length > 1">
      <span
        v-for="(_, index) in paginatedList.length"
        :key="index"
        class="dot"
        :class="{ active: currentPage === index + 1 }"
        @click="scrollToPage(index + 1)"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
import { storeToRefs } from 'pinia';
import { getDecorationStore } from '@/store';
import { debounce } from 'lodash-es';

// 默认占位图
const placeholderImageUrl = 'https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/03/placeholderImg_1751531869993.png?wdISI={%22imageWidth%22:112,%22imageHeight%22:112}';

// 连接到 Pinia Store
const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);

// 从 Store 中查找并返回当前"金刚位"组件的配置数据
const detailData = computed(() => 
  decorationInfo.value.components.find((item: any) => item.templateId === 'jinGang')
);

// 计算属性：将列表数据处理为分页后的嵌套数组，并用占位符补齐每页
const paginatedList = computed(() => {
  const list = detailData.value?.info?.list || [];
  const pages = [];
  const pageSize = detailData.value?.info?.displayCount || 10; // 使用配置的displayCount
  const totalItems = Math.max(pageSize, list.length);
  const numPages = Math.ceil(totalItems / pageSize);

  for (let i = 0; i < numPages; i++) {
    const pageItems = list.slice(i * pageSize, (i + 1) * pageSize);
    const placeholdersNeeded = pageSize - pageItems.length;
    
    const placeholderItems = Array.from({ length: placeholdersNeeded }, (_, j) => ({
      id: `placeholder-${i}-${j}`,
      imgUrl: '',
      name: '',
    }));

    pages.push([...pageItems, ...placeholderItems]);
  }
  return pages;
});

// --- 滚动与分页逻辑 ---
const listContainerRef = ref<HTMLElement | null>(null);
const currentPage = ref(1);

// 滚动到指定页面
const scrollToPage = (page: number) => {
  if (!listContainerRef.value) return;
  
  const { clientWidth } = listContainerRef.value;
  listContainerRef.value.scrollTo({
    left: (page - 1) * clientWidth,
    behavior: 'smooth'
  });
  
  currentPage.value = page;
};

const handleScroll = debounce(() => {
  if (!listContainerRef.value) return;
  const { scrollLeft, clientWidth } = listContainerRef.value;
  const newPage = Math.floor(scrollLeft / clientWidth) + 1;
  
  if (newPage !== currentPage.value) {
    currentPage.value = newPage;
  }
}, 100);

onMounted(() => {
  listContainerRef.value?.addEventListener('scroll', handleScroll);
});

onBeforeUnmount(() => {
  listContainerRef.value?.removeEventListener('scroll', handleScroll);
});
</script>

<style lang="less" scoped>
.king-kong-preview {
  padding: 12px 8px;
  background-color: #f7f8fa;
  border-radius: 12px;
  position: relative;
}

.list-container {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory; /* 启用x轴的强制滚动捕捉 */
  /* 显示横向滚动条 */
  scrollbar-width: thin; /* Firefox */
  &::-webkit-scrollbar {
    display: block; /* WebKit */
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
}

.page {
  flex: 0 0 100%;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 8px 0;
  scroll-snap-align: start; /* 设置每个页面为滚动捕捉点 */
}

.item {
  width: 20%;
  height: 86px; 
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  box-sizing: border-box;
}

.image-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.item-name {
  font-size: 12px;
  color: #333;
  margin-top: 8px;
  width: 100%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 4px;
}

.pagination-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 10px;
  margin-bottom: 4px; /* 为滚动条预留空间 */

  .dot {
    width: 6px;
    height: 4px;
    border-radius: 2px;
    background-color: #e5e6eb;
    margin: 0 2px;
    transition: all 0.3s ease-in-out;

    &.active {
      background-color: #1890ff;
      width: 12px;
    }
  }
}
</style>
