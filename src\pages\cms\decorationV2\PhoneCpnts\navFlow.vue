<template>
  <div class="nav-flow-preview" v-if="navFlowData?.info">
    <!-- 导航栏 - 只有在不是仅显示第一个时才显示 -->
    <div class="nav-list" :class="{ sliding: navFlowData.info.sliding }" v-if="!navFlowData.info.onlyDisplayFirst">
      <div
        v-for="(item, index) in displayList"
        :key="item.id"
        class="nav-item"
        :class="{ 
          placeholder: item.isPlaceholder, 
          sliding: navFlowData.info.sliding 
        }"
        @click="handleItemClick(index, item.isPlaceholder)"
      >
        <img :src="getImageUrl(item, index)" class="nav-image" alt="导航图标" />
      </div>
    </div>

    <!-- 内容展示区域 -->
    <div class="nav-content" v-if="currentNavItem && !currentNavItem.isPlaceholder">
      <!-- 商品组件预览 -->
      <GoodsComponentPreview
        v-if="currentRelatedComponent && currentRelatedComponent.templateId === 'goods'"
        :category-info="currentRelatedComponent.info"
      />
      <!-- 品牌组件预览 -->
      <BrandComponentPreview
        v-else-if="currentRelatedComponent && currentRelatedComponent.templateId === 'brand'"
        :brand-info="currentRelatedComponent.info"
      />
      <!-- 店铺组件预览 -->
      <ShopComponentPreview
        v-else-if="currentNavItem.param && currentNavItem.param.tabType === 'SHOP_COMPONENT'"
        :shop-info="currentNavItem.param"
      />
      <!-- 团购组件预览 -->
      <GroupComponentPreview
        v-else-if="currentNavItem.param && currentNavItem.param.tabType === 'GROUPBUY_COMPONENT'"
        :group-info="currentNavItem.param"
      />
      <!-- 二级页面和系统页面不支持预览 -->
      <div
        v-else-if="currentNavItem.navType && ['PAGE', 'SYSTEM_PAGE', 'CUSTOM_LINK'].includes(currentNavItem.navType)"
        class="unsupported-preview"
      >
        <img
          src="https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/05/unableToPreview_1751680608040.png?wdISI={%22imageWidth%22:184,%22imageHeight%22:184}"
          alt="不支持预览"
          class="unsupported-image"
        />
        <div class="unsupported-text">当前内容不支持预览，请在手机上查看具体效果</div>
      </div>
      <!-- 其他类型的内容可以在这里扩展 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import { getDecorationStore } from '@/store';

// 使用异步组件解决导入问题
const GoodsComponentPreview = defineAsyncComponent(() => import('./components/GoodsComponentPreview.vue'));
const BrandComponentPreview = defineAsyncComponent(() => import('./components/BrandComponentPreview.vue'));
const ShopComponentPreview = defineAsyncComponent(() => import('./components/ShopComponentPreview.vue'));
const GroupComponentPreview = defineAsyncComponent(() => import('./components/GroupComponentPreview.vue'));

// 占位图
const PLACEHOLDER_IMG_UNSELECTED = 'https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/03/navFlowHolderImg_1751545351451.png?wdISI={%22imageWidth%22:194,%22imageHeight%22:107}';
const PLACEHOLDER_IMG_SELECTED = 'https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/03/navFlowHolderImg-actived_1751545351425.png?wdISI={%22imageWidth%22:194,%22imageHeight%22:107}';

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);

// 获取当前组件的配置数据
const navFlowData = computed(() =>
  decorationInfo.value.components.find((item: any) => item.templateId === 'navFlow')
);

// 当前选中的导航索引
const activeTabIndex = ref(0);

// 获取关联组件信息
const getRelatedComponentInfo = (componentId) => {
  if (!decorationInfo.value.relatedComponents || !componentId) return null;
  
  return decorationInfo.value.relatedComponents.find(
    (item) => String(item.id) === String(componentId)
  );
};

// 当前选中的导航项
const currentNavItem = computed(() => {
  const info = navFlowData.value?.info;
  if (!info || !info.list) return null;

  const originalList = info.list || [];
  // 如果是仅显示第一个模式，始终显示第一个导航的内容
  const index = info.onlyDisplayFirst ? 0 : activeTabIndex.value;
  const item = originalList[index] || null;
  console.log(item,'查看item')
  return item;
});

// 当前选中导航项关联的组件信息
const currentRelatedComponent = computed(() => {
  if (!currentNavItem.value || !currentNavItem.value.param || !currentNavItem.value.param.componentId) {
    return null;
  }
  
  return getRelatedComponentInfo(currentNavItem.value.param.componentId);
});

// 用于渲染的列表，在初始化时会补齐到4个，配置后显示实际数量
const displayList = computed(() => {
  const info = navFlowData.value?.info;
  if (!info) return [];

  const originalList = info.list || [];
  
  // 如果列表为空或所有项都没有配置图片，则显示4个占位符
  const hasConfiguredItems = originalList.some(item => item.icon || item.iconSelected);
  
  if (!hasConfiguredItems) {
    // 初始化状态，显示4个占位符
    const placeholders = [];
    for (let i = 0; i < 4; i++) {
      placeholders.push({
        id: `placeholder-${i}`,
        isPlaceholder: true,
      });
    }
    return placeholders;
  }
  
  // 已配置状态，显示实际列表
  return originalList;
});

// 获取图片URL的逻辑
const getImageUrl = (item: any, index: number) => {
  if (item.isPlaceholder) {
    // 占位符使用未选中状态的图
    return PLACEHOLDER_IMG_UNSELECTED;
  }
  const isActive = activeTabIndex.value === index;
  if (isActive) {
    return item.iconSelected || PLACEHOLDER_IMG_SELECTED;
  }
  return item.icon || PLACEHOLDER_IMG_UNSELECTED;
};

// 点击事件处理，占位符不可点击
const handleItemClick = (index: number, isPlaceholder: boolean) => {
  if (isPlaceholder) return;
  activeTabIndex.value = index;
};
</script>

<style lang="less" scoped>
.nav-flow-preview {
  background-color: #ffffff;
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden; /* 确保内部溢出不会影响外部布局 */
}

.nav-list {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;

  &.sliding {
    justify-content: flex-start;
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
    
    /* 参考金刚位组件的滚动条样式 */
    scrollbar-width: thin; /* Firefox */
    &::-webkit-scrollbar {
      display: block;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
  }
}

.nav-content {
  margin-top: 8px;
}

.unsupported-preview {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  min-height: 300px;
}

.unsupported-image {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 16px;
  opacity: 0.6;
}

.unsupported-text {
  font-size: 14px;
  color: #999999;
  text-align: center;
  line-height: 1.5;
  max-width: 280px;
}

.nav-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 54px;
  border-radius: 8px;
  cursor: pointer;
  background-color: transparent;
  transition: background-color 0.2s ease-in-out;

  &.placeholder {
    cursor: default;
  }

  &.sliding {
    flex: 0 0 auto;
    width: 97px; /* 在滑动模式下保持固定宽度 */
    min-width: 97px; /* 确保不会被压缩 */
  }

  .nav-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style> 