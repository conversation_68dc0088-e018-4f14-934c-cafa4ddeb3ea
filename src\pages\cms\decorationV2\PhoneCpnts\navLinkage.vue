<template>
  <div class="navLinkAge-content">
    <div class="navLinkAge-frame">
      <div class="navLinkAge-top">
        <ul class="navLinkAge-top-ul" v-if="info.list[0].name">
          <template v-for="(item, index) in info.list" :key="index">
            <li @click="tapNavigation(index)">
              <div
                class="navLinkAge-top-line"
                :class="{ selectColor: index == stairIndex }"
              >
                {{ item.name }}
              </div>
              <div class="navLinkAge-line" v-if="index == stairIndex"></div>
            </li>
          </template>
        </ul>
      </div>
      <div class="navLinkAge-bottom">
        <ul class="navLinkAge-bottom-ul" v-if="childrenData.children[0].name">
          <template v-for="(item, index) in childrenData.children" :key="index">
            <li
              :class="{ secondTypeBg: index == secondTypeIndex }"
              @click="tapSecondNav(item, index)"
            >
              {{ item.name }}
            </li>
          </template>
        </ul>
      </div>
    </div>

    <!-- 兼容老数据，直接渲染组件 -->
    <template v-if="targetPhoneComponentArr.length > 0">
      <template v-for="(item, index) in targetPhoneComponentArr" :key="index">
        <component :is="item.component" :info="item.info" />
      </template>
    </template>

    <!-- V2版本根据navType区分 -->
    <div v-else-if="activeItem" class="content-area">
      <!-- 渲染关联组件 -->
      <GoodsComponentPreview
        v-if="
          activeItem.navType === 'GOODS_COMPONENT' && currentRelatedComponent
        "
        :category-info="currentRelatedComponent.info"
      />
      <BrandComponentPreview
        v-else-if="
          activeItem.navType === 'BRAND_COMPONENT' && currentRelatedComponent
        "
        :brand-info="currentRelatedComponent.info"
      />
      <ShopComponentPreview
        v-else-if="
          activeItem.navType === 'SYSTEM_CONTENT_PAGE' &&
          activeItem.param.tabType === 'SHOP_COMPONENT'
        "
        :shop-info="activeItem.param"
      />
      <GroupComponentPreview
        v-else-if="
          activeItem.navType === 'SYSTEM_CONTENT_PAGE' &&
          activeItem.param.tabType === 'GROUPBUY_COMPONENT'
        "
        :group-info="activeItem.param"
      />

      <!-- 不支持预览的提示 -->
      <div v-else-if="['PAGE', 'SYSTEM_PAGE', 'CUSTOM_LINK'].includes(activeItem.navType)" class="unsupported-preview">
        <img
          src="https://file.shwoody.com/prod/image/in_coming/life_plat/2025/07/05/unableToPreview_1751680608040.png?wdISI={%22imageWidth%22:184,%22imageHeight%22:184}"
          alt="不支持预览"
        />
        <div class="unsupported-text">
          当前内容不支持预览，请在手机上查看具体效果
        </div>
      </div>
    </div>
  </div>
  <template v-if="info.list.every((item) => !item.name)">
    <img
      :src="`${VITE_API_IMG}/2024/09/2601296f8b1a4029a1890f5f9cc01d44.png`"
    />
  </template>
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, reactive, watchEffect, computed, defineAsyncComponent } from "vue";
import { getDecorationStore } from "@/store";
import { storeToRefs } from "pinia";
import { phoneComponentArr } from "../config/phoneCfg";
import { getDecorationDetail } from "@/api/decoration";

const GoodsComponentPreview = defineAsyncComponent(
  () => import("./components/GoodsComponentPreview.vue")
);
const BrandComponentPreview = defineAsyncComponent(
  () => import("./components/BrandComponentPreview.vue")
);
const ShopComponentPreview = defineAsyncComponent(
  () => import("./components/ShopComponentPreview.vue")
);
const GroupComponentPreview = defineAsyncComponent(
  () => import("./components/GroupComponentPreview.vue")
);

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);

const childrenData = ref<any>([]);
childrenData.value = props.info.list[0];
const stairIndex = ref<string | number>(0);
const secondTypeIndex = ref<string | number>(0);
const activeItem = ref<any>(null);

const phoneComponent = reactive(phoneComponentArr);
const targetPhoneComponentArr = ref<any>([]);

const currentRelatedComponent = computed(() => {
  if (
    !activeItem.value ||
    !activeItem.value.param ||
    !activeItem.value.param.componentId ||
    !decorationInfo.value.relatedComponents
  ) {
    return null;
  }
  return decorationInfo.value.relatedComponents.find(
    (item) => String(item.id) === String(activeItem.value.param.componentId)
  );
});

const handleLink = (item) => {
  // 兼容老数据，老数据没有navType
  if (!item.navType && item.param?.id) {
    activeItem.value = null; // 清空新数据状态
    httpGetDecorationDetail(item.param.id);
  } else {
    // 新数据逻辑
    targetPhoneComponentArr.value = []; // 清空老数据状态
    activeItem.value = item;
  }
};

// 选择一级类目
const tapNavigation = (index) => {
  stairIndex.value = index;
  secondTypeIndex.value = 0;
  childrenData.value = props.info.list[index];
  handleLink(childrenData.value.children[0]);
};
// 选择二级类目
const tapSecondNav = (item, index) => {
  secondTypeIndex.value = index;
  handleLink(item);
};

// ================= 老数据兼容逻辑 start =================
const httpGetDecorationDetail = async (id) => {
  let toolNavs = [];
  const res = await getDecorationDetail({ id: id });
  toolNavs = res.data.components;
  toolNavs.forEach((item: any, index) => {
    item.flagId = item.templateId + index;
  });
  computeTargetPhoneComponent(toolNavs);
};
const computeTargetPhoneComponent = (data) => {
  const componentArr = [];
  data.forEach((item) => {
    phoneComponent.forEach((itm: any) => {
      if (item.templateId === itm.templateId) {
        componentArr.push({ ...itm, ...item, flagId: item.flagId });
      }
    });
  });
  targetPhoneComponentArr.value = componentArr;
};
// ================= 老数据兼容逻辑 end =================

watchEffect(() => {
  if (props.info.list[0]?.children?.[0]) {
    const firstItem = props.info.list[0].children[0];
    if (firstItem) {
      handleLink(firstItem);
    }
  }
});
</script>
<style lang="less" scoped>
@import "../css/navLinkagePhone.less";
.unsupported-preview {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  min-height: 200px;
  margin-top: 10px;
}

.unsupported-image {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 16px;
  opacity: 0.6;
}

.unsupported-text {
  font-size: 14px;
  color: #999999;
  text-align: center;
  line-height: 1.5;
  max-width: 280px;
}
.content-area {
  margin-top: 10px;
}
</style>
