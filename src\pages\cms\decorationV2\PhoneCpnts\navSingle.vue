<template>
  <div :style="{ background: bgColor }" class="nav-box flex">
    <div
      v-for="(item, index) in detailData.info.list"
      :key="index"
      :style="{ color: info.textColor }"
      :class="['item', { active: activeNav.id === item.id }]"
      @click="handleChooseNav(item)"
    >
      {{ item.name }}
    </div>
  </div>

  <div v-if="activeNav.param.id && isShowContent">
    <content :ct-id="activeNav.param.id" />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { getDecorationStore } from '@/store';
import content from './content.vue';

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find((item: any) => item.templateId === 'navSingle');

const info = ref<any>({});
const bgColor = ref<string>('');

watchEffect(() => {
  info.value = detailData.info || {};
  if (detailData.info && detailData.info.bgColor) {
    bgColor.value = detailData.info.isTransparent ? '#01D47B' : detailData.info.bgColor;
  } else {
    bgColor.value = detailData.info.isTransparent ? '#01D47B' : '#fff';
  }
});

// rightbar/navSingle选中导航链接后，当前项param.id更改，触发info.list的监听，
// 此时如果activeNav.param.id为空，则赋新的值
watch(
  detailData.info,
  (newValue) => {
    newValue.list.forEach((item: any) => {
      if (item.id === activeNav.value.id) {
        activeNav.value = item;
      }
    });
    if (!newValue.list.find((item: any) => item.id === activeNav.value.id)) {
      isShowContent.value = false;
    }
  },
  {
    deep: true,
  },
);

// 导航点击
const scrollEventObj = ref(null);
const activeNav = ref<any>();
activeNav.value = detailData?.info.list[0];
const handleChooseNav = (item: any) => {
  if (scrollEventObj.value) {
    scrollEventObj.value.target.scrollTop = 123;
  }
  activeNav.value = item;
};
const handleScroll = (e: any) => {
  scrollEventObj.value = e;
};
onMounted(() => {
  const phoneDom = document.getElementById('phoneDom');
  phoneDom.addEventListener('scroll', handleScroll);
});
onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll);
});

const isShowContent = ref(true);
watch(activeNav, (newVal) => {
  if (newVal.clickType === '0') {
    isShowContent.value = true;
  } else {
    isShowContent.value = false;
  }
});
</script>

<style lang="less" scoped>
.nav-box {
  width: 100%;
  padding: 18px 10px;
  overflow-x: auto;
  position: sticky;
  top: 82px;
  z-index: 10;
  .item {
    white-space: nowrap;
    margin-right: 20px;
    cursor: pointer;
    opacity: 0.65;
  }
  .active {
    font-weight: bold;
    font-size: 18px;
    opacity: 1;
  }
}
.t-tabs {
  background-color: transparent;
}
.tab-item {
  text-align: center;
  &:hover {
    background-color: none;
  }
}
/* 自定义整个滚动条 */
::-webkit-scrollbar {
  height: 6px;
  border-radius: 6px;
  /* 设置滚动条的宽度 */
  background-color: #f9f9f9;
  overflow: hidden;
  padding: 20px 0;
}
/* 自定义滚动条轨道 */
::-webkit-scrollbar-track {
  background: #e1e1e1;
  /* 轨道的背景色 */
  border-radius: 2px;
  /* 轨道的圆角 */
}

/* 自定义滚动条的滑块（thumb） */
::-webkit-scrollbar-thumb {
  background-color: #949292b6;
  /* 滑块的背景色 */
  border-radius: 6px;
  /* 滑块边框 */
}

/* 滑块hover效果 */
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
  /* 滑块hover时的背景色 */
}
</style>
