<template>
    <div>
        <a-row>
            <a-col :span="8">全部分类</a-col>
            <a-col :span="8">附近</a-col>
            <a-col :span="8">排序方式</a-col>
        </a-row>
        <div class="main-card">
            <div class="flex-box">
                <div>
                    <img src="../../../assets/images/shop.png" alt="">
                </div>
                <div>
                    <h3>78978979</h3>
                    <a-rate v-model:value="value" />
                    <p>uyioweyurtiowe</p>
                </div>
                <div>
                    <p>营业中</p>
                    <p>24.5km</p>
                </div>
            </div>
        </div>    
    </div>
</template>
<script lang='ts' setup>
import {  ref } from 'vue';
const value = ref<number>(2);
</script>
<style scoped lang='less'>
.main-card{
    .flex-box{
        display: flex;
        flex-direction: row;
        
    }
}
</style>
