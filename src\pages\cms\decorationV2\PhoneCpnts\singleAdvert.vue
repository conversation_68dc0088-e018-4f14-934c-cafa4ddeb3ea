<template>
  <div class="aduert-Image singleAd">
    <template v-if="!info.imgUrl">
      <div class="aduert-Image-default">
        <img
          :src="`${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/13/singlePre_1749781776696.png`"
        />
      </div>
    </template>
    <template v-else>
      <img style="width: 100%" :src="info.imgUrl" />

      <!-- <div
          :class="
            scene != 'firstScreen' ? 'aduert-Image-hd' : 'aduert-Image-sy'
          "
        >
          <img :src="info.imgUrl" />
        </div> -->
    </template>
  </div>
</template>
<script setup lang="ts">
const { VITE_API_IMG_CMS } = import.meta.env;
defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  scene: {
    type: String,
    default: "",
  },
});
</script>
<style lang="less" scoped>
@import "../css/advertPhone.less";
.singleAd {
  margin: 0;
}
</style>
