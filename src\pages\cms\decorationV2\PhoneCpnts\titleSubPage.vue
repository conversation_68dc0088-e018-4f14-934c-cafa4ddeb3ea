<template>
  <div style="position: fixed; z-index: 10">
    <div class="search-box flex">
      <img
        :src="`${VITE_API_IMG}/2024/08/481b9f6c7b25457bada59b1155530773.png`"
        alt=""
        class="back-icon"
      />
      <div class="title-text">{{ info.text }}</div>
      <img
        :src="`${VITE_API_IMG}/2024/08/f1c9d6683b5a4c8bb8a6f67c738d6f44.png`"
        alt=""
        class="jiaonang"
      />
    </div>
  </div>
  <div style="height: 43px"></div>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref } from "vue";
import { getDecorationStore } from "@/store";

const decorationStore = getDecorationStore();
const detailData = decorationStore.decorationInfo.components.find(
  (item: any) => item.templateId === "subPageTitle"
);

const info = ref<any>({});
info.value = detailData.info || {};
</script>

<style lang="less" scoped>
.search-box {
  width: 375px;
  height: 72px;
  align-items: center;
  justify-content: space-between;
  margin-top: -30px;
  padding: 30px 8px 10px 4px;
  background: #fff;
  border-radius: 2px 2px 0 0;
  .back-icon {
    width: 24px;
    height: 24px;
  }
  .title-text {
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    font-size: 18px;
    color: #000000;
    font-weight: bold;
    width: 211px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }
  .jiaonang {
    width: 90px;
    height: auto;
    z-index: 2;
  }
}
</style>
