<template>
  <div class="brand-container">
    <div class="brand-title">类型</div>
    <div class="brand-radio">
      <a-radio-group
        v-model:value="brandData.type"
        name="city"
        :options="brandRadioData"
        @change="brandRadioChange"
      />
    </div>
    <!-- 指定分类 -->
    <template v-if="brandData.type == 1">
      <div class="brand-goods">商品管理</div>
      <div class="brand-select-details" @click="showBrandPopupMethod">
        <div class="select-details-left">选择分类</div>
        <div class="select-details-right">
          <span v-if="brandData.platCategoryName" class="details-span">{{
            brandData.platCategoryName
          }}</span>
          <span v-if="!brandData.platCategoryName">请选择商品内容</span>
          <img
            :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
          />
        </div>
      </div>
    </template>
    <!-- 指定品牌 -->
    <template v-if="brandData.type == 2">
      <div class="brand-goods">品牌管理</div>
      <a-button type="primary" size="middle" class="mt10" @click="showBrandNamePopupMethod"
        >选择品牌</a-button
      >
      <div v-if="brandData.brands.length > 0" class="brand-info">
        <span>品牌信息</span>
        <span>最多支持100个品牌</span>
      </div>
      <draggable :sort="true" :list="brandData.brands" :animation="300">
        <template #item="{ element, index }">
          <div class="brand-list">
            <img
              v-if="!element.brandImgUrl"
              class="brand-Image"
              :src="`${VITE_API_IMG}/2024/08/b1ee6ddf0e3f4f11b32b2940fed5e693.png`"
            />
            <img
              v-if="element.brandImgUrl"
              class="brand-Image"
              :src="element.brandImgUrl"
            />
            <div class="brand-box">
              <div class="brand-box-name">
                {{
                  element.brandName ? element.brandName : "-------------------"
                }}
              </div>
            </div>
            <img
              class="brand-shut-Img"
              :src="`${VITE_API_IMG}/2024/08/b48cb30452984634ba1c6114202be146.png`"
              @click.stop="shutImage(index)"
            />
          </div>
        </template>
      </draggable>
    </template>
  </div>
  <!-- 品牌平台分类弹框 -->
  <brandSort ref="brandSortRef" @on-brand-call-back="onBrandCallBack" />
  <!-- 品牌商品名称弹框 -->
  <brandName
    ref="brandNameRef"
    @on-brand-name-call-back="onBrandNameCallBack"
  />
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { message } from "woody-ui";
import draggable from "vuedraggable";
import { brandRadioData } from "../config/index";
import { getDecorationStore } from "@/store";
import brandSort from "./components/brandSort.vue";
import brandName from "./components/brandName.vue";

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find(
  (item: any) => item.templateId == "brand"
);
const brandData = ref<any>({});
brandData.value = detailData.info;

// 获取子组件方法
type brandSortType = { showBrandSortRef: () => void };
const brandSortRef = ref<brandSortType | null>(null);
type brandNameType = { showBrandNameRef: (data) => void };
const brandNameRef = ref<brandNameType | null>(null);

const brandRadioChange = () => {
  brandData.value.platCategoryId = null;
  brandData.value.platCategoryName = null;
  brandData.value.brands = [];
};
const onBrandCallBack = (item) => {
  const { id, categoryName } = item;
  brandData.value.platCategoryId = id;
  brandData.value.platCategoryName = categoryName;
};
// 是否显示品牌平台分类组件
const showBrandPopupMethod = () => {
  if (brandSortRef.value) {
    if (typeof brandSortRef.value.showBrandSortRef === "function") {
      brandSortRef.value.showBrandSortRef();
    }
  }
};
const onBrandNameCallBack = (data) => {
  const uniqueData = [];
  const seenIds = new Set();
  data.forEach((item) => {
    brandData.value.brands.push({
      brandId: item.brandId,
      order: null,
      brandImgUrl: item.brandUrl,
      brandName: item.brandName,
    });
  });
  brandData.value.brands.forEach((item) => {
    if (!seenIds.has(item.brandId)) {
      seenIds.add(item.brandId);
      uniqueData.push(item);
    }
  });
  brandData.value.brands = uniqueData;
};
// 删除品牌信息列表
const shutImage = (index) => {
  brandData.value.brands.splice(index, 1);
};
// 是否显示品牌名称组件
const showBrandNamePopupMethod = () => {
  if (brandNameRef.value) {
    if (typeof brandNameRef.value.showBrandNameRef === "function") {
      brandNameRef.value.showBrandNameRef(brandData.value.brands);
    }
  }
};
</script>
<style lang="less" scoped>
@import "../css/brand.less";
</style>
