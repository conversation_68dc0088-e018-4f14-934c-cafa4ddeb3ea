<template>
  <a-modal
    :visible="visible"
    title="展示规则设置"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="600"
    :footer="null"
  >
    <div class="modal-content">
      <a-form ref="formRef" :model="formState" layout="vertical">
        <a-form-item label="展示设置" required>
          <a-radio-group v-model:value="formState.displayType">
            <a-radio value="long-term">长期展示</a-radio>
            <a-radio value="timed">时间选择</a-radio>
          </a-radio-group>
        </a-form-item>

        <template v-if="formState.displayType === 'timed'">
          <a-form-item label="是否循环" required>
            <a-switch v-model:checked="formState.isLoop" />
          </a-form-item>

          <a-form-item v-if="formState.isLoop">
            <a-radio-group v-model:value="formState.loopType">
              <a-radio value="weekly">按周循环</a-radio>
              <a-radio value="monthly">按月循环</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item name="timeRange">
             <a-range-picker
                v-model:value="formState.timeRange"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                @calendar-change="onCalendarChange"
                @open-change="onPickerOpenChange"
             />
          </a-form-item>

        </template>
      </a-form>
    </div>
    <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk" style="margin-left: 10px;">确认</a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { message } from 'woody-ui';

dayjs.extend(isoWeek);

const emit = defineEmits(['confirm']);

const visible = ref(false);
const currentItemIndex = ref<number | null>(null);
const formRef = ref();

const defaultFormState = {
  displayType: 'long-term',
  isLoop: false,
  loopType: 'weekly',
  timeRange: [] as any,
};

const formState = reactive({ ...defaultFormState });

// 将API数据转换为组件内部状态
const transformApiToState = (timerControl: any) => {
  if (!timerControl) {
    return { ...defaultFormState };
  }
  const state = { ...defaultFormState };
  state.displayType = timerControl.cycleType === 'longTerm' ? 'long-term' : 'timed';
  state.isLoop = timerControl.loopFlag === 1;

  if (timerControl.cycleType === 'timer' && timerControl.timer) {
      if (timerControl.timer.scope && timerControl.timer.scope.length > 0) {
        state.timeRange = [dayjs(timerControl.timer.scope[0]), dayjs(timerControl.timer.scope[1])];
      }
      if(timerControl.loopFlag === 1){
          state.loopType = timerControl.timer.type === 'month' ? 'monthly' : 'weekly';
      }
  }
  return state;
};

// 将组件内部状态转换为API数据
const transformStateToApi = (state: any) => {
  const timerControl: any = {
    cycleType: state.displayType === 'long-term' ? 'longTerm' : 'timer',
    loopFlag: state.isLoop ? 1 : 0,
    timer: null,
  };
  if (state.displayType === 'timed') {
    timerControl.timer = {
        type: state.isLoop ? (state.loopType === 'monthly' ? 'month' : 'week') : null,
        scope: (state.timeRange && state.timeRange.length > 0) ? state.timeRange.map(date => date.valueOf()) : [],
    };
  }
  return timerControl;
};

const openModal = (item: any, index: number) => {
  currentItemIndex.value = index;
  formRef.value?.resetFields();
  // 重置并填充表单状态
  const initialState = transformApiToState(item.timerControl);
  Object.assign(formState, initialState);
  visible.value = true;
};

const handleOk = async () => {
    try {
        if (formState.displayType === 'timed' && (!formState.timeRange || formState.timeRange.length === 0)) {
            message.error('请选择起止时间');
            return;
        }
        await formRef.value?.validate();
        const apiData = transformStateToApi(formState);
        emit('confirm', apiData, currentItemIndex.value);
        visible.value = false;
    } catch (error) {
        console.log('Validation failed:', error);
    }
};

const handleCancel = () => {
  visible.value = false;
};

// 跨周选择限制
const firstSelectedDate = ref<Dayjs | null>(null);

const onCalendarChange = (dates: any) => {
  if (dates && dates[0]) {
    firstSelectedDate.value = dates[0];
  } else {
    firstSelectedDate.value = null;
  }
};

const onPickerOpenChange = (open: boolean) => {
  if (open) {
    if (formState.isLoop && formState.timeRange && formState.timeRange.length > 0) {
      firstSelectedDate.value = formState.timeRange[0];
    }
  } else {
    firstSelectedDate.value = null; // 关闭时重置
  }
};

const disabledDate = (current: Dayjs): boolean => {
  if (!formState.isLoop || !firstSelectedDate.value) {
    return false;
  }

  if (formState.loopType === 'weekly') {
    // 使用 isoWeek (周一到周日)
    const startOfWeek = firstSelectedDate.value.startOf('isoWeek');
    const endOfWeek = firstSelectedDate.value.endOf('isoWeek');
    return current.isBefore(startOfWeek) || current.isAfter(endOfWeek);
  }

  if (formState.loopType === 'monthly') {
    const startOfMonth = firstSelectedDate.value.startOf('month');
    const endOfMonth = firstSelectedDate.value.endOf('month');
    return current.isBefore(startOfMonth) || current.isAfter(endOfMonth);
  }

  return false;
};


// 切换到"长期展示"时，重置时间相关的设置
watch(() => formState.displayType, (newVal) => {
  if (newVal === 'long-term') {
    formState.isLoop = false;
    formState.loopType = 'weekly';
    formState.timeRange = [];
  }
});

// 关闭循环时，重置循环相关的设置
watch(() => formState.isLoop, (newVal) => {
    if (!newVal) {
        formState.loopType = 'weekly';
    }
})

// 切换循环类型时，检查时间范围
watch(() => formState.loopType, (newVal) => {
  if (formState.timeRange && formState.timeRange.length === 2) {
    const start = formState.timeRange[0] as Dayjs;
    const end = formState.timeRange[1] as Dayjs;

    if (newVal === 'weekly' && start.isoWeek() !== end.isoWeek()) {
      message.warning('按周循环模式下，起止时间不能跨周，将为您清空已选时间。');
      formState.timeRange = [];
      firstSelectedDate.value = null;
    } else if (newVal === 'monthly' && start.month() !== end.month()) {
      message.warning('按月循环模式下，起止时间不能跨月，将为您清空已选时间。');
      formState.timeRange = [];
      firstSelectedDate.value = null;
    }
  }
});

defineExpose({
  openModal,
});
</script>

<script lang="ts">
export default {
    name: 'DisplayRuleModal'
}
</script>

<style lang="less" scoped>
.modal-content {
  padding: 24px;
  padding-bottom: 60px;
}

.modal-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #f0f0f0;
    padding: 10px 16px;
    background: #fff;
    text-align: right;
    border-radius: 0 0 4px 4px;
}
</style> 