<template>
  <a-drawer
    header="选择指定品牌"
    :size="'large'"
    prevent-scroll-through
    :visible="isBrandName"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isBrandName = false)"
    @cancel="() => (isBrandName = false)"
  >
    <a-form :data="goodsFrom" layout="vertical">
      <a-row :align="'bottom'" :gutter="20">
        <a-col :span="8">
          <a-form-item label="品牌名称">
            <a-input v-model:value="goodsFrom.brandName" placeholder="请输入品牌名称" clearable>
              <template #suffix>
                <img
                  class="selectPage-icon"
                  :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
                />
              </template>
            </a-input>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item>
            <a-button type="primary" @click="brandInquire" class="mr10">查询</a-button>
            <a-button @click="resetBrandOk">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="brandName-table">
      <a-table
        :scroll="{ x: 1000 }"
        max-height="500px"
        :columns="brandNameColumns"
        :pagination="orderPagination"
        :data-source="goodsTableData"
        :loading="isLoading"
        :row-selection="rowSelection"
        row-key="brandId"
        hover
        @change="onBrandSize"
      >
        <template #bodyCell="{ column, record }">
          <!-- <template v-if="column.dataIndex === 'radio'">
            <a-radio
              :checked="selectedRowKeys?.brandId === record.brandId"
              @change="() => brandSelect(record)"
            />
          </template> -->
          <template v-if="column.dataIndex === 'brandName'">
            <div class="selectPage-img-center">
              <a-image
                :src="record.brandUrl"
                :alt="record.brandName"
                :lazy="true"
                fit="cover"
                :style="{ width: '60px', height: '60px' }"
              />
              <span>{{ record.brandName }}</span>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" size="medium" @click="submitOk" class="mr10">确定</a-button>
        <a-button @click="isBrandName = false">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
  const { VITE_API_IMG } = import.meta.env;
  import { ref, reactive, computed } from 'vue';
  import { message } from 'woody-ui';
  import { akcPageBrandInfo } from '@/api/cms/decoration/index';
  import { BRANDNAME_COLUMNS, BRANDSIZE, GOODSTYPE } from './const';

  const emit = defineEmits(['onBrandNameCallBack']);
  const isBrandName = ref(false);
  const emitBrandData = ref([]);
  const selectedRowKeys = ref([]);
  const isLoading = ref(false);
  const brandNameColumns = reactive(BRANDNAME_COLUMNS);
  const orderPagination = reactive(BRANDSIZE);
  const goodsFrom = reactive(GOODSTYPE);
  const goodsTableData = ref([]);
  const goodsSelectData = ref([]);

  const httpBrandNameList = async () => {
    isLoading.value = true;
    try {
      const { current, pageSize } = orderPagination;
      const params = { ...goodsFrom, current, pageSize };
      const res = await akcPageBrandInfo(params);
      res.data.records.forEach(item => {
        const found = goodsSelectData.value.find(it => item.brandId == it.brandId);
        if (found) item.isForbid = true;
      });
      goodsTableData.value = res.data.records;
      orderPagination.total = res.data.total;
    } catch (error) {
      goodsTableData.value = [];
      orderPagination.total = 0;
      console.error('Error in onTabChange:', error);
    }
  };
  // 指定品牌分页
  const onBrandSize = (event: any) => {
    const { current, pageSize } = event.pagination;
    orderPagination.current = current;
    orderPagination.pageSize = pageSize;
    httpBrandNameList().finally(() => {
      isLoading.value = false;
    });
  };
  // 查询指定品牌
  const brandInquire = () => {
    orderPagination.current = 1;
    orderPagination.pageSize = 10;
    selectedRowKeys.value = [];
    httpBrandNameList().finally(() => {
      isLoading.value = false;
    });
  };
  // 重置指定品牌
  const resetBrandOk = () => {
    orderPagination.pageSize = 10;
    orderPagination.current = 1;
    goodsFrom.brandName = null;
    selectedRowKeys.value = [];
    httpBrandNameList().finally(() => {
      isLoading.value = false;
    });
  };
  // 选择指定品牌
  // const brandSelect = (value, ctx) => {
  //   if (ctx.selectedRowData) {
  //     if (ctx.type === 'check') {
  //       selectedRowKeys.value = value;
  //       emitBrandData.value = ctx.selectedRowData;
  //     }
  //   }
  //   emitBrandData.value = emitBrandData.value.filter(record => {
  //     return value.includes(record.brandId);
  //   });
  // };
  const rowSelection = computed(() => ({
    type: "checkout",
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled: record.checked === true, // Column configuration not to be checked
    }),
  }));

  const onSelectChange = (newSelectedRowKeys) => {
    selectedRowKeys.value = newSelectedRowKeys;
    // 根据选中的keys获取对应的数据
    emitBrandData.value = goodsTableData.value.filter(item =>
      newSelectedRowKeys.includes(item.brandId)
    );
  };
  // 单选处理函数
  // const brandSelect = record => {
  //   selectedRowKeys.value = record;
  // };
  // 确认指定品牌
  const submitOk = () => {
    if (emitBrandData.value.length > 0) {
      if (emitBrandData.value.length > 100) {
        message.warning('最多支持100个品牌！');
      } else {
        emit('onBrandNameCallBack', emitBrandData.value);
        isBrandName.value = false;
      }
    } else {
      message.warning('请先选择内容!');
    }
  };
  // if (selectedRow.value) {
  //   emit("onBrandCallBack", selectedRow.value);
  //   isBrandSort.value = false;
  // } else {
  //   message.warning("请选择指定平台分类!");
  // }
  // 是否显示品牌平台分类列表弹框方法
  const showBrandNameRef = data => {
    orderPagination.current = 1;
    orderPagination.pageSize = 10;
    emitBrandData.value = [];
    selectedRowKeys.value = [];
    goodsSelectData.value = data;
    httpBrandNameList().finally(() => {
      isLoading.value = false;
    });
    isBrandName.value = true;
  };

  defineExpose({ showBrandNameRef });
</script>
<style lang="less" scoped>
  .drawer-footer {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .brandName-table {
    margin-top: 24px;
  }
  .selectPage-icon {
    width: 16px;
    height: 16px;
  }
  .selectPage-img-center {
    display: flex;
    align-items: center;
  }
</style>
