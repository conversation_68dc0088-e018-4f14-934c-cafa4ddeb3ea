

export const PAGE_TYPE = {
  pageType: "PAGE",
  pageSubTypeList: ["subPage"],
  pageStatus: "PUBLISHED",
  pageName: null,
};

export const PAGESIZE = {
  current: 1,
  pageSize: 10,
  total: 0,
};

export const PAGE_COLUMNS: any = [
  {
    dataIndex: "radio",
    type: "single",
    width: 60,
    // align: 'center',
    stopPropagation: true,
  },
  {
    title: "标题",
    dataIndex: "pageName",
    // align: 'center',
  },
  {
    title: "类型",
    dataIndex: "pageTypeName",
    // align: 'center',
  },
  {
    title: "模板",
    dataIndex: "pageSubTypeName",
    // align: 'center',
  },
  {
    title: "状态",
    dataIndex: "pageStatus",
    customRender: ({ record }: any) => {
      if (record.pageStatus === "PUBLISHED") {
        return "已发布";
      }
    },
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    // align: 'center',
  },
];

export const GOODS_TYPE = {
  productName: null,
  spuId: null,
  productSource: "0",
};

export const GOODS_SIZE = {
  current: 1,
  pageSize: 10,
  total: 0,
};

export const GOODS_COLUMNS: any = [
  {
    dataIndex: "radio",
    type: "single",
    width: 40,
    align: "center",
    fixed:'left',
    stopPropagation: true,
  },
  {
    title: "SPU ID",
    dataIndex: "productId",
    align: "left",
    width: 160,
  },
  {
    title: "商品信息",
    dataIndex: "operate",
    width: 160,
  },
  {
    title: "销售价",
    dataIndex: "price",
    align: "center",
    width: 120,
  },
  {
    title: "商品来源",
    dataIndex: "productSounce",
    align: "center",
    width: 120,
  },
  {
    title: "商品品牌",
    dataIndex: "brandName",
    align: "center",
    width: 120,
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
    align: "center",
    width: 200,
  },
];

export const BRAND_TYPE = {
  brandName: null,
};

export const BRAND_SIZE = {
  current: 1,
  pageSize: 10,
  total: 0,
};

export const BRAND_COLUMNS: any = [
  {
    dataIndex: "radio",
    type: "single",
    stopPropagation: true,
    width: 40,
    align: "center",
    fixed:'left',
  },
  {
    title: "品牌信息",
    dataIndex: "brandName",
    width: 150,
  },
  {
    title: "品牌来源",
    dataIndex: "brandSource",
    align: "center",
    width: 150,
  },
  {
    title: "活动结束时间",
    dataIndex: "endTime",
    align: "center",
    width: 150,
  },
];

export const SYSTEM_PAGE = {
  current: 1,
  pageSize: 10,
  total: 0,
};

export const SYSTEM_COLUMNS: any = [
  {
    dataIndex: "radio",
    type: "single",
    align: "center",
    fixed:"left",
    width:40,
    stopPropagation: true,
  },
  {
    title: "标题",
    dataIndex: "pageName",
    width:180,
  },
  {
    title: "链接地址",
    dataIndex: "link",
    width:160,
  },
  {
    title: "可传参数",
    dataIndex: "param",
    width:160,
  },
];

export const SYSTEM_TYPE = {};

export const PAGE__TYPE = {
  pageName: null,
  pageType: "CONTENT",
  pageStatus: "PUBLISHED",
  pageSubTypeList: ["goods", "brand"],
  pageTypeList: null,
};

export const NAVLINKPAGESIZE = {
  current: 1,
  pageSize: 10,
  total: 0,
};

export const NAVLINKPAGE_COLUMNS: any = [
  {
    dataIndex: "row-select",
    type: "single",
    align: "center",
    stopPropagation: true,
  },
  {
    title: "标题",
    dataIndex: "pageName",
    align: "center",
  },
  {
    title: "模板",
    dataIndex: "pageTypeName",
    align: "center",
  },
  {
    title: "类型",
    dataIndex: "pageSubTypeName",
    align: "center",
  },
  {
    title: "状态",
    dataIndex: "pageStatus",
    align: "center",
    cell: (h, { row }) => {
      if (row.pageStatus === "UNPUBLISHED") {
        return "未发布";
      }
      if (row.pageStatus === "PUBLISHED") {
        return "已发布";
      }
      if (row.pageStatus === "SCHEDULED_PUBLISHED") {
        return "定时发布";
      }
    },
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    align: "center",
  },
];

export const CLASSIFY_SIZE = {
  page: 1,
  size: "-1",
  total: 0,
};

export const CLASSIFY_TYPE = {
  categoryName: null,
  isExtraCon: true,
};

export const GOODSMAM_COLUMNS: any = [
  {
    dataIndex: "row-select",
    type: "single",
    align: "center",
    stopPropagation: true,
    checkProps: ({ row }) => ({
      disabled: row.children.length != 0,
    }),
  },
  {
    title: "分类名称",
    dataIndex: "categoryName",
    align: "center",
  },
  {
    title: "分类类型",
    dataIndex: "categoryType",
    align: "center",
    cell: (h, { row }) => {
      if (row.level === 1) {
        if (row.categoryType === 1) {
          return "单层";
        }
        if (row.categoryType === 2) {
          return "双层";
        }
      }
    },
  },
];

export const BRANDSIZE = {
  pageSize: 10,
  total: 0,
  current: 1,
};

export const BRANDTYPE = {
  categoryName: null,
  id: null,
};

export const BRANDSORT_COLUMNS: any = [
  {
    key: "row-select",
    dataIndex: "row-select",
    type: "single",
    align: "center",
    stopPropagation: true,
  },
  {
    title: "品牌平台分类",
    key: "categoryName",
    dataIndex: "categoryName",
    align: "center",
  },
  {
    title: "创建时间",
    key: "createTime",
    dataIndex: "createTime",
    align: "center",
  },
];

export const GOODSTYPE = {
  brandName: null,
};

export const BRANDNAME_COLUMNS: any = [
  {
    dataIndex: "radio",
    type: "multiple",
    align: "center",
    // checkProps: ({ row }) => ({ disabled: row.isForbid }),
  },
  {
    title: "品牌信息",
    dataIndex: "brandName",
  },
  {
    title: "关联商品数",
    dataIndex: "productCount",
    align: "center",
  },
  {
    title: "活动结束时间",
    dataIndex: "endTime",
    align: "center",
  },
];
