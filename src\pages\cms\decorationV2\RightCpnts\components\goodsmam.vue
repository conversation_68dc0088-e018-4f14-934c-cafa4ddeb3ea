<template>
  <a-drawer
    title="选择前台分类"
    size="large"
    prevent-scroll-through
    :visible="isGoosmam"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isGoosmam = false)"
  >
      <a-form :model="classifyData">
        <a-row>
          <a-col>
            <a-form-item label="分类名称">
              <a-input v-model:value="classifyData.categoryName" style="width:300px;" placeholder="请输入分类名称" clearable />
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item>
              <a-button type="primary" @click="classifyInquire" class="ml10 mr10">查询</a-button>
              <a-button @click="resetGoodsOk">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="goodsmam-table">
        <a-table
          v-model:expanded-tree-nodes="expandedTreeNodes"
          v-model:selected-row-keys="selectedRowKeys"
          min-width="2000"
          max-height="500px"
          row-key="id"
          :columns="goodsmamColumns"
          :children-column-name="treeConfig.childrenKey"
          :indent-size="treeConfig.indent"
          :data-source="classifyTableData"
          :loading="isLoading"
          :tree="{ childrenKey: 'children', expandTreeNodeOnClick: true }"
          hover
          @select-change="classifySelect"
          @expanded-tree-nodes-change="expandedTreeNodesChange"
        >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'radio'">
            <a-radio 
              :checked="selectedRow?.id === record.id"
              :disabled="record.children"
              @change="() => handleSelect(record)"
            />
          </template>
        </template>
        </a-table>
      </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" @click="submitOk" class="mr10">确定</a-button>
        <a-button @click="isGoosmam = false">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, onUpdated } from 'vue';
import { message } from 'woody-ui';
import { feCategoryPage } from '@/api/cms/decoration/index';
import {  CLASSIFY_SIZE, CLASSIFY_TYPE } from './const';
import { isObj } from '@/utils';
import { setEmptyArraysToNull } from "@/utils/utils";

const emit = defineEmits(['onClassifyCallBack']);
const selectedRowKeys = ref<any[]>([]);
const selectedRow = ref<any>({});
const isGoosmam = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const classifyData = reactive(CLASSIFY_TYPE);
import { toRaw } from 'vue';
const treeConfig = {
  childrenKey: "children",
  treeNodeColumnIndex: 0,
  indent: 25,
  expandTreeNodeOnClick: true,
};
const goodsmamColumns = [
  // {
  //   colKey: 'row-select',
  //   type: 'single',
  //   align: 'center',
  //   stopPropagation: true,
  //   checkProps: ({ row }) => ({
  //     disabled: row.children.length != 0,
  //   }),
  // },
  {
    title: "",
    dataIndex: "radio",
    key: "radio",
    align: "left",
    width: 80,
  },
  {
    title: "分类名称",
    dataIndex: "categoryName",
    key: "categoryName",
    align: "left",
    width: 150,
  },
  {
    title: "分类类型",
    dataIndex: "categoryType",
    key: "categoryType",
    align: "left",
    width: 150,
  },
  // {
  //   title: '分类类型',
  //   colKey: 'categoryType',
  //   align: 'center',
  //   cell: (h, { row }) => {
  //     if (row.level === 1) {
  //       if (row.categoryType === 1) {
  //         return h(Tag, { theme: 'primary', variant: 'light' }, '单层');
  //       }
  //       if (row.categoryType === 2) {
  //         return h(Tag, { variant: 'light' }, '双层');
  //       }
  //     }
  //   },
  // },
];
const classifyTableData = ref<any[]>([]);
const pagingSize = reactive(CLASSIFY_SIZE);

// 单选处理函数
const handleSelect = (record) => {
  selectedRow.value = record;
};

// 查询前台分类列表
const httpFeCategoryPage = async () => {
  isLoading.value = true;
  try {
    const { page, size } = pagingSize;
    const params = { ...classifyData, page, size };
    const res = await feCategoryPage(params);
    firstUpdated.value = true;
    classifyTableData.value = res.data.records;
    classifyTableData.value = setEmptyArraysToNull(classifyTableData.value);
    initTree();
    pagingSize.total = res.data.total;
  } catch (error) {
    firstUpdated.value = true;
    expandedTreeNodes.value = [];
    classifyTableData.value = [];
    pagingSize.total = 0;
  }
};

// 获取前台分类列表ID
const classifySelect = (value, ctx) => {
  if (ctx.currentRowData) {
    selectedRowKeys.value = value;
    selectedRow.value = ctx.currentRowData;
  }
};
// 查询前台分类列表
const classifyInquire = () => {
  pagingSize.page = 1;
  pagingSize.size = '-1';
  selectedRowKeys.value = [];
  showData.value = [];
  expandedTreeNodes.value = [];
  httpFeCategoryPage().finally(() => {
    isLoading.value = false;
  });
};
// 重置前台分类列表
const resetGoodsOk = () => {
  pagingSize.page = 1;
  pagingSize.size = '-1';
  classifyData.categoryName = null;
  selectedRowKeys.value = [];
  showData.value = [];
  expandedTreeNodes.value = [];
  httpFeCategoryPage().finally(() => {
    isLoading.value = false;
  });
};
const submitOk = () => {
  if (JSON.stringify(selectedRow.value) != '{}') {
    emit('onClassifyCallBack', selectedRow.value);
    isGoosmam.value = false;
  } else {
    message.warning('请选择前台分类!');
  }
};
// 是否显示商品,品牌列表弹框方法
const showGoodsmamRef = () => {
  pagingSize.page = 1;
  pagingSize.size = '-1';
  selectedRow.value = {};
  classifyTableData.value = [];
  selectedRowKeys.value = [];
  httpFeCategoryPage().finally(() => {
    isLoading.value = false;
  });
  isGoosmam.value = true;
};


const expandedTreeNodes = ref([]);
const expandedIndexMap = reactive(new Map());
// 表格书展开/收起时的逻辑
const expandedTreeNodesChange = (nodes, options) => {
  if (isObj(options)) {
    if (options.type === 'expand') {
      expandedIndexMap.set(options.rowState?.id, true);
    } else {
      expandedIndexMap.delete(options.rowState?.id);
    }
    showData.value = changeExpandProperty(showData.value, nodes);
    initTree();
  }
};

const firstUpdated = ref(true)
const showData = ref([])
function initTree() {
  if(firstUpdated.value) {
    firstUpdated.value = false;
    setTimeout(() => {
      showData.value = [];
      showData.value = JSON.parse(JSON.stringify(classifyTableData.value));
      showData.value = addExpandProperty(showData.value);
      // 获取testdiv中的所有<tr>元素
      var trElements = document.querySelectorAll('.t-table__body tr');
      // 遍历所有<tr>元素并添加类
      trElements.forEach((tr, index) =>{
        if(index > 0) {
          tr.className = '';
          tr.childNodes[0]['className'] = "parent-td-style";
          tr.id = showData.value[index-1].id;
          if(showData.value[index-1]?.children?.length > 0) {
            tr.classList.add('can-expand-row');
          } else {
            tr.classList.add('not-expand-row');
          }
          tr.classList.add('t-table-tr--level-0');
          tr.classList.add('parent-td-style');
        }
      });
    }, 500)
  } else {
    // 获取testdiv中的所有<tr>元素
    var trElements = document.querySelectorAll('.t-table__body tr');
    // 遍历所有<tr>元素并添加类
    trElements.forEach((tr, index) =>{
      if(index > 0) {
        tr.className = '';
        let findNode = findNodeById(showData.value, tr.id);
        if(findNode?.children?.length > 0) {
          tr.classList.add('can-expand-row');
          if(findNode.expand) {
            tr.className = '';
            tr.classList.add('expanded-row');
          }
        } else {
          tr.classList.add('not-expand-row');
          if(!tr.id) {
            tr.classList.add('t-table-tr--level-1');
          }
        }
      }
    });
  }
}

function addExpandProperty(nodes) {
  return nodes.map(node => {
    const newNode = { ...node, expand: false };
    if (newNode.children) {
      newNode.children = addExpandProperty(newNode.children);
    }
    return newNode;
  });
}

function changeExpandProperty(nodes, idArr) {
  if(!nodes) 
    return null;
  for (const node of nodes) {
    if(idArr.length > 0) {
      for (const nowid of idArr) {
        if (node.id === nowid) {
          node.expand = true;
          break;
        } else if (node.id !== nowid){
          node.expand = false;
        } else {
          changeExpandProperty(node.children, idArr);
        }
      }
    } else {
      node.expand = false;
      changeExpandProperty(node.children, idArr);
    }
  }
  return nodes;
}

function findNodeById(nodes ,id) {
  for (const node of nodes) {
    if (node.id === id) {
      return node; // 返回找到的节点
    }
    if (node.children.length > 0) {
      const foundNode = findNodeById(node.children, id);
      if (foundNode) {
        return foundNode; // 如果在子节点中找到，返回该节点
      }
    }
  }
  return undefined; // 如果没有找到，返回undefined
}


defineExpose({ showGoodsmamRef });
</script>
<style lang="less" scoped>
.drawer-footer {
  display: flex;
  align-items: center;
  justify-content: end;
}
.goodsmam-table {
  margin-top: 24px;
}
:deep(.t-table__body) {
  .t-table__tree-leaf-node {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .t-table__tree-op-icon {
    width: 16px;
    height: 16px;
    background-image: url('');
    background-size: 100% 100%;
    display: block;
    svg {
      display: none;
    }
  }
  .not-expand-row {
    .t-table__tree-op-icon {
      background-image: url('');
    }
  }
  .can-expand-row {
    .t-table__tree-op-icon {
      background-image: url('@/assets/images/tree-open-icon.png');
    }
  }
  .expanded-row {
    .t-table__tree-op-icon {
      background-image: url('@/assets/images/tree-closed-icon.png');
    }
  }
}
</style>
