<template>
  <a-drawer
    v-model:open="isShowImage"
    class="custom-class"
    root-class-name="root-class-name"
    :root-style="{ color: 'blue' }"
    title="选择图片列表"
    size="large"
    placement="right"
    :close-on-overlay-click="true"
    @close="() => (isShowImage = false)"
  >
    <div class="myPicture-drawer">
      <div class="myPicture-left">
        <p class="p-h1">文件目录</p>
        <div
          class="myPicture-left-fz"
          :class="{ selectColor: 0 === ownerGroupId }"
          @click="selectCategory(0, 'WEI_FEN_ZU')"
        >
          <span class="myPicture-left-span-one">未分组</span>
          <span class="myPicture-left-span-two">{{ materialCnt }}</span>
        </div>
        <div class="myPicture-tree">
          <div
            v-for="(item, index) in treeData"
            :key="index"
            class="myPicture-tree-list"
          >
            <!-- 一级类目 -->
            <div
              class="myPicture-tree__item"
              :class="{ selectbg: item.id === ownerGroupId }"
              @click="selectCategory(item, 'LEI_MU')"
            >
              <div class="myPicture-tree_item-left">
                <template v-if="item.children">
                  <div
                    class="myPicture-tree__item-icon"
                    @click.stop="item.isEdit = !item.isEdit"
                  >
                    <img
                      v-if="item.isEdit"
                      :src="`${VITE_API_IMG}/2024/08/5dab1ed923034e36867b1e775d706cc6.png`"
                    />
                    <img
                      v-if="!item.isEdit"
                      :src="`${VITE_API_IMG}/2024/08/32a8943c656a4f0e836993fa177098d6.png`"
                    />
                  </div>
                </template>
                <span :class="{ moveBy: !item.children }">{{ item.name }}</span>
              </div>
              <span class="myPicture-tree_item-right">{{
                item.materialCnt
              }}</span>
            </div>
            <!-- 二级类目 -->
            <template v-if="item.isEdit">
              <div
                v-for="(it, idx) in item.children"
                :key="idx"
                class="myPicture-tree_level"
                :class="{ selectbg: it.id === ownerGroupId }"
                @click="selectCategory(it, 'LEI_MU')"
              >
                <div class="myPicture-tree_level-left">
                  <span>{{ it.name }}</span>
                </div>
                <span class="myPicture-tree_level-right">{{
                  it.materialCnt
                }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="myPicture-right">
        <div class="myPicture-right-Input">
          <a-input
            v-model:value="materialName"
            autofocus
            clearable
            show-clear-icon-on-empty
            allow-clear
            @pressEnter="InputEnterImg"
            @clear="InputClearImg"
          >
            <template #prefix>
              <img
                class="Input-box"
                :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
              />
            </template>
          </a-input>
          <div class="right-Input-updata">
            <div class="updata-button ml10" @click="showUploadDialogMethod">
              <img
                :src="`${VITE_API_IMG}/2024/08/982c106fef074063b964e8347edd1d6c.png`"
              />
              <span>上传图片</span>
            </div>
            <div class="updata-sc" @click="goToMaterialCenter">
              <span>前往素材中心</span>
            </div>
          </div>
        </div>
        <a-spin :spinning="isLoading">
          <div class="myPicture-right-Image">
            <template v-if="uploadImageArr && uploadImageArr.length > 0">
              <div
                v-for="(item, index) in uploadImageArr"
                :key="index"
                class="Image-list"
                @click="selectImage(item)"
              >
                <div class="Image-mask">
                  <img :src="item.url" alt="图片加载失败" />
                  <div class="Image-cd">
                    {{ item.imageWidth }} * {{ item.imageHeight }}
                  </div>
                </div>
                <div class="Image-text">
                  <img
                    v-if="!item.isRadio"
                    :src="`${VITE_API_IMG}/2024/08/4776bb7a2dec4b7b9f1164032cd05762.png`"
                  />
                  <img
                    v-if="item.isRadio"
                    :src="`${VITE_API_IMG}/2024/08/88e8a6c6958e401e8587f62be91615df.png`"
                  />
                  <span class="Image-span">{{ item.name }}</span>
                </div>
              </div>
            </template>
            <template v-if="uploadImageArr.length === 0">
              <div class="default-graph">
                <img
                  :src="`${VITE_API_IMG}/2024/08/26af98c38b3f4ae68c2efa3c25eb001f.png`"
                />
                <p>暂无数据</p>
              </div>
            </template>
          </div>
        </a-spin>
        <a-pagination
          v-model:current="pageSize.page"
          :total="pageSize.total"
          :pageSize="pageSize.size"
          show-quick-jumper
          @change="pageChange"
          style="margin-top: 20px"
          size="small"
        />
      </div>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" @click="confirmOk" class="ml10">确定</a-button>
        <a-button @click="isShowImage = false">取消</a-button>
      </div>
    </template>
  </a-drawer>
  <!-- 上传图片弹框 -->
  <uploadImage
    ref="uploadImageRef"
    @on-upload-image-call-back="onUploadImageCallBack"
  />
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, onMounted } from "vue";
import { message } from "woody-ui";
import {
  getGroupQuery,
  getPageSearch,
  getGroupMateriallCnt,
} from "@/api/cms/decoration";
import uploadImage from "./uploadImage.vue";
import router from "@/router";

const emit = defineEmits(["onImageCallBack"]);
const isShowImage = ref<boolean>(false);
const treeData = ref<any[]>([]);
const materialName = ref<string | null>(null);
const ownerGroupId = ref<number | null>(0);
const materialCnt = ref<number | null>(0);
const pageSize = ref<any>({ page: 1, size: 10, total: 0 });
const isLoading = ref<boolean>(false);
const parentIndex = ref<number | null>(null);
const parentIdx = ref<number | null>(null);
const uploadImageArr = ref<any[]>([]);
// 获取子组件方法
type uploadImageType = { showUpdataImageRef: () => void };
const uploadImageRef = ref<uploadImageType | null>(null);

// 跳转素材中心
const goToMaterialCenter = () => {
  // let routeUrl = router.resolve({ path: '/cms/materialCenter' });
  // window.open(routeUrl.href, '_blank');
  const { VITE_CURRENT_URL } = import.meta.env;
  const url = `${VITE_CURRENT_URL}/#/market/index?market=/#/cms/materialCenter`;
  window.open(url, "_blank");
};

// 图片上传成功回调
const onUploadImageCallBack = () => {
  httpGetgroupQuery();
  httpGroupMateriallCnt();
  httpPageSearch().finally(() => {
    isLoading.value = false;
  });
};
// 所有素材分组查询
const httpGetgroupQuery = () => {
  getGroupQuery({ flatFlag: false }).then((res) => {
    if (res.code === 0) {
      treeData.value = res.data;
      treeData.value.forEach((item) => (item.isEdit = false));
    }
  });
};
// 素材分组下所有素材数量查询
const httpGroupMateriallCnt = () => {
  const params = { groupId: ownerGroupId.value };
  getGroupMateriallCnt(params).then((res) => {
    if (res.code === 0) {
      materialCnt.value = res.data;
    }
  });
};
// 选择类目
const selectCategory = (item, type) => {
  if (type === "WEI_FEN_ZU") ownerGroupId.value = item;
  if (type === "LEI_MU") ownerGroupId.value = item.id;
  uploadImageArr.value = [];
  pageSize.value.page = 1;
  pageSize.value.size = 10;
  try {
    httpPageSearch().finally(() => {
      isLoading.value = false;
    });
  } catch (error) {
    console.error("Error occurred during httpPageSearch:", error);
  }
};
// 通过素材名称+分组进行列表搜索
const httpPageSearch = async () => {
  isLoading.value = true;
  try {
    const { page, size } = pageSize.value;
    const mateObj = { materialName: materialName.value };
    const groupObj = { ownerGroupId: ownerGroupId.value };
    const params = { ...mateObj, ...groupObj, page, size };
    const res = await getPageSearch(params);
    res.data.records.forEach((item) => {
      item.isRadio = false;
      if (item.ext && item.ext.trim() !== "") {
        try {
          const extData = item.ext ? JSON.parse(item.ext) : {};
          item.imageWidth = extData.imageWidth;
          item.imageHeight = extData.imageHeight;
        } catch (error) {
          console.error("Failed to parse ext data:", error);
        }
      }
    });
    uploadImageArr.value = res.data.records;
    pageSize.value.total = res.data.total;
  } catch (error) {
    uploadImageArr.value = [];
    pageSize.value.total = 0;
  }
};
// 选择图片
const selectImage = (item) => {
  uploadImageArr.value.forEach((item) => {
    item.isRadio = false;
  });
  item.isRadio = true;
};
// 回车键按下时触发
const InputEnterImg = () => {
  uploadImageArr.value = [];
  pageSize.value.page = 1;
  pageSize.value.size = 10;
  httpPageSearch().finally(() => {
    isLoading.value = false;
  });
};
// 清空按钮点击时触发
const InputClearImg = () => {
  uploadImageArr.value = [];
  pageSize.value.page = 1;
  pageSize.value.size = 10;
  httpPageSearch().finally(() => {
    isLoading.value = false;
  });
};
// 分页切换
const pageChange = (page, size) => {
  pageSize.value.page = page;
  pageSize.value.size = size;
  try {
    httpPageSearch().finally(() => {
      isLoading.value = false;
    });
  } catch (error) {
    console.error("Error during httpPageSearch:", error);
  }
};
const confirmOk = () => {
  if (!uploadImageArr.value || uploadImageArr.value.length === 0) {
    message.warning("请选择图片！");
  } else {
    uploadImageArr.value.forEach((item) => {
      if (item.isRadio) {
        emit("onImageCallBack", {
          index: parentIndex.value,
          idx: parentIdx.value,
          url: item.url,
        });
        isShowImage.value = false;
      }
    });
  }
};
// 是否显示上传图片组件
const showUploadDialogMethod = () => {
  if (uploadImageRef.value) {
    if (typeof uploadImageRef.value.showUpdataImageRef === "function") {
      uploadImageRef.value.showUpdataImageRef();
    }
  }
};
// 是否显示图片列表弹框方法
const showImageRef = (index?: number, idx?: number) => {
  if (index === null) return;
  parentIndex.value = index ?? 0;
  parentIdx.value = idx ?? 0;
  pageSize.value.page = 1;
  pageSize.value.size = 10;
  materialName.value = null;
  ownerGroupId.value = 0;
  try {
    httpPageSearch().finally(() => {
      isLoading.value = false;
    });
  } catch (error) {
    console.error("Error during httpPageSearch:", error);
  }
  isShowImage.value = true;
};

onMounted(() => {
  httpGetgroupQuery();
  httpGroupMateriallCnt();
});

defineExpose({ showImageRef });
</script>
<style lang="less" scoped>
.myPicture-drawer {
  display: flex;
  .myPicture-left {
    width: 200px;
    height: calc(100vh - 210px);
    border-right: 1px solid #f2f5f9;

    .p-h1 {
      font-weight: 600;
      font-size: 16px;
      color: #05082c;
    }

    .myPicture-left-fz {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      margin-top: 13px;
      justify-content: space-between;
      cursor: pointer;
      .myPicture-left-span-one {
        font-size: 14px;
        color: #05082c;
        font-weight: 400;
        margin-left: 4px;
      }
      .myPicture-left-span-two {
        font-size: 14px;
        color: #818999;
        font-weight: 400;
        margin-right: 10px;
      }
    }
    .selectColor {
      background: #f0f9ff;
      border-right: 3px solid #1a7af8;
      span {
        color: #1a7af8 !important;
      }
    }
    .myPicture-tree {
      margin-top: 2px;
      height: 600px;
      overflow-y: auto;
      .myPicture-tree-list {
        .myPicture-tree__item {
          width: 100%;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          .myPicture-tree_item-left {
            display: flex;
            align-items: center;
            .myPicture-tree__item-icon {
              width: 20px;
              height: 20px;
            }
            img {
              width: 20px;
              height: 20px;
            }
            .moveBy {
              margin-left: 5px;
            }
            span {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #000000;
            }
          }
          .myPicture-tree_item-right {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #495366;
            margin-right: 10px;
          }
        }
        .myPicture-tree_level {
          width: 100%;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          .myPicture-tree_level-left {
            display: flex;
            align-items: center;
            span {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #333333;
              margin-left: 25px;
            }
          }
          .myPicture-tree_level-right {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #495366;
            margin-right: 10px;
          }
        }
        .selectbg {
          background: #f0f9ff;
          border-right: 3px solid #1a7af8;
          span {
            color: #1a7af8 !important;
          }
        }
        .myPicture-tree-tow {
          width: 100%;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          animation: initial;
          transition: opacity 0.15s linear 0s, max-height 0.15s linear 0.15s;
          span:first-child {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            margin-left: 25px;
          }
          span:last-child {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #495366;
            margin-right: 10px;
          }
        }
        .selectbg {
          background: #f0f9ff;
          border-right: 3px solid #1a7af8;
          span {
            color: #1a7af8 !important;
          }
        }
        .myPicture-tree-tow:hover {
          background: #f0f9ff;
        }
      }
    }
  }

  .myPicture-right {
    width: 79%;
    padding-left: 24px;
    .t-pagination {
      padding-top: 10px;
    }
    .myPicture-right-Input {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .t-input__wrap {
        width: 237px;
        height: 32px;
      }

      .Input-box {
        width: 16px;
        height: 16px;
      }

      .right-Input-updata {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .updata-button {
          width: 112px;
          height: 32px;
          background: #f2f5f9;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          img {
            width: 16px;
            height: 16px;
          }

          span {
            margin-left: 10px;
            font-weight: 400;
            font-size: 14px;
            color: #05082c;
          }
        }

        .updata-sc {
          width: 116px;
          height: 32px;
          background: #f2f5f9;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          margin-left: 8px;

          span {
            margin-left: 10px;
            font-weight: 400;
            font-size: 14px;
            color: #05082c;
          }
        }
      }
    }

    .myPicture-right-Image {
      margin-top: 24px;
      display: flex;
      flex-wrap: wrap;
      max-height: 600px;
      overflow-y: auto;
      .Image-list {
        width: 180px;
        height: 210px;
        margin-right: 15px;
        position: relative;
        margin-bottom: 15px;
        cursor: pointer;
        .Image-mask {
          width: 180px;
          height: 180px;
          border-radius: 8px;
          border: 1px solid #f2f5f9;
          background: #f6f6f6;
          position: relative;
          display: flex;
          box-sizing: border-box;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          img {
            width: 180px;
            height: auto;
            border-radius: 8px;
          }
          .Image-cd {
            width: 180px;
            line-height: 40px;
            background: #1d2426;
            border-radius: 0px 0px 8px 8px;
            opacity: 0.9;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            text-align: center;
            position: absolute;
            left: 0;
            bottom: -2px;
            display: none;
          }
        }
        .Image-mask:hover {
          .Image-cd {
            display: block;
          }
        }
        .Image-text {
          width: 180px;
          margin-top: 4px;
          display: flex;
          align-items: center;
          img {
            width: 16px;
            height: 16px;
          }
          .Image-span {
            width: 150px;
            font-weight: 400;
            font-size: 14px;
            color: #05082c;
            white-space: nowrap;
            /* 防止文本换行 */
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            margin-left: 8px;
          }
        }
      }
      .default-graph {
        width: 100%;
        height: 300px;
        img {
          display: block;
          margin-left: auto;
          margin-right: auto;
          margin-top: 50px;
        }
        p {
          text-align: center;
          font-size: 16px;
          color: #000000;
          padding-top: 25px;
        }
      }
    }
  }
}

.t-drawer__footer {
  display: flex;
  align-items: center;
  justify-content: end;
}
.drawer-footer {
  display: flex;
  align-items: center;
  justify-content: end;
}
</style>
