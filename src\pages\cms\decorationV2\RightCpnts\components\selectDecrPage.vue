<template>
  <!-- 弹窗 -->
  <div>
    <a-drawer
      :visible="mVal.visible"
      placement="right"
      size="large"
      title="选择"
      @close="closeDrawer"
    >
      <div v-if="props.mVal.navType !== 'firstScreen'" class="filter-box flex">
        <div v-if="props.mVal.navType === 'other'" class="item">
          <div class="item-name">类型</div>
          <a-select
            v-model:value="decorationContentTable.params.pageType"
            clearable
            placeholder="请选择"
            :options="decorationTypeList"
          ></a-select>
        </div>
        <div class="item">
          <div class="item-name">标题</div>
          <a-input v-model:value="decorationContentTable.params.pageName" clearable />
        </div>
        <div class="item">
          <div class="item-name">模版</div>
          <a-select
            v-model:value="decorationContentTable.params.pageSubTypeList"
            clearable
            placeholder="请选择"
            @change="chooseSubType"
            style="width: 200px"
            :options="decorationTemplateList"
          ></a-select>
        </div>
        <div class="btn-box flex">
          <a-button type="primary" :loading="searchOrderLoading" @click="handleSearch" class="mr10">
            查询
          </a-button>
          <a-button @click="resetTableData">重置</a-button>
        </div>
      </div>
      <a-table
        min-width="2000"
        :data-source="decorationContentTable.data.records"
        :columns="decorationContentColumn"
        row-key="id"
        :pagination="decorationContentPagination"
        hover
        @change="changeDecorationPage"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'radio'">
            <a-radio
              :checked="selectedRow?.id === record.id"
              @change="() => handleSelect(record)"
            />
          </template>
          <template v-if="column.key === 'pageType'">
            <div v-if="record.pageType === 'PAGE'">页面</div>
            <div v-if="record.pageType === 'CONTENT'">内容</div>
          </template>
          <template v-if="column.key === 'pageStatus'">
            <a-tag v-if="record.pageStatus === 'PUBLISHED'" theme="default" variant="light">
              已发布
            </a-tag>
          </template>
        </template>
      </a-table>
      <template #footer>
        <div class="drawer-footer">
          <a-button type="primary" @click="confirmChooseContent" class="mr10">确定</a-button>
          <a-button @click="mVal.visible = false">取消</a-button>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, watchEffect } from 'vue';
  import { storeToRefs } from 'pinia';
  import { message } from 'woody-ui';
  import { DECORA_CONTENT_COLUMNS } from './const_navs';
  import { DecorationContentTableData, decorationContentParams } from '../../type';
  import { getPageContentList } from '@/api/decoration';
  import { getDecorationStore } from '@/store';

  const props = defineProps({
    mVal: {
      type: Object,
      default: () => ({}),
    },
  });
  const selectedRow = ref(null); // 存储单选选中的行数据
  const decorationStore = getDecorationStore();
  const { decorationInfo } = storeToRefs(decorationStore);

  const info = ref<any>({});

  const decorationTypeList = [
    { value: 'PAGE', label: '页面' },
    { value: 'CONTENT', label: '内容' },
  ];
  const decorationTemplateList = ref([]);

  const emit = defineEmits(['update:mVal']);
  function closeDrawer() {
    resetTableData();
    selectedRow.value = null;
    emit('update:mVal', { ...props.mVal, visible: false });
  }

  // Table
  const searchOrderLoading = ref(false);
  const decorationContentColumn = DECORA_CONTENT_COLUMNS;
  const decorationContentPagination = ref({
    current: 1,
    pageSize: 10,
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  });
  const decorationContentTable = reactive(new DecorationContentTableData());
  let searchParams: decorationContentParams = {
    page: 1,
    size: 10,
    pageName: '',
    pageType: '',
    pageSubTypeList: [],
  };
  const chooseSubType = (e: string) => {
    if (e) {
      searchParams.pageSubTypeList = [e];
    } else {
      searchParams.pageSubTypeList = [];
    }
  };

  // 单选处理函数
  const handleSelect = record => {
    selectedRow.value = record;
  };
  const getDecorationContentData = () => {
    searchParams.pageStatus = 'PUBLISHED';

    if (Array.isArray(searchParams.pageSubTypeList)) {
      // 弹窗初始化，pageSubTypeList = []
      if (!searchParams.pageSubTypeList.length) {
        if (props.mVal.navType === 'firstScreen') {
          searchParams.pageSubTypeList = ['firstScreen','CONTENT'];// CONTENT: 内容新
        }
        if (props.mVal.navType === 'other') {
          searchParams.pageSubTypeList = ['brand', 'goods', 'subPage'];
        }
        if (props.mVal.navType === 'subPage') {
          searchParams.pageSubTypeList = ['brand', 'goods'];
        }
      }
    } else if (searchParams.pageSubTypeList) {
      // 进行了模版筛选，pageSubTypeList为 字符串
      searchParams.pageSubTypeList = [searchParams.pageSubTypeList];
    } else {
      // 清除了模版筛选，pageSubTypeList为 undefined
      if (props.mVal.navType === 'other') {
        searchParams.pageSubTypeList = ['brand', 'goods', 'subPage'];
      }
      if (props.mVal.navType === 'subPage') {
        searchParams.pageSubTypeList = ['brand', 'goods'];
      }
    }

    getPageContentList({ ...searchParams })
      .then(res => {
        if (res.code === 0) {
          const { records, total } = res.data;
          decorationContentTable.data = { records, total };
          decorationContentPagination.value = {
            ...decorationContentPagination.value,
            total,
          };
          searchOrderLoading.value = false;
        }
      })
      .finally(() => {
        searchOrderLoading.value = false;
      });
  };
  const changeDecorationPage = (e: any) => {
    const page = e.current;
    const size = e.pageSize;
    decorationContentPagination.value.current = page;
    decorationContentPagination.value.pageSize = size;
    searchParams.page = page;
    searchParams.size = size;
    getDecorationContentData();
  };
  const handleSearch = () => {
    searchOrderLoading.value = true;
    decorationContentTable.params.page = 1;
    decorationContentPagination.value.current = 1;
    searchParams = { ...decorationContentTable.params };
    getDecorationContentData();
  };
  const resetTableData = () => {
    decorationContentTable.params = {
      page: 1,
      size: 10,
      pageName: '',
      pageType: '',
      pageSubTypeList: [],
    };
    decorationContentPagination.value.current = 1;
    decorationContentPagination.value.pageSize = 10;
    searchParams = { ...decorationContentTable.params };
    getDecorationContentData();
  };

  const selectedData = ref([]);
  // const selectRowData = (value, { selectedRowData }) => {
  //   selectedRow.value = value;
  //   selectedData.value = selectedRowData;
  // };
  const confirmChooseContent = () => {
    console.log(selectedRow.value, 'selectedRow');
    if (!selectedRow.value) {
      message.warning('请选择链接内容');
      return;
    }

    [props.mVal.selectNavData] = [selectedRow.value];
    const { pageName, id, clickType } = props.mVal.selectNavData;
    if (props.mVal.navType === 'subPage') {
      info.value.id = id;
      info.value.title = pageName;
    } else if (props.mVal.navType === 'firstScreen' || props.mVal.navType === 'other') {
      // 确认选择后，更改pinia中的值（给Phone/navSingle当前导航栏链接传param.id）
      info.value.list[props.mVal.selectNavIndex] = {
        ...info.value.list[props.mVal.selectNavIndex],
        param: { id },
        uriName: pageName,
        uriType: '0',
        clickType,
      };
    }

    closeDrawer();
  };

  watchEffect(() => {
    searchParams.pageSubTypeList = []; // 弹窗初始化,pageSubTypeList = []
    let detailData = { info: {} };
    if (props.mVal.navType === 'subPage') {
      // 二级页面
      detailData = decorationInfo.value.components.find(
        (item: any) => item.templateId === 'content',
      );
    } else if (props.mVal.navType === 'firstScreen' || props.mVal.navType === 'other') {
      // 首页单层导航
      detailData = decorationInfo.value.components.find(
        (item: any) => item.templateId === 'navSingle',
      );
    }
    info.value = detailData.info || {};

    if (props.mVal.visible) {
      decorationTemplateList.value =
        props.mVal.navType === 'subPage'
          ? [
              { value: 'goods', label: '商品' },
              { value: 'brand', label: '品牌' },
            ]
          : [
              { value: 'subPage', label: '二级' },
              { value: 'goods', label: '商品' },
              { value: 'brand', label: '品牌' },
            ];
      getDecorationContentData();
    }
  });
</script>

<style lang="less" scoped>
  .filter-box {
    margin-bottom: 16px;
    .item {
      margin-right: 16px;
      .item-name {
        color: #05082c;
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
    .btn-box {
      height: 60px;
      align-items: flex-end;
      flex: 1;
      justify-content: flex-end;
    }
  }
  .drawer-footer {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  :deep(.t-drawer__footer) {
    > div {
      justify-content: flex-end !important;
    }
  }
  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;
    &:first-child {
      border-top-left-radius: 8px;
    }
    &:last-child {
      border-top-right-radius: 8px;
    }
  }
  :deep(td) {
    border-bottom-color: @table-boder-color;
  }
  :deep(.t-table__pagination) {
    padding-top: 28px;
    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style>
