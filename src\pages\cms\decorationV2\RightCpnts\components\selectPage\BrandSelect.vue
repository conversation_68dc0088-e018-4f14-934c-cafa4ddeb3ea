<template>
  <div class="brand-select">
    <div class="brand-select-size">
      <a-form layout="vertical" :model="brandData">
        <a-row :align="'bottom'" :gutter="20">
          <a-col :span="8">
            <a-form-item label="品牌名称">
              <a-input v-model:value="brandData.brandName" placeholder="请输入品牌内容" clearable>
                <template #suffix>
                  <img
                    class="brand-select-icon"
                    :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
                  />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item>
              <div style="margin-top: 28px">
                <a-button type="primary" @click="brandInquire" class="mr20">查询</a-button>
                <a-button @click="resetBrandOk">重置</a-button>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="brand-select-table">
      <a-table
        :scroll="{ x: 1000 }"
        max-height="500px"
        :columns="brandColumns"
        :pagination="brandSize"
        :data-source="brandTabelData"
        :loading="isBrandLoading"
        :row-selection="rowSelection"
        row-key="brandId"
        hover
        @change="onBrandSize"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'radio'">
            <a-radio
              :checked="selectedRowKeys?.brandId === record.brandId"
              @change="() => brandSelect(record)"
            />
          </template>
          <template v-if="column.dataIndex === 'brandName'">
            <div class="brand-select-img-center">
              <a-image
                :src="record.brandUrl"
                :alt="record.brandName"
                :lazy="true"
                fit="cover"
                :style="{ width: '60px', height: '60px' }"
              />
              <span class="ml10">{{ record.brandName }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'brandSource'">
            <span>爱库存</span>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'woody-ui';
  import { BRAND_COLUMNS, BRAND_TYPE, BRAND_SIZE } from '../const';
  import { brandInfo } from '@/api/cms/decoration/index';
  const { VITE_API_IMG } = import.meta.env;

  const emit = defineEmits(['onFormData']);

  // table品牌数据列表
  const brandColumns = reactive(BRAND_COLUMNS);
  const brandData = reactive({ ...BRAND_TYPE });
  const brandSize = reactive({ ...BRAND_SIZE });
  const brandTabelData = ref<any[]>([]);
  const isBrandLoading = ref<boolean>(false);
  const selectedRowKeys = ref(null);

  // 行选择配置（设置为null禁用默认的多选）
  const rowSelection = null;

  // 获取品牌列表
  const httpBrandInfo = async () => {
    isBrandLoading.value = true;
    try {
      const { current, pageSize } = brandSize;
      const params = { ...brandData, current, pageSize };
      const res = await brandInfo(params);
      brandTabelData.value = res.data.records;
      brandSize.total = res.data.total;
    } catch (error) {
      brandTabelData.value = [];
      brandSize.total = 0;
    } finally {
      isBrandLoading.value = false;
    }
  };

  // 品牌查询
  const brandInquire = () => {
    brandSize.current = 1;
    brandSize.pageSize = 10;
    selectedRowKeys.value = null;
    httpBrandInfo();
  };

  // 品牌分页
  const onBrandSize = event => {
    const { current, pageSize } = event;
    brandSize.current = current;
    brandSize.pageSize = pageSize;
    httpBrandInfo();
  };

  // 品牌重置
  const resetBrandOk = () => {
    brandSize.current = 1;
    brandSize.pageSize = 10;
    brandData.brandName = null;
    selectedRowKeys.value = null;
    httpBrandInfo();
  };

  // 选择品牌
  const brandSelect = record => {
    selectedRowKeys.value = record;
    emit('onFormData', {
      ...record,
      enums: 'BRAND',
      clickType: '1',
      uriRouteType: '1',
    });
  };

  // 验证函数
  const validate = () => {
    if (!selectedRowKeys.value) {
      message.warning('请选择一个品牌');
      return false;
    }
    return true;
  };

  onMounted(() => {
    httpBrandInfo();
  });

  // 暴露方法给父组件
  defineExpose({
    resetData: () => {
      brandSize.current = 1;
      brandSize.pageSize = 10;
      brandData.brandName = null;
      brandTabelData.value = [];
      selectedRowKeys.value = null;
      httpBrandInfo();
    },
    validate,
  });
</script>
<script lang="ts">
  export default {
    name: 'BrandSelect',
  };
</script>
<style lang="less" scoped>
  .brand-select {
    .brand-select-size {
      padding-top: 48px;

      .brand-select-icon {
        width: 16px;
        height: 16px;
      }
    }

    .brand-select-table {
      padding-top: 24px;
    }

    .brand-select-img-center {
      display: flex;
      align-items: center;
    }
  }

  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;

    &:first-child {
      border-top-left-radius: 8px;
    }

    &:last-child {
      border-top-right-radius: 8px;
    }
  }

  :deep(td) {
    border-bottom-color: @table-boder-color;
  }

  :deep(.t-table__pagination) {
    padding-top: 28px;

    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style>
