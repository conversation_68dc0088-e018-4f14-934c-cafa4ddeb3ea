<template>
  <div class="goods-select">
    <div class="goods-select-size">
      <a-form layout="vertical" :model="goodsData">
        <a-row :gutter="20">
          <a-col :span="6">
            <a-form-item label="商品名称" name="name">
              <a-input v-model:value="goodsData.productName" placeholder="请输入商品内容" clearable>
                <template #suffix>
                  <img
                    class="goods-select-icon"
                    :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
                  />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="SPU ID" name="name">
              <a-input
                v-model:value="goodsData.spuId"
                maxlength="19"
                placeholder="请输入SPU ID"
                clearable
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="商品来源" name="productSource">
              <a-select
                default-value="0"
                v-model:value="goodsData.productSource"
                :options="productOpt"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item>
              <div style="margin-top: 28px">
                <a-button type="primary" @click="goodsInquire" class="mr10">查询</a-button>
                <a-button @click="resetGoodsOk">重置</a-button>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="goods-select-table">
      <a-table
        :scroll="{ x: 1000 }"
        max-height="500px"
        :columns="goodsColumns"
        :pagination="goodsSize"
        :data-source="goodsTabelData"
        :loading="isGoodsLoading"
        :row-selection="rowSelection"
        row-key="productId"
        hover
        @change="onGoodsSize"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'radio'">
            <a-radio
              :checked="selectedRowKeys?.productId === record.productId"
              @change="() => goodsSelect(record)"
            />
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <div class="goods-select-img-center">
              <a-image
                :src="record.picUrl"
                :alt="record.prductName"
                :lazy="true"
                fit="cover"
                :style="{ width: '60px', height: '60px' }"
              />
              <span class="ml10">{{ record.prductName }}</span>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'woody-ui';
  import { GOODS_COLUMNS, GOODS_TYPE, GOODS_SIZE } from '../const';
  import { pageProduct } from '@/api/cms/decoration/index';
  const { VITE_API_IMG } = import.meta.env;

  const emit = defineEmits(['onFormData']);

  // table商品数据列表
  const goodsColumns = reactive(GOODS_COLUMNS);
  const goodsData = reactive({ ...GOODS_TYPE });
  const goodsSize = reactive({ ...GOODS_SIZE });
  const goodsTabelData = ref<any[]>([]);
  const isGoodsLoading = ref<boolean>(false);
  const selectedRowKeys = ref(null);

  // 行选择配置（设置为null禁用默认的多选）
  const rowSelection = null;

  const productOpt = [
    {
      label: '供货仓',
      value: '0',
    },
    {
      label: '京东',
      value: '8',
    },
  ];

  // 获取商品列表
  const HttpPageProduct = async () => {
    isGoodsLoading.value = true;
    try {
      const { current, pageSize } = goodsSize;
      goodsData.spuId = goodsData.spuId ? goodsData.spuId : null;
      const params = { ...goodsData, current, pageSize };
      const res = await pageProduct(params);
      goodsTabelData.value = res.data.records;
      goodsSize.total = res.data.total;
    } catch (error) {
      goodsTabelData.value = [];
      goodsSize.total = 0;
    } finally {
      isGoodsLoading.value = false;
    }
  };

  // 商品查询
  const goodsInquire = () => {
    goodsSize.current = 1;
    goodsSize.pageSize = 10;
    selectedRowKeys.value = null;
    HttpPageProduct();
  };

  // 商品分页
  const onGoodsSize = event => {
    const { current, pageSize } = event;
    goodsSize.current = current;
    goodsSize.pageSize = pageSize;
    HttpPageProduct();
  };

  // 商品重置
  const resetGoodsOk = () => {
    goodsSize.current = 1;
    goodsSize.pageSize = 10;
    goodsData.spuId = null;
    goodsData.productName = null;
    goodsData.productSource = '0';
    selectedRowKeys.value = null;
    HttpPageProduct();
  };

  // 选择商品
  const goodsSelect = record => {
    selectedRowKeys.value = record;
    emit('onFormData', {
      ...record,
      enums: 'GOODS',
      clickType: '1',
      uriRouteType: '1',
    });
  };

  onMounted(() => {
    HttpPageProduct();
  });

  // 验证函数
  const validate = () => {
    if (!selectedRowKeys.value) {
      message.warning('请选择一个商品');
      return false;
    }
    return true;
  };

  // 暴露方法给父组件
  defineExpose({
    resetData: () => {
      goodsSize.current = 1;
      goodsSize.pageSize = 10;
      goodsData.spuId = null;
      goodsData.productName = null;
      goodsData.productSource = '0';
      goodsTabelData.value = [];
      selectedRowKeys.value = null;
      HttpPageProduct();
    },
    validate,
  });
</script>
<script lang="ts">
  export default {
    name: 'GoodsSelect',
  };
</script>
<style lang="less" scoped>
  .goods-select {
    .goods-select-size {
      padding-top: 48px;

      .goods-select-icon {
        width: 16px;
        height: 16px;
      }
    }

    .goods-select-table {
      padding-top: 24px;
    }

    .goods-select-img-center {
      display: flex;
      align-items: center;
    }
  }

  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;

    &:first-child {
      border-top-left-radius: 8px;
    }

    &:last-child {
      border-top-right-radius: 8px;
    }
  }

  :deep(td) {
    border-bottom-color: @table-boder-color;
  }

  :deep(.t-table__pagination) {
    padding-top: 28px;

    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style>
