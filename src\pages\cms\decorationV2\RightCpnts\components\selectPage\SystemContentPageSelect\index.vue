<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="shop" tab="店铺列表">
      <CategoryTable ref="shopTableRef" :table-data="tableData" :is-loading="isLoading" @onSelect="handleSelect" type="SHOP_COMPONENT"/>
    </a-tab-pane>
    <a-tab-pane key="group" tab="精选团购" force-render>
      <CategoryTable ref="groupTableRef" :table-data="tableData" :is-loading="isLoading" @onSelect="handleSelect" type="GROUPBUY_COMPONENT"/>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import CategoryTable from './CategoryTable.vue';
import { getShopOrGroupCategories } from '@/api/decoration';

const emit = defineEmits(['onFormData']);
const activeKey = ref('shop');
const shopTableRef = ref<any>(null);
const groupTableRef = ref<any>(null);
const tableData = ref<any[]>([]);
const isLoading = ref<boolean>(false);

const transformData = (categories: any[], level = 1, parentKey = '') => {
  return categories.map((category: any, index: number) => {
    const currentKey = parentKey ? `${parentKey}_${index}` : `${level}_${index}`;
    const node: any = {
      key: currentKey,
      title: category.categoryName,
      level: level,
    };

    if (level === 1) {
      node.uri = category.firstCategoryId;
      if (category.secondContentCategories && category.secondContentCategories.length > 0) {
        node.children = transformData(category.secondContentCategories, 2, currentKey);
      }
    } else if (level === 2) {
      node.uri = category.secondCategoryId;
      if (category.thirdContentCategories && category.thirdContentCategories.length > 0) {
        node.children = transformData(category.thirdContentCategories, 3, currentKey);
      }
    } else if (level === 3) {
      node.uri = category.thirdCategoryId;
    }
    
    return node;
  });
};

const fetchData = async () => {
  isLoading.value = true;
  try {
    const res: any = await getShopOrGroupCategories({});
    if (res.code === 0 && res.data) {
      tableData.value = transformData(res.data.categories || []);
    }
  } catch (error) {
    console.error("Failed to fetch category data:", error);
  } finally {
    isLoading.value = false;
  }
};

const handleSelect = (data: any) => {
  console.log(data,'data123')
  emit('onFormData', { ...data, enums: 'SYSTEM_CONTENT_PAGE', clickType: '0' ,tabType:data.tabType});
};

const resetData = () => {
  shopTableRef.value?.resetData();
  groupTableRef.value?.resetData();
};

watch(activeKey, (newKey) => {
  if (newKey === 'shop') {
    groupTableRef.value?.clearSelection();
  } else {
    shopTableRef.value?.clearSelection();
  }
});

onMounted(() => {
  fetchData();
});

defineExpose({ resetData });
</script>

<script lang="ts">
export default {
  name: 'SystemContentPageSelect'
}
</script>

<style lang="less" scoped>
/* 根据需要添加样式 */
</style> 