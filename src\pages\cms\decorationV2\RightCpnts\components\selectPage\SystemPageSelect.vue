<template>
  <div class="goodsmam-table">
    <a-table
      v-model:selected-row-keys="selectedRowKeys"
      row-key="key"
      :columns="systemPageColumns"
      :data-source="tableData"
      :loading="isLoading"
      :expand-row-by-click="true"
      :pagination="false"
      :row-class-name="rowClassName"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'radio'">
          <a-radio
            v-if="record.link"
            :checked="selectedRow?.key === record.key"
            @change="() => handleSelect(record)"
          />
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
import { getListCategorySystemPage } from '@/api/decoration';
import { ref, onMounted } from 'vue';
import { message } from "woody-ui";
// import { getListCategorySystemPage } from '@/api/decoration';

const emit = defineEmits(['onFormData']);
const selectedRowKeys = ref<any[]>([]);
const selectedRow = ref<any>({});
const isLoading = ref<boolean>(false);

const systemPageColumns = [
  {
    title: "",
    dataIndex: "radio",
    key: "radio",
    align: "left",
    width: 80,
  },
  {
    title: "本地生活",
    dataIndex: "title",
    key: "title",
    align: "left",
  },
];
const tableData = ref<any[]>([]);

const fetchData = async () => {
  isLoading.value = true;
  try {
    const res: any = await getListCategorySystemPage();
    if (res.code === 0) {
      tableData.value = res.data.map((category, index) => ({
        key: `category_${index}`,
        title: category.categoryName,
        children: category.systemPages.map((page, subIndex) => ({
          key: `page_${index}_${subIndex}`,
          title: page.pageName,
          link: page.link,
          ...page,
        })),
      }));
    }
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const handleSelect = (record) => {
  console.log(record,'record');
  if (record.link) {
    selectedRow.value = record;
    selectedRowKeys.value = [record.key];
    emit('onFormData', {
      enums: 'SYSTEM_PAGE',
      name: record.title,
      uri: record.link,
      clickType: '1',
      uriRouteType: '1',
    });
  }
};

const rowClassName = (record) => {
  const classNames = [];
  if (record.link) {
    classNames.push('child-row');
  }
  if (selectedRow.value && record.key === selectedRow.value.key) {
    classNames.push('selected-row');
  }
  return classNames.join(' ');
};

const validate = () => {
  if (!selectedRow.value || Object.keys(selectedRow.value).length === 0) {
    message.warning('请选择一个系统页面');
    return false;
  }
  return true;
};

const resetData = () => {
  selectedRow.value = {};
  selectedRowKeys.value = [];
  if (tableData.value.length === 0) {
    fetchData();
  }
}

onMounted(() => {
  fetchData();
});

defineExpose({ resetData, validate });
</script>
<style lang="less" scoped>
.goodsmam-table {
  margin-top: 24px;
}

/* 隐藏子节点的缩进和展开图标，以实现更好的对齐 */
:deep(.child-row .ant-table-row-indent),
:deep(.child-row .ant-table-row-expand-icon) {
  display: none !important;
}

:deep(.selected-row > td),
:deep(.selected-row:hover > td) {
  background-color: #E6F4FF !important;
}
</style> 