<template>
  <a-drawer
    title="选择"
    :size="'large'"
    prevent-scroll-through
    :visible="isShowUrl"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isShowUrl = false)"
    @cancel="() => (isShowUrl = false)"
    @open="handleDrawerOpen"
  >
    <div class="selectPage">
      <a-tabs v-model:activeKey="tabsIndex" @change="onTabChange">
        <a-tab-pane v-for="tab in visibleTabs" :key="tab.key" :tab="tab.title">
          <!-- 
            使用动态组件，并监听onFormData事件获取子组件的表单数据
            onFormData事件预期返回的数据格式：
            {
              enums: string, // 数据类型标识，如'GOODS'、'SYSTEM_PAGE'、'CUSTOM_LINK'等
              ...otherData    // 其他相关数据，根据不同的组件类型会有不同的字段
            }
          -->
          <component
            :is="tab.component"
            :ref="el => setComponentRef(el, tab.refName)"
            @onFormData="handleFormData"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button @click="isShowUrl = false" class="mr10">取消</a-button>
        <a-button type="primary" @click="submitOk">确定</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue';
  import { message } from 'woody-ui';
  import { isObj } from '@/utils';
  import SubPageSelect from './SubPageSelect.vue';
  import GoodsSelect from './GoodsSelect.vue';
  import BrandSelect from './BrandSelect.vue';
  import GoodsComp from './GoodsComp.vue';
  import CustomLinkSelect from './CustomLinkSelect.vue';
  import BrandComp from './BrandComp.vue';
  import SystemPageSelect from './SystemPageSelect.vue';
  import SystemContentPageSelect from './SystemContentPageSelect/index.vue';

  /**
   * 所有标签页的配置
   * 每个标签页对应一个组件，通过onFormData事件获取组件内的表单数据
   */
  const TABS_CONFIG = [
    { key: 1, title: '商品', component: GoodsSelect, refName: 'goodsSelectRef' },
    { key: 2, title: '品牌', component: BrandSelect, refName: 'brandSelectRef' },
    { key: 3, title: '商品组件', component: GoodsComp, refName: 'goodsCompRef' },
    { key: 4, title: '品牌组件', component: BrandComp, refName: 'brandCompRef' },
    { key: 5, title: '二级页面', component: SubPageSelect, refName: 'pageSelectRef' },
    { key: 6, title: '系统页面', component: SystemPageSelect, refName: 'systemPageSelectRef' },
    { key: 7, title: '系统内容页', component: SystemContentPageSelect, refName: 'systemContentPageSelectRef' },
    { key: 8, title: '自定义链接', component: CustomLinkSelect, refName: 'customLinkRef' },
  ];

  const props = defineProps({
    templateId: {
      type: String,
      default: '',
    },
    // 控制显示的标签页，为 null 则显示所有
    enabledTabs: {
      type: Array,
      default: null, // 为 null 或 undefined 意味着所有标签页都启用
    },
  });

  const emit = defineEmits(['onPageCallBack']);
  const isShowUrl = ref<boolean>(false);
  const uriIndex = ref<number | null>(null);
  const tabsIndex = ref<number | null>(null);
  const emitSelectData = ref<any>({});
  
  // 存储子组件实例的引用
  const componentRefs = ref({});
  // 动态设置子组件的引用
  const setComponentRef = (el, refName) => {
    if (el && refName) {
      componentRefs.value[refName] = el;
    }
  };

  // 根据 enabledTabs 计算出需要显示的标签页
  const visibleTabs = computed(() => {
    if (!props.enabledTabs) {
      return TABS_CONFIG;
    }
    const enabledKeys = new Set(props.enabledTabs);
    return TABS_CONFIG.filter(tab => enabledKeys.has(tab.key));
  });

  // 选择第一个可见的标签页
  const selectFirstVisibleTab = () => {
    if (visibleTabs.value && visibleTabs.value.length > 0) {
      tabsIndex.value = visibleTabs.value[0].key;
      nextTick(() => {
        onTabChange(tabsIndex.value);
      });
    }
  };

  // 当drawer打开时触发
  const handleDrawerOpen = () => {
    selectFirstVisibleTab();
  };

  // 监听visibleTabs变化，确保在标签页过滤后仍选中第一个可见标签
  watch(() => visibleTabs.value, () => {
    if (isShowUrl.value) {
      selectFirstVisibleTab();
    }
  }, { deep: true });

  // 在组件挂载时初始化
  onMounted(() => {
    // 可以在这里处理其他初始化逻辑，如果需要
  });

  // tab选项卡切换
  const onTabChange = value => {
    try {
      const tabConfig = TABS_CONFIG.find(t => t.key === value);
      if (tabConfig && tabConfig.refName) {
        const componentRef = componentRefs.value[tabConfig.refName];
        if (componentRef && typeof componentRef.resetData === 'function') {
          componentRef.resetData();
        }
      }
    } catch (error) {
      console.error('Error in onTabChange:', error);
    }
  };

  /**
   * 统一处理子组件通过onFormData事件传递的表单数据
   * 各子组件通过enums字段标识数据类型，如'GOODS'、'BRAND'、'PAGE'等
   * 数据会被存储在emitSelectData中，并在submitOk方法中通过onPageCallBack事件传递给父组件
   * @param {Object} data - 子组件传递的表单数据
   */
  const handleFormData = data => {
    emitSelectData.value = {
      ...data,
      index: uriIndex.value,
    };
  };

  // 确认选择
  const submitOk = () => {
    const activeTab = TABS_CONFIG.find(tab => tab.key === tabsIndex.value);
    let isValid = false;

    if (activeTab && activeTab.refName) {
      const activeComponent = componentRefs.value[activeTab.refName];
      // 如果组件有自己的validate方法，则使用它
      if (activeComponent && typeof activeComponent.validate === 'function') {
        isValid = activeComponent.validate();
      } else {
        // 否则，使用通用检查
        isValid = Object.keys(emitSelectData.value).length > 0;
        if (!isValid) {
          message.warning('请先选择或配置内容!');
        }
      }
    } else {
      // 对没有ref的组件进行回退检查
      isValid = Object.keys(emitSelectData.value).length > 0;
      if (!isValid) {
        message.warning('请先选择或配置内容!');
      }
    }
    
    if (isValid) {
      emit('onPageCallBack', emitSelectData.value);
      isShowUrl.value = false;
    }
  };

  const selectPageRef = index => {
    uriIndex.value = index;
    emitSelectData.value = {};
    
    // 设置为可见后立即选中第一个标签
    isShowUrl.value = true;
    nextTick(() => {
      selectFirstVisibleTab();
    });
  };

  defineExpose({ selectPageRef });
</script>
<script lang="ts">
export default {
  name: 'SelectPageIndex'
}
</script>
<style lang="less" scoped>
  .selectPage {
    padding-top: 10px;
    padding-left: 10px;

    .selectPage-size {
      padding-top: 48px;
    }
  }
  .drawer-footer {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  :deep(.ant-tabs-nav) {
    margin: 0 !important;
  }
  :deep(.ant-tabs-content-holder) {
    margin-top: 10px;
  }
</style> 