<template>
  <a-modal
      v-model:open="isShowUpdataImage"
      width="35%"
      destroy-on-close
      title="上传图片"
      okText="确定"
      cancelText="取消"
      @ok="confirmOk"
      @cancel="() => (isShowUpdataImage = false)"
    >
    <div class="updata-image">
        <div class="updata-select">
          <span>所在分组</span>
          <span>*</span>
        </div>
        <div class="updata-select-xz">
          <a-tree-select
            v-model:value="ownerGroupId"
            :tree-data="treeData"
            :field-names="treeProps"
            allow-clear
            placeholder="请选择内容"
            style="width:300px"
          />
          <a-button
            type="primary"
            style="margin-left:10px"
            @click="goToMaterialCenter"
          >
            添加分组
          </a-button>
        </div>
        <div class="updata-image-sc">
          <span>本地图片</span>
          <span>*</span>
        </div>
        <div class="updata-image-list">
          <wd-upload
            biz-type="cms"
            :file-size="1"
            :max-width="750"
            :max-count="10"
            multiple
            :file-list="fileList"
            @get-url-list="afferentUrlChange"
          />
          <p>支持.jpg、.gif、.jpeg、.png格式，最多10张，上传图片宽度限制为750px，大小限制1M</p>
        </div>
      </div>
  </a-modal>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { message } from "woody-ui";
import { getGroupQuery, getMaterialSave } from "@/api/cms/decoration";
import router from "@/router";
import { useRoute } from "vue-router";
import WdUpload from "@/components/WdUpload/index.vue";

const emit = defineEmits(["onUploadImageCallBack"]);
const isShowUpdataImage = ref<boolean>(false);
const treeData = ref<any[]>([]);
const ownerGroupId = ref<any | null>(null);
const updataCopy = ref<any[]>([]);
const updatas = ref<any>("");
const route = useRoute();
const fileList = ref([]);

const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id',
};

// 跳转素材中心
const goToMaterialCenter = () => {
  const { VITE_CURRENT_URL } = import.meta.env;
  const url = `${VITE_CURRENT_URL}/#/market/index?market=/#/cms/materialCenter?modalType=ADD`;
  window.open(url, "_blank");
};
// 所有素材分组查询
const httpGetgroupQuery = () => {
  getGroupQuery({ flatFlag: false }).then((res) => {
    if (res.code === 0) {
      treeData.value = [
        { id: "0", level: 1, name: "未分组", parentId: "0", children: null },
        ...res.data,
      ];
    }
  });
};

// 图片上传
const afferentUrlChange = (list) => {
  updatas.value = list;
};

// 确认
const confirmOk = async () => {
  if (ownerGroupId.value == null) {
    message.warning("请选择所在分组！");
  } else if (updatas.value.length === 0) {
    message.warning("请上传图片！");
  } else {
    let params = {
      materialList: updatas.value,
      ownerGroupId: ownerGroupId.value === "0" ? "" : ownerGroupId.value,
    };
    const res = await getMaterialSave(params);
    if (res.code === 0) {
      message.success("上传成功！");
      emit("onUploadImageCallBack");
      isShowUpdataImage.value = false;
    } else {
      message.error("上传失败，请重试！");
    }
  }
};
// 是否显示图片上传弹框方法
const showUpdataImageRef = () => {
  updatas.value = [];
  treeData.value = [];
  updataCopy.value = [];
  ownerGroupId.value = "0";
  httpGetgroupQuery();
  isShowUpdataImage.value = true;
};

defineExpose({ showUpdataImageRef });
</script>
<style lang="less">
.updata-image {
  .updata-select {
    display: flex;
    align-items: center;

    span:first-child {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }

    span:last-child {
      font-weight: 400;
      font-size: 14px;
      color: #ff436a;
      margin-left: 5px;
    }
  }

  .updata-select-xz {
    display: flex;
    align-items: center;
    margin-top: 8px;
    .t-select-input {
      width: 324px;
    }
    .t-input__wrap {
      width: 324px;
      height: 32px;
      background: #ffffff;
    }

    .t-link {
      margin-left: 20px;
    }
  }

  .updata-image-sc {
    display: flex;
    align-items: center;
    margin-top: 24px;

    span:first-child {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }

    span:last-child {
      font-weight: 400;
      font-size: 14px;
      color: #ff436a;
      margin-left: 5px;
    }
  }

  .updata-image-list {
    margin-top: 12px;
    .t-upload__card-content {
      padding: 0 !important;
    }
  }
}
</style>
