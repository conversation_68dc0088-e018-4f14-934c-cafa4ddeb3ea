<template>
  <div class="item">
    <div class="item-title">选择内容<span class="require">*</span></div>
    <div class="link-box" @click="openDecoraTableDia('subPage')">
      <div v-if="info.name" class="text">{{ info?.name }}</div>
      <div v-else class="text2">请选择</div>
      <img
        class="icon"
        :src="`${VITE_API_IMG}/2024/08/8dc33db7311c40d8830980c24583aee4.png`"
        alt=""
      />
    </div>
  </div>

  <select-decr-page v-model:m-val="drawerState" />
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { storeToRefs } from "pinia";
import { ref, watch, watchEffect } from "vue";
import SelectDecrPage from "./components/selectDecrPage.vue";
import { getDecorationStore } from "@/store";

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);

const info = ref<any>();
watchEffect(() => {
  const detailData = decorationInfo.value.components.find(
    (item) => item.templateId === "content"
  ).info;
  info.value = detailData;
});

const drawerState = ref({
  visible: false,
  navType: "subPage",
  selectNavData: { pageName: "", id: "" },
});
const openDecoraTableDia = (navType: string) => {
  drawerState.value.visible = true;
  drawerState.value.navType = navType;
};
watch(drawerState, () => {
  const { pageName, id } = drawerState.value.selectNavData;
  if (pageName) {
    info.value.name = pageName;
    info.value.id = id;
  }
});
</script>

<style lang="less" scoped>
.item {
  margin-bottom: 30px;

  .item-title {
    font-size: 14px;
    color: #05082c;
    margin-bottom: 8px;
    .require {
      font-size: 14px;
      color: #ff436a;
      margin-left: 2px;
    }
  }
}
.link-box {
  width: 100%;
  height: 30px;
  position: relative;
  color: var(--td-text-color-primary);
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-default);
  cursor: pointer;
  padding: 0 8px;
  display: flex;
  align-items: center;
  .text {
    color: #05082c;
  }
  .text2 {
    color: #939596;
  }
  .icon {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 5px;
    top: 6px;
  }
}
</style>
