<template>
  <div class="item">
    <div class="item-title">
      选择搜索页面
      <span class="require">*</span>
    </div>
    <a-select
      placeholder="请选择"
      v-model:value="info.id"
      filterable
      clearable
      :options="searchPageArr"
      :loading="searchPageLoading"
      @blur="blurSearchPage"
      @input-change="inputSearchPageName($event)"
      style="width: 300px"
    ></a-select>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { getDecorationStore } from '@/store';
  import { searchPageList } from '@/api/decoration';

  const decorationStore = getDecorationStore();
  const detailData = decorationStore.decorationInfo.components.find(
    (item: any) => item.templateId === 'contentSearch',
  );

  const info = ref<any>({});
  info.value = detailData.info;

  const searchPageLoading = ref(false);
  const searchPageArr = ref<any>([{ label: '同城店铺搜索页', value: null }]);
  const searchPageText = ref('');
  // const getSearchPageList = () => {
  //   searchPageLoading.value = true;
  //   searchPageList({ versionName: searchPageText.value })
  //     .then((res) => {
  //       if (res.code === 0) {
  //         searchPageArr.value = res.data.map((item: any) => {
  //           return {
  //             ...item,
  //             value: `${item.id}`,
  //             label: item.versionName,
  //           };
  //         });
  //         searchPageLoading.value = false;
  //       }
  //     })
  //     .finally(() => {
  //       searchPageLoading.value = false;
  //     });
  // };
  const inputSearchPageName = (e: any) => {
    searchPageText.value = e;
  };
  const blurSearchPage = () => {
    searchPageText.value = '';
  };

  onMounted(() => {
    // getSearchPageList();
  });
</script>

<style lang="less" scoped>
  .item {
    margin-bottom: 30px;

    .item-title {
      font-size: 14px;
      color: #05082c;
      margin-bottom: 8px;
      .require {
        font-size: 14px;
        color: #ff436a;
        margin-left: 2px;
      }
    }
  }
</style>
