<template>
  <div class="cube-content">
    <div class="cube-bgColor-set">
      <span class="bgColor-text">背景色设置</span>
      <span class="bgColor-xh">*</span>
    </div>
    <div class="cube-selectBg">
      <color-picker
        v-model:pureColor="cubeData.bkColor"
      />
    </div>
    <div class="cube-switch-title">
      <span class="switch-bgColor-text">是否支持透明背景</span>
      <span class="switch-bgColor-xh">*</span>
    </div>
    <div class="cube-switch">
      <a-switch v-model:checked="cubeData.isBglucency"></a-switch>
    </div>
    <div class="cube-tpl">
      <span>选择一个模板</span>
      <span>*</span>
      <span>{{
        tplIconData.find((item) => item.iconType == cubeData.iconType).iconText
      }}</span>
    </div>
    <div class="cube-tpl-list">
      <div class="tp-list">
        <img
          v-for="(item, index) in tplIconData"
          :key="index"
          :src="
            item.iconType != cubeData.iconType ? item.icon : item.selectIcon
          "
          @click="selectIcon(item)"
        />
      </div>
      <!-- 模板图片展示 -->
      <div class="tpl-list-img">
        <!-- 一行二个 -->
        <template v-if="cubeData.iconType == 'TWO_ROW'">
          <div
            v-for="(item, index) in cubeData.list"
            :key="index"
            class="row-two"
            :class="{ 'row-two-border': index === 0 }"
          >
            <div v-if="item.imgUrl === null">
              <p>宽度</p>
              <p>355像素</p>
            </div>
            <template v-if="item.imgUrl && item.imgUrl != null">
              <img :src="item.imgUrl" />
            </template>
          </div>
        </template>
        <!-- 一行三个 -->
        <template v-if="cubeData.iconType == 'THREE_ROW'">
          <div
            v-for="(item, index) in cubeData.list"
            :key="index"
            class="row-three"
          >
            <div v-if="!item.imgUrl">
              <p>宽度</p>
              <p>237像素</p>
            </div>
            <template v-if="item.imgUrl && item.imgUrl != null">
              <img :src="item.imgUrl" />
            </template>
          </div>
        </template>
        <!-- 一行四个 -->
        <template v-if="cubeData.iconType == 'FOUR_ROW'">
          <div
            v-for="(item, index) in cubeData.list"
            :key="index"
            class="row-four"
          >
            <template v-if="!item.imgUrl">
              <div class="cube-row-four-p">
                <p>宽度</p>
                <p>178像素</p>
              </div>
            </template>
            <template v-if="item.imgUrl && item.imgUrl != null">
              <img :src="item.imgUrl" />
            </template>
          </div>
        </template>
        <!-- 一行五个 -->
        <template v-if="cubeData.iconType == 'FIVE_ROW'">
          <div
            v-for="(item, index) in cubeData.list"
            :key="index"
            class="row-five"
          >
            <template v-if="!item.imgUrl">
              <div class="cube-row-five-p">
                <p>宽度</p>
                <p>142像素</p>
              </div>
            </template>
            <template v-if="item.imgUrl && item.imgUrl != null">
              <img :src="item.imgUrl" />
            </template>
          </div>
        </template>
        <!-- 二行五个 -->
        <template v-if="cubeData.iconType == 'TWO_FIVE_ROW'">
          <div
            v-for="(item, index) in cubeData.list"
            :key="index"
            class="two-five-row"
          >
            <template v-if="!item.imgUrl">
              <div class="two-five-row-p">
                <p>宽度</p>
                <p>142像素</p>
              </div>
            </template>
            <template v-if="item.imgUrl && item.imgUrl != null">
              <img :src="item.imgUrl" />
            </template>
          </div>
        </template>
        <!-- 四宫格 -->
        <template v-if="cubeData.iconType == 'FOUR_GRID'">
          <div class="four-grid-row">
            <div
              v-for="(item, index) in cubeData.list"
              :key="index"
              class="four-grid"
            >
              <div v-if="!item.imgUrl">
                <p>宽度</p>
                <p>355像素</p>
              </div>
              <template v-if="item.imgUrl && item.imgUrl != null">
                <img :src="item.imgUrl" />
              </template>
            </div>
          </div>
        </template>
        <!-- 一左两右 -->
        <template v-if="cubeData.iconType == 'ONE_LEFT_TWO_RIGHT'">
          <div class="row-one">
            <div v-if="!cubeData.list[0].imgUrl">
              <p>宽度</p>
              <p>355像素</p>
            </div>
            <template
              v-if="cubeData.list[0].imgUrl && cubeData.list[0].imgUrl != null"
            >
              <img :src="cubeData.list[0].imgUrl" />
            </template>
          </div>
          <div class="row-right-center">
            <div class="row-right-tow row-right-tow-border">
              <div v-if="!cubeData.list[1].imgUrl">
                <p>宽度</p>
                <p>355像素</p>
              </div>
              <template
                v-if="
                  cubeData.list[1].imgUrl && cubeData.list[1].imgUrl != null
                "
              >
                <img :src="cubeData.list[1].imgUrl" />
              </template>
            </div>
            <div class="row-right-tow">
              <div v-if="!cubeData.list[2].imgUrl">
                <p>宽度</p>
                <p>355像素</p>
              </div>
              <template
                v-if="
                  cubeData.list[2].imgUrl && cubeData.list[2].imgUrl != null
                "
              >
                <img :src="cubeData.list[2].imgUrl" />
              </template>
            </div>
          </div>
        </template>
        <!-- 一上两下 -->
        <template v-if="cubeData.iconType == 'ONE_UP_TWO_DOWN'">
          <div class="row-top-one">
            <div v-if="!cubeData.list[0].imgUrl">
              <p>宽度</p>
              <p>710像素</p>
            </div>
            <template
              v-if="cubeData.list[0].imgUrl && cubeData.list[0].imgUrl != null"
            >
              <img :src="cubeData.list[0].imgUrl" />
            </template>
          </div>
          <div class="row-top-tow row-top-tow-border">
            <div v-if="!cubeData.list[1].imgUrl">
              <p>宽度</p>
              <p>355像素</p>
            </div>
            <template
              v-if="cubeData.list[1].imgUrl && cubeData.list[1].imgUrl != null"
            >
              <img :src="cubeData.list[1].imgUrl" />
            </template>
          </div>
          <div class="row-top-tow">
            <div v-if="!cubeData.list[2].imgUrl">
              <p>宽度</p>
              <p>355像素</p>
            </div>
            <template
              v-if="cubeData.list[2].imgUrl && cubeData.list[2].imgUrl != null"
            >
              <img :src="cubeData.list[2].imgUrl" />
            </template>
          </div>
        </template>
      </div>
      <!-- 添加模板图片 -->
      <draggable :sort="true" :list="cubeData.list" :animation="300">
        <template #item="{ element, index }">
          <div class="tpl-from-content">
            <div class="tpl-from-list">
              <div class="from-list-left" @click="showImageMethod(index)">
                <template v-if="element.imgUrl">
                  <img class="from-show-img" :src="element.imgUrl" />
                  <div class="from-click-img">更换图片</div>
                </template>
                <template v-if="!element.imgUrl">
                  <img
                    class="from-icon-add"
                    :src="`${VITE_API_IMG}/2024/08/061bfc84196541b9b8210e5f0ee43fc9.png`"
                  />
                  <p>点击上传图片</p>
                </template>
              </div>
              <div class="from-list-right">
                <p>建议图片大小1M以内。</p>
                <div class="from-input" @click="showUrlMethod(index)">
                  <span
                    :style="{ color: element.uriName ? '#05082c' : '#c7d1d0' }"
                    >{{
                      element.uriName ? element.uriName : "请选择链接地址"
                    }}</span
                  >
                  <img
                    :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
                  />
                </div>
                <!-- 参数配置 -->
                <template
                  v-if="
                    element.uriType === '4' &&
                    !isEmptyValue(element.param.b_label)
                  "
                >
                  <div class="parameter">
                    可传参数：{{ element.param.b_label }}
                  </div>
                  <div class="set-parameter">
                    <div class="label">参数值：</div>
                    <a-input v-model:value="element.param.c_categoryName" clearable />
                  </div>
                </template>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
  <!-- 我的图片列表弹框 -->
  <myPicture ref="myPictureRef" @on-image-call-back="onImageCallBack" />
  <!-- 选择跳转页面（内容）弹框 -->
  <selectPage
    ref="selectPageRef"
    :enabledTabs="[1,2,5,6,8]"
    :template-id="detailData?.templateId"
    @on-page-call-back="onPageCallBack"
  />
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, onMounted, watchEffect } from "vue";
import { storeToRefs } from "pinia";
import draggable from "vuedraggable";
import { tplIconData, iconTypeMap } from "../config/index";
import { getDecorationStore } from "@/store";
import myPicture from "./components/myPicture.vue";
import selectPage from "./components/selectPage/index.vue";
import { isEmptyValue } from "@/utils";

const decorationStore = getDecorationStore();
const { decorationInfo, activeNav } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find(
  (item: any) => item.flagId === activeNav.value.flagId
);
const cubeData = ref<any>({});
cubeData.value = detailData.info;
// 获取子组件方法
type myPictureType = { showImageRef: (index: number) => void };
const myPictureRef = ref<myPictureType | null>(null);
type selectPageType = { selectPageRef: (index) => void };
const selectPageRef = ref<selectPageType | null>(null);
// 选择模板
const selectIcon = (item: { iconType: PropertyKey }) => {
  // 检查 item.iconType 是否有效
  if (!iconTypeMap.hasOwnProperty(item.iconType)) return;
  const tplIndex = iconTypeMap[item.iconType];
  cubeData.value.iconType = item.iconType;
  cubeData.value.list = [];
  for (let i = 0; i < tplIndex; i++) {
    cubeData.value.list.push({
      imgUrl: null,
      uriType: 0,
      uriName: null,
      param: {
        b_id: null,
        b_link: null,
        b_label: null,
        c_categoryName: "",
      },
    });
  }
};

const onImageCallBack = (item: { index: any; url: any }) => {
  const { index, url } = item;
  cubeData.value.list[index].imgUrl = url;
};
const onPageCallBack = (item) => {
  debugger;
  const {
    index,
    id,
    prodSource,
    productId,
    pageName,
    brandName,
    prductName,
    brandId,
    link,
    systemPageId,
    enums,
    clickType,
    uriRouteType,
  } = item;
  const cubeList = cubeData.value.list[index];
  if (cubeList.param) {
    cubeList.param.systemPage = {
      systemPageId: null,
      systemParam: {
        classId: "",
      },
    };
  }

  // 统一接收子组件传递的clickType || uriRouteType
  cubeList.clickType = clickType;
  if (uriRouteType !== undefined) {
    cubeList.uriRouteType = uriRouteType;
  } else {
    delete cubeList.uriRouteType; // 如果子组件没传，就删除旧值
  }

  switch (enums) {
    case "PAGE":
      cubeList.uriType = 0;
      cubeList.uriName = pageName;
      if (!cubeList.param) cubeList.param = { id };
      if (cubeList.param) cubeList.param.id = id;
      cubeList.uri = item.uri;
      break;
    case "GOODS":
      cubeList.uriType = 1;
      cubeList.uriName = prductName;
      if (!cubeList.param) cubeList.param = { id: productId, prodSource };
      if (cubeList.param) cubeList.param.id = productId;
      if (cubeList.param) cubeList.param.prodSource = prodSource;
      break;
    case "BRAND":
      cubeList.uriType = 2;
      cubeList.uriName = brandName;
      if (!cubeList.param) cubeList.param = { id: brandId };
      if (cubeList.param) cubeList.param.id = brandId;
      break;
    case "SYSTEM":
      const param = JSON.parse(item.param);
      cubeList.uriType = "4";
      cubeList.uriName = pageName;
      cubeList.clickType = isEmptyValue(systemPageId) ? "" : "1";
      cubeList.param = {
        b_id: systemPageId,
        b_link: link,
        b_label: Array.isArray(param) ? param[0].label : null,
        c_categoryName: Array.isArray(param) ? param[0].key : null,
      };
  }
};
// 是否显示我的图片组件
const showImageMethod = (index: number) => {
  if (myPictureRef.value) {
    if (typeof myPictureRef.value.showImageRef === "function") {
      myPictureRef.value.showImageRef(index);
    }
  }
};

// 是否显示跳转链接图片组件
const showUrlMethod = (index: number) => {
  if (selectPageRef.value) {
    if (typeof selectPageRef.value.selectPageRef === "function") {
      selectPageRef.value.selectPageRef(index);
    }
  }
};

watchEffect(() => {
  decorationStore.setDecorationInfo({
    ...decorationInfo.value,
    components: decorationInfo.value.components.map((item: any) => {
      if (item.flagId === activeNav.value.flagId) {
        cubeData.value = item.info;
      }
      return item;
    }),
  });
});

onMounted(() => {});
</script>
<style lang="less" scoped>
@import "../css/cube.less";
</style>
