<template>
  <div class="d-box flex">
    <div class="text1">分割间距</div>
    <div class="text2">单位：像素</div>
  </div>
  <!-- <a-slider
    v-model:value="info.lineHeight"
    :show-tooltip="true"
    :marks="[1, 8, 16, 24, 32, 40]"
    :input-number-props="true"
    :min="1"
    :max="40"
  /> -->
  <a-row style="width: 300px">
    <a-col :span="16">
      <a-slider
        v-model:value="info.lineHeight"
        :min="1"
        :max="40"
        :marks="marks"
        :tooltip-visible="true"
      />
    </a-col>
    <a-col :span="6" :offset="1">
      <a-input-number
        v-model:value="info.lineHeight"
        :min="1"
        :max="40"
        style="margin-left: 16px"
      />
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { getDecorationStore } from '@/store';

const decorationStore = getDecorationStore();
const { decorationInfo, activeNav } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find((item: any) => item.flagId === activeNav.value.flagId);

const info = ref<any>({});
info.value = detailData.info;

const marks = {
  1: '1',
  8: '8',
  16: '16',
  24: '24',
  32: '32',
  40: '40'
}

watchEffect(() => {
  decorationStore.setDecorationInfo({
    ...decorationInfo.value,
    components: decorationInfo.value.components.map((item: any) => {
      if (item.flagId === activeNav.value.flagId) {
        info.value = item.info;
      }
      return item;
    }),
  });
});
</script>

<style lang="less" scoped>
.d-box {
  margin-bottom: 20px;
  font-size: 14px;
  .text1 {
    color: #05082c;
  }
  .text2 {
    color: #a2abbd;
    margin-left: 8px;
  }
}
</style>
