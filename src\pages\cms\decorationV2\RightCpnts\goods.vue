<template>
  <div class="goods-content">
    <div class="goods-title">样式设置</div>
    <div class="goods-radio">
      <a-radio-group
        v-model:value="goodsData.style"
        name="city"
        :options="radioData"
        @change="onRadioStyle"
      />
    </div>
    <div class="goods-manage">商品管理</div>
    <div class="goods-select-details">
      <div class="select-details-list" @click="showGoodsTableMethod">
        <span class="select-details-text">
          {{
            goodsData.feCategoryName
              ? goodsData.feCategoryName
              : "请选择前台分类"
          }}
        </span>
        <img
          :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
        />
      </div>
    </div>
    <!-- 展示一行二个 -->
    <template
      v-if="
        goodsData.style == 2 &&
        goodsData.feCategoryName &&
        goodsData.feCategoryId
      "
    >
      <div class="goodsImg-title">图片组</div>
      <div class="goodsImg-text">建议图片尺寸宽度350px，高度不限制。</div>
      <template v-for="(item, sup) in goodsData.imgGroup" :key="sup">
        <div class="goodsImg-arr">
          <span>图片组排序</span>
          <span>支持组内拖动排序</span>
        </div>
        <div class="goodsImg-InputCenter">
          <div class="Input-wt">
            <a-input-number
              v-model:value="item.order"
              placeholder="请输入排序值"
              theme="normal"
              :max="99"
              :min="1"
              :allow-input-over-limit="false"
              style="width: 200px"
            />
          </div>
          <a-popconfirm title="确认删除吗" @confirm="deleteImgArr(sup)">
            <div class="InputCenter-Img">
              <img
                :src="`${VITE_API_IMG}/2024/08/f65f4cf66d574ece885af0cccfcaaa7b.png`"
              />
            </div>
          </a-popconfirm>
        </div>
        <div v-if="item.order" class="goodsImg-list">
          <draggable :sort="true" :list="item.imgs" :animation="300">
            <template #item="{ element, index }">
              <div class="goodsImg-list-center">
                <div class="Image-add" @click="showImageMethod(sup, index)">
                  <div v-if="!element.imgUrl" class="Image-add-center">
                    <img
                      :src="`${VITE_API_IMG}/2024/08/5df954a7fc7a4cc890eeec1db794ac50.png`"
                    />
                    <p>点击上传图片</p>
                  </div>
                  <img v-if="element.imgUrl" :src="element.imgUrl" />
                </div>
                <div class="Image-url">
                  <p class="url-p">
                    建议图片尺寸宽度350px，高度不限制。大小1M以内。
                  </p>
                  <div class="url-box" @click="showUrlPopupMethod(sup, index)">
                    <div class="url-box-dz">
                      <span>{{
                        element.uriName ? element.uriName : "请选择链接"
                      }}</span>
                      <img
                        :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
                      />
                    </div>
                  </div>
                </div>
                <a-popconfirm
                  title="确认删除吗"
                  :popup-props="{ placement: 'top-left' }"
                  @confirm="shutImage(sup, index)"
                >
                  <img
                    v-if="index != 0"
                    class="shut-Img"
                    :src="`${VITE_API_IMG}/2024/08/b48cb30452984634ba1c6114202be146.png`"
                  />
                </a-popconfirm>
              </div>
            </template>
          </draggable>
          <div class="goodsImg-list-footer" @click="addImagelist(sup)">
            <div class="footer-button">
              <span>添加图片{{ item.imgs.length }}</span>
              <span :style="{ color: item.imgs.length == 5 ? '#333333' : '' }"
                >/5</span
              >
            </div>
          </div>
        </div>
      </template>
      <div class="goodsImg-addArr" @click="addImageArr">
        <div class="addArr-center">
          <img
            :src="`${VITE_API_IMG}/2024/08/2b7d0c791cab4b08b13244678572f7f5.png`"
          />
          <span>添加图片组</span>
        </div>
      </div>
    </template>
  </div>
  <!-- 我的图片列表弹框 -->
  <myPicture ref="myPictureRef" @on-image-call-back="onImageCallBack" />
  <!-- 选择跳转页面（内容）弹框 -->
  <selectPage ref="selectPageRef" @on-page-call-back="onPageCallBack" />
  <!-- 商品管理列表弹框 -->
  <goodsmam ref="goodsmamRef" @on-classify-call-back="onClassifyCallBack" />
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref } from "vue";
import { storeToRefs } from "pinia";
import draggable from "vuedraggable";
import { radioData } from "../config/index";
import { getDecorationStore } from "@/store";
import myPicture from "./components/myPicture.vue";
import selectPage from "./components/selectPage/index.vue";
import goodsmam from "./components/goodsmam.vue";

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find(
  (item: any) => item.templateId == "goods"
);
const goodsData = ref<any>({});
goodsData.value = detailData.info;
type myPictureType = { showImageRef: (sup, index) => void };
const myPictureRef = ref<myPictureType | null>(null);
type SelectPageType = { selectPageRef: () => void };
const selectPageRef = ref<SelectPageType | null>(null);
const clickPageIndex = ref<any>({});
type goodsmamType = { showGoodsmamRef: () => void };
const goodsmamRef = ref<goodsmamType | null>(null);
// 切换样式清空数据
const onRadioStyle = () => {
  goodsData.value.feCategoryId = null;
  goodsData.value.feCategoryName = null;
  goodsData.value.imgGroup = [
    {
      order: null,
      imgs: [
        {
          imgUrl: null,
          uriType: 0,
          uriName: null,
          param: { id: null, prodSource: null },
        },
      ],
    },
  ];
};
// 添加一级类目图片组
const addImageArr = () => {
  if (goodsData.value.imgGroup.length < 20) {
    goodsData.value.imgGroup.push({
      order: null,
      imgs: [
        {
          imgUrl: null,
          uriType: 0,
          uriName: null,
          param: { id: null, prodSource: null },
        },
      ],
    });
  }
};
// 删除一级类目图片组
const deleteImgArr = (sup) => {
  goodsData.value.imgGroup.splice(sup, 1);
};
// 添加二级类目图片列表
const addImagelist = (sup) => {
  if (goodsData.value.imgGroup[sup].imgs.length < 5) {
    goodsData.value.imgGroup[sup].imgs.push({
      imgUrl: null,
      uriType: 0,
      uriName: null,
      param: { id: null, prodSource: null },
    });
  }
};
// 关闭二级类目图片列表
const shutImage = (sup, index) => {
  if (goodsData.value.imgGroup[sup].imgs.length > 1) {
    goodsData.value.imgGroup[sup].imgs.splice(index, 1);
  }
};
const onImageCallBack = (item) => {
  const { index, idx, url } = item;
  goodsData.value.imgGroup[index].imgs[idx].imgUrl = url;
};
// 是否显示我的图片组件
const showImageMethod = (sup, index) => {
  if (myPictureRef.value) {
    if (typeof myPictureRef.value.showImageRef === "function") {
      myPictureRef.value.showImageRef(sup, index);
    }
  }
};
const onPageCallBack = (item) => {
  const { sup, index } = clickPageIndex.value;
  const {
    id,
    productId,
    prodSource,
    pageName,
    brandName,
    prductName,
    brandId,
    enums,
  } = item;
  const goodsList = goodsData.value.imgGroup[sup];
  switch (enums) {
    case "PAGE":
      goodsList.imgs[index].uriType = 0;
      goodsList.imgs[index].uriName = pageName;
      if (!goodsList.imgs[index].param) goodsList.imgs[index].param = { id };
      if (goodsList.imgs[index].param) goodsList.imgs[index].param.id = id;
      break;
    case "GOODS":
      goodsList.imgs[index].uriType = 1;
      goodsList.imgs[index].uriName = prductName;
      if (!goodsList.imgs[index].param)
        goodsList.imgs[index].param = { id: productId, prodSource };
      if (goodsList.imgs[index].param) {
        goodsList.imgs[index].param.id = productId;
        goodsList.imgs[index].param.prodSource = prodSource;
      }
      break;
    case "BRAND":
      goodsList.imgs[index].uriType = 2;
      goodsList.imgs[index].uriName = brandName;
      if (!goodsList.imgs[index].param)
        goodsList.imgs[index].param = { id: brandId };
      if (goodsList.imgs[index].param) goodsList.imgs[index].param.id = brandId;
      break;
  }
};
// 是否显示跳转链接图片组件
const showUrlPopupMethod = (sup, index) => {
  if (selectPageRef.value) {
    if (typeof selectPageRef.value.selectPageRef === "function") {
      clickPageIndex.value = { sup, index };
      selectPageRef.value.selectPageRef();
    }
  }
};
const onClassifyCallBack = (item) => {
  console.log(item,'item')
  const { id, categoryName } = item;
  goodsData.value.feCategoryId = id;
  goodsData.value.feCategoryName = categoryName;
};
// 是否显示商品管理组件
const showGoodsTableMethod = () => {
  if (goodsmamRef.value) {
    if (typeof goodsmamRef.value.showGoodsmamRef === "function") {
      goodsmamRef.value.showGoodsmamRef();
    }
  }
};
</script>
<style lang="less" scoped>
@import "../css/goods.less";
</style>
