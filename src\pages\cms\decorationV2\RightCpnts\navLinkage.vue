<template>
  <div class="navLinage-content">
    <div class="navLinage-nav">
      <ul class="nav-center">
        <li
          v-for="(item, index) in navLinkagetData.list"
          :key="index"
          class="nav-li"
          :class="{ active: currentTab == index }"
          @click="selectTab(index)"
        >
          <span>{{ item.stairTitle }}</span>
          <template v-if="index != 0">
            <a-popconfirm title="确认删除吗" @confirm="shutNavOk(index)">
              <img
                :src="`${VITE_API_IMG}/2024/08/b51391e417c346298640dc527fa111da.png`"
              />
            </a-popconfirm>
          </template>
        </li>
      </ul>
      <template v-if="navLinkagetData.list.length < 4">
        <div class="navAdd-right" @click="addNavigation">
          <img
            :src="`${VITE_API_IMG}/2024/08/5df954a7fc7a4cc890eeec1db794ac50.png`"
          />
        </div>
      </template>
    </div>
    <div class="navLinage-Input-center">
      <div class="navLinage-Input-title">
        <div class="Input-title">
          <span>一级标题</span>
          <span>*</span>
        </div>
        <div class="Input-text">
          <a-input
            v-model:value="childrenData.name"
            show-count
            :maxlength="5"
            placeholder="请输入内容"
            auto-width
            @change="onChangeNavTitle"
          />
        </div>
        <div class="Input-suffix">
          <a-input-number
            v-model:value="childrenData.order"
            :max="10"
            :min="1"
            placeholder="请输入排序"
          />
        </div>
      </div>
      <template v-if="childrenData.name">
        <p class="Input-text-p">以下二级导航，支持拖动排序</p>
        <draggable
          :sort="true"
          :list="childrenData.children"
          :animation="300"
          :move="onMove"
        >
          <template #item="{ element, index }">
            <div class="Input-nav-list">
              <div class="nav-list-title">
                <div class="title">
                  <span>二级标题</span>
                  <span>*</span>
                </div>
                <div class="text-Input">
                  <a-input
                    v-model:value="element.name"
                    :maxlength="5"
                    show-count
                    placeholder="请输入内容"
                    auto-width
                  />
                </div>
              </div>
              <div class="nav-list-url">
                <div class="title">
                  <span>链接</span>
                  <span>*</span>
                </div>
                <div class="url-Input" @click="openLinkSelector(index)">
                  <span v-if="!element.uriName" class="url-Input-span1"
                    >请选择内容</span
                  >
                  <span class="url-Input-span2">{{ element.uriName }}</span>
                  <img
                    :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
                  />
                </div>
              </div>
              <template v-if="index != 0">
                <a-popconfirm
                  title="确认删除吗"
                  @confirm="levelShutNav(index)"
                >
                  <img
                    class="nav-list-shut"
                    :src="`${VITE_API_IMG}/2024/08/b48cb30452984634ba1c6114202be146.png`"
                  />
                </a-popconfirm>
              </template>
            </div>
          </template>
        </draggable>
      </template>
    </div>
    <template v-if="childrenData.name">
      <div class="navLinkage-footer" @click="addNavLink">
        <div class="footer-image">
          <img
            :src="`${VITE_API_IMG}/2024/08/2b7d0c791cab4b08b13244678572f7f5.png`"
          />
          <div class="navLinkage-addImage-tp">
            <span>添加导航{{ childrenData.children.length }}/</span>
            <span>20</span>
          </div>
        </div>
      </div>
    </template>
  </div>
  <!-- 选择发布模板 -->
  <SelectPage ref="selectPageRef" :enabled-tabs="currentEnabledTabs" @onPageCallBack="onLinkSelected" />
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { storeToRefs } from "pinia";
import draggable from "vuedraggable";
import { getDecorationStore } from "@/store";
import SelectPage from './components/selectPage/index.vue';
import { createRelatedComponent, deleteRelatedComponent } from '@/services/relatedComponentService';

const { VITE_API_IMG } = import.meta.env;
const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find(
  (item: any) => item.templateId === "navLinkage"
);
const navLinkagetData = ref<any>({});
navLinkagetData.value = detailData.info;
const currentTab = ref<string | number>(0);
const childrenData = ref<any>({});
childrenData.value = navLinkagetData.value.list[0];

const selectPageRef = ref(null);
const currentLinkIndex = ref(0);
const currentEnabledTabs = ref(null);

// 定义允许的标签页
const FIRST_NAV_TABS = [3, 4, 7]; // 首个导航只支持商品组件、品牌组件、系统内容页
const OTHER_NAV_TABS = [3, 4, 5, 6, 7, 8]; // 其他导航支持商品组件、品牌组件、二级页面、系统页面、系统内容页、自定义链接

// 新增一级导航
const addNavigation = () => {
  if (navLinkagetData.value.list.length < 4) {
    navLinkagetData.value.list.push({
      stairTitle: "导航",
      name: null,
      order: null,
      children: [
        {
          name: null,
          uriName: null,
          param: null,
          navType: null,
        },
      ],
    });
  }
};
const onChangeNavTitle = () => {
  if (!childrenData.value.name) {
    childrenData.value.children = [
      {
        name: null,
        uriName: null,
        param: null,
        navType: null,
      },
    ];
  }
};
// 选择一级导航按钮
const selectTab = (index) => {
  currentTab.value = index;
  childrenData.value = navLinkagetData.value.list[index];
};
// 删除一级导航
const shutNavOk = (index) => {
  // 在删除一级导航前，先删除其下所有二级导航关联的组件
  const navToDelete = navLinkagetData.value.list[index];
  if (navToDelete && navToDelete.children) {
    navToDelete.children.forEach(child => {
      if (
        (child.navType === 'GOODS_COMPONENT' || child.navType === 'BRAND_COMPONENT') &&
        child.param &&
        child.param.componentId
      ) {
        deleteRelatedComponent(child.param.componentId);
      }
    });
  }

  navLinkagetData.value.list.splice(index, 1);
  currentTab.value = 0;
  childrenData.value = navLinkagetData.value.list[0];
};
// 新增二级类目
const addNavLink = () => {
  const idx = currentTab.value;
  const childrenList = navLinkagetData.value.list[idx].children;
  if (childrenList.length < 20) {
    childrenList.push({
      name: null,
      uriName: null,
      param: null,
      navType: null,
    });
  }
};
// 删除二级导航
const levelShutNav = (index) => {
  const idx = currentTab.value;
  const navItem = navLinkagetData.value.list[idx].children[index];

  // 检查是否有关联组件需要删除
  if (
    (navItem.navType === 'GOODS_COMPONENT' || navItem.navType === 'BRAND_COMPONENT') &&
    navItem.param &&
    navItem.param.componentId
  ) {
    deleteRelatedComponent(navItem.param.componentId);
  }

  navLinkagetData.value.list[idx].children.splice(index, 1);
};

const openLinkSelector = (index: number) => {
  currentLinkIndex.value = index;
  // 根据索引确定启用哪些标签页
  currentEnabledTabs.value = index === 0 ? FIRST_NAV_TABS : OTHER_NAV_TABS;
  selectPageRef.value?.selectPageRef(index);
};

const onLinkSelected = async (data: any) => {
  if (childrenData.value.children[currentLinkIndex.value]) {
    const currentItem = childrenData.value.children[currentLinkIndex.value];
    const { enums, pageName, id, uri, name, clickType, uriRouteType } = data;

    // 如果之前已经关联了商品组件或品牌组件，需要先删除旧的关联
    if (
      (currentItem.navType === 'GOODS_COMPONENT' || currentItem.navType === 'BRAND_COMPONENT') &&
      currentItem.param &&
      currentItem.param.componentId
    ) {
      deleteRelatedComponent(currentItem.param.componentId);
    }
    
    // 统一接收子组件传递的clickType || uriRouteType
    currentItem.clickType = clickType;
    if (uriRouteType !== undefined) {
      currentItem.uriRouteType = uriRouteType;
    } else {
      delete currentItem.uriRouteType; // 如果子组件没传，就删除旧值
    }

    switch (enums) {
      // 二级页面
      case 'PAGE':
        currentItem.uriName = data.pageName || data.name;
        currentItem.navType = 'PAGE';
        currentItem.param = { id: data.id };
        currentItem.uri = data.uri;
        break;
      case 'GOODS':
        currentItem.uriName = data.prductName || data.productName;
        currentItem.navType = 'GOODS';
        currentItem.param = { id: data.productId };
        break;
      case 'BRAND':
        currentItem.uriName = data.brandName;
        currentItem.navType = 'BRAND';
        currentItem.param = { id: data.brandId };
        break;
      case 'GOODS_COMPONENT':
        currentItem.uriName = data.name || '商品组件';
        currentItem.navType = 'GOODS_COMPONENT';
        const goodsStyle = data.style;
        const goodsComponentId = await createRelatedComponent(
          detailData.flagId,
          'GOODS_COMPONENT',
          {
            feCategoryId: data.feCategoryId,
            feCategoryName: data.feCategoryName,
            style: goodsStyle,
            imgGroup: goodsStyle === '1' ? null : data.imgGroup || [],
          },
        );
        currentItem.param = { componentId: goodsComponentId };
        break;
      case 'BRAND_COMPONENT':
        currentItem.uriName = data.name || '品牌组件';
        currentItem.navType = 'BRAND_COMPONENT';
        const brandComponentId = await createRelatedComponent(
          detailData.flagId,
          'BRAND_COMPONENT',
          {
            type: data.type,
            platCategoryId: data.platCategoryId,
            platCategoryName: data.platCategoryName,
            brands: data.brands || [],
          },
        );
        currentItem.param = { componentId: brandComponentId };
        break;
      case 'SYSTEM_PAGE':
        currentItem.uriName = name || data.name;
        currentItem.navType = 'SYSTEM_PAGE';
        currentItem.param = { id, uri };
        break;
      case 'SYSTEM_CONTENT_PAGE':
        currentItem.uriName = data.name;
        currentItem.navType = 'SYSTEM_CONTENT_PAGE';
        currentItem.param = { id: data.contentId, uri: data.uri };
        break;
      case 'CUSTOM_LINK':
        currentItem.uriName = name || data.name || uri;
        currentItem.navType = 'CUSTOM_LINK';
        currentItem.uri = uri;
        break;
      default:
        currentItem.uriName = pageName || name;
        currentItem.navType = enums;
        if (id) {
          currentItem.param = { id };
        }
        break;
    }
  }
};

// 表示不能被停靠，其他元素不可以与当前元素调换位置
const onMove = (e) => {
  // 如果拖拽的是第一个元素，不允许移动
  if (e.draggedContext.index === 0) {
    return false;
  }
  // 如果要移动到第一个位置，不允许
  if (e.relatedContext.index === 0) {
    return false;
  }
  // 其他情况允许移动
  return true;
};

watch(navLinkagetData.value, (newVal) => {
  newVal.list.forEach((item, index) => {
    if (index === 1) item.stairTitle = "导航二";
    if (index === 2) item.stairTitle = "导航三";
    if (index === 3) item.stairTitle = "导航四";
  });
});

onMounted(() => {
  navLinkagetData.value.list.forEach((item, index) => {
    if (!item.stairTitle) {
      if (index === 0) item.stairTitle = "导航一";
      if (index === 1) item.stairTitle = "导航二";
      if (index === 2) item.stairTitle = "导航三";
      if (index === 3) item.stairTitle = "导航四";
    }
  });
});
</script>
<style lang="less" scoped>
@import "../css/navLinkage.less";
</style>
