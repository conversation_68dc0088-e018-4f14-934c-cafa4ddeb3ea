<template>
  <div class="item">
    <div class="item-title">文字颜色</div>
    <a-radio-group v-model:value="info.textColor">
      <a-radio value="#172119">黑色</a-radio>
      <a-radio value="#fff">白色</a-radio>
    </a-radio-group>
  </div>
  <div class="item">
    <div class="item-title">是否支持透明背景</div>
    <a-switch v-model:checked="info.isTransparent"></a-switch>
  </div>
  <div v-if="!info.isTransparent" class="item">
    <div class="item-title">背景色<span class="require">*</span></div>
    <div class="bg">
      <color-picker
        v-model:pureColor="info.bgColor"
      />
    </div>
  </div>
  <div class="item">
    <div class="item-title">选择搜索页面<span class="require">*</span></div>
    <a-select
      style="width: 320px"
      v-model:value="info.searchPageId"
      show-search
      allow-clear
      :options="searchPageArr"
      :loading="searchPageLoading"
      :filter-option="filterOption"
      @search="getSearchPageList"
      @focus="getSearchPageList"
      @blur="blurSearchPage"
      @change="inputSearchPageName"
    >
    </a-select>
  </div>
  <div class="item">
    <div class="item-title">搜索滚动词</div>
    <a-switch v-model:checked="info.isSearchSwiperWords"></a-switch>
  </div>
  <div v-if="info.isSearchSwiperWords" class="item">
    <div class="item-title">选择滚动词组<span class="require">*</span></div>
    <a-select
      style="width: 320px"
      v-model:value="info.rollingWordsId"
      show-search
      allow-clear
      :loading="swiperWordsLoading"
      :options="swiperWordsArr"
      placeholder="请选择滚动词条"
      :filter-option="filterOption"
      @focus="getSwiperWordsList"
      @blur="blurSwiperWords"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { getDecorationStore } from "@/store";
import { searchPageList, swiperWordsList } from "@/api/decoration";

const decorationStore = getDecorationStore();
const detailData = decorationStore.decorationInfo.components.find(
  (item: any) => item.templateId === "homePageTitle"
);

const info = ref<any>({});
info.value = detailData.info;

// 选择搜索页面
const searchPageLoading = ref(false);
const searchPageArr = ref<any>([]);
const searchPageText = ref("");
const getSearchPageList = () => {
  searchPageLoading.value = true;
  searchPageList({ versionName: searchPageText.value })
    .then((res) => {
      if (res.code === 0) {
        searchPageArr.value = res.data.map((item: any) => {
          return {
            ...item,
            value: `${item.id}`,
            label: item.versionName,
          };
        });
        searchPageLoading.value = false;
      }
    })
    .finally(() => {
      searchPageLoading.value = false;
    });
};
// 自定义输入过滤规则（忽略大小写）
const filterOption = (input, option) => {
  return option?.label?.toLowerCase().includes(input.toLowerCase())
}
const inputSearchPageName = (e: any) => {
  searchPageText.value = e;
};
const blurSearchPage = () => {
  searchPageText.value = "";
};

// 选择滚动词
const swiperWordsLoading = ref(false);
const swiperWordsArr = ref<any>([]);
const swiperWordsText = ref("");
const getSwiperWordsList = () => {
  swiperWordsLoading.value = true;
  swiperWordsList({ wordGroupName: swiperWordsText.value })
    .then((res) => {
      if (res.code === 0) {
        swiperWordsArr.value = res.data.map((item: any) => {
          return {
            ...item,
            value: `${item.id}`,
            label: item.wordGroupName,
          };
        });
        swiperWordsLoading.value = false;
      }
    })
    .finally(() => {
      swiperWordsLoading.value = false;
    });
};
const inputSwiperWordsName = (e: any) => {
  swiperWordsText.value = e;
};
const blurSwiperWords = () => {
  swiperWordsText.value = "";
};

onMounted(() => {
  getSearchPageList();
  getSwiperWordsList();
});
</script>

<style lang="less" scoped>
.item {
  margin-bottom: 30px;

  .item-title {
    font-size: 14px;
    color: #05082c;
    margin-bottom: 8px;
  }
}
.bg {
  width: 180px;
}
.require {
  font-size: 14px;
  color: #ff436a;
  margin-left: 2px;
}
</style>
