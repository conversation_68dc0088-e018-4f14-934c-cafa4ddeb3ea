<template>
  <div v-if="!props.toolConfig.length" class="box1"></div>
  <div v-else class="box">
    <div
      v-for="(item, index) in props.toolConfig"
      :key="index"
      class="tool-item"
      @click="chooseToolNavs(item)"
    >
      <img class="icon" :src="item.icon" alt="" />
      <div class="text">{{ item.templateName }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { PropType, ref } from "vue";
import { message } from "woody-ui";
import { Tool } from "../type";
import { getDecorationStore } from "@/store";
import { getComponentDefaultValue } from "../config/defaultValue";

const decorationStore = getDecorationStore();

const props = defineProps({
  toolConfig: {
    type: Array as PropType<Tool[]>,
    default: () => [],
  },
});

const { decorationInfo } = storeToRefs(decorationStore);
const toolNavs = ref([]);
const chooseToolNavs = (item: Tool) => {
  toolNavs.value = decorationInfo.value.components;

  const hasNavLinkage = toolNavs.value.some(nav => nav.templateId === 'navLinkage');
  const hasNavFlow = toolNavs.value.some(nav => nav.templateId === 'navFlow');

  if (item.templateId === 'navFlow' && hasNavLinkage) {
    message.warning('联动导航和瀑布流导航只能存在一个');
    return;
  }

  if (item.templateId === 'navLinkage' && hasNavFlow) {
    message.warning('联动导航和瀑布流导航只能存在一个');
    return;
  }

  if (
    !toolNavs.value.find((nav: Tool) => nav.templateId === item.templateId) ||
    item.templateId === "cube" ||
    item.templateId === "divider" ||
    item.templateId === "singleAdvert"
  ) {
    const newItem = {
      ...item,
      flagId: item.templateId + new Date().getTime(),
      info: getComponentDefaultValue(item.templateId),
    };
    const targetArr = [...toolNavs.value];

    if (newItem.templateId === "search") {
      // 如果是页面search组件，则移动到标题栏后面（index:1）
      targetArr.splice(1, 0, newItem);
    } else if (newItem.templateId === "contentSearch") {
      // 如果是内容的search组件，默认置顶
      targetArr.splice(0, 0, newItem);
    } else {
      const bottomCompIndex = targetArr.findIndex((comp) => comp.isBottom);
      if (newItem.isBottom) {
        targetArr.push(newItem);
      } else if (bottomCompIndex > -1) {
        targetArr.splice(bottomCompIndex, 0, newItem);
      } else {
        targetArr.push(newItem);
      }
    }

    decorationStore.setDecorationInfo({
      ...decorationInfo.value,
      components: targetArr,
    });
    decorationStore.setActiveNav(newItem);
  } else {
    message.warning("该组件已存在，请勿重复添加！");
  }
};
</script>

<style lang="less" scoped>
.box1 {
  width: 284px;
  height: calc(100vh - 76px);
}

.box {
  width: 284px;
  height: calc(100vh - 76px);
  background-color: #fff;
  border-radius: 0px 16px 16px 0px;
  flex-wrap: wrap;
  padding: 12px;
}

.tool-item {
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: 18px;
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: #f7f8fa;
    border-radius: 8px;
  }

  .icon {
    width: 36px;
    height: 36px;
  }

  .text {
    font-size: 13px;
    color: #495366;
  }
}
</style>
