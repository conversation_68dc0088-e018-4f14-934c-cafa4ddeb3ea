import advert from "@/pages/cms/decorationV2/PhoneCpnts/advert.vue";
import brand from "@/pages/cms/decorationV2/PhoneCpnts/brand.vue";
import shop from "@/pages/cms/decorationV2/PhoneCpnts/shop.vue";
import cube from "@/pages/cms/decorationV2/PhoneCpnts/cube.vue";
import divider from "@/pages/cms/decorationV2/PhoneCpnts/divider.vue";
import goods from "@/pages/cms/decorationV2/PhoneCpnts/goods.vue";
import notice from "@/pages/cms/decorationV2/PhoneCpnts/notice.vue";
import search from "@/pages/cms/decorationV2/PhoneCpnts/search.vue";
import contentSearch from "@/pages/cms/decorationV2/PhoneCpnts/contentSearch.vue";
import homePageTitle from "@/pages/cms/decorationV2/PhoneCpnts/titleHomePage.vue";
import subPageTitle from "@/pages/cms/decorationV2/PhoneCpnts/titleSubPage.vue";
import navSingle from "@/pages/cms/decorationV2/PhoneCpnts/navSingle.vue";
import navLinkage from "@/pages/cms/decorationV2/PhoneCpnts/navLinkage.vue";
import navTop from "@/pages/cms/decorationV2/PhoneCpnts/navTop.vue";
import navLeft from "@/pages/cms/decorationV2/PhoneCpnts/navLeft.vue";
import navFlow from "@/pages/cms/decorationV2/PhoneCpnts/navFlow.vue";
import navDouble from "@/pages/cms/decorationV2/PhoneCpnts/navDouble.vue";
import content from "@/pages/cms/decorationV2/PhoneCpnts/content.vue";
import singleAdvert from "@/pages/cms/decorationV2/PhoneCpnts/singleAdvert.vue";
import jinGang from "@/pages/cms/decorationV2/PhoneCpnts/jinGang.vue";

export const phoneComponentArr = [
  {
    templateId: "advert",
    templateName: "图片广告",
    component: advert,
  },
  {
    templateId: "brand",
    templateName: "品牌",
    component: brand,
  },
  {
    templateId: "shop",
    templateName: "店铺",
    component: shop,
  },
  {
    templateId: "cube",
    templateName: "魔方",
    component: cube,
  },
  {
    templateId: "divider",
    templateName: "辅助分割",
    component: divider,
  },
  {
    templateId: "goods",
    templateName: "商品",
    component: goods,
  },
  {
    templateId: "navSingle",
    templateName: "单层导航",
    component: navSingle,
  },
  {
    templateId: "navLinkage",
    templateName: "联动导航",
    component: navLinkage,
  },
  {
    templateId: "navFlow",
    templateName: "瀑布流导航",
    component: navFlow,
  },
  {
    templateId: "navTop",
    templateName: "顶部分类商品",
    component: navTop,
  },
  {
    templateId: "navLeft",
    templateName: "左侧分类商品",
    component: navLeft,
  },
  {
    templateId: "navDouble",
    templateName: "双层分类商品",
    component: navDouble,
  },
  {
    templateId: "notice",
    templateName: "公告",
    component: notice,
  },
  {
    templateId: "search",
    templateName: "搜索",
    component: search,
  },
  {
    templateId: "contentSearch",
    templateName: "搜索",
    component: contentSearch,
  },
  {
    templateId: "homePageTitle",
    templateName: "首页标题栏",
    component: homePageTitle,
  },
  {
    templateId: "subPageTitle",
    templateName: "标题栏",
    component: subPageTitle,
  },
  {
    templateId: "content",
    templateName: "内容",
    component: content,
  },
  {
    templateId: "singleAdvert",
    templateName: "单图广告",
    component: singleAdvert,
  },
  {
    templateId: "jinGang",
    templateName: "金刚位",
    component: jinGang,
  },
];
