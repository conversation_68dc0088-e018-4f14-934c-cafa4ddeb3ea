.adver-content {
  .advert-title {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }

  .advert-switch {
    margin-top: 14px;
    margin-bottom: 30px;
    .t-switch {
      width: 36px !important;
      height: 20px !important;
    }
  }

  .advert-image {
    .advert-image-one {
      font-weight: 600;
      font-size: 14px;
      color: #000000;
    }

    .advert-image-two {
      font-weight: 400;
      font-size: 12px;
      color: #a2abbd;
      margin-left: 8px;
    }
  }

  .advert-image-cd {
    font-weight: 400;
    font-size: 12px;
    color: #495366;
    margin-bottom: 16px;
  }
  .advert-add-border-list {
    .advert-add-border {
      width: 100%;
      border-radius: 8px;
      border: 1px solid #f2f5f9;
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      position: relative;
      &:hover .brand-shut-Img {
        opacity: 1;
      }
      .advert-add-border-pad {
        width: 100%;
        padding-top: 15px;
        padding-left: 15px;
        padding-bottom: 15px;
        display: flex;
        align-items: center;
        .advert-addImage-left {
          width: 120px;
          height: 120px;
          background: #f1f6f8;
          border: 1px solid #e0e8ed;
          border-radius: 5px;
          overflow: hidden;
          cursor: pointer;
          position: relative;
          .advert-addImage-left-img {
            width: 120px !important;
            height: 120px !important;
            margin-top: 0 !important;
          }
          img {
            width: 24px;
            height: 24px;
            margin-left: auto;
            margin-right: auto;
            display: block;
            margin-top: 33px;
          }
          p {
            font-weight: 400;
            font-size: 12px;
            color: #636d7e;
            text-align: center;
          }
          .aduert-update {
            width: 100%;
            line-height: 38px;
            background: #1d2426;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            text-align: center;
            display: none;
            position: absolute;
            bottom: 0;
            left: 0;
          }
        }
        .advert-addImage-left:hover {
          .aduert-update {
            display: block !important;
          }
        }
        .advert-addImage-right {
          width: 52%;
          height: 120px;
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .advert-addImage-right-cd {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #818999;
          }
          .advert-addImage-right-Input {
            width: 100%;
            height: 32px;
            border: 1px solid #edf1f2;
            border-radius: 3px;
            display: flex;
            align-items: center;
            cursor: pointer;
            &:hover {
              border-color: var(--td-brand-color);
              .right-Input-tz {
                border-top: 1px solid var(--td-brand-color);
                border-bottom: 1px solid var(--td-brand-color);
              }
            }

            .right-Input-tz {
              width: 44px;
              line-height: 32px;
              height: 32px;
              background: #edf1f2;
              font-weight: 400;
              font-size: 14px;
              color: #05082c;
              text-align: center;
              box-sizing: border-box;
              border-top: 1px solid #edf1f2;
              border-bottom: 1px solid #edf1f2;
            }
            .right-Input-text {
              width: 100px;
              font-weight: 400;
              font-size: 14px;
              color: #636d7e;
              line-height: 32px;
              text-indent: 10px;
            }
            .right-Input-mm {
              font-weight: 400;
              font-size: 14px;
              color: #333333;
              width: 85%;
              text-indent: 10px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .right-Input-img {
              width: 16px;
              height: 16px;
              margin-right: 5px;
            }
          }

          .advert-addImage-right-bg {
            p {
              font-weight: 400;
              font-size: 14px;
              color: #05082c;
              margin-bottom: 5px;
            }

            .t-input--auto-width {
              width: 100% !important;
            }
          }
        }
      }
      .brand-shut-Img {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 4px;
        right: 6px;
        cursor: pointer;
        opacity: 0;
      }
    }
    .advert-add-border:hover{
      cursor: pointer;
      display: block;
      border-radius: 8px;
      box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.12),
        0px 4px 5px 0px rgba(0,0,0,0.08),
        0px 1px 10px 0px rgba(0,0,0,0.05);
      background: #FFFFFF;
      .brand-shut-Img{
        display: block;
      }
    }
  }

  .advert-addImage-pus {
    width: 305px;
    height: 40px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px dashed #1a7af8;
    margin-left: auto;
    margin-right: auto;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .advert-addImage-text {
      display: flex;
      align-items: center;

      img {
        width: 18px;
        height: 18px;
      }

      .advert-addImage-tp {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;

        span {
          font-weight: 400;
          font-size: 16px;
          color: #1a7af8;
          margin-left: 10px;
        }

        :last-child {
          color: #a8d0ff;
          margin: 0 !important;
        }
      }
    }
  }
}
.drawer-footer {
  display: flex;
  align-items: center;
  justify-content: end;
}
