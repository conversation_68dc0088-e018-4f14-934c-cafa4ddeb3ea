.brand-container {
    margin-left: 8px;
    margin-right: 8px;

    .container-center {
        background: #ffffff;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;

        .brand-card-cell-header {
            display: flex;
            align-items: center;

            .brand-icon-box {
                width: 50px;
                height: 50px;
                border-radius: 4px;
                border: 1px solid #f5f6f7;

                img {
                    width: 50px;
                    height: 50px;
                    border-radius: 4px;
                }
            }

            .brand-right-box {
                width: 83%;
                height: 50px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-left: 8px;

                .brand-enter-box {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .brand-shop-name {
                        width: 50%;
                        display: flex;
                        align-items: center;
                        span {
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            font-size: 14px;
                            color: #333333;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }

                        img {
                            width: 16px;
                            height: 16px;
                            margin-left: 4px;
                        }
                    }

                    .btn {
                        display: block;
                        width: 80px;
                        height: 24px;
                    }

                    .brand-distance-day {
                        display: flex;
                        align-items: center;

                        .day-one {
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            font-size: 12px;
                            color: #777777;
                        }

                        .day-num {
                            padding-left: 4px;
                            padding-right: 4px;
                            line-height: 20px;
                            background: #F43B3B;
                            border-radius: 4px;
                            text-align: center;
                            margin-right: 6px;
                            margin-left: 6px;
                            font-family: Source Han Sans CN;
                            font-weight: bold;
                            font-size: 14px;
                            color: #ffffff;
                        }

                        .day-t {
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            font-size: 12px;
                            color: #777777;
                        }
                    }
                }

                .brand-distance-end-tips {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .brand-distance-label {
                        display: flex;
                        align-items: center;

                        img {
                            width: 45px;
                            height: 18px;
                            margin-right: 4px;
                        }
                    }
                }
            }
        }

        .prod-card-list-body {
            display: flex;
            align-items: center;
            padding-top: 8px;
            overflow-x: auto;
            scrollbar-width: none;
            scrollbar-color: transparent transparent;

            .card-item-box {
                width: 100px;
                margin-right: 10px;

                .prod-image {
                    width: 100px;
                    height: 100px;
                    border-radius: 8px;
                }

                .prod-card-info {
                    margin-left: 4px;

                    .prod-card-btn {
                        width: 16px;
                        line-height: 16px;
                        background: #ffedf1;
                        border-radius: 4px;
                        border: 1px solid #ffcdfa;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        font-size: 12px;
                        color: #d52cff;
                        text-align: center;
                        text-indent: 2px;
                    }

                    .prod-card-price {
                        display: flex;
                        align-items: baseline;

                        .price-one {
                            font-family: OPlusSans 30;
                            font-weight: bold;
                            font-size: 12px;
                            color: #f43b3b;
                        }

                        .price-two {
                            font-family: OPlusSans 30;
                            font-weight: bold;
                            font-size: 16px;
                            color: #f43b3b;
                        }
                    }

                    .price-lineation {
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        font-size: 12px;
                        color: #d9d9d9;
                        text-decoration: line-through;
                    }
                }
            }
        }
    }

    .brand-Image-default {
        width: 100%;
        height: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        border-radius: 8px;

        img {
            width: 103px;
            height: 82px;
            display: block;
            margin-left: auto;
            margin-right: auto;

        }
    }
}