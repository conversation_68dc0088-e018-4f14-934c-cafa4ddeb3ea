<template>
  <div class="goods-content">
    <template v-if="goodsList.length != 0">
      <div class="goods-nav-tap">
        <div class="nav-tap-zh">综合</div>
        <div class="nav-tap-price">
          <span>价格</span>
          <img :src="priceSortPng" />
        </div>
        <div class="nav-tap-new">上新</div>
      </div>
      <!-- 一行一个 -->
      <template v-if="prop.info.style == 1">
        <template v-for="(item, index) in goodsList" :key="index">
          <div v-if="item.type === 0" class="goods-rowOne">
            <div class="goods-rowOne-left">
              <img class="rowOne-left-Img" :src="item.productCard.pic" />
            </div>
            <div class="goods-rowOne-right">
              <div class="goods-rowOne-top">
                <div class="goods-title-line">
                  <div class="goods-name">{{ item.productCard.prodName }}</div>
                  <div
                    v-if="item.productCard.buyingPointTags"
                    class="goods-title"
                  >
                    {{
                      item.productCard.buyingPointTags.split(",").join(" | ")
                    }}
                  </div>
                </div>
                <div class="goods-price">
                  <div class="goods-price-left">
                    <div class="price-unit">¥</div>
                    <div class="price-unit-num">
                      {{ String(item.productCard.price).split(".")[0] }}
                    </div>
                    <div class="price-unit">
                      .{{
                        String(item.productCard.price).split(".")[1]
                          ? String(item.productCard.price).split(".")[1]
                          : "00"
                      }}
                    </div>
                    <div v-if="item.productCard.unit" class="price-tag">
                      /{{ item.productCard.unit }}
                    </div>
                    <div class="price-Scribing">
                      ¥{{ item.productCard.oriPrice }}
                    </div>
                  </div>
                  <div class="goods-price-right">
                    <img
                      :src="`${VITE_API_IMG}/2023/11/6f7296ba797344d6b9cae2efb2586a4b.png`"
                    />
                  </div>
                </div>
              </div>
              <div class="goods-shop">
                <img :src="item.productCard.shopImgUrl" />
                <span>
                  {{
                    item.productCard.shopName
                      ? item.productCard.shopName
                      : "-------------------"
                  }}</span
                >
              </div>
            </div>
          </div>
        </template>
      </template>
      <!-- 一行两个 -->
      <template v-if="prop.info.style == 2">
        <div class="goods-container">
          <!-- 满足瀑布流 单数-->
          <div class="goods-container-left">
            <div
              v-for="(item, index) in goodsLeftList"
              :key="index"
              class="goods-column-container"
            >
              <!-- 轮播图配置 -->
              <template v-if="item.type == 1">
                <div class="column-colo-banner">
                  <template v-if="item.imgCard.imgs.length > 1">
                    <a-carousel
                      autoplay
                      :dots="false"
                      class="column-swiper-Img"
                    >
                      <div
                        v-for="(it, idx) in item.imgCard.imgs"
                        :key="idx"
                        class="banner-swiper-Img"
                        style="background: #ffffff"
                      >
                        <a-image
                          v-if="it.imgUrl != null"
                          :src="it.imgUrl"
                          :preview="false"
                          :style="{
                            width: '172px',
                            height: 'auto',
                            objectFit: 'fill',
                          }"
                        />
                      </div>
                    </a-carousel>
                  </template>
                  <template v-if="item.imgCard.imgs.length === 1">
                    <a-image
                      :src="item.imgCard.imgs[0].imgUrl"
                      :lazy="true"
                      :fit="'fill'"
                      :position="'center'"
                      :style="{
                        width: '173px',
                        height: 'auto',
                        background: '#Ffffff',
                      }"
                    />
                  </template>
                </div>
              </template>
              <!-- 商品配置 -->
              <template v-if="item.type == 0">
                <div class="goods-item-box-content">
                  <div class="goods-item-box-wd">
                    <a-image
                      :src="item.productCard.pic"
                      :lazy="true"
                      :fit="'contain'"
                      :position="'center'"
                      :style="{
                        width: '172px',
                        height: '172px',
                        background: '#Ffffff',
                      }"
                    />
                  </div>
                  <div class="goods-item-box-details">
                    <div class="goods-name-box">
                      {{ item.productCard.prodName }}
                    </div>
                    <template v-if="item.productCard.buyingPointTags">
                      <div class="prod-buy-point-tags">
                        {{
                          item.productCard.buyingPointTags
                            .split(",")
                            .join(" | ")
                        }}
                      </div>
                    </template>
                    <div class="goods-price-box">
                      <div class="goods-price-left">
                        <div class="goods-price-unit-group">
                          <div class="goods-price-unit">¥</div>
                          <div class="goods-price-text">
                            {{ String(item.productCard.price).split(".")[0] }}
                          </div>
                          <div class="goods-price-unit">
                            .{{
                              String(item.productCard.price).split(".")[1]
                                ? String(item.productCard.price).split(".")[1]
                                : "00"
                            }}
                          </div>
                        </div>
                        <div class="crossedPrice">
                          ¥{{ item.productCard.oriPrice }}
                        </div>
                      </div>
                      <div class="shop-car-fix">
                        <img
                          :src="`${VITE_API_IMG}/2023/11/6f7296ba797344d6b9cae2efb2586a4b.png`"
                        />
                      </div>
                    </div>
                    <div class="supply-shop-box">
                      <img :src="item.productCard.shopImgUrl" />
                      <p>
                        {{
                          item.productCard.shopName
                            ? item.productCard.shopName
                            : "-------------"
                        }}
                      </p>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <!-- 满足瀑布流 双数-->
          <div class="goods-container-right">
            <div
              v-for="(item, index) in goodsRightList"
              :key="index"
              class="goods-column-container"
            >
              <!-- 轮播图配置 -->
              <template v-if="item.type == 1">
                <div class="column-colo-banner">
                  <template v-if="item.imgCard.imgs.length > 1">
                    <a-carousel
                      autoplay
                      :dots="false"
                      class="column-swiper-Img"
                    >
                      <div
                        v-for="(it, idx) in item.imgCard.imgs"
                        :key="idx"
                        class="banner-swiper-Img"
                      >
                        <a-image
                          v-if="it.imgUrl != null"
                          :src="it.imgUrl"
                          loading="lazy"
                          :preview="false"
                          :style="{
                            width: '172px',
                            height: 'auto',
                            objectFit: 'fill',
                            background: '#ffffff',
                          }"
                        />
                      </div>
                    </a-carousel>
                  </template>
                  <template v-if="item.imgCard.imgs.length === 1">
                    <a-image
                      :src="item.imgCard.imgs[0].imgUrl"
                      :lazy="true"
                      :fit="'fill'"
                      :position="'center'"
                      :style="{
                        width: '173px',
                        height: 'auto',
                        background: '#Ffffff',
                      }"
                    />
                  </template>
                </div>
              </template>
              <!-- 商品配置 -->
              <template v-if="item.type == 0">
                <div class="goods-item-box-content">
                  <div class="goods-item-box-wd">
                    <a-image
                      :src="item.productCard.pic"
                      :lazy="true"
                      :fit="'contain'"
                      :position="'center'"
                      :style="{
                        width: '172px',
                        height: '172px',
                        background: '#Ffffff',
                      }"
                    />
                  </div>
                  <div class="goods-item-box-details">
                    <div class="goods-name-box">
                      {{ item.productCard.prodName }}
                    </div>
                    <template v-if="item.productCard.buyingPointTags">
                      <div class="prod-buy-point-tags">
                        {{
                          item.productCard.buyingPointTags
                            .split(",")
                            .join(" | ")
                        }}
                      </div>
                    </template>
                    <div class="goods-price-box">
                      <div class="goods-price-left">
                        <div class="goods-price-unit-group">
                          <div class="goods-price-unit">¥</div>
                          <div class="goods-price-text">
                            {{ String(item.productCard.price).split(".")[0] }}
                          </div>
                          <div class="goods-price-unit">
                            .{{
                              String(item.productCard.price).split(".")[1]
                                ? String(item.productCard.price).split(".")[1]
                                : "00"
                            }}
                          </div>
                        </div>
                        <div class="crossedPrice">
                          ¥{{ item.productCard.oriPrice }}
                        </div>
                      </div>
                      <div class="shop-car-fix">
                        <img
                          :src="`${VITE_API_IMG}/2023/11/6f7296ba797344d6b9cae2efb2586a4b.png`"
                        />
                      </div>
                    </div>
                    <div class="supply-shop-box">
                      <img :src="item.productCard.shopImgUrl" />
                      <p>
                        {{
                          item.productCard.shopName
                            ? item.productCard.shopName
                            : "-------------"
                        }}
                      </p>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </template>
    </template>
    <template v-if="goodsList.length === 0">
      <div class="goods-Image-default">
        <img
          :src="`${VITE_API_IMG}/2024/09/c4260f883cda4ed7ad72569e2c807da7.png`"
        />
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, watchEffect } from "vue";
import { feCategorySearchPage } from "@/api/cms/decoration/index";
import priceSortPng from "@/assets/images/price_sort.png";

// 商品列表
const goodsList = ref<any[]>([]);
const goodsLeftList = ref<any[]>([]);
const goodsRightList = ref<any[]>([]);
const prop = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
// 获取商品接口
const httpFeCategorySearchPage = async () => {
  try {
    prop.info.page = 1;
    prop.info.size = 50;
    const res = await feCategorySearchPage(prop.info);
    goodsList.value = res.records;
    goodsLeftList.value = res.records.filter((_item, index) => index % 2 === 0);
    goodsRightList.value = res.records.filter(
      (_item, index) => index % 2 !== 0
    );
  } catch (error) {
    goodsLeftList.value = [];
    goodsRightList.value = [];
    goodsList.value = [];
    console.error("Error in onTabChange:", error);
  }
};
watchEffect(() => {
  if (prop.info.feCategoryId) {
    httpFeCategorySearchPage().catch(() => {
      goodsLeftList.value = [];
      goodsRightList.value = [];
      goodsList.value = [];
    });
  } else {
    goodsLeftList.value = [];
    goodsRightList.value = [];
    goodsList.value = [];
  }
});
</script>
<style lang="less" scoped>
@import "../css/goodsPhone.less";
</style>
