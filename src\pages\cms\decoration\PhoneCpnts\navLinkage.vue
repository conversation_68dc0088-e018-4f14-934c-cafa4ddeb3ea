<template>
  <div class="navLinkAge-content">
    <div class="navLinkAge-frame">
      <div class="navLinkAge-top">
        <ul class="navLinkAge-top-ul" v-if="info.list[0].name">
          <template v-for="(item, index) in info.list" :key="index">
            <li @click="tapNavigation(index)">
              <div
                class="navLinkAge-top-line"
                :class="{ selectColor: index == stairIndex }"
              >
                {{ item.name }}
              </div>
              <div class="navLinkAge-line" v-if="index == stairIndex"></div>
            </li>
          </template>
        </ul>
      </div>
      <div class="navLinkAge-bottom">
        <ul class="navLinkAge-bottom-ul" v-if="childrenData.children[0].name">
          <template v-for="(item, index) in childrenData.children" :key="index">
            <li
              :class="{ secondTypeBg: index == secondTypeIndex }"
              @click="tapSecondNav(item, index)"
            >
              {{ item.name }}
            </li>
          </template>
        </ul>
      </div>
    </div>
    <template v-for="(item, index) in targetPhoneComponentArr" :key="index">
      <component :is="item.component" :info="item.info" />
    </template>
  </div>
  <template v-if="info.list.every((item) => !item.name)">
    <img
      :src="`${VITE_API_IMG}/2024/09/2601296f8b1a4029a1890f5f9cc01d44.png`"
    />
  </template>
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, reactive, watchEffect } from "vue";
import { phoneComponentArr } from "../config/phoneCfg";
import { getDecorationDetail } from "@/api/decoration";
const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
const childrenData = ref<any>([]);
childrenData.value = props.info.list[0];
const stairIndex = ref<string | number>(0);
const secondTypeIndex = ref<string | number>(0);
const id = ref<string | null>(null);
id.value = childrenData.value.children[0].param.id;
const phoneComponent = reactive(phoneComponentArr);
const targetPhoneComponentArr = ref<any>([]);

// 选择一级类目
const tapNavigation = (index) => {
  stairIndex.value = index;
  secondTypeIndex.value = 0;
  childrenData.value = props.info.list[index];
  if (childrenData.value.children[0].param.id) {
    let id = childrenData.value.children[0].param.id;
    httpGetDecorationDetail(id);
  } else {
    targetPhoneComponentArr.value = [];
  }
};
// 选择二级类目
const tapSecondNav = (item, index) => {
  secondTypeIndex.value = index;
  if (item.param.id) {
    httpGetDecorationDetail(item.param.id);
  }
};
const httpGetDecorationDetail = async (id) => {
  let toolNavs = [];
  const res = await getDecorationDetail({ id: id });
  toolNavs = res.data.components;
  toolNavs.forEach((item: any, index) => {
    item.flagId = item.templateId + index;
  });
  computeTargetPhoneComponent(toolNavs);
};
const computeTargetPhoneComponent = (data) => {
  const componentArr = [];
  data.forEach((item) => {
    phoneComponent.forEach((itm: any) => {
      if (item.templateId === itm.templateId) {
        componentArr.push({ ...itm, ...item, flagId: item.flagId });
      }
    });
  });
  targetPhoneComponentArr.value = componentArr;
};

watchEffect(() => {
  if (props.info.list[0].children[0].param.id) {
    let id = props.info.list[0].children[0].param.id;
    httpGetDecorationDetail(id);
  }
});
</script>
<style lang="less" scoped>
@import "../css/navLinkagePhone.less";
</style>
