<template>
  <div class="n-box flex">
    <img
      :src="`${VITE_API_IMG}/2024/08/3220be215ad84d94a0cf746dcb1c119d.png`"
      alt=""
      class="icon"
    />
    <div>{{ info.text }}</div>
  </div>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.n-box {
  margin-left: 8px;
  margin-right: 8px;
  font-size: 14px;
  color: #ff7d00;
  background: #ffffff;
  border-radius: 8px;
  padding: 8px 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  .icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}
</style>
