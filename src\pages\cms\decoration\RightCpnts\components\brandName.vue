<template>
  <a-drawer
    title="选择指定品牌"
    :size="'large'"
    prevent-scroll-through
    :visible="isBrandName"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isBrandName = false)"
    @cancel="() => (isBrandName = false)"
  >
    <a-form :data="goodsFrom" layout="vertical">
      <a-row :align="'bottom'" :gutter="20">
        <a-col :span="8">
          <a-form-item label="品牌名称">
            <a-input v-model:value="goodsFrom.brandName" placeholder="请输入品牌名称" clearable>
              <template #suffix>
                <img
                  class="selectPage-icon"
                  :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
                />
              </template>
            </a-input>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item>
            <a-button type="primary" @click="brandInquire" class="mr10">查询</a-button>
            <a-button @click="resetBrandOk">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="brandName-table">
      <a-table
        :scroll="{ x: 1000 }"
        max-height="500px"
        :columns="brandNameColumns"
        :pagination="orderPagination"
        :data-source="goodsTableData"
        :loading="isLoading"
        :row-selection="rowSelection"
        row-key="brandId"
        hover
        @change="onBrandSize"
      >
        <template #bodyCell="{ column, record }">
          <!-- <template v-if="column.dataIndex === 'radio'">
            <a-radio
              :checked="selectedRowKeys?.brandId === record.brandId"
              @change="() => brandSelect(record)"
            />
          </template> -->
          <template v-if="column.dataIndex === 'brandName'">
            <div class="selectPage-img-center">
              <a-image
                :src="record.brandUrl"
                :alt="record.brandName"
                :lazy="true"
                fit="cover"
                :style="{ width: '60px', height: '60px' }"
              />
              <span>{{ record.brandName }}</span>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" size="medium" @click="submitOk" class="mr10">确定</a-button>
        <a-button @click="isBrandName = false">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
  const { VITE_API_IMG } = import.meta.env;
  import { ref, reactive, computed } from 'vue';
  import { message } from 'woody-ui';
  import { akcPageBrandInfo } from '@/api/cms/decoration/index';
  import { BRANDNAME_COLUMNS, BRANDSIZE, GOODSTYPE } from './const';

  const emit = defineEmits(['onBrandNameCallBack']);
  const isBrandName = ref(false);
  const emitBrandData = ref<any[]>([]); // 跨页记录完整数据
  const selectedRowKeys = ref<any[]>([]); // 跨页记录 brandId
  const isLoading = ref(false);
  const brandNameColumns = reactive(BRANDNAME_COLUMNS);
  const orderPagination = reactive({ ...BRANDSIZE });
  const goodsFrom = reactive({ ...GOODSTYPE });
  const goodsTableData = ref<any[]>([]);
  const goodsSelectData = ref<any[]>([]);

  // 获取品牌列表
  const httpBrandNameList = async () => {
    isLoading.value = true;
    try {
      const { current, pageSize } = orderPagination;
      const params = { ...goodsFrom, current, pageSize };
      const res = await akcPageBrandInfo(params);
      const records = res.data.records || [];

      // 标记为禁用
      records.forEach(item => {
        const found = goodsSelectData.value.find(it => it.brandId === item.brandId);
        if (found) item.isForbid = true;
      });

      goodsTableData.value = records;
      orderPagination.total = res.data.total;

      // 恢复当前页勾选状态
      selectedRowKeys.value = emitBrandData.value.map(i => i.brandId);
    } catch (error) {
      goodsTableData.value = [];
      orderPagination.total = 0;
      console.error('Error in httpBrandNameList:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // 分页切换
  const onBrandSize = (pagination: any) => {
    orderPagination.current = pagination.current;
    orderPagination.pageSize = pagination.pageSize;
    httpBrandNameList();
  };

  // 查询
  const brandInquire = () => {
    orderPagination.current = 1;
    httpBrandNameList();
  };

  // 重置
  const resetBrandOk = () => {
    orderPagination.current = 1;
    goodsFrom.brandName = null;
    httpBrandNameList();
  };

  // 多选配置
  const rowSelection = computed(() => ({
    selectedRowKeys: selectedRowKeys.value,
    onChange: (newSelectedKeys: any[], selectedRows: any[]) => {
      selectedRowKeys.value = newSelectedKeys;

      // 添加新勾选的
      selectedRows.forEach(item => {
        if (!emitBrandData.value.find(i => i.brandId === item.brandId)) {
          emitBrandData.value.push(item);
        }
      });

      // 从当前页移除取消勾选的
      goodsTableData.value.forEach(item => {
        if (!newSelectedKeys.includes(item.brandId)) {
          const index = emitBrandData.value.findIndex(i => i.brandId === item.brandId);
          if (index !== -1) {
            emitBrandData.value.splice(index, 1);
          }
        }
      });
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.isForbid === true,
    }),
  }));

  // 提交
  const submitOk = () => {
    if (emitBrandData.value.length === 0) {
      message.warning('请先选择内容!');
      return;
    }
    emit('onBrandNameCallBack', emitBrandData.value);
    isBrandName.value = false;
  };

  // 显示 Drawer 并初始化数据
  const showBrandNameRef = (data: any[]) => {
    orderPagination.current = 1;
    emitBrandData.value = [...data];
    selectedRowKeys.value = data.map(i => i.brandId);
    goodsSelectData.value = data;
    httpBrandNameList();
    isBrandName.value = true;
  };

  defineExpose({ showBrandNameRef });
</script>

<style lang="less" scoped>
  .drawer-footer {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .brandName-table {
    margin-top: 24px;
  }
  .selectPage-icon {
    width: 16px;
    height: 16px;
  }
  .selectPage-img-center {
    display: flex;
    align-items: center;
  }
</style>
