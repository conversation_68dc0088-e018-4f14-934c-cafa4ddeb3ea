<template>
  <a-drawer
    title="选择指定分类"
    width="600"
    prevent-scroll-through
    :visible="isBrandSort"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isBrandSort = false)"
  >
    <a-form :model="brandData">
      <a-row>
        <a-col>
          <a-form-item label="品牌平台分类">
            <a-select
              v-model:value="brandData.id"
              filterable
              :fieldNames="{ label: 'categoryName', value: 'id' }"
              placeholder="请选择分类名称"
              clearable
              :options="optionsList"
              style="width: 300px"
              class="mr10"
            />
          </a-form-item>
        </a-col>
        <a-col>
          <a-form-item>
            <a-button type="primary" @click="brandInquire" class="mr10"
              >查询</a-button
            >
            <a-button @click="resetBrandOk">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="brandSort-table">
      <a-table
        min-width="2000"
        max-height="500px"
        :columns="brandSortColumns"
        :pagination="orderPagination"
        :data-source="barandTableData"
        :loading="isLoading"
        :row-selection="rowSelection"
        row-key="id"
        hover
        @change="onBrandSize"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'radio'">
            <a-radio
              :checked="selectedRow?.id === record.id"
              @change="() => handleSelect(record)"
            />
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" @click="submitOk" class="mr10">确定</a-button>
        <a-button @click="isBrandSort = false">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { message } from "woody-ui";
import { akcPageCategory, akcListCategory } from "@/api/cms/decoration/index";
import { BRANDSORT_COLUMNS, BRANDSIZE, BRANDTYPE } from "./const";

const emit = defineEmits(["onBrandCallBack"]);
const isBrandSort = ref(false);
const selectedRow = ref(null); // 存储单选选中的行数据
const brandSortColumns = reactive([
  {
    title: "",
    key: "radio",
    width: 50,
    align: "center",
  },
  ...BRANDSORT_COLUMNS,
]);
const orderPagination = reactive(BRANDSIZE);
const brandData = reactive(BRANDTYPE);
const barandTableData = ref([]);
const isLoading = ref(false);
const optionsList = ref([]);

// 单选处理函数
const handleSelect = (record) => {
  selectedRow.value = record;
};

// 查询分类列表
const httpAkcListCategory = async () => {
  const res = await akcListCategory({});
  optionsList.value = res.data;
};

// 查询指定分类列表
const httpAkcPageCategory = async () => {
  isLoading.value = true;
  try {
    const { current, pageSize } = orderPagination;
    const params = { ...brandData, current, pageSize };
    const res = await akcPageCategory(params);
    barandTableData.value = res.data.records;
    orderPagination.total = res.data.total;
  } catch (error) {
    barandTableData.value = [];
    orderPagination.total = 0;
  } finally {
    isLoading.value = false;
  }
};

// 品牌分页
const onBrandSize = (event) => {
  const { current, pageSize } = event;
  orderPagination.current = current;
  orderPagination.pageSize = pageSize;
  httpAkcPageCategory();
};

// 平台分类搜索
const brandInquire = () => {
  orderPagination.pageSize = 10;
  orderPagination.current = 1;
  selectedRow.value = null;
  httpAkcPageCategory();
};

// 重置指定分类
const resetBrandOk = () => {
  orderPagination.pageSize = 10;
  orderPagination.current = 1;
  brandData.categoryName = null;
  brandData.id = null;
  selectedRow.value = null;
  httpAkcPageCategory();
};

// 确认选择
const submitOk = () => {
  if (selectedRow.value) {
    emit("onBrandCallBack", selectedRow.value);
    isBrandSort.value = false;
  } else {
    message.warning("请选择指定平台分类!");
  }
};

// 是否显示品牌平台分类列表弹框方法
const showBrandSortRef = (initialSelected = null) => {
  orderPagination.pageSize = 10;
  orderPagination.current = 1;
  selectedRow.value = initialSelected; // 初始化选中项
  httpAkcListCategory().catch(() => {
    optionsList.value = [];
  });
  httpAkcPageCategory();
  isBrandSort.value = true;
};

// 行选择配置（设置为null禁用默认的多选）
const rowSelection = null;

defineExpose({ showBrandSortRef });
</script>

<style lang="less" scoped>
.drawer-footer {
  display: flex;
  align-items: center;
  justify-content: end;
}
.brandSort-table {
  margin-top: 24px;
}
.selectPage-img-center {
  display: flex;
  align-items: center;
}
</style>
