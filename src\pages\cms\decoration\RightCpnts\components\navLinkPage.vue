<template>
  <a-drawer
    title="选择"
    :size="'large'"
    prevent-scroll-through
    :visible="isNavLinkPage"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isNavLinkPage = false)"
    @cancel="() => (isNavLinkPage = false)"
  >
    <a-form :model="fromData" layout="vertical">
      <a-row :align="'bottom'" :gutter="20">
        <a-col :span="8">
          <a-form-item label="标题">
            <a-input v-model:value="fromData.pageName" placeholder="请输入内容" clearable />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="模板">
            <a-select v-model="fromData.pageTypeList" placeholder="请选择内容" clearable>
              <a-option label="商品" value="goods">商品</a-option>
              <a-option label="品牌" value="brand">品牌</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item>
            <a-button type="primary" @click="navLinkInquire" class="mr10">查询</a-button>
            <a-button @click="resetNavLinkPageOk">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="navLinkPage-table">
      <a-table
        v-model:selected-row-keys="selectedRowKeys"
        min-width="2000"
        :columns="navLinkPageColumns"
        :pagination="pagingSize"
        :data-source="navListTableData"
        :loading="isLoading"
        row-key="id"
        hover
        @change="onNavLinkSize($event)"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'row-select'">
            <a-radio
              :checked="selectedRow?.id === record.id"
              @change="() => handleSelect(record)"
            />
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" @click="submitOk" class="mr10">确定</a-button>
        <a-button type="default" @click="isNavLinkPage = false">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { message } from 'woody-ui';
  import { basePage } from '@/api/cms/decoration/index';
  import { NAVLINKPAGE_COLUMNS, NAVLINKPAGESIZE, PAGE__TYPE } from './const';

  const emit = defineEmits(['onNavLinkCallBack']);
  const emitNavLinkData = ref<any>({});
  const selectedRowKeys = ref<any[]>([]);
  const isNavLinkPage = ref<boolean>(false);
  const uriIndex = ref<number | null>(null);
  const navLinkPageColumns = reactive(NAVLINKPAGE_COLUMNS);
  const pagingSize = reactive(NAVLINKPAGESIZE);
  const fromData = reactive(PAGE__TYPE);
  const navListTableData = ref<any[]>([]);
  const isLoading = ref<boolean>(false);
  const selectedRow = ref(null); // 存储单选选中的行数据
  // 页面列表
  const httpBasePage = async () => {
    isLoading.value = true;
    const { current, pageSize } = pagingSize;
    if (fromData.pageTypeList) fromData.pageSubTypeList = [fromData.pageTypeList];
    if (!fromData.pageTypeList) fromData.pageSubTypeList = ['goods', 'brand'];
    const params = { ...fromData, current, pageSize };
    const res = await basePage(params);
    navListTableData.value = res.data.records;
    pagingSize.total = res.data.total;
  };
  // 页面分页
  const onNavLinkSize = event => {
    const { current, pageSize } = event;
    pagingSize.current = current;
    pagingSize.pageSize = pageSize;
    httpBasePage().finally(() => {
      isLoading.value = false;
    });
  };

  // 单选处理函数
  const handleSelect = record => {
    selectedRow.value = record;
    emitNavLinkData.value = {
      ...selectedRow.value,
      index: uriIndex.value,
    };
  };
  // 查询页面列表
  const navLinkInquire = () => {
    pagingSize.current = 1;
    pagingSize.pageSize = 10;
    selectedRowKeys.value = [];
    httpBasePage().finally(() => {
      isLoading.value = false;
    });
  };
  // 重置页面
  const resetNavLinkPageOk = () => {
    pagingSize.current = 1;
    pagingSize.pageSize = 10;
    fromData.pageName = null;
    fromData.pageTypeList = null;
    selectedRowKeys.value = [];
    httpBasePage().finally(() => {
      isLoading.value = false;
    });
  };
    // 获取单选数据
  // const navLinkSelect = (value, ctx) => {
  //   if (ctx.selectedRowData) {
  //     selectedRowKeys.value = value;
  //     emitNavLinkData.value = {
  //       ...ctx.selectedRowData[0],
  //       index: uriIndex.value,
  //     };
  //   }
  // };
  // 确认选择
  const submitOk = () => {
    if (JSON.stringify(emitNavLinkData.value) !== '{}') {
      emit('onNavLinkCallBack', emitNavLinkData.value);
      isNavLinkPage.value = false;
    } else {
      message.warning('请选择商品/品牌!');
    }
  };
  // 是否显示商品,品牌列表弹框方法
  const showNavLinkRef = index => {
    pagingSize.current = 1;
    pagingSize.pageSize = 10;
    uriIndex.value = index;
    selectedRowKeys.value = [];
    emitNavLinkData.value = {};
    httpBasePage().finally(() => {
      isLoading.value = false;
    });
    isNavLinkPage.value = true;
  };

  defineExpose({ showNavLinkRef });
</script>
<style lang="less" scoped>
  .drawer-footer {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .navLinkPage-table {
    margin-top: 24px;
  }
  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;
    &:first-child {
      border-top-left-radius: 8px;
    }
    &:last-child {
      border-top-right-radius: 8px;
    }
  }
  :deep(td) {
    border-bottom-color: @table-boder-color;
  }
  :deep(.t-table__pagination) {
    padding-top: 28px;
    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style>
