<template>
  <a-drawer
    title="选择"
    :size="'large'"
    prevent-scroll-through
    :visible="isShowUrl"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="() => (isShowUrl = false)"
    @cancel="() => (isShowUrl = false)"
  >
    <div class="selectPage">
      <a-tabs v-model:activeKey="tabsIndex" @change="onTabChange">
        <a-tab-pane :key="1" tab="页面">
          <div class="selectPage-size">
            <a-form ref="pageRef" layout="vertical" :model="pageData">
              <a-row :gutter="20">
                <a-col :span="8">
                  <a-form-item label="标题">
                    <a-input v-model:value="pageData.pageName" placeholder="请输入内容" clearable />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item>
                    <div style="margin-top: 28px">
                      <a-button type="primary" @click="pageInquire" class="mr10">查询</a-button>
                      <a-button @click="resetPageOk">重置</a-button>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <div class="selectPage-table">
            <a-table
              min-width="2000"
              max-height="500px"
              :columns="pageColumns"
              :pagination="pageSizes"
              :data-source="pageTabelData"
              :loading="isPageLoading"
              :row-selection="rowSelection"
              row-key="id"
              hover
              @change="onPageSize"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'radio'">
                  <a-radio
                    :checked="selectedRowKeys?.id === record.id"
                    @change="() => pageSelect(record)"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
        <a-tab-pane :key="2" tab="商品">
          <div class="selectPage-size">
            <a-form layout="vertical" :model="goodsData">
              <a-row :gutter="20">
                <a-col :span="6">
                  <a-form-item label="商品名称" name="name">
                    <a-input
                      v-model:value="goodsData.productName"
                      placeholder="请输入商品内容"
                      clearable
                    >
                      <template #suffix>
                        <img
                          class="selectPage-icon"
                          :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
                        />
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="SPU ID" name="name">
                    <a-input
                      v-model:value="goodsData.spuId"
                      maxlength="19"
                      placeholder="请输入SPU ID"
                      clearable
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="商品来源" name="productSource">
                    <a-select
                      default-value="0"
                      v-model:value="goodsData.productSource"
                      :options="productOpt"
                    ></a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item>
                    <div style="margin-top: 28px">
                      <a-button type="primary" @click="goodsInquire" class="mr10">查询</a-button>
                      <a-button @click="resetGoodsOk">重置</a-button>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <div class="selectPage-table">
            <a-table
              :scroll="{ x: 1000 }"
              max-height="500px"
              :columns="goodsColumns"
              :pagination="goodsSize"
              :data-source="goodsTabelData"
              :loading="isGoodsLoading"
              :row-selection="rowSelection"
              row-key="productId"
              hover
              @change="onGoodsSize"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'radio'">
                  <a-radio
                    :checked="selectedRowKeys?.productId === record.productId"
                    @change="() => pageSelect(record)"
                  />
                </template>
                <template v-if="column.dataIndex === 'operate'">
                  <div class="selectPage-img-center">
                    <a-image
                      :src="record.picUrl"
                      :alt="record.prductName"
                      :lazy="true"
                      fit="cover"
                      :style="{ width: '60px', height: '60px' }"
                    />
                    <span class="ml10">{{ record.prductName }}</span>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
        <a-tab-pane :key="3" tab="品牌">
          <div class="selectPage-size">
            <a-form layout="vertical" :model="brandData">
              <a-row :align="'bottom'" :gutter="20">
                <a-col :span="8">
                  <a-form-item label="品牌名称">
                    <a-input
                      v-model:value="brandData.brandName"
                      placeholder="请输入品牌内容"
                      clearable
                    >
                      <template #suffix>
                        <img
                          class="selectPage-icon"
                          :src="`${VITE_API_IMG}/2024/08/5194fc9055dd4ceca535ef5a1a531221.png`"
                        />
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item>
                    <div style="margin-top: 28px">
                      <a-button type="primary" @click="brandInquire" class="mr20">查询</a-button>
                      <a-button @click="resetBrandOk">重置</a-button>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <div class="selectPage-table">
            <a-table
              :scroll="{ x: 1000 }"
              max-height="500px"
              :columns="brandColumns"
              :pagination="brandSize"
              :data-source="brandTabelData"
              :loading="isBrandLoading"
              :row-selection="rowSelection"
              row-key="brandId"
              hover
              @change="onBrandSize"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'radio'">
                  <a-radio
                    :checked="selectedRowKeys?.brandId === record.brandId"
                    @change="() => pageSelect(record)"
                  />
                </template>
                <template v-if="column.dataIndex === 'brandName'">
                  <div class="selectPage-img-center">
                    <a-image
                      :src="record.brandUrl"
                      :alt="record.brandName"
                      :lazy="true"
                      fit="cover"
                      :style="{ width: '60px', height: '60px' }"
                    />
                    <span class="ml10">{{ record.brandName }}</span>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'brandSource'">
                  <span>爱库存</span>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
        <a-tab-pane v-if="templateId === 'cube'" :key="4" tab="系统页面">
          <div class="selectPage-size">
            <a-table
              :scroll="{ x: 1000 }"
              max-height="500px"
              :columns="systemColumns"
              :pagination="systemSize"
              :data-source="systemTableData"
              :loading="isSystemLoading"
              :row-selection="rowSelection"
              row-key="systemPageId"
              hover
              @change="onSystemSize"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'radio'">
                  <a-radio
                    :checked="selectedRowKeys?.systemPageId === record.systemPageId"
                    @change="() => pageSelect(record)"
                  />
                </template>
                <template v-if="column.dataIndex === 'dataIndex'">
                  <span v-if="isEmptyValue(record.param)">-</span>
                  <template v-else>
                    <span v-for="item in JSON.parse(record.param)" :key="item.key">
                      {{ item.label }}
                    </span>
                  </template>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button @click="isShowUrl = false" class="mr10">取消</a-button>
        <a-button type="primary" @click="submitOk">确定</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
  const { VITE_API_IMG } = import.meta.env;
  import { ref, reactive } from 'vue';
  import { message } from 'woody-ui';
  import { isEmptyValue } from '@/utils';
  import {
    PAGE_COLUMNS,
    GOODS_COLUMNS,
    BRAND_COLUMNS,
    PAGE_TYPE,
    PAGESIZE,
    GOODS_TYPE,
    GOODS_SIZE,
    BRAND_TYPE,
    BRAND_SIZE,
    SYSTEM_PAGE,
    SYSTEM_COLUMNS,
    SYSTEM_TYPE,
  } from './const';
  import { basePage, pageProduct, brandInfo, getSystemPage } from '@/api/cms/decoration/index';
  import { isObj } from '@/utils';

  defineProps({
    templateId: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['onPageCallBack']);
  const isShowUrl = ref<boolean>(false);
  const uriIndex = ref<number | null>(null);
  const tabsIndex = ref<number | null>(1);
  const emitSelectData = ref<any>({});
  const selectedRowKeys = ref(null);
  // table页面数据列表
  const pageRef = ref(null);
  const pageColumns = reactive(PAGE_COLUMNS);
  const pageData = reactive(PAGE_TYPE);
  const pageSizes = reactive(PAGESIZE);
  const pageTabelData = ref<any[]>([]);
  const isPageLoading = ref<boolean>(false);
  // table商品数据列表
  const goodsColumns = reactive(GOODS_COLUMNS);
  const goodsData = reactive(GOODS_TYPE);
  const goodsSize = reactive(GOODS_SIZE);
  const goodsTabelData = ref<any[]>([]);
  const isGoodsLoading = ref<boolean>(false);
  // table品牌数据列表
  const brandColumns = reactive(BRAND_COLUMNS);
  const brandData = reactive(BRAND_TYPE);
  const brandSize = reactive(BRAND_SIZE);
  const brandTabelData = ref<any[]>([]);
  const isBrandLoading = ref<boolean>(false);
  // table系统页面数据列表
  const systemColumns = reactive(SYSTEM_COLUMNS);
  const systemData = reactive(SYSTEM_TYPE);
  const systemSize = reactive(SYSTEM_PAGE);
  const systemTableData = ref<any[]>([]);
  const isSystemLoading = ref<boolean>(false);

  // 行选择配置（设置为null禁用默认的多选）
  const rowSelection = null;

  const productOpt = [
    {
      label: '供货仓',
      value: '0',
    },
    {
      label: '京东',
      value: '8',
    },
  ];
  // tab选项卡切换
  const onTabChange = value => {
    try {
      switch (value) {
        case 1:
          pageSizes.current = 1;
          pageSizes.pageSize = 10;
          pageData.pageName = null;
          httpBasePage().finally(() => {
            isPageLoading.value = false;
          });
          break;
        case 2:
          goodsSize.current = 1;
          goodsSize.pageSize = 10;
          goodsData.productName = null;
          goodsData.spuId = null;
          goodsData.productSource = '0';
          HttpPageProduct().finally(() => {
            isGoodsLoading.value = false;
          });
          break;
        case 3:
          brandSize.current = 1;
          brandSize.pageSize = 10;
          brandData.brandName = null;
          httpBrandInfo().finally(() => {
            isBrandLoading.value = false;
          });
          break;
        case 4:
          systemSize.current = 1;
          systemSize.pageSize = 10;
          getSystemList();
      }
    } catch (error) {
      console.error('Error in onTabChange:', error);
    }
  };

  // 获取页面列表
  const httpBasePage = async () => {
    isPageLoading.value = true;
    try {
      const { current, pageSize } = pageSizes;
      const params = { ...pageData, current, pageSize };
      const res = await basePage(params);
      pageTabelData.value = res.data.records;
      pageSizes.total = res.data.total;
    } catch (error) {
      pageTabelData.value = [];
      pageSizes.total = 0;
    }
  };
  // 页面分页
  const onPageSize = event => {
    console.log(event, 'event');
    pageSizes.current = event.current;
    pageSizes.pageSize = event.pageSize;
    httpBasePage().finally(() => {
      isPageLoading.value = false;
    });
  };
  // 页面查询
  const pageInquire = () => {
    pageSizes.current = 1;
    pageSizes.pageSize = 10;
    selectedRowKeys.value = null;
    httpBasePage().finally(() => {
      isPageLoading.value = false;
    });
  };
  // 页面重置
  const resetPageOk = () => {
    pageSizes.current = 1;
    pageSizes.pageSize = 10;
    pageData.pageName = null;
    selectedRowKeys.value = null;
    httpBasePage().finally(() => {
      isPageLoading.value = false;
    });
  };
  // 获取商品列表
  const HttpPageProduct = async () => {
    isGoodsLoading.value = true;
    try {
      const { current, pageSize } = goodsSize;
      goodsData.spuId = goodsData.spuId ? goodsData.spuId : null;
      const params = { ...goodsData, current, pageSize };
      const res = await pageProduct(params);
      goodsTabelData.value = res.data.records;
      goodsSize.total = res.data.total;
    } catch (error) {
      goodsTabelData.value = [];
      goodsSize.total = 0;
    }
  };
  // 商品查询
  const goodsInquire = () => {
    goodsSize.current = 1;
    goodsSize.pageSize = 10;
    selectedRowKeys.value = null;
    HttpPageProduct().finally(() => {
      isGoodsLoading.value = false;
    });
  };
  // 商品分页
  const onGoodsSize = event => {
    const { current, pageSize } = event;
    goodsSize.current = current;
    goodsSize.pageSize = pageSize;
    HttpPageProduct().finally(() => {
      isGoodsLoading.value = false;
    });
  };
  // 系统页面分页
  const onSystemSize = event => {
    const { current, pageSize } = event;
    systemSize.current = current;
    systemSize.pageSize = pageSize;
    getSystemList();
  };
  // 商品重置
  const resetGoodsOk = () => {
    goodsSize.current = 1;
    goodsSize.pageSize = 10;
    goodsData.spuId = null;
    goodsData.productName = null;
    goodsData.productSource = '0';
    selectedRowKeys.value = null;
    HttpPageProduct().finally(() => {
      isGoodsLoading.value = false;
    });
  };
  // 获取品牌列表
  const httpBrandInfo = async () => {
    isBrandLoading.value = true;
    try {
      const { current, pageSize } = brandSize;
      const params = { ...brandData, current, pageSize };
      const res = await brandInfo(params);
      brandTabelData.value = res.data.records;
      brandSize.total = res.data.total;
    } catch (error) {
      brandTabelData.value = [];
      brandSize.total = 0;
    }
  };
  // 品牌查询
  const brandInquire = () => {
    brandSize.current = 1;
    brandSize.pageSize = 10;
    selectedRowKeys.value = null;
    httpBrandInfo().finally(() => {
      isBrandLoading.value = false;
    });
  };
  // 品牌分页
  const onBrandSize = event => {
    const { current, pageSize } = event;
    brandSize.current = current;
    brandSize.pageSize = pageSize;
    httpBrandInfo().finally(() => {
      isBrandLoading.value = false;
    });
  };
  // 品牌重置
  const resetBrandOk = () => {
    brandSize.current = 1;
    brandSize.pageSize = 10;
    brandData.brandName = null;
    selectedRowKeys.value = null;
    httpBrandInfo().finally(() => {
      isBrandLoading.value = false;
    });
  };
  // 获取系统页面列表
  const getSystemList = () => {
    const params = {
      current: systemSize.current,
      pageSize: systemSize.pageSize,
    };
    isSystemLoading.value = true;
    getSystemPage(params)
      .then(res => {
        if (res.code === 0 && isObj(res.data)) {
          systemTableData.value = Array.isArray(res.data.records) ? res.data.records : [];
          systemSize.total = res.data.total;
        }
      })
      .finally(() => {
        isSystemLoading.value = false;
      });
  };
  // 获取页面ID
  const pageSelect = _value => {
    let enums = null;
    if (tabsIndex.value == 1) enums = 'PAGE';
    if (tabsIndex.value == 2) enums = 'GOODS';
    if (tabsIndex.value == 3) enums = 'BRAND';
    if (tabsIndex.value === 4) enums = 'SYSTEM';
    selectedRowKeys.value = _value;
    emitSelectData.value = {
      ..._value,
      index: uriIndex.value,
      enums,
    };
    console.log(emitSelectData.value, 'emitSelectData.value');
  };
  // 确认选择
  const submitOk = () => {
    if (JSON.stringify(emitSelectData.value) != '{}') {
      emit('onPageCallBack', emitSelectData.value);
      isShowUrl.value = false;
    } else {
      message.warning('请先选择内容!');
    }
  };
  const selectPageRef = index => {
    tabsIndex.value = 1;
    pageSizes.current = 1;
    pageData.pageName = null;
    pageTabelData.value = [];
    selectedRowKeys.value = null;
    emitSelectData.value = {};
    uriIndex.value = index;
    httpBasePage().finally(() => {
      isPageLoading.value = false;
      isShowUrl.value = true;
    });
  };

  defineExpose({ selectPageRef });
</script>
<style lang="less" scoped>
  .selectPage {
    padding-top: 10px;
    padding-left: 10px;

    .selectPage-size {
      padding-top: 48px;
      .selectPage-icon {
        width: 16px;
        height: 16px;
      }
    }

    .selectPage-table {
      padding-top: 24px;
    }
    .selectPage-img-center {
      display: flex;
      align-items: center;
    }
  }
  .drawer-footer {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;
    &:first-child {
      border-top-left-radius: 8px;
    }
    &:last-child {
      border-top-right-radius: 8px;
    }
  }
  :deep(td) {
    border-bottom-color: @table-boder-color;
  }
  :deep(.t-table__pagination) {
    padding-top: 28px;
    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style>
