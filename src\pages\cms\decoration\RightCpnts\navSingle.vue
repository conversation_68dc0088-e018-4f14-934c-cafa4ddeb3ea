<template>
  <div class="item">
    <div class="item-title">文字颜色</div>
    <a-radio-group v-model:value="info.textColor">
      <a-radio value="#172119">黑色</a-radio>
      <a-radio value="#fff">白色</a-radio>
    </a-radio-group>
  </div>
  <div class="item">
    <div class="item-title">是否支持透明背景</div>
    <a-switch v-model:checked="info.isTransparent"></a-switch>
  </div>
  <div v-if="!info.isTransparent" class="item">
    <div class="item-title">背景色<span class="require">*</span></div>
    <div class="bg">
      <color-picker v-model:pureColor="info.bgColor" />
    </div>
  </div>
  <div class="item">
    <div class="tip-text">最多添加20个导航，支持拖动排序</div>
    <div class="nav-item-wrap">
      <div class="nav-item">
        <div class="nav-row flex margin-b">
          <div class="name flex">标题<span class="require">*</span></div>
          <a-input v-model:value="info.list[0].name" class="value"></a-input>
        </div>
        <div class="nav-row flex">
          <div class="name flex">链接<span class="require">*</span></div>
          <div class="link-box" @click="openDecoraTableDia('firstScreen', 0)">
            <div class="text">{{ info.list[0].uriName }}</div>
            <img
              :src="`${VITE_API_IMG}/2024/08/ecc6d780ac4244cf9c48ee7c2f5ae1bf.png`"
              alt=""
              class="icon link"
            />
          </div>
        </div>
      </div>
      <draggable :sort="true" :list="info.list" :animation="300">
        <template #item="{ element, index }">
          <div v-if="index !== 0" class="nav-item">
            <a-popconfirm
              placement="left"
              title="确认删除吗"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteNav(element)"
            >
              <template #icon>
                <ExclamationCircleOutlined style="color: #faad14" />
              </template>
              <img
                :src="`${VITE_API_IMG}/2024/08/8e95e3dccdb743cc819885108a8ee29f.png`"
                alt=""
                class="close-icon"
              />
            </a-popconfirm>
            <div class="nav-row flex margin-b">
              <div class="name flex">标题<span class="require">*</span></div>
              <a-input v-model:value="element.name" class="value"></a-input>
            </div>
            <div class="nav-row flex">
              <div class="name flex">链接<span class="require">*</span></div>
              <div class="link-box" @click="openDecoraTableDia('other', index)">
                <div class="text">{{ element.uriName }}</div>
                <img
                  :src="`${VITE_API_IMG}/2024/08/ecc6d780ac4244cf9c48ee7c2f5ae1bf.png`"
                  alt=""
                  class="icon link"
                />
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
  <div
    :style="{ cursor: info.list.length >= 20 ? 'not-allowed' : 'pointer' }"
    class="add-btn flex"
    @click="addNav"
  >
    <img
      class="icon"
      :src="`${VITE_API_IMG}/2024/08/d4a87b01bab145f18eb0ed06743bdb95.png`"
      alt=""
    />
    <div class="add-text">添加导航</div>
    <div class="add-num flex">
      <div>{{ info.list.length }}</div>
      <div :class="info.list.length >= 20 ? 'disable-color' : 'enable-color'">
        /20
      </div>
    </div>
  </div>

  <select-decr-page v-model:m-val="drawerState" />
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, watchEffect } from "vue";
import draggable from "vuedraggable";
import { storeToRefs } from "pinia";
import { getDecorationStore } from "@/store";
import SelectDecrPage from "./components/selectDecrPage.vue";

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);
const detailData = decorationInfo.value.components.find(
  (item: any) => item.templateId === "navSingle"
);

const info = ref<any>({});

const addNav = () => {
  if (info.value.list.length >= 20) return;
  info.value.list.push({
    id: `${new Date().getTime()}`,
    name: "",
    uriName: "",
  });
};
const deleteNav = (item: any) => {
  info.value.list = info.value.list.filter((nav: any) => nav.id !== item.id);
};

const drawerState = ref({
  visible: false,
  navType: "",
  selectNavIndex: 0,
  selectNavData: { pageName: "", id: "", clickType: "" },
});
const openDecoraTableDia = (navType: string, index: number) => {
  drawerState.value.visible = true;
  drawerState.value.navType = navType;
  drawerState.value.selectNavIndex = index;
};

watchEffect(() => {
  info.value = detailData.info || {};
});
</script>

<style lang="less" scoped>
.require {
  font-size: 14px;
  color: #ff436a;
  margin-left: 2px;
}
.link-box {
  width: 218px;
  height: 30px;
  position: relative;
  color: var(--td-text-color-primary);
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-default);
  cursor: pointer;
  padding: 0 8px;
  display: flex;
  align-items: center;
  &:hover {
    border-color: var(--td-brand-color);
  }
  .icon {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 5px;
    top: 6px;
  }
  .text {
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.item {
  margin-bottom: 30px;

  .item-title {
    font-size: 14px;
    color: #05082c;
    margin-bottom: 8px;
  }
}
.bg {
  width: 180px;
}
.tip-text {
  font-size: 12px;
  color: #a2abbd;
  margin-bottom: 16px;
}
.nav-item-wrap {
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #f2f5f9;
  .nav-item {
    width: 100%;
    height: 136px;
    padding: 24px;
    // border: 1px solid #f2f5f9;
    border-bottom: 1px solid #f2f5f9;
    position: relative;
    &:last-child {
      border-bottom: none;
    }
    &:hover .close-icon {
      opacity: 1;
    }
    .nav-row {
      .name {
        margin-right: 22px;
        font-size: 14px;
        color: #05082c;
        white-space: nowrap;
      }
    }
    .close-icon {
      width: 16px;
      height: 16px;
      position: absolute;
      cursor: pointer;
      right: 5px;
      top: 5px;
      opacity: 0;
    }
    .margin-b {
      margin-bottom: 24px;
    }
  }
}
.add-btn {
  width: 304px;
  height: 40px;
  background: #ffffff;
  border-radius: 3px 3px 3px 3px;
  border: 1px dashed #1a7af8;
  margin: 25px auto;
  cursor: pointer;
  font-size: 16px;
  color: #1a7af8;
  justify-content: center;
  user-select: none;
  .icon {
    width: 18px;
    height: 18px;
  }
  .add-text {
    margin: 0 14px 0 8px;
  }
  .add-num {
    .enable-color {
      color: #a8d0ff;
    }
    .disable-color {
      color: #1a7af8;
    }
  }
}
</style>
