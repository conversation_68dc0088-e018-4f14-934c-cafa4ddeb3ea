<template>
  <div class="n-title">公告内容</div>
  <a-textarea v-model:value="info.text" placeholder="请输入公告内容，字数限制在50个字内" :maxcharacter="50"></a-textarea>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getDecorationStore } from '@/store';

const decorationStore = getDecorationStore();
const detailData = decorationStore.decorationInfo.components.find((item: any) => item.templateId === 'notice');

const info = ref<any>({});
info.value = detailData.info;
</script>

<style lang="less" scoped>
.n-title {
  font-size: 14px;
  color: #05082c;
  margin-bottom: 8px;
}
</style>
