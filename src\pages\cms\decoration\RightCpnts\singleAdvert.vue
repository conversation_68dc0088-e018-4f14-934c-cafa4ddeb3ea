<template>
  <div class="adver-content">
    <div class="advert-image">
      <span class="advert-image-one">添加图片</span>
    </div>
    <template v-if="sceneType !== 'firstScreen'">
      <div class="advert-image-cd"></div>
    </template>
    <div class="advert-add-border-list">
      <div class="advert-add-border">
        <div class="advert-add-border-pad">
          <div class="advert-addImage-left" @click="showImagePopupMethod(0)">
            <template v-if="advertData.imgUrl">
              <img class="advert-addImage-left-img" :src="advertData.imgUrl" />
            </template>
            <template v-if="!advertData.imgUrl">
              <img
                :src="`${VITE_API_IMG}/2024/08/061bfc84196541b9b8210e5f0ee43fc9.png`"
              />
              <p>点击上传图片</p>
            </template>
            <div v-if="advertData.imgUrl" class="aduert-update">更换图片</div>
          </div>
          <div class="advert-addImage-right">
            <template v-if="sceneType !== 'firstScreen'">
              <div class="advert-addImage-right-cd">
                建议图片尺寸宽度750px，高度不限制。大小1M以内。
              </div>
            </template>
            <div
              class="advert-addImage-right-Input"
              @click="showUrlPopupMethod(0)"
            >
              <template v-if="!advertData.uriName && !advertData.param.id">
                <div class="right-Input-tz">链接</div>
                <div class="right-Input-text">请选择链接</div>
              </template>
              <template
                v-if="advertData.uriName != null && advertData.param.id != null"
              >
                <div class="right-Input-mm">{{ advertData.uriName }}</div>
              </template>
              <img
                class="right-Input-img"
                :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 我的图片列表弹框 -->
  <myPicture ref="myPictureRef" @on-image-call-back="onImageCallBack" />
  <!-- 选择跳转页面（内容）弹框 -->
  <selectPage ref="selectPageRef" @on-page-call-back="onPageCallBack" />
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, onMounted, watchEffect } from "vue";
import draggable from "vuedraggable";
import { storeToRefs } from "pinia";
import { getDecorationStore } from "@/store";
import { ImageData } from "../type";
import myPicture from "./components/myPicture.vue";
import selectPage from "./components/selectPage.vue";

const decorationStore = getDecorationStore();
const { decorationInfo, activeNav } = storeToRefs(decorationStore);
const sceneType = decorationInfo.value.scene;
const detailData = decorationInfo.value.components.find(
  (item: any) => item.flagId === activeNav.value.flagId
);
const advertData = ref<any>({});
advertData.value = detailData.info;
type myPictureType = { showImageRef: (index: number) => void };
const myPictureRef = ref<myPictureType | null>(null);
type selectPageType = { selectPageRef: (index) => void };
const selectPageRef = ref<selectPageType | null>(null);

watchEffect(() => {
  decorationStore.setDecorationInfo({
    ...decorationInfo.value,
    components: decorationInfo.value.components.map((item: any) => {
      if (item.flagId === activeNav.value.flagId) {
        advertData.value = item.info;
      }
      return item;
    }),
  });
});

const onPageCallBack = (item) => {
  const {
    id,
    prodSource,
    productId,
    pageName,
    brandName,
    prductName,
    brandId,
    enums,
  } = item;
  switch (enums) {
    case "PAGE":
      advertData.value.uriType = 0;
      advertData.value.uriName = pageName;
      advertData.value.param.id = id;
      break;
    case "GOODS":
      advertData.value.uriType = 1;
      advertData.value.uriName = prductName;
      advertData.value.param.id = productId;
      advertData.value.param.prodSource = prodSource;
      break;
    case "BRAND":
      advertData.value.uriType = 2;
      advertData.value.uriName = brandName;
      advertData.value.param.id = brandId;
      break;
  }
};
const onImageCallBack = (item: { index: number; url: string }) => {
  const { url } = item;
  advertData.value.imgUrl = url;
};
const showImagePopupMethod = (index: number) => {
  if (myPictureRef.value) {
    if (typeof myPictureRef.value.showImageRef === "function") {
      myPictureRef.value.showImageRef(index);
    }
  }
};
const showUrlPopupMethod = (index: number) => {
  if (selectPageRef.value) {
    if (typeof selectPageRef.value.selectPageRef === "function") {
      selectPageRef.value.selectPageRef(index);
    }
  }
};

onMounted(() => {});
</script>
<style lang="less" scoped>
@import "../css/advert.less";
</style>
