<template>
  <div class="item">
    <div class="item-title">页面标题<span class="require">*</span></div>
    <a-input v-model:value="info.text" maxlength="30" placeholder="请输入页面标题" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getDecorationStore } from '@/store';

const decorationStore = getDecorationStore();
const detailData = decorationStore.decorationInfo.components.find((item: any) => item.templateId === 'subPageTitle');

const info = ref<any>({});
info.value = detailData.info;
</script>

<style lang="less" scoped>
.item {
  margin-bottom: 30px;
  .item-title {
    font-size: 14px;
    color: #05082c;
    margin-bottom: 8px;
    .require {
      font-size: 14px;
      color: #ff436a;
      margin-left: 2px;
    }
  }
}
</style>
