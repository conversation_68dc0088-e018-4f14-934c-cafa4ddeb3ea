<template>
  <div class="phone-container flex">
    <div class="phone-wrap">
      <div id="phoneDom" class="phone-box">
        <!-- status-bar -->
        <div :style="{ background: bgColor }" class="status-bar flex">
          <div :style="{ color: info.textColor }" class="time bold">
            {{ timeStr }}
          </div>
          <img
            v-if="info.textColor === '#fff'"
            class="status"
            :src="`${VITE_API_IMG}/2024/08/370f602cdbdf4c059b80f45d0aafb167.png`"
          />
          <img
            v-else
            class="status"
            :src="`${VITE_API_IMG}/2024/08/f0e5b2c15e614b218413e09ea0bf9eff.png`"
          />
        </div>
        <!-- 内容区域 -->
        <div v-if="toolNavs && toolNavs.length" class="content-sec">
          <div v-for="(item, index) in computedPhoneComponentArr" :key="index">
            <div
              :class="
                activeNav.flagId === item.flagId &&
                activeNav.templateId !== 'homePageTitle' &&
                activeNav.templateId !== 'subPageTitle' &&
                activeNav.templateId !== 'navSingle'
                  ? 'outline'
                  : 'noline'
              "
              @click="selectPhoneSection(item)"
            >
              <component
                :is="item.component"
                :info="item.info"
                :scene="scene"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 侧边操作栏 -->
    <div
      v-if="activeSection.templateId && hasPhoneSideBtns"
      class="operate-box"
    >
      <div v-if="activeSection.templateId !== 'search'" class="wrapper">
        <div class="item" @click="handleSectionSort(1)">
          <img
            class="icon"
            :src="`${VITE_API_IMG}/2024/08/3dc9daf23f0b42278efedff53821f7ce.png`"
            alt=""
          />
        </div>
        <div class="item" @click="handleSectionSort(2)">
          <img
            class="icon"
            :src="`${VITE_API_IMG}/2024/08/2d0625cfa9a24113b101b8f87da90de8.png`"
            alt=""
          />
        </div>
        <div class="item" @click="handleSectionSort(3)">
          <img
            class="icon"
            :src="`${VITE_API_IMG}/2024/08/c5dbda5e95b64b53baaa0a32c259514d.png`"
            alt=""
          />
        </div>
        <div class="item" @click="handleSectionSort(4)">
          <img
            class="icon"
            :src="`${VITE_API_IMG}/2024/08/0e7b5af82324441ca1d44266d34187a6.png`"
            alt=""
          />
        </div>
      </div>
      <div class="wrapper">
        <div class="item" @click="openDelCpntDia">
          <img
            class="icon"
            :src="`${VITE_API_IMG}/2024/08/02661056a72d421a847dfd527ac41819.png`"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
  <!-- 删除确认弹窗 -->
  <a-modal
    v-model:open="cpntDelDia.visible"
    title="删除"
    :cancelText="null"
    @ok="handleCpntDelete"
  >
    <div class="del-dia-text">确定要删除该组件吗？</div>
  </a-modal>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { onMounted, onUnmounted, reactive, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { message } from "woody-ui";
import { phoneComponentArr } from "../config/phoneCfg";
import { getDecorationStore } from "@/store";

const decorationStore = getDecorationStore();
const { decorationInfo, activeNav } = storeToRefs(decorationStore);
const toolNavs = ref([]);
const activeSection = ref<any>({});
const scene = ref<string | null>(null);
const selectPhoneSection = (item: any) => {
  decorationStore.setActiveNav(item);
};

// 侧边操作栏
const hasPhoneSideBtns = ref(true);
const handleSectionSort = (type: number) => {
  decorationStore.setNavSort(type);
};
const openDelCpntDia = () => {
  cpntDelDia.visible = true;
};
// 删除组件
const cpntDelDia = reactive({
  data: {},
  visible: false,
});
const handleCpntDelete = () => {
  let activeIndex = decorationInfo.value.components.findIndex(
    (item: any) => item.flagId === activeNav.value.flagId
  );
  decorationInfo.value.components.splice(activeIndex, 1);
  if (activeIndex > decorationInfo.value.components.length - 1) {
    activeIndex -= 1;
  }
  decorationStore.setActiveNav(
    decorationInfo.value.components.length
      ? decorationInfo.value.components[activeIndex]
      : []
  );
  decorationStore.setDecorationInfo(decorationInfo.value);
  cpntDelDia.visible = false;
  message.success("删除成功");
};

watch(
  [decorationInfo, activeNav],
  (newVals) => {
    console.log(decorationInfo, activeNav, "333333", newVals);
    scene.value = decorationInfo.value.scene;
    toolNavs.value = newVals[0].components || [].map((item) => item);
    activeSection.value = activeNav.value;
    if (
      activeSection.value.templateId === "homePageTitle" ||
      activeSection.value.templateId === "navSingle" ||
      activeSection.value.templateId === "content" ||
      activeSection.value.templateId === "subPageTitle"
    ) {
      hasPhoneSideBtns.value = false;
    } else {
      hasPhoneSideBtns.value = true;
    }
    decorationStore.setActiveNav(activeSection.value);
    if (toolNavs.value && toolNavs.value.length) {
      const targetPhoneArr = computeTargetPhoneComponent();
      targetPhoneArr.forEach((item: any) => {
        toolNavs.value.forEach((itm) => {
          if (item.flagId === itm.flagId) {
            // 将toolNavs数据同步到computedPhoneComponentArr，info字段动态传入<component />
            item.info = itm.info;
          }
        });
      });
      computedPhoneComponentArr.value = targetPhoneArr;
      const titleData = decorationInfo.value.components.find(
        (item: any) => item.templateId === "homePageTitle"
      );
      if (titleData) {
        info.value = titleData.info;
        bgColor.value = titleData.info.isTransparent
          ? "#01D47B"
          : titleData.info.bgColor;
      } else {
        info.value = { textColor: "black", bgColor: "transparent" };
        bgColor.value = "transparent";
      }
    }
  },
  {
    deep: true,
  }
);

// phoneComponentArr 按照toolNavs重新排序
const computedPhoneComponentArr = ref<any>();
function computeTargetPhoneComponent() {
  const targetPhoneComponentArr = [];
  toolNavs.value.forEach((item) => {
    phoneComponentArr.forEach((itm: any) => {
      if (item.templateId === itm.templateId) {
        // itm.flagId = item.flagId;
        targetPhoneComponentArr.push({ ...itm, flagId: item.flagId });
      }
    });
  });
  return targetPhoneComponentArr;
}

// statusBar 配置
const info = ref<any>({ textColor: "black" });
const bgColor = ref("");
const timeStr = ref();
const timer = ref(null);
timer.value = setInterval(() => {
  getTimeStr();
}, 1000);
onMounted(() => {
  getTimeStr();
});
onUnmounted(() => {
  clearInterval(timer.value);
});
function getTimeStr() {
  timeStr.value = `${
    new Date().getHours() < 10
      ? `0${new Date().getHours()}`
      : new Date().getHours()
  }:${
    new Date().getMinutes() < 10
      ? `0${new Date().getMinutes()}`
      : new Date().getMinutes()
  }`;
}
</script>

<style lang="less" scoped>
.phone-container {
  align-items: center;
}
.phone-wrap {
  background-color: #fff;
  border-radius: 30px;
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 8px;
  box-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.08),
    0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 6px 30px 5px rgba(0, 0, 0, 0.05);
}
.phone-box {
  width: 375px;
  height: calc(100vh - 180px);
  min-height: 660px;
  background: #f5f7f7;
  border-radius: 22px;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: content-box;
}

/* 自定义整个滚动条 */
::-webkit-scrollbar {
  width: 6px;
  border-radius: 6px;
  /* 设置滚动条的宽度 */
  background-color: #f9f9f9;
  overflow: hidden;
  padding: 20px 0;
}

/* 自定义滚动条轨道 */
::-webkit-scrollbar-track {
  background: #e1e1e1;
  /* 轨道的背景色 */
  border-radius: 2px;
  /* 轨道的圆角 */
}

/* 自定义滚动条的滑块（thumb） */
::-webkit-scrollbar-thumb {
  background-color: #949292b6;
  /* 滑块的背景色 */
  border-radius: 6px;
  /* 滑块边框 */
}

/* 滑块hover效果 */
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
  /* 滑块hover时的背景色 */
}
.status-bar {
  width: 100%;
  height: 30px;
  // border-radius: 22px 22px 0 0;
  padding: 0 14px;
  justify-content: space-between;
  position: sticky;
  top: 0;
  left: 0;
  z-index: 11;

  .time {
    color: #fff;
  }

  .status {
    height: 12px;
  }
}

.operate-box {
  margin-right: 20px;
  .wrapper {
    cursor: pointer;
    overflow: hidden;
    margin-bottom: 8px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 3px 14px 2px
        rgba(0, 0, 0, 5%, 0 8px 10px 1px rgbal0, 0, 0, 6%),
      0 5px 5px -3px rgba(0, 0, 0, 10%);

    .item {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon {
        width: 16px;
        height: 16px;
        margin: 0 auto;
        user-select: none;
      }

      &:hover {
        background-color: #f5f7f7;
      }
    }
  }
}

.outline {
  border: 2px solid #5c9dff;
  // margin: 0;
  // box-shadow: 0 3px 14px 2px rgba(0, 0, 0, 0.05), 0 5px 5px -3px #1a7af8;
}

.noline {
  border: none;
  // margin: 2px;
}
.del-dia-text {
  padding: 20px;
  padding-bottom: 0;
  font-size: 16px;
  font-weight: bold;
}
</style>
