<template>
  <!-- 页面 -->
  <div v-if="decorationInfo.type === 'PAGE'" class="box flex">
    <div v-if="isShowNav" class="nav-box">
      <div class="title-sec flex">
        <div>页面布局</div>
        <img
          class="close"
          :src="`${VITE_API_IMG}/2024/08/3f34bde96e834c4aa269605d64bc19d1.png`"
          alt=""
          @click="isShowNav = false"
        />
      </div>
      <div class="content">
        <div
          v-for="(item, index) in decorationInfo.components"
          :key="index"
          class="item flex"
          :style="{
            background:
              activeNavItem.flagId === item.flagId ? '#f0f9ff' : '#fff',
            color: activeNavItem.flagId === item.flagId ? '#1a7af8' : '#05082c',
          }"
          @click="selectNavItem(item)"
        >
          {{ index + 1 }} {{ item.templateName }}
        </div>
      </div>
    </div>
    <div v-else class="expand-btn flex" @click="isShowNav = true">
      <img
        :src="`${VITE_API_IMG}/2024/08/a86b9f59d68a4fe7b76b93767ce3d5bb.png`"
        class="icon"
      />
    </div>
    <div class="content-box">
      <div class="title">
        {{ activeNavItem.templateName }}
      </div>
      <div class="content">
        <div v-if="toolNavs && toolNavs.length">
          <div v-for="(item, index) in rightbarComponentArr" :key="index">
            <div v-if="activeNavItem.templateId === item.templateId">
              <component :is="item.component" />
            </div>
          </div>
        </div>
        <div v-else class="empty-box">
          <img
            :src="`${VITE_API_IMG}/2024/08/e68773ad41b643a3b39b078b31542426.png`"
            class="icon"
          />
          <div class="text">暂无组件设置</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 内容 -->
  <div v-else class="box flex">
    <div v-if="isShowNav" class="nav-box">
      <div class="title-sec flex">
        <div>页面布局</div>
        <img
          class="close"
          :src="`${VITE_API_IMG}/2024/08/3f34bde96e834c4aa269605d64bc19d1.png`"
          alt=""
          @click="isShowNav = false"
        />
      </div>
      <div class="content">
        <draggable
          :sort="true"
          :list="decorationInfo.components"
          :animation="300"
        >
          <template #item="{ element, index }">
            <div
              class="item flex"
              :style="{
                background:
                  activeNavItem.flagId === element.flagId ? '#f0f9ff' : '#fff',
                color:
                  activeNavItem.flagId === element.flagId
                    ? '#1a7af8'
                    : '#05082c',
              }"
              @click="selectNavItem(element)"
            >
              <img
                :src="`${VITE_API_IMG}/2024/08/45bfd64447f44c51a2dd27c0a2a4e55c.png`"
                alt=""
                class="icon"
              />
              {{ index + 1 }} {{ element.templateName }}
            </div>
          </template>
        </draggable>
      </div>
    </div>
    <div v-else class="expand-btn flex" @click="isShowNav = true">
      <img
        :src="`${VITE_API_IMG}/2024/08/a86b9f59d68a4fe7b76b93767ce3d5bb.png`"
        alt=""
        class="icon"
      />
    </div>
    <div class="content-box">
      <div class="title">
        {{ activeNavItem.templateName }}
      </div>
      <div class="content">
        <div v-if="toolNavs && toolNavs.length">
          <div v-for="(item, index) in rightbarComponentArr" :key="index">
            <div v-if="activeNavItem.templateId === item.templateId">
              <component :is="item.component" />
            </div>
          </div>
        </div>
        <div v-else class="empty-box">
          <img
            :src="`${VITE_API_IMG}/2024/08/e68773ad41b643a3b39b078b31542426.png`"
            alt=""
            class="icon"
          />
          <div class="text">暂无组件设置</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, watchEffect } from "vue";
import { storeToRefs } from "pinia";
import draggable from "vuedraggable";
import { rightbarComponentArr } from "../config/rightbarCfg";
import { getDecorationStore } from "@/store";

const decorationStore = getDecorationStore();

const { decorationInfo, activeNav } = storeToRefs(decorationStore);
const toolNavs = ref([]);

const activeNavItem = ref<any>({});
const selectNavItem = (item: any) => {
  decorationStore.setActiveNav(item);
};

watchEffect(() => {
  toolNavs.value = decorationInfo.value.components;
  console.log(
    toolNavs.value,
    "toolNavs.value",
    decorationInfo.value.components
  );

  activeNavItem.value = activeNav.value;
  decorationStore.setActiveNav(activeNavItem.value);

  if (
    toolNavs.value &&
    toolNavs.value.length &&
    !activeNavItem.value.templateId
  ) {
    activeNavItem.value = { ...toolNavs.value[0] };
    decorationStore.setActiveNav(toolNavs.value[0]);
  }
});

const isShowNav = ref(true);
</script>

<style lang="less" scoped>
.box {
  height: calc(100vh - 76px);
  background-color: #fff;
  border-radius: 16px 0px 0px 16px;
  position: relative;
}

.nav-box {
  width: 175px;
  height: calc(100vh - 76px);
  border-right: 1px solid #f2f5f9;

  .title-sec {
    height: 56px;
    padding: 0 16px;
    justify-content: space-between;
    border-bottom: 1px solid #f2f5f9;

    .close {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
  .content {
    width: 100%;
    height: calc(100vh - 132px);
    padding-bottom: 50px;
    overflow-y: auto;
  }

  .item {
    width: 170px;
    height: 48px;
    font-size: 14px;
    color: #05082c;
    padding: 0 16px;
    user-select: none;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      cursor: move;
    }

    &:hover {
      background: #f7f8fa !important;
    }
  }
  /* 自定义整个滚动条 */
  ::-webkit-scrollbar {
    width: 2px;
    /* 设置滚动条的宽度 */
    background-color: #f9f9f9;
    /* 滚动条的背景色 */
  }

  /* 自定义滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: #e1e1e1;
    /* 轨道的背景色 */
    border-radius: 2px;
    /* 轨道的圆角 */
  }

  /* 自定义滚动条的滑块（thumb） */
  ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    /* 滑块的背景色 */
    border-radius: 2px;
    /* 滑块的圆角 */
    border: 1px solid #1a7af8;
    /* 滑块边框 */
  }

  /* 滑块hover效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
    /* 滑块hover时的背景色 */
  }
}

.content-box {
  width: 380px;
  height: calc(100vh - 76px);
  overflow-y: hidden;

  .title {
    height: 56px;
    padding-left: 16px;
    font-size: 16px;
    color: #05082c;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  .content {
    height: calc(100vh - 132px);
    padding: 24px;
    padding-bottom: 50px;
    overflow-y: auto;
  }

  /* 自定义整个滚动条 */
  ::-webkit-scrollbar {
    width: 2px;
    /* 设置滚动条的宽度 */
    background-color: #f9f9f9;
    /* 滚动条的背景色 */
  }

  /* 自定义滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: #e1e1e1;
    /* 轨道的背景色 */
    border-radius: 2px;
    /* 轨道的圆角 */
  }

  /* 自定义滚动条的滑块（thumb） */
  ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    /* 滑块的背景色 */
    border-radius: 2px;
    /* 滑块的圆角 */
    border: 1px solid #1a7af8;
    /* 滑块边框 */
  }

  /* 滑块hover效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
    /* 滑块hover时的背景色 */
  }
}

.expand-btn {
  width: 40px;
  height: 40px;
  background: #fff;
  border-radius: 8px;
  position: absolute;
  left: -48px;
  top: 1px;
  justify-content: center;
  cursor: pointer;

  .icon {
    width: 16px;
    height: 16px;
  }
}

.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20vh;
  .icon {
    width: 100px;
    height: 100px;
  }
  .text {
    font-size: 12px;
    color: #a2abbd;
  }
}
</style>
