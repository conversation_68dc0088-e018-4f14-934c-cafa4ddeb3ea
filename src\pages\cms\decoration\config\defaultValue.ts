let defaultValue: any;
export function getComponentDefaultValue(type: string) {
  if (type === "homePageTitle") {
    defaultValue = {
      textColor: "#172119",
      bgColor: "#fff",
      isTransparent: false,
      searchPageUrl: "",
      isSearchSwiperWords: true,
      swiperWords: [],
    };
  }
  if (type === "navSingle") {
    defaultValue = {
      textColor: "#172119",
      bgColor: "#fff",
      isTransparent: false,
      list: [
        {
          id: "1",
          name: "推荐",
          param: {
            id: "",
          },
          uriName: "",
        },
      ],
    };
  }
  if (type === "advert") {
    defaultValue = {
      imgUrl: null,
      bgColor: "#FFFFFF",
      uriType: 0,
      uriName: null,
      param: { id: null },
    };
  }
  if (type === "notice") {
    defaultValue = {
      text: "这里是公告内容",
    };
  }
  if (type === "search") {
    defaultValue = {
      id: "",
    };
  }
  if (type === "contentSearch") {
    defaultValue = {
      id: null,
    };
  }
  if (type === "divider") {
    defaultValue = {
      lineHeight: 8,
    };
  }
  if (type === "advert") {
    defaultValue = {
      bkChange: true,
      list: [
        {
          imgUrl: null,
          clickType: null,
          uriName: null,
          uriType: null,
          bgColor: "#FFFFFF",
          param: { id: null, prodSource: null },
        },
      ],
    };
  }
  if (type === "brand") {
    defaultValue = {
      type: "1",
      platCategoryId: null,
      platCategoryName: null,
      brands: [],
    };
  }
  if (type === "cube") {
    defaultValue = {
      bkColor: "#FFFFFF",
      isBglucency: false,
      iconType: "TWO_ROW",
      list: [
        {
          imgUrl: null,
          uriType: 0,
          uriName: null,
          param: {
            b_id: null,
            b_link: null,
            b_label: null,
            c_categoryName: "",
          },
        },
        {
          imgUrl: null,
          uriType: 0,
          uriName: null,
          param: {
            b_id: null,
            b_link: null,
            b_label: null,
            c_categoryName: "",
          },
        },
      ],
    };
  }
  if (type === "navLinkage") {
    defaultValue = {
      list: [
        {
          stairTitle: "导航一",
          name: null,
          order: 1,
          children: [
            {
              name: null,
              uriName: null,
              uriType: 0,
              param: { id: null },
            },
          ],
        },
      ],
    };
  }
  if (type === "homePage") {
    defaultValue = {
      list: [
        {
          id: "1",
          templateName: "标题栏",
          templateId: "homePageTitle",
          info: {
            textColor: "#172119",
            bgColor: "#fff",
            isTransparent: false,
            searchPageUrl: "",
            isSearchSwiperWords: true,
            swiperWords: [],
          },
        },
        {
          id: "2",
          templateName: "导航栏",
          templateId: "navSingle",
          info: {
            textColor: "#172119",
            bgColor: "#fff",
            isTransparent: false,
            list: [
              {
                id: "1",
                name: "全部",
                param: {
                  id: "",
                },
                uriName: "",
              },
            ],
          },
        },
      ],
    };
  }
  if (type === "subPage") {
    defaultValue = {
      list: [
        {
          id: "1",
          templateName: "标题栏",
          templateId: "subPageTitle",
          info: {
            text: "二级页面标题",
          },
        },
        {
          id: "2",
          templateName: "内容",
          templateId: "content",
          info: {},
        },
      ],
    };
  }
  if (
    type === "offlinePay" ||
    type === "onlinePayFinish" ||
    type === "offlinePayFinish"
  ) {
    defaultValue = {
      list: [
        {
          id: "",
          templateName: "内容",
          templateId: "content",
          info: {},
        },
      ],
    };
  }
  if (type === "singleAdvert") {
    defaultValue = {
      id: "",
      imgUrl: null,
      clickType: null,
      uriName: null,
      uriType: null,
      bgColor: "#FFFFFF",
      param: { id: null, prodSource: null },
    };
  }
  if (type === "goods") {
    defaultValue = {
      style: "2",
      feCategoryId: null,
      feCategoryName: null,
      imgGroup: [
        {
          order: null,
          imgs: [
            {
              imgUrl: null,
              uriType: 0,
              uriName: null,
              param: { id: null, prodSource: null },
            },
          ],
        },
      ],
    };
  }
  return defaultValue;
}
