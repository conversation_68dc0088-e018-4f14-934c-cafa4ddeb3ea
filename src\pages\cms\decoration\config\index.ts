import { Tool } from "../type";
const { VITE_API_IMG, VITE_API_IMG_CMS } = import.meta.env;

export const TOOLCOMPONENTS = [
  {
    id: 1,
    templateName: "标题栏",
    templateId: "homePageTitle",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/search_1749202399727.png`,
  },
  {
    id: 2,
    templateName: "标题栏",
    templateId: "subPageTitle",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/search_1749202399727.png`,
  },
  {
    id: 3,
    templateName: "搜索",
    templateId: "search",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/search_1749202399727.png`,
  },
  {
    id: 4,
    templateName: "公告",
    templateId: "notice",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/notice_1749202399726.png`,
  },
  {
    id: 5,
    templateName: "辅助分割",
    templateId: "divider",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/divider_1749202399579.png`,
  },
  {
    id: 6,
    templateName: "商品",
    templateId: "goods",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/goods_1749202399714.png`,
  },
  {
    id: 7,
    templateName: "品牌卡片",
    templateId: "brand",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/brand_1749202399581.png`,
  },
  {
    id: 8,
    templateName: "轮播广告",
    templateId: "advert",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/loopAd_1749202399727.png`,
  },
  {
    id: 9,
    templateName: "单层导航", // 单层导航，只有首页有
    templateId: "navSingle",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/navigation_1749202399608.png`,
  },
  {
    id: 10,
    templateName: "联动导航", // 只有首页有
    templateId: "navLinkage",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/navigation_1749202399608.png`,
  },
  {
    id: 11,
    templateName: "魔方",
    templateId: "cube",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/cube_1749202399726.png`,
  },
  // {
  //   id: 11,
  //   templateName: '顶部分类',
  //   templateId: 'navTop',
  //   icon: 'https://img.zsjming.com/2024/08/b28ef4b94bbb4a4aa1f41c870c23f68c.png',
  // },
  // {
  //   id: 12,
  //   templateName: '左侧分类',
  //   templateId: 'navLeft',
  //   icon: 'https://img.zsjming.com/2024/08/dda1bca645f944f5a339566d4915384c.png',
  // },
  // {
  //   id: 13,
  //   templateName: '双层分类',
  //   templateId: 'navDouble',
  //   icon: 'https://img.zsjming.com/2024/08/56e104fbc2b6438588c7a089a00ee852.png',
  // },
  {
    id: 14,
    templateName: "搜索",
    templateId: "contentSearch",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/search_1749202399727.png`,
  },
  {
    id: 15,
    templateName: "单图广告",
    templateId: "singleAdvert",
    icon: `${VITE_API_IMG_CMS}/prod/image/in_coming/life_plat/2025/06/06/singleAd_1749202399724.png`,
  },
];

export const tplIconData = [
  {
    iconText: "一行两个",
    iconType: "TWO_ROW",
    icon: `${VITE_API_IMG}/2024/08/d939d959bf9f4921bd8b6a8335874b80.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/23584542be324637a9132d3fd35d18e0.png`,
  },
  {
    iconText: "一行三个",
    iconType: "THREE_ROW",
    icon: `${VITE_API_IMG}/2024/08/8013b01fe328430f8a08ef352655fa0d.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/fed54fb0da4c47d3bfddd77cf52c9926.png`,
  },
  {
    iconText: "一行四个",
    iconType: "FOUR_ROW",
    icon: `${VITE_API_IMG}/2024/08/5e2281913bb34e1ca95a8b6ec264c0a5.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/388770f751d146fc85da38028dc37083.png`,
  },
  {
    iconText: "一行五个",
    iconType: "FIVE_ROW",
    icon: `${VITE_API_IMG}/2024/08/20d8b8f02a7147f8a5feab45f207d5ee.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/bfe9fc43259849b4abca3a8f7a519001.png`,
  },
  {
    iconText: "二行五个",
    iconType: "TWO_FIVE_ROW",
    icon: `${VITE_API_IMG}/2024/12/71e6a0a90a2b4f1ca420249802883cd1.png`,
    selectIcon: `${VITE_API_IMG}/2024/12/ed192ec730d04d91853421722ffc0d8b.png`,
  },
  {
    iconText: "四宫格",
    iconType: "FOUR_GRID",
    icon: `${VITE_API_IMG}/2024/08/042e257f42b5466f96b279021f117056.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/0803a41601e9461ebb178b7098970a5a.png`,
  },
  {
    iconText: "一左两右",
    iconType: "ONE_LEFT_TWO_RIGHT",
    icon: `${VITE_API_IMG}/2024/08/8c1e862d34d84609afc64adc18a590c4.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/ce0a0c7285e946888221e467888cea7b.png`,
  },
  {
    iconText: "一上两下",
    iconType: "ONE_UP_TWO_DOWN",
    icon: `${VITE_API_IMG}/2024/08/24a958c7feef45a985d06b1786c425c9.png`,
    selectIcon: `${VITE_API_IMG}/2024/08/653b14501e0d4fb7986943a2bd107b4d.png`,
  },
];

export const iconTypeMap = {
  TWO_ROW: 2,
  THREE_ROW: 3,
  FOUR_ROW: 4,
  FIVE_ROW: 5,
  TWO_FIVE_ROW: 10,
  FOUR_GRID: 4,
  ONE_LEFT_TWO_RIGHT: 3,
  ONE_UP_TWO_DOWN: 3,
};

export const radioData = [
  {
    value: "2",
    label: "一行两个",
  },
  {
    value: "1",
    label: "一行一个",
  },
];

export const brandRadioData = [
  {
    value: "1",
    label: "指定分类",
  },
  {
    value: "2",
    label: "指定品牌",
  },
];

/**
 *
 * @param array tool组件name集合
 * @returns tool组件对象数据集合
 */
export function transToolName2Obj(array: Array<string>) {
  const tempArr = [];
  array.forEach((item) => {
    tempArr.push(TOOLCOMPONENTS.find((tool) => tool.templateId === item));
  });
  return tempArr;
}

/**
 *
 * @param decorationScene 装修场景
 * @returns tool组件列表
 * @description 根据装修场景获取tool组件列表
 */
export function computeToolConfig(decorationScene: string) {
  switch (decorationScene) {
    case "subPage": // 页面-二级页面
      return transToolName2Obj(["search"]);
    case "firstScreen": // 内容-首屏
      return transToolName2Obj([
        "advert",
        "cube",
        "divider",
        "notice",
        "navLinkage",
      ]);
    case "goods": // 内容-商品
      return transToolName2Obj([
        "contentSearch",
        "advert",
        "singleAdvert",
        "cube",
        "divider",
        "goods",
      ]);
    case "brand": // 内容-品牌
      return transToolName2Obj(["advert", "cube", "divider", "brand"]);
    default:
      return [];
  }
}

/**
 *
 * @param array tool组件对象数据集合
 * @returns tool组件name集合
 */
export function transToolObj2Name(array: Array<Tool>) {
  const tempArr = [];
  array.forEach((item: Tool) => {
    tempArr.push(item.templateId);
  });
  return tempArr;
}
