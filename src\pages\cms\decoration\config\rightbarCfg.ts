import advert from "@/pages/cms/decoration/RightCpnts/advert.vue";
import brand from "@/pages/cms/decoration/RightCpnts/brand.vue";
import cube from "@/pages/cms/decoration/RightCpnts/cube.vue";
import divider from "@/pages/cms/decoration/RightCpnts/divider.vue";
import goods from "@/pages/cms/decoration/RightCpnts/goods.vue";
import notice from "@/pages/cms/decoration/RightCpnts/notice.vue";
import search from "@/pages/cms/decoration/RightCpnts/search.vue";
import contentSearch from "@/pages/cms/decoration/RightCpnts/contentSearch.vue";
import homePageTitle from "@/pages/cms/decoration/RightCpnts/titleHomePage.vue";
import subPageTitle from "@/pages/cms/decoration/RightCpnts/titleSubPage.vue";
import navSingle from "@/pages/cms/decoration/RightCpnts/navSingle.vue";
import navLinkage from "@/pages/cms/decoration/RightCpnts/navLinkage.vue";
import navTop from "@/pages/cms/decoration/RightCpnts/navTop.vue";
import navLeft from "@/pages/cms/decoration/RightCpnts/navLeft.vue";
import content from "@/pages/cms/decoration/RightCpnts/content.vue";
import singleAdvert from "@/pages/cms/decoration/RightCpnts/singleAdvert.vue";

export const rightbarComponentArr = [
  {
    templateId: "advert",
    templateName: "轮播广告",
    component: advert,
  },
  {
    templateId: "brand",
    templateName: "品牌",
    component: brand,
  },
  {
    templateId: "cube",
    templateName: "魔方",
    component: cube,
  },
  {
    templateId: "divider",
    templateName: "辅助分割",
    component: divider,
  },
  {
    templateId: "goods",
    templateName: "商品",
    component: goods,
  },
  {
    templateId: "navSingle",
    templateName: "单层导航",
    component: navSingle,
  },
  {
    templateId: "navLinkage",
    templateName: "联动导航",
    component: navLinkage,
  },
  {
    templateId: "navTop",
    templateName: "顶部分类商品",
    component: navTop,
  },
  {
    templateId: "navLeft",
    templateName: "左侧分类商品",
    component: navLeft,
  },
  {
    templateId: "notice",
    templateName: "公告",
    component: notice,
  },
  {
    templateId: "search",
    templateName: "搜索",
    component: search,
  },
  {
    templateId: "contentSearch",
    templateName: "搜索",
    component: contentSearch,
  },
  {
    templateId: "homePageTitle",
    templateName: "首页标题栏",
    component: homePageTitle,
  },
  {
    templateId: "subPageTitle",
    templateName: "标题栏",
    component: subPageTitle,
  },
  {
    templateId: "content",
    templateName: "内容",
    component: content,
  },
  {
    templateId: "singleAdvert",
    templateName: "单图广告",
    component: singleAdvert,
  },
];
