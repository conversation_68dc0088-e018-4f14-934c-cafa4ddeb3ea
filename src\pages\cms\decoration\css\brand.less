.brand-container {
    .brand-title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #05082c;
    }
    .brand-radio {
        margin-top: 13px;
    }
    .brand-goods {
        margin-top: 29px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #05082c;
    }
    .brand-select-details {
        display: flex;
        align-items: center;
        height: 32px;
        border: 1px solid #e0e8ed;
        border-radius: 3px;
        margin-top: 8px;
        cursor: pointer;
        .select-details-left {
            width: 72px;
            line-height: 32px;
            background: #edf1f2;
            text-align: center;
            border-radius: 3px 0px 0px 3px;
            color: #05082c;
        }
        .select-details-right {
            width: 76%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            span {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #636d7e;
                margin-left: 8px;
            }
            .details-span{
                color: #05082c !important;
            }
            img {
                width: 16px;
                height: 16px;
            }
        }
    }
    .brand-info {
        display: flex;
        align-items: center;
        margin-top: 29px;
        span:first-child {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #000000;
        }
        span:last-child {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #a2abbd;
            margin-left: 8px;
        }
    }
    .brand-list {
        display: flex;
        align-items: center;
        padding: 24px;
        cursor: pointer;
        margin-top: 10px;
        position: relative;
        .brand-Image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            border: 1px solid #cccccc;
        }
        .brand-box {
            width: 68%;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-left: 10px;
            .brand-box-name {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #05082c;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2; /* 显示的行数 */
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .brand-shut-Img {
            width: 16px;
            height: 16px;
            position: absolute;
            top: 8px;
            right: 8px;
            display: none;
        }
    }
    .brand-list:hover {
        background: #ffffff;
        box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12), 0px 4px 5px 0px rgba(0, 0, 0, 0.08),
            0px 1px 10px 0px rgba(0, 0, 0, 0.05);
        border-radius: 8px 8px 8px 8px;
        .brand-shut-Img {
            display: block;
        }
    }
}