.cube-content {
  .cube-bgColor-set {
    display: flex;
    align-items: center;

    .bgColor-text {
      font-weight: bold;
      font-size: 14px;
      color: #05082c;
    }

    .bgColor-xh {
      font-weight: 400;
      font-size: 14px;
      color: #ff436a;
      margin-left: 5px;
    }
  }

  .cube-selectBg {
    width: 80%;
    margin-top: 14px;
  }

  .cube-switch-title {
    display: flex;
    align-items: center;
    margin-top: 24px;

    .switch-bgColor-text {
      font-weight: bold;
      font-size: 14px;
      color: #05082c;
    }

    .switch-bgColor-xh {
      font-weight: 400;
      font-size: 14px;
      color: #ff436a;
      margin-left: 5px;
    }
  }

  .cube-switch {
    margin-top: 14px;
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }

  .cube-tpl {
    display: flex;
    align-items: center;
    margin-top: 24px;

    :nth-child(1) {
      font-weight: bold;
      font-size: 14px;
      color: #05082c;
    }

    :nth-child(3) {
      margin-left: 10px;
      font-weight: 400;
      font-size: 12px;
      color: #a2abbd;
    }

    :nth-child(2) {
      font-weight: 400;
      font-size: 14px;
      color: #ff436a;
      margin-left: 5px;
    }
  }

  .cube-tpl-list {
    margin-top: 14px;

    .tp-list {
      width: 70%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: start;
      cursor: pointer;

      img {
        width: 52px;
        height: 34px;
        // margin-left: 2px;
        margin-bottom: 2px;
      }
    }

    .tpl-list-img {
      width: 330px;
      margin-top: 16px;
      border: 1px solid #e0e8ed;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      border-radius: 4px;
      overflow: hidden;
      box-sizing: border-box;

      .row-two {
        width: 50%;
        height: auto;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }

        img {
          width: 100%;
          height: auto;
          display: block;
          border-radius: 3px;
        }
      }

      .row-two-border {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-three {
        width: 33.3%;
        height: auto;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }

        img {
          width: 100%;
          height: auto;
          display: block;
          border-radius: 3px;
        }
      }

      .row-three:nth-child(1) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-three:nth-child(2) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-three:nth-child(3) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-four {
        width: 25%;
        height: auto;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        .cube-row-four-p{
          p {
            font-weight: 400;
            font-size: 14px;
            color: #c7d1d0;
            text-align: center;
          }
        }
        

        img {
          width: 100%;
          height: auto;
          border-radius: 3px;
          display: block;
        }
      }

      .row-four:nth-child(1) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-four:nth-child(2) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-four:nth-child(3) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-five {
        width: 20%;
        height: auto;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }

        img {
          width: 100%;
          /* 图像宽度填满容器 */
          height: auto;
          border-radius: 3px;
          display: block;
        }
      }

      .row-five:nth-child(1) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-five:nth-child(2) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-five:nth-child(3) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }

      .row-five:nth-child(4) {
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
      }
      .two-five-row{
        width: 20%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        border-right: 1px solid #e0e8ed;
        box-sizing: border-box;
        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }
        img {
          width: 100%;
          /* 图像宽度填满容器 */
          height:auto;
          border-radius: 3px;
        }
      }
      .two-five-row:nth-child(1) {
        border-bottom: 1px solid #e0e8ed;
        box-sizing: border-box;
      }
      .two-five-row:nth-child(2) {
        border-bottom: 1px solid #e0e8ed;
        box-sizing: border-box;
      }
      .two-five-row:nth-child(3) {
        border-bottom: 1px solid #e0e8ed;
        box-sizing: border-box;
      }
      .two-five-row:nth-child(4) {
        border-bottom: 1px solid #e0e8ed;
        box-sizing: border-box;
      }
      .two-five-row:nth-child(5) {
        border-bottom: 1px solid #e0e8ed;
        border-right: none !important;
        box-sizing: border-box;
      }
      .two-five-row:nth-child(10) {
        border-right: none !important;
        box-sizing: border-box;
      }
      .four-grid-row {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        box-sizing: border-box;

        .four-grid {
          width: 50%;
          height: auto;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;

          p {
            font-weight: 400;
            font-size: 14px;
            color: #c7d1d0;
            text-align: center;
          }

          img {
            width: 100%;
            height: auto;
            display: block;
            border-radius: 3px;
          }
        }

        .four-grid:nth-child(1) {
          box-sizing: border-box;
          border-right: 1px solid #e0e8ed;
          border-bottom: 1px solid #e0e8ed;
          box-sizing: border-box;
        }

        .four-grid:nth-child(2) {
          border-bottom: 1px solid #e0e8ed;
          box-sizing: border-box;
        }

        .four-grid:nth-child(3) {
          border-right: 1px solid #e0e8ed;
          box-sizing: border-box;
        }
      }

      .row-one {
        width: 50%;
        height:auto;
        border-right: 1px solid #e0e8ed;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }

        img {
          width: 100%;
          height:auto;
          display: block;
          border-radius: 3px;
        }
      }

      .row-right-center {
        width: 50%;
        height: auto;
        overflow: hidden;
        box-sizing: border-box;

        .row-right-tow {
          width: 100%;
          height: auto;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;

          p {
            font-weight: 400;
            font-size: 14px;
            color: #c7d1d0;
            text-align: center;
          }

          img {
            width: 100%;
            height: auto;
            display: block;
            border-radius: 3px;
          }
        }

        .row-right-tow-border {
          border-bottom: 1px solid #e0e8ed;
          box-sizing: border-box;
        }
      }

      .row-top-one {
        width: 100%;
        height: auto;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #e0e8ed;
        box-sizing: border-box;

        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }

        img {
          width: 100%;
          height: auto;
          display: block;
          border-radius: 3px;
        }
      }

      .row-top-tow {
        width: 50%;
        height: auto;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        p {
          font-weight: 400;
          font-size: 14px;
          color: #c7d1d0;
          text-align: center;
        }
        img {
          width: 100%;
          height: auto;
          display: block;
          border-radius: 3px;
        }
      }

      .row-top-tow-border {
        box-sizing: border-box;
        border-right: 1px solid #e0e8ed;
      }
    }

    .tpl-from-content {
      width: 100%;
      border-bottom: 1px solid #f2f5f9;
      cursor: pointer;
      .tpl-from-list {
        width: auto;
        display: flex;
        align-items: center;
        border-radius: 6px;
        padding-top: 10px;
        padding-bottom: 10px;
        .from-list-left {
          width: 120px;
          height: 120px;
          background: #f1f6f8;
          border-radius: 6px;
          overflow: hidden;
          cursor: pointer;
          position: relative;
          margin-left: 10px;
          &:hover .from-click-img {
            display: block;
          }

          .from-show-img {
            width: 120px;
            height: 120px;
            border-radius: 3px;
          }

          .from-click-img {
            width: 100%;
            line-height: 38px;
            background: #1d2426;
            opacity: 0.9;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            text-align: center;
            position: absolute;
            bottom: 0;
            left: 0;
            border-bottom-left-radius: 6rpx;
            border-bottom-right-radius: 6rpx;
            display: none;
          }

          .from-icon-add {
            width: 24px;
            height: 24px;
            display: block;
            margin-left: auto;
            margin-right: auto;
            margin-top: 33px;
          }

          p {
            margin-top: 5px;
            font-weight: 400;
            font-size: 12px;
            color: #636d7e;
            text-align: center;
          }
        }

        .from-list-right {
          width: 55%;
          height: 120px;
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          p {
            font-weight: 400;
            font-size: 12px;
            color: #818999;
            margin-bottom: 0;
          }

          .from-input {
            width: 100%;
            height: 32px;
            border: 1px solid #e0e8ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 3px;
            cursor: pointer;

            span {
              font-weight: 400;
              font-size: 14px;
              color: #05082c;
              margin-left: 10px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            img {
              width: 16px;
              height: 16px;
              margin-right: 10px;
            }

            &:hover {
              border-color: #5c9dff;
            }
          }
        }
      }
    }
    .tpl-from-content:first-child{
      margin-top: 10px;
    }
    .tpl-from-content:last-child{
      border: none !important;
    }
    .tpl-from-content:hover {
      .tpl-from-list {
        box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.12), 0px 4px 5px 0px rgba(0,0,0,0.08), 0px 1px 10px 0px rgba(0,0,0,0.05);
      }
    }
  }

  .parameter {
    margin-top: 10px;
  }

  .set-parameter {
    display: flex;
    align-items: center;
    .label {
      width: 56px;
    }
    :deep(.t-input__wrap) {
      width: calc(100% - 56px)
    }
  }
}