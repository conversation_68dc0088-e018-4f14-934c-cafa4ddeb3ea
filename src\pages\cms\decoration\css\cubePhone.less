.cube-content {
    margin-left: 8px;
    margin-right: 8px;
    display: flex;
    justify-content:space-between;
    align-items: center;
    border-radius: 8px;
    .cube-content-default {
        width: 100%;
        height: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
            width: 122px;
            height: 70px;
            display: block;
        }
    }
    .cube-row-two {
        width: 50%;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: content-box;
        border-radius: 8px;
        overflow: hidden;
        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }

    .cube-row-three {
        width: 33.33333%;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: content-box;
        border-radius: 8px;
        overflow: hidden;
        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }
    .cube-row-four {
        width: 25%;
        display: flex;
        justify-content: center;
        box-sizing: content-box;
        border-radius: 8px;
        overflow: hidden;
        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }
    .cube-row-five {
        width: 20%;
        display: flex;
        justify-content: center;
        box-sizing: content-box;
        border-radius: 8px;
        overflow: hidden;
        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }
    .two-five-fow-list{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow: hidden;
        box-sizing: border-box;
        border-radius: 8px;
        .two-five-fow {
            width:20%;
            display: flex;
            justify-content: center;
            box-sizing: content-box;
            border-radius: 8px;
            overflow: hidden;
            img {
                width: 100%;
                height: auto;
                display: block;
            }
        }
    }
    
    .cube-four-grid {
        width: 100%;
        .cube-four-grid-list {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            .four-grid-list-img {
                width: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                overflow: hidden;
                img {
                    width: 100%;
                    display: block;
                }
            }
        }
    }
    .cube-row-one {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .cube-row-one-list {
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            border-radius: 8px;
            overflow: hidden;
            img {
                width: 100%;
                display: block;
            }
            .row-one-list-left {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .row-one-img {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    align-items: center;
                    border-radius: 8px;
                    overflow: hidden;
                    img{
                        width: 100%;
                        display: block;
                    }
                }
            }
        }
    }
    .cube-row-topButtom {
        width: 100%;
        .topButtom-list {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            overflow: hidden;
            img {
                width: 100%;
                display: block;
            }
        }
        .topButtom-list-tow{
            width: 100%;
            display: flex;
            justify-content: space-between;
            border-radius: 8px;
            .topButtom-list-img {
                width: 50%;
                display: flex;
                justify-content: center;
                border-radius: 8px;
                overflow: hidden;
                img {
                    width: 100%;
                    display: block;
                }
            }
        }
    }
}
.cube-Image-default{
    margin-left: 8px;
    margin-right: 8px;
    height: 160px;
    background:#ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    img{
        width:100%;
        height: auto;
        display: block;
    }
}