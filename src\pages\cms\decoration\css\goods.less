.goods-content {
  .goods-title {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }
  .goods-radio {
    margin-top: 13px;
  }
  .goods-manage {
    margin-top: 29px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }
  .goods-select-details {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    border: 1px solid #e0e8ed;
    border-radius: 3px;
    margin-top: 8px;
    cursor: pointer;
    .select-details-list {
      width: 98%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .select-details-text {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-left: 8px;
      }
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  .goodsImg-title {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #000000;
    margin-top: 37px;
  }
  .goodsImg-text {
    font-family: <PERSON>Fang SC;
    font-weight: 400;
    font-size: 12px;
    color: #495366;
  }
  .goodsImg-arr {
    margin-top: 16px;
    display: flex;
    align-items: center;
    span:first-child {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }
    span:last-child {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #a2abbd;
      margin-left: 7px;
    }
  }
  .goodsImg-InputCenter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 8px;
    .Input-wt {
      width: 87%;
      .t-input-number {
        width: 100%;
      }
    }
    .InputCenter-Img {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f1f6f8;
      border-radius: 3px;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  .goodsImg-list {
    width: 100%;
    border: 1px solid #f2f5f9;
    border-radius: 8px;
    margin-top: 24px;
    .goodsImg-list-center {
      margin-left: 24px;
      padding-right: 24px;
      padding-top: 24px;
      padding-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #f2f5f9;
      border-radius: 8px;
      position: relative;
      .Image-add {
        width: 100px;
        height: 100px;
        background: #f1f6f8;
        border: 1px dashed #e0e8ed;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        .Image-add-center {
          img {
            width: 24px;
            height: 24px;
            margin-left: auto;
            margin-right: auto;
            display: block;
          }
          p {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #636d7e;
            text-align: center;
          }
        }
        img {
          width: 100px;
          height: 100px;
          border-radius: 3px;
        }
      }
      .Image-url {
        width: 60%;
        height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .url-p {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #818999;
        }
        .url-box {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;
          border: 1px solid #e0e8ed;
          border-radius: 3px;
          .url-box-dz {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            span {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: #05082c;
              margin-left: 10px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            img {
              width: 16px;
              height: 16;
              margin-right: 8px;
            }
          }
        }
      }
      .shut-Img {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 8px;
        right: 8px;
        opacity: 0;
      }
    }
    .goodsImg-list-center:last-child {
      border-bottom: 0 !important;
    }
    .goodsImg-list-center:hover {
      margin: 0;
      padding-left: 24px;
      box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12), 0px 4px 5px 0px rgba(0, 0, 0, 0.08),
        0px 1px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      border-bottom: 0 !important;
      cursor: pointer;
      .shut-Img {
        opacity: 1;
      }
    }
    .goodsImg-list-footer {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      .footer-button {
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #05082c;
        }
        span:last-child {
          color: #a2abbd;
        }
      }
    }
  }
  .goodsImg-addArr {
    width: 290px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #1a7af8;
    border-radius: 3px;
    margin-top: 24px;
    margin-left: auto;
    margin-right: auto;
    cursor: pointer;
    .addArr-center {
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 18px;
        height: 18px;
      }
      span {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #1a7af8;
        margin-left: 12px;
      }
    }
  }
}
