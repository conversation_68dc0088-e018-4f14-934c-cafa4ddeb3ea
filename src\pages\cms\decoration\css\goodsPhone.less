.goods-content {
    margin-left: 8px;
    margin-right: 8px;

    .goods-nav-tap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;
        font-family: PingFang SC;
        font-weight: 500;
        cursor: pointer;
        .nav-tap-zh {
            width: 33.33%;
            color: #000000;
            font-size: 14px;
            text-align: center;
        }

        .nav-tap-price {
            width: 33.33%;
            display: flex;
            align-items: center;
            justify-content: center;
            span {
                font-size: 14px;
                color: #919C99;

            }
            img {
                width: 12px;
            }
        }

        .nav-tap-new {
            width: 33.33%;
            color: #919C99;
            font-size: 14px;
            text-align: center;
        }
    }

    // 一行一个
    .goods-rowOne {
        border-radius: 8px;
        background: #ffffff;
        padding-top: 8px;
        display: flex;
        align-items: flex-start;
        padding-bottom: 8px;
        margin-bottom: 10px;

        .goods-rowOne-left {
            width: 142px;
            height: 142px;
            border-radius: 6px;
            overflow: hidden;
            margin-left: 8px;

            .rowOne-left-Img {
                width: 142px;
                height: 142px;
                border-radius: 6px;
            }
        }

        .goods-rowOne-right {
            width: 52%;
            height: 142px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-left: 10px;

            .goods-rowOne-top {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 112px;

                .goods-title-line {
                    .goods-name {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: #000000;
                        display: -webkit-box;
                        /* 必须配合此属性使用 */
                        -webkit-box-orient: vertical;
                        /* 设置为垂直方向 */
                        -webkit-line-clamp: 2;
                        /* 显示的行数 */
                        overflow: hidden;
                        /* 隐藏超出的内容 */
                    }

                    .goods-title {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #00a473;
                        padding-top: 5px;
                    }
                }

                .goods-Sales {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #919c99;
                }

                .goods-notice {
                    display: flex;
                    align-items: center;
                    height: 18px;
                    background: #fff7ea;
                    border-radius: 4px;
                    justify-content: space-between;

                    .goods-notice-box {
                        display: flex;
                        align-items: center;

                        .goods-notice-jb {
                            width: 19px;
                            height: 18px;
                        }

                        span {
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            font-size: 12px;
                            color: #ed991a;
                            margin-left: 8px;
                        }
                    }

                    .goods-notice-fh {
                        width: 16px;
                        height: 16px;
                    }
                }

                .goods-price {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-top: 4px;
                    padding-bottom: 5px;

                    .goods-price-left {
                        display: flex;
                        align-items: baseline;

                        .price-unit {
                            font-family: OPlusSans 30;
                            font-weight: bold;
                            font-size: 14px;
                            color: #f43b3b;
                        }

                        .price-unit-num {
                            font-family: OPlusSans 30;
                            font-weight: bold;
                            font-size: 20px;
                            color: #f43b3b;
                        }

                        .price-tag {
                            font-family: OPlusSans 30;
                            font-weight: 400;
                            font-size: 12px;
                            color: #86909c;
                        }

                        .price-Scribing {
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            font-size: 12px;
                            color: #d9d9d9;
                            text-decoration: line-through;
                            margin-left: 4px;
                        }
                    }

                    .goods-price-right {
                        width: 24px;
                        height: 24px;

                        img {
                            width: 20px;
                            height: 20px;
                        }
                    }
                }

                .goods-tag-list {
                    display: flex;
                    align-items: center;
                    padding-top: 4px;
                    padding-bottom: 4px;

                    .tag-bq {
                        padding-left: 5px;
                        padding-right: 5px;
                        height: 18px;
                        display: flex;
                        align-items: center;
                        background: #fff9e8;
                        border-radius: 4px;
                        border: 1px solid #ffecba;
                        margin-right: 6px;

                        img {
                            width: 14px;
                            height: 14px;
                        }

                        span {
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            font-size: 12px;
                            color: #ffa800;
                            margin-left: 2px;
                        }
                    }

                    .tag-mj {
                        margin-right: 6px;
                        padding-left: 5px;
                        padding-right: 5px;
                        line-height: 18px;
                        background: #fff0ed;
                        border-radius: 4px;
                        border: 1px solid #ffd9cd;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        font-size: 12px;
                        color: #f43b3b;
                    }
                }
            }

            .goods-shop {
                height: 30px;
                display: flex;
                align-items: center;
                border-top: 1px solid #f5f7f7;

                img {
                    width: 19px;
                    height: 19px;
                    border: 1px solid #e0e8ed;
                    border-radius: 4px;
                }

                span {
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    font-size: 12px;
                    color: #616a66;
                    margin-left: 4px;
                }
            }
        }
    }

    // 一行两个
    .goods-container {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .goods-container-left,
        .goods-container-right {
            .goods-column-container {
                width: 172px;
                height: auto;
                border-radius: 6px;
                margin-bottom: 10px;
                overflow: hidden;

                /** banner轮播图片 */
                .column-colo-banner {
                    width: 173px;
                    height: auto;

                    .column-swiper-Img {
                        width: 173px;
                        height: auto;
                        overflow: hidden;

                        .banner-swiper-Img {
                            width: 100%;
                            height: auto;
                            object-fit: scale-down;
                            border-radius: 4px;
                            overflow: hidden;

                            img {
                                display: block !important;
                            }
                        }
                    }

                    .row-default {
                        width: 103px;
                        height: 82px;
                        display: block;
                        margin-left: auto;
                        margin-right: auto;
                    }

                    .t-image {
                        display: block !important;
                    }
                }

                /** 商品信息模块 */
                .goods-item-box-content {
                    width: 100%;

                    .goods-item-box-wd {
                        width: 172px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .t-image {
                            width: 100%;
                            height: auto;
                            display: block !important;
                        }
                    }

                    .goods-item-box-details {
                        background: #ffffff;
                        padding-left: 10px;
                        padding-right: 10px;
                        padding-top: 10px;

                        .goods-name-box {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 14px;
                            color: #1d2129;
                            display: -webkit-box;
                            /* 必须配合此属性使用 */
                            -webkit-box-orient: vertical;
                            /* 设置为垂直方向 */
                            -webkit-line-clamp: 2;
                            /* 显示的行数 */
                            overflow: hidden;
                            /* 隐藏超出的内容 */
                        }

                        .prod-buy-point-tags {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 12px;
                            color: #00A473;
                        }

                        .goods-price-box {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            height: 34px;

                            .goods-price-left {
                                display: flex;
                                align-items: baseline;
                                justify-content: space-between;

                                .goods-price-unit-group {
                                    display: flex;
                                    align-items: baseline;

                                    .goods-price-unit {
                                        font-family: OPlusSans 30;
                                        font-weight: bold;
                                        font-size: 14px;
                                        color: #f43b3b;
                                    }

                                    .goods-price-text {
                                        font-family: OPlusSans 30;
                                        font-weight: bold;
                                        font-size: 20px;
                                        color: #f43b3b;
                                    }
                                }

                                .crossedPrice {
                                    font-family: Source Han Sans CN;
                                    font-weight: 400;
                                    font-size: 12px;
                                    color: #D9D9D9;
                                    text-align: center;
                                    margin-left: 5px;
                                    text-decoration: line-through;
                                }
                            }

                            .shop-car-fix {
                                width: 24px;
                                height: 24px;

                                img {
                                    width: 24px;
                                    height: 24px;
                                }
                            }
                        }

                        .supply-shop-box {
                            display: flex;
                            align-items: center;
                            height: 35px;
                            border-top: 1px solid #e0e8ed;
                            border-radius: 4px;

                            img {
                                width: 19px;
                                height: 19px;
                                border: 1px solid #e0e8ed;
                            }

                            p {
                                font-family: Source Han Sans CN;
                                font-weight: 400;
                                font-size: 12px;
                                color: #616a66;
                                margin-left: 5px;
                                width: 85%;
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }
            }
        }
    }

    .goods-Image-default {
        width: 100%;
        background: #ffffff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 100%;
            height: auto;
        }
    }
}