.navLinage-content {
  .navLinage-nav {
    height: 40px;
    display: flex;
    align-items: center;
    .nav-center {
      height: 40px;
      background: #f2f5f9;
      display: flex;
      align-items: center;
      .nav-li {
        padding-left: 13px;
        padding-right: 13px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        span {
          font-weight: 400;
          font-size: 14px;
          color: #495366;
        }
        img {
          width: 16px;
          height: 16px;
        }
      }
      .active {
        background: #ffffff;
      }
      .nav-li:hover {
        background-color: #e0e7ea;
      }
    }
    .navAdd-right {
      width: 30px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f1f6f8;
      cursor: pointer;
      z-index: 10;
      box-shadow: -2px 0 5px 0 rgba(0, 0, 0, 0.1);
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  .navLinage-Input-center {
    .navLinage-Input-title {
      display: flex;
      align-items: center;
      margin-top: 24px;
      .Input-title {
        display: flex;
        align-items: center;
        :first-child {
          font-weight: 400;
          font-size: 14px;
          color: #05082c;
        }
        :last-child {
          font-weight: 400;
          font-size: 14px;
          color: #ff436a;
          margin-left: 2px;
        }
      }
      .Input-text {
        width: 140px;
        height: 32px;
        margin-left: 5px;
      }
      .Input-suffix {
        width: 105px;
        height: 32px;
        margin-left: 10px;
        .t-input-number {
          width: 100% !important;
        }
      }
    }
    .Input-text-p {
      font-weight: 400;
      font-size: 12px;
      color: #a2abbd;
      margin-top: 24px;
    }
    .Input-nav-list {
      height: 136px;
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      padding: 24px;
      background: #ffffff;
      cursor: pointer;
      margin-top: 5px;
      margin-bottom: 5px;
      position: relative;
      .nav-list-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          :first-child {
            font-weight: 400;
            font-size: 14px;
            color: #05082c;
          }
          :last-child {
            font-weight: 400;
            font-size: 14px;
            color: #ff436a;
            margin-left: 2px;
          }
        }
        .text-Input {
          width: 75%;
          height: 32px;
          margin-left: 5px;
        }
      }
      .nav-list-url {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          :first-child {
            font-weight: 400;
            font-size: 14px;
            color: #05082c;
          }
          :last-child {
            font-weight: 400;
            font-size: 14px;
            color: #ff436a;
            margin-left: 2px;
          }
        }
        .url-Input {
          width: 75%;
          height: 32px;
          border-radius: 3px;
          border: 1px solid #cccccc;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .url-Input-span1 {
            font-weight: 400;
            font-size: 14px;
            color: rgba(150, 149, 149, 0.8);
            margin-left: 8px;
          }
          .url-Input-span2 {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            margin-left: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          span {
            font-weight: 400;
            font-size: 14px;
            color: #636d7e;
            margin-left: 8px;
          }
          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }
      }
      .nav-list-shut {
        width: 16px;
        height: 16px;
        position: absolute;
        right: 5px;
        top: 5px;
        opacity: 0;
      }
    }
    .Input-nav-list:hover {
      background: #ffffff;
      box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12), 0px 4px 5px 0px rgba(0, 0, 0, 0.08),
        0px 1px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 8px 8px 8px 8px;
      .nav-list-shut {
        opacity: 1;
      }
    }
  }
  .navLinkage-footer {
    width: 304px;
    height: 40px;
    background: #ffffff;
    border-radius: 3px 3px 3px 3px;
    border: 1px dashed #1a7af8;
    margin-left: auto;
    margin-right: auto;
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .footer-image {
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 20px;
        height: 20px;
      }
      .navLinkage-addImage-tp {
        display: flex;
        align-items: center;
        margin-left: 12px;
        :first-child {
          font-weight: 400;
          font-size: 16px;
          color: #1a7af8;
        }
        :last-child {
          font-weight: 400;
          font-size: 16px;
          color: #a8d0ff;
        }
      }
    }
  }
}
