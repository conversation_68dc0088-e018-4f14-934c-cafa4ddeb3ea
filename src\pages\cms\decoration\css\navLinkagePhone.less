.navLinkAge-content {
  .navLinkAge-frame {
    margin-left: 8px;
    margin-right: 8px;
    .navLinkAge-top {
      width: 100%;
      .navLinkAge-top-ul {
        width: 100%;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
        overflow-x: auto;
        background: #ffffff;
        border-radius: 10px;
        li {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-grow: 1;
          overflow: hidden;    
          text-overflow: ellipsis;    
          white-space: nowrap;
          position: relative;
          height: 50px;
          .navLinkAge-top-line{
            font-size: 18px;
            color: #919c99;
            cursor: pointer;
            text-align: center;
            overflow: hidden;    
            text-overflow: ellipsis;    
            white-space: nowrap;
          }
          .navLinkAge-line{
            width: 30px;
            height: 5px;
            background: #01D47B;
            border-radius: 5px;
            margin-left: auto;
            margin-right: auto;
            position: absolute;
            bottom: 8px;
          }
          .navLinkAge-lines{
            width: 30px;
            height: 5px;
            border-radius: 5px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 5px;
          }
          .selectColor {
            color: #172119;
          }
        }
        li:hover {
          color: #cccccc !important;
        }
      }
    }
    .navLinkAge-bottom {
      width: 100%;
      margin-top: 10px;
      .navLinkAge-bottom-ul {
        display: flex;
        align-items: center;
        overflow-x: auto;
        position: sticky;
        top: 82px;
        scrollbar-width: none;
        scrollbar-color: transparent transparent;
        li {
          white-space: nowrap;
          margin-right: 7px;
          cursor: pointer;
          font-weight: 400;
          font-size: 14px;
          color: #616A66;
          line-height: 32px;
          padding-left: 20px;
          padding-right: 20px;
          border-radius: 5px;
          background: #ffffff;
        }
        li:last-child {
          margin-right: 0 !important;
        }
        .secondTypeBg {
          background: #E6FBF2 !important;
          color: #00A473 !important;
          border: 1px solid #01D47B;
        }
        li:hover {
          background: #E6FBF2 !important;
          color: #00A473 !important;
        }
      }
      .navLinkAge-bottom-ul::-webkit-scrollbar {
        display: none;
      }
    }
    .navLinkAge-Default {
      background: #ffffff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 10px;
      padding-bottom: 10px;
      img {
        width: 100px;
        height: 100px;
      }
    }
  }
  .navLinkAge-phone-content {
    padding-top: 10px;
  }
}
