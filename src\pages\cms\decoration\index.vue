<template>
  <div class="container">
    <topbar @back="goBack" />
    <div class="main flex">
      <leftbar :tool-config="toolConfig" />
      <phone />
      <rightbar />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onUnmounted, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import Topbar from "./components/Topbar.vue";
import Phone from "./components/Phone.vue";
import Leftbar from "./components/Leftbar.vue";
import Rightbar from "./components/Rightbar.vue";
import { computeToolConfig } from "./config/index";
import { DecorationInfo } from "./type";
import { getDecorationStore } from "@/store";
import router from "@/router";
import { getComponentDefaultValue } from "./config/defaultValue";
import { getDecorationDetail } from "@/api/decoration";

const route = useRoute();
const decorationStore = getDecorationStore();

// 左侧tool组件配置项
const toolConfig = ref<any[]>();

// 历史装修数据
const decorationInfo = ref<DecorationInfo>();
const getDecorationInfo = (id: string | string[]) => {
  getDecorationDetail({ id }).then((res) => {
    if (res.code === 0) {
      decorationInfo.value = res.data;
      if (
        decorationInfo.value.type === "PAGE" &&
        !decorationInfo.value.components.length
      ) {
        decorationInfo.value.components = getComponentDefaultValue(
          decorationInfo.value.scene
        ).list;
      }
      decorationInfo.value.components.forEach((item: any, index) => {
        item.flagId = item.templateId + index;
      });
      toolConfig.value = computeToolConfig(decorationInfo.value.scene);
      decorationStore.setDecorationInfo(decorationInfo.value);
    }
  });
};

onMounted(() => {
  const { id } = route.params;
  getDecorationInfo(id);
});

watch(route, (newVal) => {
  const { id } = newVal.params;
  getDecorationInfo(id);
});

onUnmounted(() => {
  decorationStore.handleReset();
});
// 返回
const goBack = () => {
  router.replace("/cms/index");
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  position: relative;

  .main {
    width: 100%;
    height: calc(100vh - 60px);
    min-height: 670px;
    background: #f1f3f8;
    padding-top: 16px;
    overflow-y: auto;
    align-items: flex-start;
    justify-content: space-between;
  }
}
</style>
