<template>
  <div class="local-life-container">
    <div
      style="
        padding: 32px;
        background-color: #fff;
        margin-top: 16px;
        border-radius: 16px;
        flex: 1;
      "
    >
      <a-table
        row-key="supplierId"
        :data-source="tableData"
        :columns="columns"
        :pagination="false"
        :loading="isLoading"
        class="mt16"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'pageStatus'">
            <template v-if="record.pageStatus === 'UNPUBLISHED'">
              <a-tag color="warning">未发布</a-tag>
            </template>
            <template v-if="record.pageStatus === 'PUBLISHED'">
              <a-tag color="default">已发布</a-tag>
            </template>
            <template v-if="record.pageStatus === 'SCHEDULED_PUBLISHED'">
              <a-tag color="processing">定时发布</a-tag>
              <div style="font-size: 12px; margin-top: 6px">
                {{ record.schedulePublishTime }}
              </div>
            </template>
          </template>
          <div v-if="column.key === 'operate'">
            <a-button
              type="link"
              class="btn-css"
              @click="handleEdit(e, record)"
            >
              装修
            </a-button>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { queryFixedPage } from "@/api/cms/decoration";

const router = useRouter();
const tableData = ref([]);
const isLoading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

const columns = [
  {
    title: "页面标题",
    dataIndex: "pageName",
    key: "pageName",
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
  },
  {
    title: "更新人",
    dataIndex: "updateUserName",
    key: "updateUserName",
    align: "left",
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
    key: "updateTime",
    align: "left",
  },
  {
    title: "页面状态",
    dataIndex: "pageStatus",
    key: "pageStatus",
    align: "left",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    align: "left",
  },
];

onMounted(() => {
  queryList();
});

const handleEdit = (e, row) => {
  router.push(`/cms/decorationV2/${row.id}`);
};

// 查表格数据
const queryList = (
  param = {
    current: 1,
    size: 10,
    pageSubTypeList: ["WECHAT_LOCAL_LIFE_PAGE","ALIPAY_LOCAL_LIFE_PAGE","APP_LOCAL_LIFE_PAGE"],
  }
) => {
  isLoading.value = true;
  queryFixedPage(param)
    .then((res) => {
      if (res.code === 0) {
        tableData.value = res.data.records;
        pagination.total = res.data.total;
      }
    })
    .finally(() => {
      isLoading.value = false;
    });
};
</script>
<style lang="less" scoped>
.mt16 {
  margin-top: 16px;
}

.local-life-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
