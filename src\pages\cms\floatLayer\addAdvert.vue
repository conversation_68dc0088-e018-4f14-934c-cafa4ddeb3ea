<template>
  <div class="config-form">
    <div class="search-form" style="height: 100%">
      <div class="flexl">
        <div class="line"></div>
        <div class="font">广告内容</div>
      </div>
      <div class="alarm-item">
        <a-form ref="formRef" :model="alarmData" layout="vertical">
          <a-form-item
            label="广告名称"
            name="advName"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-input
              v-model:value="alarmData.advName"
              show-count
              :maxlength="30"
              :disabled="isEdit"
              placeholder="请输入内容"
              style="width: 80%"
            ></a-input>
          </a-form-item>
          <a-form-item
            label="广告类型"
            name="advType"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.advType" :disabled="isEdit">
              <a-radio :value="0">浮框</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="广告图片"
            name="advImgUrl"
            :rules="[{ required: true, message: '必填项', trigger: 'change' }]"
          >
            <div style="float: left">
              <a-image
                v-if="alarmData.advImgUrl"
                :src="decodeURIComponent(alarmData.advImgUrl)"
                :width="100"
                :height="100"
                class="img-css"
              />
              <div class="upload-flex" @click="selectImg" v-if="!isEdit">
                <PlusOutlined class="icon-css" />
              </div>
            </div>
          </a-form-item>
          <a-form-item
            label="路径跳转"
            name="redirectPath"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.redirectPath" :disabled="isEdit">
              <a-radio :value="0">页面</a-radio>
              <a-radio :value="1">活动</a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- <a-input
            v-if="alarmData.redirectPath === 0"
            v-model:value="pageName"
            class="input-css"
            :disabled="isEdit"
            style="padding: 0 10px 0 0 !important"
            @click="drawerClick"
          >
            <template #prefix>
              <span class="span-css">链接</span>
            </template>
            <template #suffix> 🔗 </template>
          </a-input> -->
          <a-input
            v-model:value="alarmData.redirectUrl"
            placeholder="请选择链接"
            class="input-css"
            :disabled="isEdit"
            style="padding: 0 10px 0 0 !important"
            @click="drawerClick"
          >
            <template #prefix>
              <span class="span-css">链接</span>
            </template>
            <template #suffix>🔗</template>
          </a-input>
          <p style="color: red; margin-top: 20px" v-if="alarmData.redirectPath === 1">
            必须从营销活动系统中生成链接
          </p>
          <div class="flexl" style="margin-top: 50px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">
              投放规则
              <span class="rule-css">
                如若同时间段有多个同类型浮层广告展示，则根据广告时间开始倒序
              </span>
            </div>
          </div>

          <a-form-item
            label="投放时间"
            name="time"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-range-picker
              v-model:value="alarmData.time"
              allow-clear
              :disabled="isEdit"
              show-time
              style="width: 80%"
            />
          </a-form-item>
          <a-form-item
            label="投放时段"
            name="launchPeriod"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.launchPeriod" :disabled="isEdit">
              <a-radio :value="0">全天</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="推送人群"
            name="launchCrowd"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.launchCrowd" :disabled="isEdit">
              <a-radio :value="0">所有人群</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="应用页面"
            name="advApplicationPage"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.advApplicationPage" :disabled="isEdit">
              <a-radio :value="0">首页</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="投放端口"
            name="launchClient"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.launchClient" :disabled="isEdit">
              <a-radio :value="0">小程序</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="推送频次"
            name="launchFrequency"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <a-radio-group v-model:value="alarmData.launchFrequency" :disabled="isEdit">
              <a-radio :value="0">每次进入</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <div class="btn-box flex">
      <a-button class="mr10" @click="back">返回</a-button>
      <div class="flex">
        <a-button type="primary" @click="handleSubmit" v-if="!isEdit">保存</a-button>
      </div>
    </div>
  </div>
  
  <page-drawer
    v-if="isDrawer"
    @close-click="isDrawer = false"
    @select-ok="pageSelect"
  ></page-drawer>
  <pic-drawer
    v-if="isImgDrawer"
    @close-click="isImgDrawer = false"
    @select-ok="selectOk"
  ></pic-drawer>
</template>

<script lang="ts" setup>
  import { message } from 'woody-ui';
  import { onMounted, ref, reactive } from 'vue';
  import { addFloatAdd, getFloatDetail, addFloatEdit } from '@/api/cms/floatLayer/index';
  import { useRouter, useRoute } from 'vue-router';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import 'dayjs/locale/zh-cn';
  import pageDrawer from './components/pageDrawer.vue';
  import picDrawer from './components/picDrawer.vue';
  import 'dayjs/locale/zh-cn';
  const isEdit = ref(false);
  const isDrawer = ref(false);
  const isImgDrawer = ref(false);
  const pageName = ref('');
  const alarmData = ref({
    time: undefined,
    advName: '',
    advType: 0,
    advImgUrl: '',
    redirectPath: 0,
    redirectUrl: '',
    launchBeginTime: '',
    launchEndTime: '',
    launchPeriod: 0,
    launchCrowd: 0,
    advApplicationPage: 0,
    launchFrequency: 0,
    launchClient: 0,
  });
  const router = useRouter();
  const route = useRoute();

  const getData = async () => {
    const params = {
      id: route.query.id,
    };
    const res = await getFloatDetail(params);
    if (res.code === 0) {
      alarmData.value = res.data;
      alarmData.value.time = [dayjs(res.data.launchBeginTime), dayjs(res.data.launchEndTime)];
    }
  };

  const back = () => {
    router.back();
  };

  const pageSelect = data => {
    console.log(data, 'data123');
    // pageName.value = data.pageName;
    alarmData.value.redirectUrl = data.id;
    isDrawer.value = false;
  };

  const selectImg = () => {
    isImgDrawer.value = true;
  };
  const selectOk = data => {
    console.log(data, '获取图片');

    alarmData.value.advImgUrl = encodeURIComponent(data.url);
    isImgDrawer.value = false;
  };

  const drawerClick = () => {
    alarmData.value.redirectPath === 0 ? (isDrawer.value = true) : (isDrawer.value = false);
  };

  onMounted(() => {
    if (route.query.type === 'edit' || route.query.type === 'add') {
      isEdit.value = false;
    } else {
      isEdit.value = true;
    }
    if (route.query.id) getData();
  });

  //url校验
  const validateURL = url => {
    const wxUrlRegex = /^wx:\/\/[^\s]+$/;
    const httpUrlRegex = /^(https?:\/\/)(([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,})(:\d+)?(\/[^\s]*)?$/;
    return wxUrlRegex.test(url) || httpUrlRegex.test(url);
  };
  const formRef = ref();
  const handleSubmit = () => {
    const { time } = alarmData.value;
    console.log(alarmData.value, '提交数据');
    const params = {
      ...alarmData.value,
      launchBeginTime: time && time[0].format('YYYY-MM-DD HH:mm:ss'),
      launchEndTime: time && time[1].format('YYYY-MM-DD HH:mm:ss'),
    };
    delete params.time;
    console.log(params, '提交数据');
    formRef.value.validate().then(() => {
      if (alarmData.value.redirectPath === 1 && !validateURL(alarmData.value.redirectUrl)) {
        message.error('请输入正确的URL');
        return;
      }
      if (route.query.type === 'edit') {
        params['id'] = route.query.id;
        addFloatEdit(params).then(res => {
          if (res.code === 0) {
            isEdit.value = false;
            message.success('编辑成功');
            router.back();
          }
        });
      } else {
        addFloatAdd(params).then(res => {
          if (res.code === 0) {
            isEdit.value = false;
            message.success('保存成功');
            router.back();
          }
        });
      }
    });
  };
</script>

<style lang="less" scoped>
  @import url('@/style/plat.less');
  .flexl {
    display: flex;
    margin: 0 0 40px 0;
    .line {
      width: 4px;
      height: 17px;
      background: #1a7af8;
      border-radius: 4px 4px 4px 4px;
      margin: 2px 10px 0 0;
    }
    .font {
      font-weight: 600;
      font-size: 20px;
    }
  }
  .config-form {
    position: relative;
  }
  .title-box {
    font-weight: bold;
    font-size: 20px;
    color: #05082c;
    margin-bottom: 32px;
    .line {
      width: 4px;
      height: 16px;
      background: #1a7af8;
      border-radius: 4px 4px 4px 4px;
      margin-right: 12px;
    }
  }
  .title {
    font-size: 14px;
    color: #495366;
  }
  .alarm-item {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    width: 550px;
    .name {
      margin-bottom: 8px;
      font-size: 14px;
      color: #495366;
    }
    .value {
      width: 450px;
      margin-right: 10px;
    }
    .upload-flex {
      float: right;
      width: 100px;
      height: 100px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      margin-left: 10px;
      .icon-css {
        font-size: 20px;
      }
    }
  }
  .rule-css {
    margin-left: 8px;
    font-size: 14px;
    color: #495366;
    font-weight: 400;
  }
  .input-css {
    width: 80%;

    .span-css {
      background: #f2f5f9;
      line-height: 32px;
      padding: 0 10px;
      border-radius: 4px 0 0 4px;
    }
  }

  .btn-box {
    width: 100%;
    height: 56px;
    border-top: 1px solid #f2f5f9;
    background:#ffffff;
    justify-content: center;
  }
</style>
