<template>
  <a-drawer
    title="选择"
    :width="1050"
    prevent-scroll-through
    v-model:open="isImgDrawer"
    :close-on-overlay-click="true"
    :close-btn="true"
    @close="closeClick"
  >
    <select-images :isRadio="true" @radio-select="radioSelect"></select-images>
    <template #footer>
      <div class="drawer-footer">
        <a-button type="primary" @click="submitOk" class="mr10">确定</a-button>
        <a-button @click="closeClick">取消</a-button>
      </div>
    </template>
  </a-drawer>
</template>
<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import { basePage } from "@/api/cms/decoration/index";
import SelectImages from "@/components/SelectImages/index.vue";
const pageTabelData = ref([]);
const isImgDrawer = ref(true);
const selectedRow = ref(null); // 存储单选选中的行数据
const pageFrom = ref({
  pageName: "",
  pageStatus: "PUBLISHED",
});
const emits = defineEmits(["close-click","select-ok"]);
const pagePagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});
// 单选处理函数
const handleSelect = (record) => {
  selectedRow.value = record;
};
const closeClick = () => {
  isImgDrawer.value = false;
  emits('close-click')
};
const pageColumns = [
  {
    title: "",
    dataIndex: "radio",
    key: "radio",
    align: "left",
    width: 50,
  },
  {
    title: "标题",
    dataIndex: "pageName",
    key: "pageName",
    fixed: true,
    align: "left",
    width: 150,
  },
  {
    title: "类型",
    dataIndex: "pageTypeName",
    key: "pageTypeName",
    align: "left",
    width: 150,
  },
  {
    title: "模板",
    dataIndex: "pageSubTypeName",
    key: "pageSubTypeName",
    align: "left",
    width: 150,
  },
  {
    title: "状态",
    dataIndex: "pageStatus",
    key: "pageStatus",
    align: "left",
    width: 150,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 200,
  },
];

//重置
const resetOk = () => {
  pageFrom.value.pageName = "";
  pagePagination.value.pageSize = 10;
  pagePagination.value.current = 1;
  httpBasePage();
};
// 获取页面列表
const httpBasePage = async () => {
  //   isPageLoading.value = true;
  const { current, pageSize } = pagePagination.value;
  const params = { ...pageFrom.value, current, pageSize };
  const res = await basePage(params);
  pageTabelData.value = res.data.records;
  pagePagination.value.total = res.data.total;
};

// 分页变化
const pageChange = (newPagination) => {
  pagePagination.value = { ...pagePagination.value, ...newPagination };
  httpBasePage();
};

const radioSelect = (data) => {
  selectedRow.value = data;
};

const submitOk = () => {
  console.log(selectedRow.value, "selectedRow");
  emits("select-ok", selectedRow.value);
  // emits('close-click')
};
onMounted(() => {
  httpBasePage();
});
</script>
<style lang="less" scoped>
.drawer-footer {
  float: right;
}
</style>
