<template>
  <div v-if="!isDetail">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-css">
      <div class="btn-box">
        <a-button type="primary" @click="addClick" class="ml10"
          >新建广告</a-button
        >
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :scroll="{ x: 1000 }"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'advStatus'">
            <a-tag>
              {{ statusFunc(record) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'actions'">
            <a-button
              type="link"
              class="btn-css"
              @click="actionsClick('look', record)"
              >查看</a-button
            >
            <a-button
              v-if="record.advStatus === 0 || record.advStatus === 1"
              type="link"
              class="btn-css"
              @click="actionsClick('edit', record)"
              >编辑</a-button
            >
            <a-popconfirm
              ok-text="确定"
              cancel-text="取消"
              title="确定执行此操作？"
              class="btn-css"
              v-if="record.advStatus === 0 || record.advStatus === 1"
              @confirm="actionsClick('err', record)"
            >
              <a-button type="link" class="btn-css">失效</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <router-view />
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { getFloatPage, expireFloat } from "@/api/cms/floatLayer/index";

// import { getUserPage } from "@/api/activityCenter/platformFullReduction";
import { queryCategory } from "@/api/common";
import { useRouter } from "vue-router";
// import { fetchCategory } from "@/utils";
import { message } from "woody-ui";
import { getSysUserPage } from "@/api/cms/reception";
import { isEmptyValue } from "@/utils";
const addVisible = ref(false);
import type { FormInstance } from "woody-ui";
const formRef = ref<FormInstance>();
const router = useRouter();
const userOptions = ref();
const isLoading = ref(false);
const isDetail = ref(false);
const formState = reactive({
  categoryName: "",
  parentId: "",
});
const advStatus = ref([
  {
    value: 0,
    label: "未开始",
  },
  {
    value: 1,
    label: "进行中",
  },
  {
    value: 2,
    label: "已失效",
  },
  {
    value: 3,
    label: "已结束",
  },
]);
let formData = {};
// const remoteMethod = (search) => {
//   setTimeout(() => {
//     return getSysUserData(search);
//   }, 1000);
// };

const statusFunc = (record) => {
  if (record.advStatus === 0) {
    return "未开始";
  } else if (record.advStatus === 1) {
    return "进行中";
  } else if (record.advStatus === 2) {
    return "已失效";
  }else if (record.advStatus === 3) {
    return "已结束";
  }
};

// // 获取创建人列表数据
// const getSysUserData = (username) => {
//   return new Promise((resolve) => {
//     const params = {
//       page: 1,
//       size: 99999,
//     };
//     if (!isEmptyValue(username)) {
//       params["username"] = username;
//     }
//     getSysUserPage(params).then((res) => {
//       if (res.code === 0 && Array.isArray(res.data?.records)) {
//         userOptions.value = res.data.records;
//         resolve(res.data.records);
//       }
//     });
//   });
// };

// 获取创建人列表数据
const getSysUserData = (username) => {
  return new Promise((resolve) => {
    const params = {
      page: 1,
      size: 99999,
    };
    if (!isEmptyValue(username)) {
      params["username"] = username;
    }
    // formList[0][1].loading = true;
    getSysUserPage(params)
      .then((res) => {
        if (res.code === 0 && Array.isArray(res.data?.records)) {
          // formList[0][1].options = res.data.records;
          resolve(res.data.records);
        } else {
          // formList[0][1].options = [];
        }
      })
      .finally(() => {
        // formList[0][1].loading = false;
      });
  });
};
const formList = [
  {
    label: "广告名称",
    name: "advName",
    maxlength: 30,
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "广告状态",
    name: "advStatus",
    type: "select", // 输入框
    span: 6,
    options: advStatus,
  },
  // {
  //   label: "创建人",
  //   name: "createUserId",
  //   type: "select", // 输入框
  //   span: 6,
  //   lableKey: "userId",
  //   valueKey: "username",
  //   options: userOptions,
  // },
  {
    label: "创建人",
    name: "updateUserId",
    type: "select", // 下拉框
    placeholder: "请选择",
    // options: userOptions,
    searchFn: getSysUserData,
    showSearch: true,
    needFilter: true,
    labelKey: "username",
    valueKey: "userId",
    remoteMethod: getSysUserData,
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  if (param.time && param.time.length) {
    param.startTime = param.time[0] + " 00:00:00";
    param.endTime = param.time[1] + " 23:59:59";
  }
  formData = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

//table表头数据
const columns = [
  {
    title: "广告名称",
    dataIndex: "advName",
    key: "advName",
    fixed: "left",
    align: "left",
    width: 150,
  },
  {
    title: "广告状态",
    dataIndex: "advStatus",
    key: "advStatus",
    align: "left",
    width: 150,
  },
  {
    title: "投放时间段",
    dataIndex: "launchEndTime",
    key: "launchEndTime",
    align: "left",
    width: 300,
    customRender: ({ record }) => {
      return `${record.launchBeginTime} 至 ${record.launchEndTime}`;
    },
  },
  {
    title: "创建人",
    dataIndex: "createUserName",
    key: "createUserName",
    align: "left",
    width: 150,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    width: 180,
  },
];
const treeConfig = {
  childrenKey: "newCategoryModelDtos",
  treeNodeColumnIndex: 0,
  indent: 25,
  expandTreeNodeOnClick: true,
};
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const getList = async () => {
  loading.value = true;
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    ...formData,
  };
  const res = await getFloatPage(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const addClick = () => {
  router.push({
    path: "/cms/floatLayer/addAdvert",
    query: { type: "add" },
  });
};

// 操作回调
const actionsClick = async (type, data) => {
  if (type == "look" || type == "edit") {
    router.push({
      path: "/cms/floatLayer/addAdvert",
      query: { id: data.id, type },
    });
  } else if (type == "err") {
    const res = await expireFloat({
      id: data.id,
    });
    if (res.code === 0) {
      message.success("已失效");
      getList();
    }
  }
};

// 分页变化
// const onPaginationChange = (newPagination) => {
//   console.log(newPagination, "newPagination");
//   pagination.value = { ...pagination.value, ...newPagination };
//   getList();
// };

watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (newVal.path === "/cms/floatLayer/addAdvert") {
      isDetail.value = true;
    } else {
      isDetail.value = false;
    }
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  getSysUserData("");
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
