<template>
  <a-modal
    :visible="visible"
    :width="1000"
    title="选择页面"
    :btn-loading="btnLoading"
    :options="{ placement: 'center' }"
    @cancel="handleClose"
    @ok="handleConfirm"
  >
    <search-antd :formList="formList" @onSearch="handleSearch" />
    <a-table
      :data-source="tableData"
      :columns="columns"
      :pagination="pagination"
      height="calc(100vh - 500px)"
      :loading="loading"
      rowKey="id"
      class="mt20"
      @Change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'radio'">
            <a-radio 
              :checked="selectedRow?.id === record.id"
              @change="() => handleSelect(record)"
            />
          </template>
        <div v-if="column.key === 'pageStatus'">
          <a-tag v-if="record.pageStatus === 'UNPUBLISHED'" color="orange">
            未发布
          </a-tag>
          <a-tag v-else-if="record.pageStatus === 'PUBLISHED'"> 已发布 </a-tag>
          <a-tag
            v-else-if="record.pageStatus === 'SCHEDULED_PUBLISHED'"
            color="blue"
          >
            定时发布
            <div style="font-size: 12px; margin-top: 6px">
              {{ record.schedulePublishTime }}
            </div>
          </a-tag>
        </div>
        
      </template>
    </a-table>
  </a-modal>
</template>
<script lang="ts" setup>
import SearchAntd from "@/components/SearchAntd/index.vue";
import { reactive, ref, watch } from "vue";
import { basePage } from "@/api/cms/pageLayout/index";
import { isObj } from "@/utils";
import { message } from "woody-ui";
const selectedRow = ref(null); // 存储单选选中的行数据

import { bindingCmsConfigureBasePage } from "@/api/cms/cityZone/cityZone";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  supplierId: {
    type: [Number, String],
    default: "",
  },
});

const emits = defineEmits(["onClose"]);

const formList = [
  {
    type: "input",
    label: "标题",
    name: "pageName",
    span: 6,
  },
];

const columns = [
  // {
  //   colKey: "row-select",
  //   type: "single",
  // },
  {
    title: "",
    dataIndex: "radio",
    key: "radio",
    align: "left",
  },
  {
    title: "标题",
    dataIndex: "pageName",
    key: "pageName",
    align: "left",
  },
  {
    title: "类型",
    dataIndex: "pageTypeName",
    key: "pageTypeName",
    align: "left",
  },
  {
    title: "模板",
    dataIndex: "pageSubTypeName",
    key: "pageSubTypeName",
    align: "left",
  },
  {
    title: "状态",
    dataIndex: "pageStatus",
    key: "pageStatus",
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
  },
];

const loading = ref(false);
const btnLoading = ref(false);
const searchParams = ref({});
const selectedObj = ref({});
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});
const tableData = ref<Array<any>>([]);


// const selectedRowKeys = ref([]); // 列表复选框
// const rowSelection = {
//   onChange: (selectedRowKeys: string[], selectedRows: []) => {
//     isSelectRowKey.value = selectedRowKeys;
//   },
// };

// 单选处理函数
const handleSelect = (record) => {
  selectedRow.value = record;
};

// 列表复选框 事件
// const onSelectChange = (newSelectedRowKeys) => {
//   selectedRowKeys.value = newSelectedRowKeys;
//   // checkCount.value = newSelectedRowKeys.length;
// };
// const rowSelection = {
//   selectedRowKeys,
//   onChange: onSelectChange,
// };
// 获取数据
const getData = () => {
  const params = {
    current: pagination.current,
    pageSize: pagination.pageSize,
    pageSubTypeList: ["subPage"], // 只展示二级页面
    pageStatus: "PUBLISHED", // 只展示已发布的页面
    ...searchParams.value,
  };
  loading.value = true;
  basePage(params)
    .then((res) => {
      if (res.code === 0 && isObj(res.data)) {
        tableData.value = Array.isArray(res.data.records)
          ? res.data.records
          : [];
        pagination.total = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 搜索逻辑
const handleSearch = (formData) => {
  searchParams.value = formData;
  pagination.current = 1;
  getData();
};

// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  getData();
};

// 选中逻辑
const handleSelectChange = (value, { selectedRowData }) => {
  selectedObj.value = {
    supplierId: props.supplierId,
    basePageId: selectedRowData[0].id,
    basePageName: selectedRowData[0].pageName,
  };
};

// 关闭弹框
const handleClose = (isUpdate) => {
  selectedRow.value = null;
  emits("onClose", isUpdate);
};

// 确定逻辑
const handleConfirm = () => {
  console.log(selectedRow.value,'000')
  if (!selectedRow.value) {
    message.error("请选择页面");
    return;
  }
  btnLoading.value = true;
  const params = {
    supplierId: props.supplierId,
    basePageId: selectedRow.value.id,
    basePageName: selectedRow.value.pageName,
  };
  bindingCmsConfigureBasePage(params)
    .then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        handleClose(true);
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      getData();
    }
  }
);
</script>

<style lang="less" scoped>
.mt20 {
  margin-top: 20px;
}
.page-wrap {
  padding: 0;
}
</style>
