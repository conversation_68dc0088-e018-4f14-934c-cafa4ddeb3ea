<template>
  <div class="edit-local-zone-container">
    <search-antd :form-list="editFormList" @on-search="handleSearch" />
    <div
      style="
        padding: 32px;
        background-color: #fff;
        margin-top: 16px;
        border-radius: 16px;
      "
    >
      <div class="btn-container">
        <a-space>
          <a-button @click="limitFunc">下单限制</a-button>
          <a-button type="primary" @click="openAdd">添加分类 </a-button>
        </a-space>
      </div>
      <a-table
        row-key="id"
        :data-source="tableData"
        :columns="columns"
        :pagination="pagination"
        @change="handlePageChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operate'">
            <a-button type="link" @click="openAdd(e, record, 'view')"> 查看 </a-button>
            <a-button type="link" @click="openAdd(e, record, 'edit')"> 编辑 </a-button>
            <a-popconfirm
              ok-text="确定"
              cancel-text="取消"
              title="确定执行此操作？"
              @confirm="funcDelete(row)"
            >
              <a-button type="link">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="visible"
      title="下单限制"
      destroy-on-close
      @cancel="closeFunc"
      @ok="limitConfirm"
    >
      <a-form ref="form1" :model="formData1" layout="vertical" :rules="rules1">
        <a-form-item
          style="margin-bottom: 10px"
          label="同城专区最低订单金额配置"
          name="sameCityOrderLimit"
        >
          <a-input-number
            v-model:value="formData1.sameCityOrderLimit"
            style="width: 200px"
            :addon-after="'元'"
            placeholder="请输入金额"
          />
        </a-form-item>
        <div class="tip-info">用户订单最低金额不能低于设置金额。</div>
      </a-form>
    </a-modal>
    <a-drawer
      v-model:visible="drawerVisible"
      size="500px"
      placement="right"
      :hide-confirm="rowData.disabled"
      :confirm-disabled="rowData.disabled"
      :title="`${
        isEmptyValue(rowData.id) ? '添加' : rowData.disabled ? '查看' : '编辑'
      }分类`"
      @close="handleClosed"
    >
      <a-form ref="form" :model="formData" layout="vertical" :rules="rules">
        <a-form-item label="分类名称" name="categoryName">
          <a-input
            v-model:value="formData.categoryName"
            :disabled="rowData.disabled"
            :maxlength="30"
            show-count
            placeholder="请输入分类名称"
          />
        </a-form-item>
        <a-form-item label="同城排序" name="sort">
          <a-input-number
            v-model:value="formData.sort"
            :disabled="rowData.disabled"
            :min="0"
            :max="999"
            :allow-input-over-limit="false"
            theme="column"
            placeholder="请输入同城排序"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="绑定前台分类" name="feCategoryId">
          <a-select
            :options="categoryList"
            show-search
            show-arrow
            v-model:value="formData.feCategoryId"
            placeholder="请选择前台分类"
            :disabled="rowData.disabled"
            :fieldNames="{
              label: 'categoryName',
              value: 'id',
            }"
            allowClear
          >
          </a-select>
        </a-form-item>
        <div class="tip">只能选择双层前台分类</div>
      </a-form>
      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button style="margin-right: 8px" @click="handleClosed"
          >取消</a-button
        >
        <a-button type="primary" @click="handleConfirm">确定</a-button>
      </div>
    </a-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRoute } from "vue-router";
import { message } from "woody-ui";
import SearchAntd from "@/components/SearchAntd/index.vue";
import DrawerDialog from "@/components/DrawerDialog/index.vue";
import { isEmptyValue } from "@/utils";
import { editFormList } from "./const";
import {
  add,
  update,
  pageList,
  getList,
  deleteFunc,
  queryExpand,
  editOrderLimit,
} from "@/api/cms/cityZone/cityZone";

const route = useRoute();
const visible = ref(false);
const drawerVisible = ref(false);
const categoryName = ref();
const rowData = ref({});
const form1 = ref(null);
const form = ref(null);
const categoryList = ref([]);
const rules1 = {
  sameCityOrderLimit: [
    {
      required: true,
    },
  ],
};
const rules = {
  categoryName: [
    {
      required: true,
    },
  ],
  sort: [
    {
      required: true,
    },
  ],
  feCategoryId: [
    {
      required: true,
    },
  ],
};
const formData1 = reactive({
  sameCityOrderLimit: "",
});
const formData = reactive({
  categoryName: "",
  sort: "",
  feCategoryId: "",
});
const tableData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

const columns = [
  // {
  //   colKey: 'icon',
  //   title: '分类icon',
  //   width: 100,
  //   cell: (h, { row }) => <img class="icon-img" src="https://img.zsjming.com/2024/08/4b7760b379ef45df9d278738749aa8e0.png" alt="" />,
  // },
  {
    dataIndex: "categoryName",
    title: "分类名称",
  },
  {
    dataIndex: "feCategoryName",
    title: "前台分类",
  },
  {
    dataIndex: "createTime",
    title: "添加时间",
  },
  {
    dataIndex: "sort",
    title: "同城排序",
  },
  {
    dataIndex: "operate",
    title: "操作",
    className: "operation",
  },
];

onMounted(() => {
  queryTableList();
  quryTypeList();
});

// 表格数据
const queryTableList = (
  param = { page: pagination.current, size: pagination.pageSize }
) => {
  pageList({ ...param, supplierId: route.query.supplierId }).then((res) => {
    if (res.code === 0) {
      tableData.value = res.data.records;
      pagination.total = res.data.total;
    }
  });
};

// 获取一级分类数据
const quryTypeList = () => {
  getList().then((res) => {
    if (res.code === 0) {
      categoryList.value = res.data;
    }
  });
};

// 删除类别
const funcDelete = (param) => {
  deleteFunc({ id: param.id - 0 }).then((res) => {
    if (res.code === 0) {
      message.success("删除成功");
      queryTableList();
    }
  });
};
const handleSearch = (param) => {
  categoryName.value = param.categoryName;
  queryTableList({
    ...param,
    supplierId: route.query.supplierId,
    page: 1,
    size: 10,
  });
  pagination.current = 1;
  pagination.pageSize = 10;
};
//  下单限制
const limitFunc = () => {
  queryExpand({
    supplierId: route.query.supplierId,
  }).then((res) => {
    if (res.code === 0) {
      formData1.sameCityOrderLimit = res.data?.sameCityOrderLimit;
    }
  });
  visible.value = true;
};
// 关闭限制弹框
const closeFunc = () => {
  visible.value = false;
};
// 提交下单限制
const limitConfirm = () => {
  form1.value.validate().then(() => {
    editOrderLimit({
      sameCityOrderLimit: formData1.sameCityOrderLimit,
      supplierId: route.query.supplierId,
    }).then((res) => {
      if (res.code === 0) {
        message.success("设置成功！");
        visible.value = false;
        queryTableList();
      }
    });
  });
};
// 添加分类
const openAdd = (e, data, type) => {
  drawerVisible.value = true;
  if (data) {
    rowData.value = data;
    const { categoryName, sort, feCategoryId } = data;
    formData.categoryName = categoryName;
    formData.sort = sort;
    formData.feCategoryId = feCategoryId;
    if (type === "view") {
      rowData.value.disabled = true;
    } else {
      rowData.value.disabled = false;
    }
  }
};
// 抽屉确认逻辑
const handleConfirm = () => {
  form.value.validate().then(() => {
    let request;
    if (rowData.value.id) {
      request = update({
        supplierId: route.query.supplierId,
        id: rowData.value.id,
        ...formData,
      });
    } else {
      request = add({
        supplierId: route.query.supplierId,
        ...formData,
      });
    }
    request
      .then((res) => {
        if (res.code === 0) {
          message.success(`${rowData.value.id ? "编辑" : "添加"}成功`);
        }
      })
      .finally(() => {
        rowData.value = {};
        drawerVisible.value = false;
        form.value.resetFields();
        queryTableList();
      });
  });
};
// 取消逻辑
const handleClosed = () => {
  drawerVisible.value = false;
  rowData.value = {};
  form.value.resetFields();
};
// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  const obj = {
    page: pageInfo.current,
    size: pageInfo.pageSize,
  };
  if (categoryName.value) {
    obj.categoryName = categoryName.value;
  }
  queryTableList(obj);
};
</script>
<style lang="less" scoped>
.edit-local-zone-container {
  margin-top: 10px;
  min-height: 80vh;
  // background-color: #fff;
  :deep(.icon-img) {
    width: 52px;
    height: 52px;
  }
  .btn-container {
    height: 60px;
    line-height: 60px;
    text-align: right;
  }
  // .t-input-number {
  //   width: 50%;
  // }
  .tip-info {
    font-size: 12px;
    color: #636d7e;
    line-height: 20px;
    text-align: left;
  }
  .tip {
    font-size: 12px;
    line-height: 20px;
    // color: rgb(25, 144, 255);
    color: rgba(0, 0, 0, 0.5);
  }
  .t-form {
    width: 80%;
  }
  .t-input-number {
    width: 100%;
  }
  .btn-css {
    color: var(--td-brand-color);
  }
}
</style>
