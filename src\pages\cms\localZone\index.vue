<template>
  <div class="local-zone-container">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div
      style="
        padding: 32px;
        background-color: #fff;
        margin-top: 16px;
        border-radius: 16px;
        flex: 1;
      "
    >
      <a-table
        row-key="supplierId"
        :data-source="tableData"
        :columns="columns"
        :pagination="pagination"
        class="mt16"
        @change="handlePageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- <a-switch
            v-if="column.key === 'sameCityZone'"
            :value="record.sameCityZone ? 1 : 0"
            :custom-value="[1, 0]"
            @change="statusChange($event, record)"
          /> -->
              
          <a-switch
            v-if="column.key === 'sameCityZone'"
            :checked="switchValue(record)"
            :true-value="1"              
            :false-value="0"       
            @change="statusChange($event, record)"
          />
          <!-- <a-switch
              @change="statusChange(record)"
              :checked="row.sameCityZone === 1"
            /> -->
          <div v-if="column.key === 'operate'">
            <a-button type="link" class="btn-css" @click="handleEdit(e, record)">
              编辑专区
            </a-button>
            <a-button type="link" class="btn-css" @click="handleOpenEditPage(record.supplierId)"
              >配置页面</a-button
            >
          </div>
          
        </template>
        
      </a-table>
    </div>
    <edit-page
      :visible="editPageVisible"
      :supplier-id="supplierId"
      @onClose="handleCloseEditPage"
    />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { message } from "woody-ui";
import SearchAntd from "@/components/SearchAntd/index.vue";
import EditPage from "./EditPage.vue";
import {
  getSupplierPage,
  modifySameCityZone,
} from "@/api/cms/cityZone/cityZone";
import { formList } from "./const";

const router = useRouter();
const tableData = ref([]);
const shopName = ref();
const supplierId = ref("");
const editPageVisible = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

const columns = [
  {
    title: "供货仓名称",
    dataIndex: "shopName",
    key: "shopName",
    align: "left",
  },
  {
    title: "是否开启同城专区",
    dataIndex: "sameCityZone",
    key: "sameCityZone",
    align: "left",
  },
  {
    title: "店铺页面",
    dataIndex: "basePageName",
    key: "basePageName",
    align: "left",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    align: "left",
  }
];

onMounted(() => {
  queryList();
});

const handleEdit = (e, row) => {
  router.push({
    path: "/cms/editLocalZone",
    query: row,
  });
};
const handleSearch = (param) => {
  shopName.value = param.shopName;
  const obj = {
    current: 1,
    size: 10,
  };
  if (param.shopName) {
    obj.shopName = param.shopName;
  }
  queryList(obj);
  initPage();
};

// 查表格数据
const queryList = (param = { current: 1, size: 10 }) => {
  getSupplierPage(param).then((res) => {
    if (res.code === 0) {
      tableData.value = res.data.records;
      pagination.total = res.data.total;
    }
  });
};

const switchValue = (record)=> {
  // 将后端返回的 1 和 0 转换为布尔值
  return record.sameCityZone === 1;
};

// 切换是否同城状态
const statusChange = (status, record) => {
  // 将布尔值 (true/false) 转换为后端需要的值 (1/0)
  record.sameCityZone = status ? 1 : 0;
  console.log(status, record, "sameCityZone");
  const { supplierId,sameCityZone} = record;
  modifySameCityZone({
    supplierId,
    sameCityZone,
  }).then((res) => {
    if (res.code === 0) {
      const str = sameCityZone === 0 ? "关闭" : "开启";
      message.success(`已${str}`);
      queryList({
        current: pagination.current,
        size: pagination.pageSize,
      });
    }
  });
};

// 初始化分页
const initPage = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
};
// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  const obj = {
    current: pageInfo.current,
    size: pageInfo.pageSize,
  };
  if (shopName.value) {
    obj.shopName = shopName.value;
  }
  queryList(obj);
};

// 打开配置页面
const handleOpenEditPage = (id) => {
  supplierId.value = id;
  editPageVisible.value = true;
};

// 关闭配置页面弹框
const handleCloseEditPage = (isUpdate) => {
  editPageVisible.value = false;
  if (isUpdate) {
    pagination.current = 1;
    queryList();
  }
};
</script>
<style lang="less" scoped>
.mt16 {
  margin-top: 16px;
}

.local-zone-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// .btn-css {
//   color: var(--td-brand-color);
// }
</style>
