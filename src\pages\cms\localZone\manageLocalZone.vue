<template>
  <div class="manage-local-zone-container">
    <div class="top-info">
      <div>
        <div>供货仓名称</div>
        <div>比那多超市-苏州吴中</div>
      </div>
      <div>
        <div>分类名称</div>
        <div>休闲零食</div>
      </div>
    </div>
    <div style="background-color: #fff">
      <search-antd :form-list="editFormList" @on-search="handleSearch" />
      <a-table
        row-key="id"
        :data-source="tableData"
        :columns="columns"
        :pagination="pagination"
        @change="handlePageChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'information'">
            <div className="commodity-info_style">
              <div v-if="!isEmptyValue(record.commodityPic)" className="pic">
                <img :src="record.commodityPic" />
              </div>
              {{ record.commodityName }}
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive } from "vue";
// import { useRouter } from 'vue-router';
import SearchAntd from '@/components/SearchAntd/index.vue';
import { isEmptyValue } from '@/utils';
import { editFormList } from './const';

// const router = useRouter();
const tableData = ref([
  {
    info: "葡萄",
    price: "100",
    origin: "供货仓",
    brand: "巨峰",
    firstLevel: "食物",
    secondLevel: "食物",
    sort: 111,
    id: 1,
  },
  {
    info: "葡萄1",
    price: "1001",
    origin: "供货仓1",
    brand: "巨峰1",
    firstLevel: "食物1",
    secondLevel: "食物1",
    sort: 1110,
    id: 2,
  },
]);
const pagination = reactive({
  current: 1,
  pageSize: 5,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

const columns = [
  {
    dataIndex: "information",
    title: "商品信息",
    // cell: (h, { row }) => (
    //   <div class="info-container">
    //     <span>
    //       <img class="icon-img" src="https://img.zsjming.com/2024/08/4b7760b379ef45df9d278738749aa8e0.png" alt="" />
    //     </span>
    //     <span>{row.info}</span>
    //   </div>
    // ),
  },
  {
    dataIndex: "price",
    title: "销售价",
  },
  {
    dataIndex: "origin",
    title: "商品来源",
  },
  {
    dataIndex: "brand",
    title: "商品品牌",
  },
  {
    dataIndex: "firstLevel",
    title: "一级分类",
  },
  {
    dataIndex: "secondLevel",
    title: "二级分类",
  },
  {
    dataIndex: "sort",
    title: "同城排序",
  },
];

const handleSearch = () => {};
// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};
</script>
<style lang="less" scoped>
.manage-local-zone-container {
  margin-top: 10px;
  min-height: 80vh;
  // background-color: #fff;
  :deep(.info-container) {
    display: flex;
    align-items: center;
    > span:first-child {
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      // background-color: #d9d9d9;
      border-radius: 8px;
      margin-right: 5px;
    }
  }
  :deep(.icon-img) {
    width: 52px;
    height: 52px;
  }
  .top-info {
    height: 116px;
    display: flex;
    padding: 32px;
    padding-bottom: 24px;
    background-color: #fff;
    margin-bottom: 10px;
    border-radius: 8px;
    > div {
      width: 220px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-size: 14px;
      > div:first-child {
        color: #636d7e;
      }
    }
  }
}
</style>
