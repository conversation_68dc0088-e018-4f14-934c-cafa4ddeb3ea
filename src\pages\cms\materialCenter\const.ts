const formInfo = [
  {
    belong: 'ADD',
    type: 'radio',
    name: 'level',
    label: '分组类型',
    options: [
      { label: '一级', value: 1 },
      { label: '二级', value: 2 },
    ],
    relateItem: ['parentId'],
  },
  {
    belong: 'ADD',
    type: 'select',
    name: 'parentId',
    label: '所在分组',
    options: [],
    hide: true,
  },
  {
    belong: 'COMMON',
    type: 'treeSelect',
    name: 'ownerGroupId',
    label: '所在分组',
    options: [],
  },
  {
    belong: 'EDIT',
    type: 'input',
    name: 'name',
    label: '图片标题',
    maxLength: 100,
  },
  {
    belong: 'UPLOAD',
    type: 'upload',
    name: 'fileList',
    label: '本地图片',
    extra: '支持.jpg,.jpeg,.png,.gif格式，上传图片宽度限制为750px，大小限制1M，最多一次上传10张',
  },
  {
    belong: 'ADD',
    type: 'input',
    name: 'name',
    label: '分组名称',
    maxLength: 10,
    showLimit: true,
  },
];

const formType = {
  COMMON: { ownerGroupId: '0' },
  EDIT: { name: '' },
  UPLOAD: { fileList: '' },
  ADD: {
    parentId: '',
    level: '',
    name: '',
  },
};

const rulesInfo = {
  COMMON: {
    ownerGroupId: [
      {
        required: true,
      },
    ],
  },
  EDIT: {
    name: [
      {
        required: true,
      },
    ],
  },
  UPLOAD: {
    fileList: [
      {
        required: true,
      },
    ],
  },
  ADD: {
    parentId: [
      {
        required: true,
      },
    ],
    level: [
      {
        required: true,
      },
    ],
    name: [
      {
        required: true,
      },
    ],
  },
};

export { formInfo, formType, rulesInfo };
