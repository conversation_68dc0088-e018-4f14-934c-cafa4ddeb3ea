<template>
  <div class="material-center-container">
    <div class="left-part">
      <div class="title">
        <div>文件目录</div>
        <div>
          <span @click="showModal('ADD')">
            <plus-outlined style="margin-right: 3px" />
            <span>分组</span>
          </span>
        </div>
      </div>
      <div class="tree-container">
        <div :class="['un-group', ownerGroupId === '0' ? 'active' : '']" @click="treeClick({ value: '0' })">
          <div>未分组</div>
          <div>{{ countInfo }}</div>
        </div>
        <div class="real-tree">
          <a-tree
            :tree-data="convertedTreeData"
            :field-names="{ title: 'title', key: 'key', children: 'children' }"
            :default-expanded-keys="[ownerGroupId]"
            :selected-keys="[ownerGroupId]"
            @select="onSelect"
          >
            <template #title="{ title, dataRef }">
              <span>
                <span v-if="isEdit.isEdit && isEdit.id === dataRef.key" class="edit-content">
                  <a-input v-model:value="currentNode" maxlength="10" />
                  <span class="check ml10" @click="save(dataRef)">
                    <check-outlined />
                  </span>
                </span>
                <span v-else class="label-info flex" @click="treeClick(dataRef)">
                  <span>{{ dataRef.name }}</span>
                  <span>{{ dataRef.fileCnt }}</span>
                  <span class="edit-icon" @click.stop="showInput(dataRef)">
                    <edit-outlined />
                  </span>
                </span>
              </span>
            </template>
          </a-tree>
        </div>
      </div>
    </div>
    <div class="right-part">
      <div class="title">
        <div>
          <a-checkbox
            v-model:checked="checkedInfo.checkAll"
            :indeterminate="checkedInfo.indeterminate"
            :on-change="handleSelectAll"
            >已选{{ checkedInfo.count }}项</a-checkbox
          >
        </div>
        <div class="upload-search">
          <a-button
            class="mr10"
            type="primary"
            @click="showModal('UPLOAD')"
          ><upload-outlined></upload-outlined>上传图片</a-button>
          <a-input-search
            v-model:value="materialName"
            placeholder="请输入图片名称"
            style="width: 300px"
            @search="searchPic"
          />
        </div>
      </div>
      <div class="operate-group">
        <span @click="showModal('MODIFY')">修改分组</span>
        <!-- <span @click="showModal($event, 'DELETE')">删除</span> -->
        <span @click="download">批量下载</span>
      </div>
      <div v-if="picList.length" class="pic-container">
        <a-checkbox-group v-model:checked="checkedInfo.checkedList" @change="checkedListChange">
          <div v-for="(item, index) in picList" :key="index" class="pic-list">
            <div class="img-container">
              <img class="logo" :src="item.url" alt="logo" />
              <div class="bg-hover">
                <span @click="showModal('EDIT', item)">编辑</span>
                <!-- <span @click="showModal($event, 'DELETE')">删除</span> -->
              </div>
            </div>
            <div style="margin-top: 12px; width: 100%">
              <a-checkbox :key="item.url" :value="item.id" :title="item.name">{{ item.name }}</a-checkbox>
            </div>
          </div>
        </a-checkbox-group>
      </div>
      <div v-else style="margin-top: 100px; text-align: center" class="pic-container">
        <no-data />
      </div>
      <div class="page-container">
        <a-pagination
          v-model="pageInfo.current"
          :total="pageInfo.total"
          :page-size="pageInfo.pageSize"
          show-jumper
          @change="pageChange"
        />
      </div>
    </div>
    <modalComp
      :modal-info="modalInfo"
      :group-option="treeData"
      @confirm="confirmFunc"
      @cancel="cancelFunc"
      @query-group-func="queryGroupFunc"
    />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'woody-ui';
import modalComp from './modalComp.vue';
import noData from '@/components/Nodata/index.vue';
import { queryGroup, queryByPage, countQuery, saveGroup } from '@/api/cms/materialCenter/materialCenter';
import { downImg } from '@/utils/zip';
import { UploadOutlined,PlusOutlined, CheckOutlined, EditOutlined } from '@ant-design/icons-vue';
const route = useRoute();
const currentNode = ref();
const materialName = ref('');
const treeData = ref([]);
const ownerGroupId = ref('0');
const countInfo = ref();
const picList = ref([]);
const isEdit = reactive({
  isEdit: false,
  id: 0,
});
const pageInfo = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});
const checkedInfo = reactive({
  checkAll: false,
  indeterminate: false,
  count: 0,
  checkedList: [],
});
const modalInfo = reactive({
  type: '',
  showModal: false,
  checkList: [],
  formInfo: {},
});

onMounted(() => {
  queryGroupFunc();
  querCount();
  queryMaterial();
  if (route.query.modalType) {
    showModal(route.query.modalType);
  }
});

// 查询分组数据
const queryGroupFunc = () => {
  queryGroup({ flatFlag: false }).then((res) => {
    if (res.code === 0) {
      treeData.value = res.data || [];
    }
  });
};

const convertedTreeData = computed(() => {
  const mapTree = (nodes) =>
    nodes.map((item) => ({
      title: item.name,
      key: item.id,
      name: item.name,
      fileCnt: item.materialCnt,
      parentId:item.parentId,
      children: item.children ? mapTree(item.children) : [],
    }));
  return mapTree(treeData.value || []);
});

// 查询未分组数量
const querCount = () => {
  countQuery({ groupId: 0 }).then((res) => {
    if (res.code === 0) {
      countInfo.value = res.data;
    }
  });
};

//  点击当前树
const treeClick = (node) => {
  console.log(node,'node789')
  ownerGroupId.value = node.value;
  queryMaterial({
    ownerGroupId: node.value,
    page: 1,
    size: 10,
  });
  initPage();
};

const onSelect = (data) => {
  console.log(data,'data')
  queryMaterial({
    ownerGroupId: data.length ? data[0] : '',
    page: 1,
    size: 10,
  });
};

// 查询当前素材
const queryMaterial = (param) => {
  const prams = param || { ownerGroupId: 0, page: 1, size: 10 };
  queryByPage(prams).then((res) => {
    if (res.code === 0) {
      picList.value = res.data.records;
      pageInfo.total = res.data.total;
    }
  });
};
// 全选
const handleSelectAll = (e) => {
  checkedInfo.checkAll = !checkedInfo.checkAll;
  if (e) {
    checkedInfo.count = picList.value.length;
    const arr = [];
    picList.value.forEach((item) => {
      arr.push(item.id);
    });
    checkedInfo.checkedList = arr;
    checkedInfo.indeterminate = false;
  } else {
    checkedInfo.count = 0;
    checkedInfo.checkedList = [];
  }
};
// 单独勾选
const checkedListChange = (e) => {
  checkedInfo.count = e.length;
  checkedInfo.checkedList = e;
  if (e.length > 0) {
    if (e.length === picList.value.length) {
      checkedInfo.checkAll = true;
      checkedInfo.indeterminate = false;
    } else {
      checkedInfo.checkAll = false;
      checkedInfo.indeterminate = true;
    }
  } else {
    checkedInfo.checkAll = false;
    checkedInfo.indeterminate = false;
  }
};
// 编辑树
const showInput = (node) => {
  isEdit.isEdit = true;
  isEdit.id = node.key;
  currentNode.value = node.title;
};
// 保存修改
const save = (node) => {
  console.log(node,'node67')
  // 保存分组名称修改
  saveGroup({
    id: node.key,
    name: currentNode.value,
    parentId: node.children && node.children.length > 0 ? '0' : node.parentId ,
    level: node.children && node.children.length > 0 ? 1 : 2,
  }).then((res) => {
    if (res.code === 0) {
      message.success('保存成功！');
      queryGroupFunc();
    }
  });
  isEdit.isEdit = false;
  isEdit.id = 0;
};

// 批量下载
const download = () => {
  if (checkedInfo.checkedList.length === 0) {
    message.warning('请先选择素材！');
    return;
  }
  const url = [];
  picList.value.map((item) => {
    if (checkedInfo.checkedList.includes(item.id)) {
      url.push({
        url: item.url,
        title: item.name,
      });
    }
  });
  downImg(url, () => {
    message.success('下载完成');
    initCheck();
  });
};

// 弹框
const showModal = (type, formInfo) => {
  if (type === 'MODIFY' && checkedInfo.checkedList.length === 0) {
    message.warning('请先选择素材！');
    return;
  }
  modalInfo.showModal = true;
  modalInfo.type = type;
  modalInfo.checkList = checkedInfo.checkedList;
  if (formInfo) {
    modalInfo.formInfo = formInfo;
  } else {
    modalInfo.formInfo = {};
  }
};
// 隐藏弹框
const cancelFunc = () => {
  modalInfo.showModal = false;
  modalInfo.type = '';
};
// 确认弹框
const confirmFunc = () => {
  modalInfo.showModal = false;
  modalInfo.type = '';
  queryGroupFunc();
  querCount();
  queryMaterial({
    ownerGroupId: ownerGroupId.value,
    page: 1,
    size: 10,
  });
  initPage();
  initCheck();
};
const initCheck = () => {
  checkedInfo.checkedList = [];
  checkedInfo.indeterminate = false;
  checkedInfo.checkAll = false;
  checkedInfo.count = 0;
};
// 分页切换
const pageChange = (current, pageSize) => {
  pageInfo.current = current;
  pageInfo.pageSize = pageSize;
  queryMaterial({
    materialName: materialName.value,
    ownerGroupId: ownerGroupId.value,
    page: current,
    size: pageSize,
  });
  initCheck();
};

// 图片搜索
const searchPic = () => {
  queryMaterial({
    materialName: materialName.value,
    ownerGroupId: ownerGroupId.value,
    page: 1,
    size: 10,
  });
  initPage();
};

// 初始化分页数据
const initPage = () => {
  pageInfo.current = 1;
  pageInfo.pageSize = 10;
};
</script>

<style lang="less" scoped>
@import url('./style.less');
:deep(.ant-tree .ant-tree-node-content-wrapper){
    width:150px;
  }
.label-info{
  width:100%;
  
}
</style>
