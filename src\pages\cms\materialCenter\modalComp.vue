<template>
  <div
    :class="
      modalType === 'DELETE'
        ? 'dialog-special dialog-container'
        : 'dialog-container'
    "
  >
    <a-modal
      v-model:visible="modalInfo.showModal"
      :title="Title[modalType]"
      @cancel="closeFunc"
      @ok="onConfirm"
    >
      <div class="content-container">
        <div v-if="modalType === 'DELETE'" class="delete-desc">
          删除图片不会对已使用图片的业务造成影响。
        </div>
        <div v-else>
          <a-form ref="form" :model="formData" layout="vertical" :rules="rules">
            <template v-for="(item, index) in formList" :key="index">
              <a-form-item
                v-if="
                  (item.belong === modalType ||
                    (item.belong === 'COMMON' && modalType !== 'ADD')) &&
                  !item.hide
                "
                :label="item.label"
                :name="item.name"
              >
                <a-select
                  v-if="item.type === 'select'"
                  v-model:value="formData[item.name]"
                  :options="item.options"
                  clearable
                ></a-select>
                <a-tree-select
                  v-if="item.type === 'treeSelect'"
                  v-model:value="formData[item.name]"
                  :tree-data="item.options"
                  :tree-default-expand-all="true"
                  allow-clear
                  :field-names="{ label: 'name', value: 'id', children: 'children' }"
                  :tree-props="treeProps"
                  style="width:78%;float:left"
                />
                <a-button
                  type="primary"
                  v-if="item.type === 'treeSelect' && modalType === 'UPLOAD'"
                  @click="addFunc"
                  style="float:right"
                >
                  添加分组
                </a-button>
                <a-input
                  v-if="item.type === 'input'"
                  v-model:value="formData[item.name]"
                  :maxlength="item.maxLength || 20"
                  :show-count="item.showLimit"
                  placeholder="请输入内容"
                />
                <a-radio-group
                  v-if="item.type === 'radio'"
                  v-model:value="formData[item.name]"
                  :options="item.options"
                  @change="radioChange($event, item.relateItem)"
                />
                <!-- <upload-view
                    v-if="item.type === 'upload'"
                    ref="uploadRef"
                    :afferent-url="formData[item.name]"
                    form-data-key="fileList"
                    pattern="array"
                    max-width="750"
                    :options="{
                      disabled: false,
                      tips: item.extra,
                      accept: 'image/jpg, image/jpeg, image/png, image/gif',
                      max: 10,
                      sizeLimit: { size: 1.5, unit: 'MB', message: '图片大小不超过 1.5 MB' },
                    }"
                    biz-type="cms"
                    @on-afferent-url-change="afferentUrlChange"
                  /> -->
                <div class="upload-flex" v-if="item.type === 'upload'">
                  <wd-upload
                    biz-type="in_coming"
                    :file-size="1"
                    :max-width="750"
                    :max-count="10"
                    multiple
                    @get-url-list="afferentUrlChange"
                  />
                  <p>
                    支持.jpg,.jpeg,.png,.gif格式，上传图片宽度限制为750px，大小限制1M，最多一次上传10张
                  </p>
                </div>
              </a-form-item>
            </template>
          </a-form>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { message } from "woody-ui";
import { Title } from "./type";
import WdUpload from "@/components/WdUpload/index.vue";

import { formInfo, rulesInfo, formType } from "./const";
import {
  modifyGroup,
  saveMaterial,
  saveGroup,
} from "@/api/cms/materialCenter/materialCenter";

const treeProps = {
  keys: { label: "name", value: "id", children: "children" },
};
const props = defineProps({
  modalInfo: {
    type: Object,
  },
  groupOption: {
    type: Array,
  },
});
const emits = defineEmits(["confirm", "cancel", "queryGroupFunc"]);
const accept = ref(".png,.gif,.jpg");
const form = ref(null);

const modalType = ref();
const fromModal = ref();
const uploadRef = ref(null);
const formList = ref<any>(formInfo);
let rules = {
  ...rulesInfo[props.modalInfo.type],
  ...rulesInfo.COMMON,
};
const formData = ref({
  ...formType.COMMON,
  ...formType[props.modalInfo.type],
});
const picInfoList = ref([]);

const getUrlList = (urlList) => {};

watch(props.modalInfo, (value) => {
  modalType.value = value.type;
  if (value.type) {
    rules = {
      ...rulesInfo[value.type],
      ...rulesInfo.COMMON,
    };
  }
  if (value.type === "ADD") {
    formData.value = {};
    formData.value.level = 1;
    const obj = formInfo.find((item) => item.name === "level");
    radioChange(1, obj.relateItem);
  } else {
    formList.value = formInfo;
  }
  if (
    props.modalInfo.formInfo &&
    Object.keys(props.modalInfo.formInfo).length
  ) {
    const { name, ownerGroupId } = props.modalInfo.formInfo;
    formData.value.name = name;
    if (ownerGroupId && ownerGroupId !== "0") {
      formData.value.ownerGroupId = ownerGroupId;
    }
    if (value.type === "EDIT") {
      const { id, name, url } = props.modalInfo.formInfo;
      picInfoList.value = [
        {
          materialId: id,
          name,
          url,
        },
      ];
    }
  }
});

watch(
  () => props.groupOption,
  (value) => {
    if (value.length) {
      formInfo.forEach((item) => {
        if (item.name === "parentId" || item.name === "ownerGroupId") {
          if (item.belong === "ADD") {
            const arr = [];
            value.forEach((items: any) => {
              arr.push({
                label: items.name,
                value: items.id,
              });
            });
            item.options = arr;
          } else {
            item.options = [
              {
                id: "0",
                level: 1,
                name: "未分组",
                parentId: "0",
                children: null,
              },
              ...value,
            ];
            formData.value.ownerGroupId = "0";
          }
        }
      });
      formList.value = formInfo;
    }
  }
);

// 分类切换
const radioChange = (e, list) => {
  if (list) {
    const arr = JSON.parse(JSON.stringify(formInfo));
    arr.forEach((item) => {
      const curr: number = list.findIndex((items) => items === item.name);
      if (curr !== -1) {
        item.hide = e === 1;
      }
    });
    formList.value = arr;
  }
};

const onConfirm = () => {
  if (formData.value.level === 1) {
    formData.value.parentId = null;
  }

  if (form.value) {
    form.value.validate().then(() => {
        modalType.value = props.modalInfo.type;
        if (fromModal.value) {
          rules = {
            ...rulesInfo[props.modalInfo.type],
            ...rulesInfo.COMMON,
          };
          saveGroup({
            ...formData.value,
          }).then((res) => {
            if (res.code === 0) {
              message.success("新增成功！");
              emits("queryGroupFunc");
              form.value?.resetFields();
            }
          });
          fromModal.value = false;
        } else {
          const obj = {
            UPLOAD: {
              msg: "上传",
              callback: () => {
                const obj: any = {
                  materialList: picInfoList.value,
                };
                if (
                  formData.value.ownerGroupId &&
                  formData.value.ownerGroupId !== "0"
                ) {
                  obj.ownerGroupId = formData.value.ownerGroupId;
                }
                return saveMaterial(obj);
              },
            },
            DELETE: "",
            MODIFY: {
              msg: "修改",
              callback: () =>
                modifyGroup({
                  materialIdList: props.modalInfo.checkList,
                  ...formData.value,
                }),
            },
            ADD: {
              msg: "新增",
              callback: () => saveGroup({ ...formData.value }),
            },
            EDIT: {
              msg: "修改",
              callback: () => {
                const obj = picInfoList.value[0];
                const list = [
                  {
                    ...obj,
                    name: formData.value.name,
                  },
                ];
                const info: any = {
                  materialList: list,
                };
                if (formData.value.ownerGroupId !== "0") {
                  info.ownerGroupId = formData.value.ownerGroupId;
                } else if (props.modalInfo.formInfo.ownerGroupId !== "0") {
                  message.error("素材分组未发现");
                  return Promise.reject();
                }
                return saveMaterial(info);
              },
            },
          };
          obj[props.modalInfo.type].callback().then((res) => {
            if (res.code === 0) {
              message.success(`${obj[props.modalInfo.type].msg}成功！`);
              form.value?.resetFields();
              formData.value = {
                ...formType.COMMON,
                ...formType[props.modalInfo.type],
              };
              emits("confirm");
            }
          });
        }
    });
  }
};

const closeFunc = () => {
  if (fromModal.value) {
    modalType.value = props.modalInfo.type;
    rules = {
      ...rulesInfo[props.modalInfo.type],
      ...rulesInfo.COMMON,
    };
    fromModal.value = false;
  } else {
    form.value?.resetFields();
    picInfoList.value = [];
    formData.value = {
      ...formType.COMMON,
      ...formType[props.modalInfo.type],
    };
    emits("cancel");
  }
};
// 添加分组
const addFunc = () => {
  rules = {
    ...rulesInfo.ADD,
    ...rulesInfo.COMMON,
  };
  form.value?.resetFields();
  fromModal.value = true;
  modalType.value = "ADD";
  formData.value.level = 1;
  const obj = formInfo.find((item) => item.name === "level");
  radioChange(1, obj.relateItem);
};

// 图片上传
const afferentUrlChange = (list) => {
  formData.value.fileList = list;
  picInfoList.value = list;
};
</script>
<style lang="less" scoped>
.dialog-container {
  :deep(.t-dialog__header) {
    height: 73px;
    padding: 10px 32px 0;
  }

  :deep(.t-dialog__body__icon) {
    padding-top: 0;
  }

  .content-container {
    .delete-desc {
      font-weight: 400;
      font-size: 14px;
      color: #495366;
    }
  }
  // .add-btn {
  //   width: 75px;
  //   font-size: 14px;
  //   color: #1a7af8;
  //   margin-left: 20px!important;
  //   cursor: pointer;
  // }
}

.dialog-special {
  :deep(.t-dialog__header) {
    border: none;
  }
}
</style>
