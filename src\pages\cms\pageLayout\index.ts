export const FROM_DATA = {
  pageName: null,
  pageType: null,
  pageSubTypeList: null,
  pageStatus: null,
  createUserId: null,
  createTime: null,
  updateTime: null,
};

export const ORDER_PAGETION = {
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
};

export const BASE_PAGE = {
  pageName: null,
  pageType: 'PAGE',
  pageSubType: 'SUB_PAGE',
  createUserId: null,
};

export const ORDER_COLUMNS = [
  {
    title: '页面标题',
    dataIndex: "pageName",
    key: "pageName",
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 146,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 146,
  },
  {
    title: '类型',
    dataIndex: 'pageTypeName',
    key: 'pageTypeName',
  },
  {
    title: '模板',
    dataIndex: 'pageSubTypeName',
    key: 'pageSubTypeName',
    // cell: (h, { row }) => {
    // return h(Tag, { theme: 'success', variant: 'light' }, row.pageSubTypeName);
    // },
  },
  {
    title: '页面状态',
    dataIndex: 'pageStatus',
    key: 'pageStatus',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 200,
    fixed: 'right',
  },
];
