<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <page-wrap class="mt16">
    <div class="btn-page">
      <a-button type="primary" @click="handleAdd"> 新建任务 </a-button>
    </div>
    <a-table
      row-key="index"
      :dataSource="tableData"
      :columns="columns"
      :pagination="pagination"
      class="mt24"
      @change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <div
          v-if="column.key === 'taskStatus'"
          :class="[
            'status-page',
            {
              success: record.taskStatus === 1,
              filled: record.taskStatus === 2,
            },
          ]"
        >
          <span />
          {{
            record.taskStatus === 0
              ? "执行中"
              : record.taskStatus === 1
              ? "已完成"
              : "失败"
          }}
        </div>
        <div v-if="column.key === 'operate'">
          <a-button
            type="link"
            class="btn-css"
            v-if="record.taskStatus !== 0"
            :download="true"
            :href="record.fileUrl"
          >
            下载结果
          </a-button>
        </div>
      </template>
    </a-table>
  </page-wrap>
  <a-modal
    v-model:open="addVisible"
    title="新建任务"
    :options="{ placement: 'center' }"
    :is-btn-disable="isBtnDisable"
    :btn-loading="btnLoading"
    @cancel="handleClose"
    @ok="handleConfirm"
  >
    <a-form
      ref="formRef"
      :model="addFormData"
      layout="vertical"
      :rules="addFormRules"
    >
      <a-form-item label="任务名称" name="taskName">
        <a-input
          v-model:value="addFormData.taskName"
          :maxlength="30"
          show-count
          placeholder="请输入分类名称"
        />
      </a-form-item>
      <a-form-item label="模板下载">
        <a-button :href="VITE_DOWNLOAD_CMS_BIND_PROD"> 下载模板 </a-button>
      </a-form-item>
      <a-form-item v-if="addVisible" label="任务文件">
        <div class="flex">
          <a-input
            v-model:value="addFormData.filesName"
            clearable
            placeholder=""
            style="width: 80%"
            class="mr10"
          />
          <!-- <upload @file-change="handleUploadChange" :accept="accept" :fileSize="2" style="width: 20%"/> -->
          <upload
            accept=".xlsx"
            biz-type="retail_goods"
            resource-type="file"
            :file-list="legalBankFileList"
            :max-count="1"
            @get-url-list="handleUploadChange"
          />
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { onMounted, ref } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import AddCircle from "@/assets/add-circle.svg";
import TableListAntd from "@/components/TableListAntd/index.vue";
// import DialogPage from "@/components/DialogPage/index.vue";
import { VITE_DOWNLOAD_CMS_BIND_PROD } from "@/config/global";
import upload from "@/components/Upload/index.vue";

import {
  formList,
  legalBankFileList,
  tableData,
  columns,
  pagination,
  addVisible,
  isBtnDisable,
  formRef,
  loading,
  btnLoading,
  addFormData,
  addFormRules,
  initData,
  handleSearch,
  getData,
  handleAdd,
  handlePageChange,
  handleClose,
  handleConfirm,
  handleUploadChange,
} from "./setData";

const accept = ref(".xlsx, .xls");

// 上传回调
// const fileChange = (data) => {
//   addFormData.files = data
// };

onMounted(() => {
  initData();
  getData();
});
</script>

<style lang="less" scoped>
.mt8 {
  margin-top: 8px;
}
.mt16 {
  margin-top: 16px;
  flex: 1;
}
.mt24 {
  margin-top: 24px;
}
.btn-page {
  display: flex;
  flex-direction: row-reverse;
  svg {
    margin-right: 3px;
  }
}
.status-page {
  display: flex;
  align-items: center;
  &.success {
    color: #1bb599;
    span {
      background-color: #1bb599;
    }
  }
  &.filled {
    color: #ff436a;
    span {
      background-color: #ff436a;
    }
  }
  span {
    width: 6px;
    height: 6px;
    background-color: #05082c;
    border-radius: 50%;
    margin-right: 4px;
  }
  svg {
    margin-left: 8px;
    cursor: pointer;
  }
}
.t-form {
  :deep(.t-form__item) {
    .t-form__label {
      color: #05082c;
      line-height: 22px;
      min-height: 22px;
    }
    .t-form__controls {
      margin-top: 8px;
    }
    .t-input {
      width: 312px;
    }
  }
}
</style>
