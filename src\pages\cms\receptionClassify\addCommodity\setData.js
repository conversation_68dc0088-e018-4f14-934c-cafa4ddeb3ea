import { computed, reactive, ref } from 'vue';
import { message } from 'woody-ui';
import { isEmptyValue } from '@/utils';
import { getSysUserPage, getBatchList, addBatchProd } from '@/api/cms/reception';

const fileFormData = new FormData();

export const formList = [
  {
    label: "任务名称",
    name: "taskName",
    type: "input", // 输入框
    maxlength: 30,
    span: 6,
  },
  {
    label: "创建人",
    name: "createUserId",
    type: "select",
    searchFn: getSysUserData,
    labelKey: "username",
    valueKey: "userId",
    options: [],
    span: 6,
  },
  {
    label: "创建日期",
    name: "createTime",
    type: "datePicker",
    showTime: true,
    span: 6,
  },
  {
    type: "select",
    label: "任务状态",
    name: "taskStatus",
    options: [
      { label: "全部", value: "" },
      { label: "执行中", value: 0 },
      { label: "已完成", value: 1 },
      { label: "失败", value: 2 },
    ],
    span: 6,
  },
];

export const columns = [
  {
    title: "任务名称",
    dataIndex: "taskName",
    key: "taskName",
    align: "left",
  },
  {
    title: "创建人",
    dataIndex: "operateUser",
    key: "operateUser",
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
  },
  {
    title: "任务状态",
    dataIndex: "taskStatus",
    key: "taskStatus",
    align: "left",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    align: "left",
  },
  // {
  //   colKey: "operate",
  //   title: "操作",
  //   className: "operation",
  // },
];

export const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

export const addFormRules = {
  taskName: [{ required: true }],
};

export const loading = ref(false);
export const btnLoading = ref(false);
export const searchParams = ref({});
export const legalBankFileList = ref([]);
export const tableData = ref([]);
export const addVisible = ref(false);
export const formRef = ref(null);
export const addFormData = reactive({
  taskName: "",
  files: [],
  filesName: "",
});

// 初始化数据（这种通过import导入到setup里的数库，需要在onMounted里初始化下数据，否则有缓存）
export const initData = () => {
  loading.value = false;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  pagination.value.total = 0;
  btnLoading.value = false;
  searchParams.value = {};
  tableData.value = [];
  addVisible.value = false;
};

// 获取创建人列表数据
export function getSysUserData(username) {
  return new Promise((resolve) => {
    const params = {
      page: 1,
      size: 50,
    };
    if (!isEmptyValue(username)) {
      params.username = username;
    }
    // formList[0][1].loading = true;
    getSysUserPage(params)
      .then((res) => {
        if (res.code === 0 && Array.isArray(res.data?.records)) {
          // formList[0][1].options = res.data.records;
          resolve(res.data.records);
        } else {
          // formList[0][1].options = [];
        }
      })
      .finally(() => {
        // formList[0][1].loading = false;
      });
  });
}

// 搜索逻辑
export const handleSearch = (formData) => {
  searchParams.value = formData;
  getData();
};

// 分页逻辑
export const handlePageChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getData();
};

// 获取数据
export const getData = () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    ...searchParams.value,
  };
  loading.value = true;
  getBatchList(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        res.data.records.forEach((item, index) => {
          item.index = index;
        });
        tableData.value = res.data.records;
        pagination.value.total = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 打开新建任务弹框
export const handleAdd = () => {
  addFormData.filesName = "";
  addVisible.value = true;
};

// 关闭新建任务弹框
export const handleClose = () => {
  addVisible.value = false;
  formRef.value.resetFields();
  addFormData.files = [];
  fileFormData.delete("file");
};

// 文件上传前逻辑
export const handleUploadChange = (files) => {
  console.log(files, "files");
  fileFormData.append("file", files.originFileObj);
  addFormData.files = files.originFileObj;
  addFormData.filesName = files.name;
};

// 新建任务弹框确定逻辑
export const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
      if (!addFormData.filesName) {
        message.error("文件名不能为空");
        return;
      }
      const params = {
        formData: fileFormData,
        taskName: addFormData.taskName,
      };
      btnLoading.value = true;
      addBatchProd(params)
        .then((res) => {
          if (res.code === 0) {
            message.success("新建成功");
            handleClose();
            pagination.value.current = 1;
            getData();
          } else {
            message.error(res.message);
          }
        })
        .finally(() => {
          btnLoading.value = false;
          addFormData.files = [];
          addFormData.filesName = "";
          fileFormData.delete("file");
        });
    })
    .catch((error) => {
      console.log("error", error);
    });
};

// 监听新建任务弹框的确定按钮是否可点击
export const isBtnDisable = computed(() => {
  if (isEmptyValue(addFormData.taskName) || addFormData.files.length === 0) {
    return true;
  }
  return false;
});
