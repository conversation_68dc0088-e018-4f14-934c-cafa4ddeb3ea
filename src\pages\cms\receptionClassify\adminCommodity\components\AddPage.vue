<template>
  <drawer-dialog
    :visible="addVisible"
    size="1200px"
    title="添加商品"
    :confirm-disabled="!(0 in selectedRowKeys)"
    @on-close="handleClose"
    @on-confirm="handleConfirm"
  >
    <search-antd
      v-if="addVisible"
      ref="searchAntdRef"
      :form-list="formList"
      class="mt16"
      @on-search="handleSearch"
    />
    <a-table
      :dataSource="tableData"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :rowKey="'productId'"
      :rowSelection="{
        selectedRowKeys: selectedRowKeys,
        onChange: handleSelectChange,
        preserveSelectedRowKeys: true,
      }"
      class="mt24"
      :scroll="{ y: 'calc(100vh - 383px)' }"
      @change="handleTableChange"
    >
      <!-- 商品信息列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'prductName'">
          <span
            v-if="
              isEmptyValue(record.prductName) && isEmptyValue(record.picUrl)
            "
          >
            -
          </span>
          <div v-else class="commodity-info_style">
            <div v-if="!isEmptyValue(record.picUrl)" class="pic">
              <img :src="record.picUrl" />
            </div>
            {{ record.prductName }}
          </div>
        </template>

        <!-- 价格列 -->
        <template v-if="column.dataIndex === 'price'">
          <span v-if="isEmptyValue(record.price)">-</span>
          <span v-else>¥{{ record.price }}</span>
        </template>
      </template>
    </a-table>
  </drawer-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { message } from "woody-ui";
import DrawerDialog from "@/components/DrawerDialog/index.vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { isEmptyValue } from "@/utils";
import {
  feCategoryId,
  addVisible,
  handleCloseAddDialog,
  handleAddConfirm,
} from "../setData.js";
import { getAddList, getBrandList, addProduct } from "@/api/cms/reception";

const formList = [
  {
    type: "select",
    label: "商品来源",
    name: "productSource",
    defaultValue: "0",
    options: [
      // { label: "全部", value: "" },
      { label: "供货仓", value: "0" },
      { label: "京东", value: "8" },
    ],
    span: 6,
    getPopupContainer: (triggerNode) => triggerNode.parentNode || document.body,
  },
  {
    type: "input",
    label: "商品名称",
    name: "productName",
    maxlength: 30,
    span: 6,
  },
  {
    type: "cascader",
    label: "后台分类",
    name: "categoryId",
    labelKey: "categoryName",
    valueKey: "categoryId",
    childrenKey: "newCategoryModelDtos",
    searchFn: "common",
    hideAll: true, // 不添加"全部"选项
    options: [],
    getPopupContainer: (triggerNode) => triggerNode.parentNode || document.body,
  },
  {
    type: "select",
    label: "商品品牌",
    name: "brandId",
    labelKey: "name",
    valueKey: "brandId",
    showSearch: true,
    needFilter: true,
    options: [],
    span: 6,
    getPopupContainer: (triggerNode) => triggerNode.parentNode || document.body,
  },
  {
    type: "input",
    label: "SPU ID",
    name: "spuId",
    maxlength: 19,
    span: 6,
  },
];

const columns = [
  {
    title: "SPU ID",
    dataIndex: "productId",
    key: "productId",
  },
  {
    title: "商品信息",
    dataIndex: "prductName",
    key: "prductName",
  },
  {
    title: "销售价",
    dataIndex: "price",
    key: "price",
  },
  {
    title: "商品来源",
    dataIndex: "productSounce",
    key: "productSounce",
  },
  {
    title: "商品品牌",
    dataIndex: "brandName",
    key: "brandName",
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
    key: "categoryName",
  },
];

// 使用 ref 包装分页对象
const pagination = ref({
  current: 1,
  pageSize: 5,
  total: 0,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ["5", "10", "20", "50"],
  showTotal: (total) => `共 ${total} 条数据`,
  size: "small",
});

const loading = ref(false);
const searchAntdRef = ref(null);
const searchParams = ref({ productSource: "0" });
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 获取商品品牌数据
const getBrandData = () => {
  getBrandList().then((res) => {
    if (res.code === 0 && Array.isArray(res.data)) {
      res.data.unshift({ name: "全部", brandId: "" });
      formList[3].options = res.data;
    }
  });
};

// 搜索功能
const handleSearch = (params) => {
  // 如果 categoryId 是数组，取最后一个值（叶子节点）
  if (Array.isArray(params.categoryId) && params.categoryId.length > 0) {
    params.categoryId = params.categoryId[params.categoryId.length - 1];
  }
  searchParams.value = params;
  if (Object.keys(params).length === 0) {
    searchParams.value.productSource = "0";
  }
  pagination.value.current = 1;
  getData();
};

// 处理表格变化（包括分页、筛选、排序）
const handleTableChange = (pag) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  getData();
};

// 获取数据
const getData = () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    feCategoryId: feCategoryId.value,
    ...searchParams.value,
  };
  loading.value = true;
  getAddList(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        tableData.value = res.data.records;
        pagination.value.total = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 选中值发生变化时的逻辑
const handleSelectChange = (selectValues) => {
  selectedRowKeys.value = selectValues;
};

// 关闭弹框
const handleClose = () => {
  searchParams.value = { productSource: "0" };
  searchAntdRef.value.resetOnlyFunc(); // 使用 SearchAntd 的 resetOnlyFunc 方法
  selectedRowKeys.value = []; // 清空选中项
  pagination.value.current = 1;
  pagination.value.pageSize = 5;
  pagination.value.total = 0;
  handleCloseAddDialog();
};

// 确认添加逻辑
const handleConfirm = () => {
  const params = {
    feCategoryId: feCategoryId.value,
    productId: selectedRowKeys.value,
  };
  addProduct(params).then((res) => {
    if (res.code === 0) {
      message.success("添加成功");
      selectedRowKeys.value = []; // 清空选中项
      getData();
      handleAddConfirm();
      handleClose();
    } else {
      message.error(res.message);
    }
  });
};

watch(
  () => addVisible.value,
  (newValue) => {
    if (newValue) {
      getBrandData();
      getData();
    }
  }
);
</script>

<style lang="less" scoped>
.mt16 {
  margin-top: 16px;
}

.mt24 {
  margin-top: 24px;
}

.form-wrap {
  padding: 8px 0 7px 0;
}

.commodity-info_style {
  display: flex;
  align-items: center;

  .pic {
    margin-right: 8px;
    width: 40px;
    height: 40px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}
</style>
