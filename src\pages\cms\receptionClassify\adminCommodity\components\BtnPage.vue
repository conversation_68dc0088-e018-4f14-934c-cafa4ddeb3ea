<template>
  <div class="btn-box">
    <div>
      <a-space>
        <a-popconfirm
          title="确定要删除吗?"
          @confirm="handleSelectDelete"
          okText="确定"
          cancelText="取消"
          :disabled="!hasSelectedRows"
        >
          <a-button :disabled="!hasSelectedRows"> 删除 </a-button>
        </a-popconfirm>
        <a-popconfirm
          title="确定要全部删除吗?"
          @confirm="allDelete"
          okText="确定"
          cancelText="取消"
        >
          <a-button>全部删除</a-button>
        </a-popconfirm>
      </a-space>
    </div>
    <div>
      <a-space>
        <a-button @click="openSource">绑定商品来源</a-button>
        <a-button type="primary" @click="openAdd">
          <template #icon><plus-outlined /></template>
          添加商品
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { selectedRowKeys, selectDelete, allDelete, openSource, openAdd } from '../setData.js';

// 计算属性，用于判断是否有选中的行
const hasSelectedRows = computed(() => {
  return selectedRowKeys.value && selectedRowKeys.value.length > 0;
});

// 删除后清空选择
const handleSelectDelete = async () => {
  await selectDelete();
  // 删除后应清空选择数组
  selectedRowKeys.value = [];
};
</script>

<style lang="less" scoped>
.btn-box {
  display: flex;
  justify-content: space-between;
  
  // Ant Design 的图标通常会有合适的间距，如果需要调整
  :deep(.anticon) {
    margin-right: 4px;
  }
}
</style>
