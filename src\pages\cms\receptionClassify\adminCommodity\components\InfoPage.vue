<template>
  <page-wrap>
    <a-space>
      <span>分类名称：{{ infoData.categoryName }}</span>
      <span>分类类型: {{ infoData.categoryType === '1' ? '单层' : '双层' }}</span>
      <span v-if="!isEmptyValue(infoData.parentName)">上级分类名称: {{ infoData.parentName }}</span>
    </a-space>
  </page-wrap>
</template>

<script setup>
import { reactive } from 'vue';
import { useRoute } from 'vue-router';
import { isEmptyValue } from '@/utils';
import { getFeCategoryId } from '../setData';

const route = useRoute();

const { id, categoryName, categoryType, parentName } = route.query;

const infoData = reactive({
  categoryName,
  categoryType,
  parentName,
});

getFeCategoryId(id);
</script>

<style lang="less" scoped>
.page-wrap {
  margin-top: 8px;
  padding: 10px 32px;
}
</style>
