<template>
  
  <div v-if="!isEdit" class="show">{{ data.productOrder }}
    <span @click.stop="handleEdit">
      <edit-outlined />
    </span>
  </div>
  <a-input-number
    v-else
    ref="inputRef"
    v-model:value="changeNums"
    theme="column"
    :max="999"
    :min="0"
    @validate="handleValidate"
    @enter="handleSubmit"
    @blur="handleSubmit"
  />
</template>

<script setup>
import { nextTick, ref } from 'vue';
import { EditOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  data: {
    type: Object,
  },
  onChangeSort: {
    type: Function,
  },
});

const isEdit = ref(false);
const changeNums = ref(999);
const isEnter = ref(false);
const inputRef = ref(null);

// 点击编辑按钮
const handleEdit = () => {
  isEdit.value = true;
  changeNums.value = props.data.productOrder;
  nextTick(() => {
    inputRef.value.focus();
  });
};

// 数字值改变时逻辑
const handleValidate = ({ error }) => {
  if (error === 'exceed-maximum') {
    changeNums.value = 999;
  } else if (error === 'below-minimum') {
    changeNums.value = 0;
  }
};

// 修改提交逻辑
const handleSubmit = (value, { e }) => {
  if (!isEnter.value) {
    isEnter.value = e.key === 'Enter';
    isEdit.value = false;
    if (value !== props.data.productOrder) {
      props.onChangeSort(props.data, changeNums.value);
    }
    setTimeout(() => {
      isEnter.value = false;
    });
  }
};
</script>

<style lang="less" scoped>
.show {
  display: flex;
  align-items: center;
  svg {
    margin-left: 16px;
    cursor: pointer;
  }
}
.t-input-number {
  :deep(.t-button) {
    display: none;
  }
}
</style>
