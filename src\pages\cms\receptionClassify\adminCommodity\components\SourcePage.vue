<template>
  <drawer-dialog :visible="sourceVisible" size="800" title="绑定商品来源" :btn-loading="btnLoading" @on-close="handleClose"
    @on-confirm="handleConfirm">
    <template v-if="sourceVisible">
      <a-spin :spinning="loading">
        <a-tabs v-model:activeKey="currentTab">
          <a-tab-pane v-for="tab in tabs" :key="tab.value" :tab="tab.label" />
        </a-tabs>
        <div v-show="currentTab === 1">
          <div class="backend-tree-tips">后台分类必须选择</div>
          <a-tree
            v-model:checkedKeys="backendCategorySelectIds"
            v-model:expandedKeys="expandedKeys"
            :treeData="backendData"
            checkable
            :fieldNames="{ key: 'categoryId', title: 'categoryName', children: 'childCategoryList' }"
            class="custom-tree"
          />
        </div>
        <div v-show="currentTab === 2" class="supply-chain">
          <a-form ref="supplyChainForm" :model="formData" layout="vertical">
            <a-form-item label="选择供应链" name="supplyChainTypeList">
              <a-checkbox-group v-model:value="formData.supplyChainTypeList" :options="supplyChainData" />
            </a-form-item>
          </a-form>
        </div>
        <div v-show="currentTab === 3" class="supply-warehouse">
          <search-antd 
            :form-list="formList" 
            @on-search="handleSearch" 
            class="custom-search-form" 
          />
          <div class="btn-page">
            <a-button :disabled="!(0 in checkedWarehouseValues)" @click="handleClear">
              清除
            </a-button>
            <a-button @click="checkedAll">全部选择</a-button>
          </div>
          <div class="table-container">
            <a-table
              v-if="currentTab === 3"
              :dataSource="tableData"
              :columns="antColumns"
              :pagination="false"
              :loading="tableLoading"
              :rowKey="'supplierId'"
              :rowSelection="{ 
                selectedRowKeys: checkedWarehouseValues, 
                onChange: onTableSelectChange,
                preserveSelectedRowKeys: true // 跨页勾选
              }"
              class="mt16"
              :scroll="{ y: 'calc(100vh - 650px)' }"
            />
          </div>
        </div>
      </a-spin>
      
      <div v-if="currentTab === 3 && tableData.length > 0" class="pagination-wrapper">
        <a-pagination
          v-model:current="pagination.current"
          v-model:pageSize="pagination.pageSize"
          :total="pagination.total"
          :showSizeChanger="true"
          :pageSizeOptions="['10', '20', '50', '100']"
          :showQuickJumper="true"
          :showTotal="(total) => `共 ${total} 条`"
          @change="handlePageChange"
          @showSizeChange="handleSizeChange"
          size="small"
        />
      </div>
    </template>
  </drawer-dialog>
</template>

<script setup>
import { reactive, ref, watch, computed } from 'vue';
import { message } from "woody-ui";
import DrawerDialog from '@/components/DrawerDialog/index.vue';
import SearchAntd from '@/components/SearchAntd/index.vue';
import { feCategoryId, sourceVisible, handleCloseSourceDialog } from '../setData.js';
import { isEmptyValue, isObj } from '@/utils';
import { getBindInfo, getCategoryTree, getSupplierPage, bindProdSource } from '@/api/cms/reception';

const tabs = [
  { label: '后台分类', value: 1 },
  { label: '供应链', value: 2 },
  { label: '供货仓', value: 3 },
];

const supplyChainData = [
  {
    label: '爱库存',
    value: 4,
  },
  {
    label: '京东',
    value: 8,
  }
];

const pagination = {
  current: 1,
  pageSize: 10,
  total: 0,
  size: 'small',
  showQuickJumper: false,
  showSizeChanger: false,
};

const formList = [
  {
    type: 'input',
    label: '店铺名称',
    name: 'shopName',
    maxlength: 30,
    span: 12,
  },
  {
    type: 'input',
    label: '绑定电话',
    name: 'tel',
    span: 12,
  },
];

const columns = [
  {
    dataIndex: 'shopName',
    title: '店铺名称',
    customRender: ({ text }) => text || '-'
  },
  {
    dataIndex: 'tel',
    title: '绑定电话',
    customRender: ({ text }) => text || '-'
  },
];

const antColumns = computed(() => {
  return columns.map(col => ({
    ...col,
    key: col.dataIndex,
    customRender: col.customRender || (({ text }) => text || '-')
  }));
});

let isAll = false;

const loading = ref(false);
const currentTab = ref(null);
const backendCategorySelectIds = ref([]); // 后台分类已选择值
const checkedWarehouseValues = ref([]); // 供货仓已选择值
const backendData = ref([]);
const formData = reactive({
  supplyChainTypeList: [4], // 供应链已选择值
});
const searchParams = ref({});
const supplyChainForm = ref(null);
const tableLoading = ref(false);
const tableData = ref([]);
const excludeSupplierIdList = ref([]); // 排除的选择项
const btnLoading = ref(false);

// 添加展开的键值状态
const expandedKeys = ref([]);

// 全选状态标记
const isAllSelected = ref(false);

// 获取已绑定信息
const getBindData = async () => {
  try {
    const res = await getBindInfo({ id: feCategoryId.value });
    if (res.code === 0 && isObj(res.data)) {
      const { backendCategoryIdList, supplyChainBindInfo, supplierIdList } = res.data;
      if (Array.isArray(backendCategoryIdList)) {
        backendCategorySelectIds.value = backendCategoryIdList;
      }
      if (isObj(supplyChainBindInfo) && Array.isArray(supplyChainBindInfo.supplyChainTypeList)) {
        formData.supplyChainTypeList = supplyChainBindInfo.supplyChainTypeList;
      }
      if (Array.isArray(supplierIdList)) {
        checkedWarehouseValues.value = supplierIdList;
      }
    }
  } catch (error) {
    console.error('Request failed:', error);
    throw error;
  }
};

// 获取后端分类数据
const getBackEnd = async () => {
  try {
    const res = await getCategoryTree();
    const result = [{ categoryId: '', categoryName: '全部', children: [] }];
    if (res.code === 0 && Array.isArray(res.data)) {
      result[0].childCategoryList = res.data;
      backendData.value = result;
      
      // 设置默认展开的第一级节点
      if (result[0] && result[0].categoryId !== undefined) {
        expandedKeys.value = [result[0].categoryId];
      }
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
};

// 获取供货仓数据
const getData = () => {
  const params = {
    current: pagination.current,
    size: pagination.pageSize,
    ...searchParams.value,
  };
  tableLoading.value = true;
  getSupplierPage(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        tableData.value = res.data.records;
        pagination.total = res.data.total;
      }
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

// 供货仓搜索逻辑
const handleSearch = (formData) => {
  searchParams.value = formData;
  pagination.current = 1;
  getData();
};

// 分页逻辑
const handlePageChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  getData();
};

// 表格选择回调
const onTableSelectChange = (selectedRowKeys, selectedRows) => {
  // 如果是全选状态下的操作
  if (isAllSelected.value) {
    // 找出哪些记录被取消选中
    const deselectedIds = checkedWarehouseValues.value.filter(id => !selectedRowKeys.includes(id));
    
    // 将取消选中的ID添加到排除列表中
    deselectedIds.forEach(id => {
      if (!excludeSupplierIdList.value.includes(id)) {
        excludeSupplierIdList.value.push(id);
      }
    });

    // 找出新选中的ID
    const newSelectedIds = selectedRowKeys.filter(id => !checkedWarehouseValues.value.includes(id));
    
    // 从排除列表中移除新选中的ID
    newSelectedIds.forEach(id => {
      const index = excludeSupplierIdList.value.indexOf(id);
      if (index !== -1) {
        excludeSupplierIdList.value.splice(index, 1);
      }
    });
  }

  // 更新选中值
  checkedWarehouseValues.value = selectedRowKeys;
  
  // 如果没有选中项，取消全选状态
  if (selectedRowKeys.length === 0) {
    isAllSelected.value = false;
  }
};

// 获取所有符合条件的ID
const fetchAllIds = async () => {
  // 为了减少修改，我们使用现有的API，但设置更大的分页大小
  // 如果数据量非常大，可能需要多次请求
  try {
    const batchSize = 1000; // 每批获取的数量
    let currentPage = 1;
    let allIds = [];
    let hasMore = true;
    
    while (hasMore) {
      const params = {
        ...searchParams.value,
        current: currentPage,
        size: batchSize
      };
      
      const res = await getSupplierPage(params);
      
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        const batchIds = res.data.records
          .map(item => item.supplierId)
          .filter(id => !excludeSupplierIdList.value.includes(id));
        
        allIds = [...allIds, ...batchIds];
        
        // 更新UI显示进度
        checkedWarehouseValues.value = [...new Set([
          ...checkedWarehouseValues.value,
          ...batchIds
        ])];
        
        // 判断是否还有更多数据
        hasMore = res.data.records.length === batchSize && 
                  allIds.length < res.data.total;
        
        currentPage++;
      } else {
        hasMore = false;
      }
    }
    
    // 确保所有ID都被添加到选中集合
    checkedWarehouseValues.value = [...new Set([
      ...checkedWarehouseValues.value,
      ...allIds
    ])];
  } catch (error) {
    console.error('获取全部ID失败', error);
    message.error('全选操作失败，请重试');
    isAllSelected.value = false;
  }
};

// 清空
const handleClear = () => {
  checkedWarehouseValues.value = [];
  isAllSelected.value = false;
  excludeSupplierIdList.value = [];
};

// 全选逻辑
const checkedAll = async () => {
  isAllSelected.value = true;
  
  try {
    tableLoading.value = true;
    
    // 清空排除列表
    excludeSupplierIdList.value = [];
    
    // 获取所有ID
    await fetchAllIds();
  } finally {
    tableLoading.value = false;
  }
};

// 关闭弹框逻辑
const handleClose = () => {
  currentTab.value = null; // 设置为空，是因为watch这个属性，下次打开弹框防止同一个值无法走watch逻辑
  backendCategorySelectIds.value = []; // 清空后台分类选择的值
  if (supplyChainForm.value && supplyChainForm.value.resetFields) {
    supplyChainForm.value.resetFields(); // 清空供应链选择的值
  }
  checkedWarehouseValues.value = []; // 清空供货仓选择的值
  tableData.value = []; // 清空表格数据
  searchParams.value = {}; // 清空筛选项
  excludeSupplierIdList.value = []; // 清空需要过滤的选择项
  pagination.current = 1; // 初始化当前页为1
  pagination.pageSize = 10; // 初始化当前每页展示数量为10
  pagination.total = 0; // 初始化当前总共数据为0
  isAllSelected.value = false;
  handleCloseSourceDialog();
};

// 确定逻辑
const handleConfirm = async () => {
  const params = {
    feCategoryId: feCategoryId.value,
    backendCategoryIdList: backendCategorySelectIds.value,
    supplyChainBindInfo: {
      supplyChainTypeList: formData.supplyChainTypeList,
    },
    supplierBindInfoDTO: {
      isSearchFlag: isAllSelected.value,
    },
  };
  if (isAllSelected.value) {
    params.supplierBindInfoDTO.supplierSearchDTO = {
      ...searchParams.value,
    };
    if (0 in excludeSupplierIdList.value) {
      params.supplierBindInfoDTO.supplierSearchDTO.excludeSupplierIdList = excludeSupplierIdList.value;
    }
  } else {
    params.supplierBindInfoDTO.supplierIdList = checkedWarehouseValues.value;
  }
  btnLoading.value = true;
  bindProdSource(params)
    .then((res) => {
      if (res.code === 0) {
        message.success('绑定成功');
        handleClose();
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

// 添加页码大小变更处理函数
const handleSizeChange = (current, size) => {
  pagination.current = 1;
  pagination.pageSize = size;
  getData();
};

watch(
  () => sourceVisible.value,
  async (newValue) => {
    if (newValue) {
      loading.value = true;
      currentTab.value = 1;
      await getBindData();
      await getBackEnd();
      
      // 确保第一级节点展开
      if (backendData.value && backendData.value.length > 0) {
        expandedKeys.value = [backendData.value[0].categoryId];
      }
      loading.value = false;
    }
  },
);

watch(
  () => currentTab.value,
  (newValue) => {
    if (newValue === 3) {
      if (!(0 in tableData.value)) {
        getData();
      }
    }
  },
);
</script>

<style lang="less" scoped>
.mt16 {
  margin-top: 16px;
}

.backend-tree-tips {
  color: #003cab;
  margin-top: 10px;
  font-weight: bold;
}

.supply-chain {
  padding: 36px 32px 0;
}

.supply-warehouse {
  margin-top: 24px;

  .form-wrap {
    padding: 0;
  }

  .btn-page {
    margin-top: 24px;
    
    .ant-btn {
      margin-right: 8px;
    }
  }
}

/* 修改 a-tree 相关样式，防止干扰整体滚动 */
.custom-tree {
  height: auto !important;
  max-height: 400px;  // 设置一个合理的高度，防止树太长
  
  :deep(.ant-tree-list-holder-inner) {
    width: 100%;
  }
  
  :deep(.ant-tree-treenode) {
    width: 100%;
    padding: 4px 0;
  }
}

/* 确保表格显示正常 */
:deep(.ant-table-wrapper) {
  overflow: visible !important;
}

:deep(.ant-table-body) {
  overflow: visible !important;
}

.table-container {
  margin-bottom: 16px;
}

/* 固定在底部的分页样式 */
.pagination-wrapper {
  position: absolute;
  bottom: 49px; /* 调整为底部按钮区域的高度 */
  left: auto; /* 不使用左侧固定定位 */
  right: 26px; /* 右侧对齐 */
  width: calc(100% - 32px); /* 宽度减去左右padding */
  padding: 8px 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
  display: flex;
  justify-content: flex-end;
  pointer-events: auto; /* 确保分页可以点击 */
}

.select-all-alert {
  margin: 16px 0;
}

/* 添加自定义样式使查询和重置按钮水平排列 */
.custom-search-form {
  :deep(.btn-container) {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
</style>
