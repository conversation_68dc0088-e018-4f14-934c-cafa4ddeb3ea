<template>
  <info-page />
  <search-antd
    :form-list="formList"
    class="mt16"
    @on-search="handleSearch"
  />
  <page-wrap class="mt16 table-container">
    <btn-page />
    <a-table
      :dataSource="tableData"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :rowKey="'id'"
      :rowSelection="{
        selectedRowKeys: selectedRowKeys,
        onChange: handleSelectChange,
        preserveSelectedRowKeys: true
      }"
      class="mt24"
      @change="handleTableChange"
    >
      <!-- 商品信息列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'prductName'">
          <span v-if="isEmptyValue(record.picUrl) && isEmptyValue(record.prductName)">-</span>
          <div v-else class="commodity-info_style">
            <div v-if="!isEmptyValue(record.picUrl)" class="pic">
              <img :src="record.picUrl" />
            </div>
            {{ record.prductName }}
          </div>
        </template>
        
        <!-- 价格列 -->
        <template v-if="column.dataIndex === 'price'">
          <span v-if="isEmptyValue(record.price)">-</span>
          <span v-else>{{ "¥" + record.price }}</span>
        </template>
        
        <!-- 排序列 -->
        <template v-if="column.dataIndex === 'productOrder'">
          <sort-page :data="record" :on-change-sort="handleChangeSort" />
        </template>
        
        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'operate'">
          <a-popconfirm
            title="确定要删除吗?"
            @confirm="() => selectDelete(record)"
            okText="确定"
            cancelText="取消"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </page-wrap>
  <add-page />
  <source-page />
</template>

<script setup>
import { onMounted } from "vue";
import InfoPage from "./components/InfoPage.vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import BtnPage from "./components/BtnPage.vue";
import AddPage from "./components/AddPage.vue";
import SourcePage from "./components/SourcePage.vue";
import SortPage from "./components/SortPage.vue";
import { isEmptyValue } from "@/utils";
import "./styled.less";
import {
  loading,
  formList,
  tableData,
  columns,
  pagination,
  selectedRowKeys,
  initData,
  handleSearch,
  getData,
  handleSelectChange,
  handlePageChange,
  selectDelete,
  handleChangeSort,
} from "./setData.js";

// 处理表格变化（包括分页、筛选、排序）
const handleTableChange = (pag) => {
  handlePageChange({
    current: pag.current,
    pageSize: pag.pageSize
  });
};

onMounted(() => {
  initData();
  getData();
});
</script>

<style lang="less" scoped>
.mt16 {
  margin-top: 16px;
}

.table-container {
  flex: 1;
}

.mt24 {
  margin-top: 16px;
}

.commodity-info_style {
  display: flex;
  align-items: center;
  
  .pic {
    margin-right: 8px;
    width: 40px;
    height: 40px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}
</style>
