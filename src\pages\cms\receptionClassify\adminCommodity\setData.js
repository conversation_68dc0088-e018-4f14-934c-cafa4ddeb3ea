import { reactive, ref } from 'vue';
import { message } from 'woody-ui';
import { getManageList, deleteManage, deleteAll, modifySort } from '@/api/cms/reception';
import { isObj, isEmptyValue } from '@/utils';

export const formList = [
  {
    type: 'input',
    label: '商品名称',
    name: 'productName',
    maxlength: 30,
  },
  {
    type: 'cascader',
    label: '后台分类',
    name: 'categoryId',
    labelKey: 'categoryName',
    valueKey: 'categoryId',
    // checkStrictly: true,
    childrenKey: "newCategoryModelDtos",
    searchFn: "common", // 使用 SearchAntd 中的通用查询方法
    hideAll: true, // 添加这个属性，表示不需要添加"全部"选项
    options: [],
  },
];

export const columns = [
  {
    title: 'SPU ID',
    dataIndex: 'productId',
    key: 'productId',
  },
  {
    title: '商品信息',
    dataIndex: 'prductName',
    key: 'prductName',
    width: 200,
  },
  {
    title: '销售价（元）',
    dataIndex: 'price',
    key: 'price',
  },
  {
    title: '商品来源',
    dataIndex: 'productSounce',
    key: 'productSounce',
  },
  {
    title: '商品品牌',
    dataIndex: 'brandName',
    key: 'brandName',
  },
  {
    title: '后台分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
  },
  {
    title: '商品排序',
    dataIndex: 'productOrder',
    key: 'productOrder',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    align: 'center',
  },
];

export const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

export const loading = ref(false);
export const feCategoryId = ref('');
export const searchParams = ref({});
export const selectedRowKeys = ref([]);
export const tableData = ref([]);
export const addVisible = ref(false);
export const addSelectedRowKeys = ref([]);
export const sourceVisible = ref(false);

// 初始化数据（这种通过import导入到setup里的数库，需要在onMounted里初始化下数据，否则有缓存）
export const initData = () => {
  loading.value = false;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  pagination.value.total = 0;
  searchParams.value = {};
  selectedRowKeys.value = [];
  tableData.value = [];
  addVisible.value = false;
  addSelectedRowKeys.value = [];
  sourceVisible.value = false;
};

// 获取分类Id
export const getFeCategoryId = (id) => {
  feCategoryId.value = id;
};

// 改变formList的属性
export const handleChangeFormList = (formListItem, rowIndex, itemIndex) => {
  formList[rowIndex][itemIndex] = formListItem;
};

// 更新表格数据
export const updateData = () => {
  pagination.value.current = 1;
  getData();
};

// 查询功能
export const handleSearch = (formData) => {
  const params = {...formData};
  
  // 如果 categoryId 是数组，取最后一个值（叶子节点）
  if (Array.isArray(params.categoryId) && params.categoryId.length > 0) {
    params.categoryId = params.categoryId[params.categoryId.length - 1];
  }
  
  searchParams.value = params;
  updateData();
};

// 选择性删除
export const selectDelete = (row) => {
  const params = {
    ids: isObj(row) && !isEmptyValue(row.id) ? [row.id] : selectedRowKeys.value,
  };
  deleteManage(params).then((res) => {
    if (res.code === 0) {
      message.success('删除成功');
      selectedRowKeys.value = [];
      
      pagination.value.current = 1;
      getData();
    } else {
      message.error('删除失败');
    }
  });
};

// 全部删除
export const allDelete = () => {
  deleteAll(feCategoryId.value).then((res) => {
    if (res.code === 0) {
      message.success('删除成功');
      selectedRowKeys.value = [];
      
      updateData();
    } else {
      message.error('删除失败');
    }
  });
};

// 添加商品确认逻辑
export const handleAddConfirm = () => {
  updateData();
};

// 打开绑定商品来源弹框
export const openSource = () => {
  sourceVisible.value = true;
};

// 关闭绑定商品来源弹框
export const handleCloseSourceDialog = () => {
  sourceVisible.value = false;
};

// 打开添加商品弹框
export const openAdd = () => {
  addVisible.value = true;
};

// 关闭添加商品弹框
export const handleCloseAddDialog = () => {
  addVisible.value = false;
};

// 获取表格数据
export const getData = () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    feCategoryId: feCategoryId.value,
    ...searchParams.value,
  };
  loading.value = true;
  getManageList(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        tableData.value = res.data.records;
        pagination.value.total = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 修改排序
export const handleChangeSort = (row, newVal) => {
  const params = {
    id: row.id,
    productOrder: newVal,
  };
  loading.value = true;
  modifySort(params)
    .then((res) => {
      if (res.code === 0) {
        message.success('修改成功');
        row.productOrder = newVal;
        updateData();
      } else {
        message.error('修改失败');
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 表格选中逻辑
export const handleSelectChange = (selectValues) => {
  selectedRowKeys.value = selectValues;
};

// 分页逻辑
export const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  getData();
};
