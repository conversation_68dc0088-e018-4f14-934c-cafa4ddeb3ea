<template>
  <drawer-dialog
    :visible="dialogVisible"
    size="500px"
    :title="`${isEmptyValue(dialogDataObj.id) ? '新建' : '编辑'}前台分类`"
    :confirm-disabled="isBtndisabled"
    :btn-loading="btnLoading"
    @on-close="handleClosed"
    @on-confirm="handleConfirm"
  >
    <a-form ref="formRef" :model="formData" layout="vertical" :rules="rules" class="a-form">
      <a-form-item label="分类类型" name="categoryType">
        <a-radio-group
          v-model:value="formData.categoryType"
          :disabled="!isEmptyValue(dialogDataObj.id)"
          @change="handleChange"
        >
          <a-radio :value="'1'">单层</a-radio>
          <a-radio :value="'2'">双层</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="分类名称" name="categoryName">
        <a-input
          v-model:value="formData.categoryName"
          :maxlength="5"
          show-count
          placeholder="请输入分类名称"
        />
      </a-form-item>
      <a-form-item
        v-if="formData.categoryType === '2'"
        label="上级分类"
        name="parentId"
      >
        <a-select
          v-model:value="formData.parentId"
          :options="upTypeList.map(item => ({ label: item.categoryName, value: item.id }))"
          :loading="upLoading"
          :disabled="!isEmptyValue(dialogDataObj.id)"
          allow-clear
          show-search
          :filter-option="(input, option) =>
            option.label.toLowerCase().includes(input.toLowerCase())"
          @focus="handleFocus"
          @search="handleSearch"
        />
        </a-form-item>
      <a-form-item
        v-if="formData.categoryType === '2' && !isEmptyValue(formData.parentId)"
        label="排序号"
        name="categoryOrder"
      >
        <a-input-number
          v-model:value="formData.categoryOrder"
          max="999"
          min="0"
          class="input-number"
          @validate="handleValidate"
        />
      </a-form-item>
      <a-form-item
        v-if="formData.categoryType === '2' && isEmptyValue(formData.parentId)"
        label="分类icon"
        name="imageUrl"
      >
        <div
          v-if="!isEmptyValue(formData.imageUrl)"
          class="show-icon"
          @click="handleUpload"
        >
          <img :src="formData.imageUrl" />
          <div class="tips">更换图片</div>
        </div>
        <div v-else class="upload-btn" @click="handleUpload">
          <plus-icon />
          <div class="tips">点击上传图片</div>
        </div>
      </a-form-item>
    </a-form>
  </drawer-dialog>
  <image-dialog
    :visible="imageVisible"
    @on-close="handleImageClose"
    @on-enter="handleImageEnter"
  />
</template>

<script setup>
import { computed, reactive, ref, watch } from "vue";
import { message } from "woody-ui";
import DrawerDialog from "@/components/DrawerDialog/index.vue";
import {
  dialogVisible,
  dialogDataObj,
  handleDialogClosed,
  handleDialogConfirm,
} from "../setData.js";
import ImageDialog from "./ImageDialog.vue";
import { isEmptyValue } from "@/utils";
import {
  getFeCategoryPage,
  EditFeCategory,
  addFeCategory,
} from "@/api/cms/reception";

const btnLoading = ref(false);
const formRef = ref(null);
const upTypeList = ref([]);
const upLoading = ref(false);
const imageVisible = ref(false);

const formData = reactive({
  id: "",
  categoryType: "1",
  categoryName: "",
  parentId: "",
  categoryOrder: "",
  imageUrl: "",
});

const rules = {
  categoryType: [{ required: true, message: "分类类型必选" }],
  categoryName: [{ required: true, message: "分类名称必填" }],
  categoryOrder: [{ required: true, message: "序号必填" }],
  imageUrl: [{ required: true, message: "分类icon必传" }],
};

// 是否可点击确定按钮
const isBtndisabled = computed(() => {
  if (isEmptyValue(formData.categoryName)) {
    return true;
  }

  if (formData.categoryType === "2") {
    if (isEmptyValue(formData.parentId)) {
      return isEmptyValue(formData.imageUrl);
    }
    return (
      !isEmptyValue(formData.parentId) && isEmptyValue(formData.categoryOrder)
    );
  }

  return false;
});

// 初始化
const initFormData = (activeType) => {
  // form.value.reset();
  formData.id = "";
  formData.imageUrl = "";
  if (!isEmptyValue(activeType)) {
    formData.categoryType = activeType;
  } else {
    formData.categoryType = "1";
  }
};

// 分类类型切换
const handleChange = (e) => {
  initFormData(e.target.value);
};

// 获取上级分类
const getUpType = (categoryName) => {
  const params = {
    level: 1,
    categoryType: formData.categoryType,
    page: 1,
    size: 50,
  };
  if (!isEmptyValue(categoryName)) {
    params.categoryName = categoryName;
  }
  upLoading.value = true;
  getFeCategoryPage(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        upTypeList.value = res.data.records;
      } else {
        upTypeList.value = [];
      }
    })
    .catch(() => {
      upTypeList.value = [];
    })
    .finally(() => {
      upLoading.value = false;
    });
};

// 上级分类选择框获取焦点逻辑
const handleFocus = () => {
  if (isEmptyValue(formData.parentId)) {
    getUpType();
  }
};

// 上级分类选择框搜索逻辑
const handleSearch = (value) => {
  getUpType(value);
};

// 校验序号
const handleValidate = ({ error }) => {
  if (error === "exceed-maximum") {
    formData.categoryOrder = 999;
  } else if (error === "below-minimum") {
    formData.categoryOrder = 0;
  }
};

// 打开上传弹框
const handleUpload = () => {
  imageVisible.value = true;
};

// 取消逻辑
const handleClosed = () => {
  handleDialogClosed();
};

// 确认逻辑
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(async () => {
      btnLoading.value = true;
    const params = {};
    Object.keys(formData).forEach((key) => {
      if (!isEmptyValue(formData[key])) {
        params[key] = formData[key];
      }
    });
    if (formData.categoryType === "1") {
      delete params.categoryOrder;
      delete params.imageUrl;
      delete params.parentId;
    } else if (isEmptyValue(formData.parentId)) {
      delete params.categoryOrder;
    }

    const fn = (res) => {
      if (res.code === 0) {
        message.success("操作成功");
        handleDialogConfirm();
      } else {
        message.error(res.message);
      }
    };

    if (!isEmptyValue(params.id)) {
      // 编辑
      EditFeCategory(params)
        .then((res) => {
          fn(res);
        })
        .finally(() => {
          btnLoading.value = false;
        });
    } else {
      // 新增
      addFeCategory(params)
        .then((res) => {
          fn(res);
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
    })
};

// 取消我的图片弹框
const handleImageClose = () => {
  imageVisible.value = false;
};

// 确认我的图片弹框里选择的图片
const handleImageEnter = (url) => {
  formData.imageUrl = url;
};

watch(
  () => dialogDataObj.value,
  (newValue) => {
    if (!isEmptyValue(newValue.id)) {
      Object.keys(formData).forEach((key) => {
        if (key === "parentId") {
          formData[key] = newValue[key] === "0" ? "" : newValue[key];
        } else {
          formData[key] = String(newValue[key]);
        }
      });
      if (!isEmptyValue(newValue.parentName)) {
        getUpType(newValue.parentName);
      }
    } else {
      initFormData();
    }
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.a-form {
  padding: 8px 0 0 8px;
  :deep(.t-form__item) {
    .t-form__label {
      line-height: 22px;
      min-height: 22px;
      color: #05082c;
      margin-bottom: 13px;
    }
    .t-form__controls-content,
    .t-form__controls {
      min-height: auto;
    }
    .t-radio {
      color: #05082c;
      margin-right: 16px;
    }
    .t-input__wrap {
      width: 292px;
    }
    .input-number {
      .t-input__wrap {
        width: 100px;
      }
    }
  }
  .show-icon {
    width: 120px;
    height: 120px;
    border: 1px solid #e0e8ed;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    &:hover {
      img {
        transform: scale(1.2);
      }
      .tips {
        display: block;
      }
    }
    img {
      width: 100%;
      height: 100%;
      vertical-align: top;
      transform: scale(1);
      transition: transform 0.5s;
    }
    .tips {
      width: 100%;
      text-align: center;
      line-height: 38px;
      background-color: rgba(0, 0, 0, 0.9);
      position: absolute;
      left: 0;
      bottom: 0;
      color: #fff;
      display: none;
    }
  }
  .upload-btn {
    width: 120px;
    height: 120px;
    background-color: #f1f6f8;
    border-radius: 3px;
    border: 1px dashed #e0e8ed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    svg {
      font-size: 26px;
    }
    .tips {
      font-size: 12px;
      color: #636d7e;
      margin-top: 5px;
    }
  }
}
</style>
