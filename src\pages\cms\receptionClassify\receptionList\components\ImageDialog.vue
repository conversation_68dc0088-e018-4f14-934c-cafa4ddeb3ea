<template>
  <a-modal
    :open="visible"
    width="1100px"
    title="我的图片"
    placement="center"
    class="image-custom_dialog"
    @cancel="handleClose"
    @ok="handleConfirm"
  >
    <div class="content mt20">
      <div class="nav-wrap">
        <a-loading :loading="navLoading">
          <div class="title">文件目录</div>
          <div
            :class="['not', { active: activeNode === 0 }]"
            @click="handleClick('not')"
          >
            未分组<span>{{ notNums }}</span>
          </div>
          <div class="tree-container">
            <a-tree
              :tree-data="convertedTreeData"
              :field-names="{
                title: 'title',
                key: 'key',
                children: 'children',
              }"
              :default-expanded-keys="[ownerGroupId]"
              :selected-keys="[ownerGroupId]"
              @select="onSelect"
            >
              <template #title="{ title, dataRef }">
                <span>
                  <span class="label-info" @click="treeClick(dataRef)">
                    <span>{{ dataRef.name }}</span>
                    <span class="num-css">{{
                      dataRef.fileCnt
                    }}</span>
                    <span class="edit-icon" @click.stop="showInput(dataRef)">
                      <edit-outlined />
                    </span>
                  </span>
                </span>
              </template>
            </a-tree>
          </div>
        </a-loading>
      </div>
      <div class="main-wrap">
        <a-loading :loading="listLoading">
          <div class="top">
            <a-input-search
              v-model:value="materialName"
              placeholder="请输入图片名称"
              style="width: 340px"
              @search="handleEnter"
            />
            <div class="btn">
              <div class="upload-btn" @click="handleUpload">
                <upload-icon />上传图片
              </div>
              <div class="go-btn" @click="handleOpenWindow">前往素材中心</div>
            </div>
          </div>
          <a-radio-group v-model:value="selectPic" class="mt20 mb20">
            <div v-if="0 in listData" class="list">
              <div
                v-for="item in listData"
                :key="item.id"
                class="item"
                @click="selectFunc(item)"
              >
                <div class="image-box">
                  <img :src="item.url" />
                  <div class="tips">750x1200</div>
                </div>
                <a-radio :value="item.url" class="mt10">
                  {{ item.name }}
                </a-radio>
              </div>
            </div>
            <div v-else class="empty-tips">暂无数据</div>
          </a-radio-group>
          <a-pagination
            :total="pagintion.total"
            :current="pagintion.current"
            :page-size="pagintion.pageSize"
            @change="pageChange"
          />
        </a-loading>
      </div>
    </div>
  </a-modal>
  <modal-comp
    :modal-info="modalInfo"
    :group-option="navList"
    @confirm="handleUploadConfirm"
    @cancel="handleUploadCancel"
    @query-group-func="getGroup"
  />
</template>

<script setup>
import { reactive, ref, watch, computed } from "vue";
import { message } from "woody-ui";
import ModalComp from "@/pages/cms/materialCenter/modalComp.vue";
import {
  getGroupQuery,
  getGroupMateriallCnt,
  getPageSearch,
} from "@/api/cms/decoration";
import { isEmptyValue } from "@/utils";

const props = defineProps({
  visible: {
    type: Boolean,
    dfault: false,
  },
});

const pagintion = reactive({
  current: 1,
  pageSize: 5,
  total: 0,
  showPreviousAndNextBtn: false,
  pageEllipsisMode: "both-ends",
});

const navLoading = ref(false);
const listLoading = ref(false);
const notNums = ref(0);
const navList = ref([]);
const activeNode = ref(0);
const materialName = ref("");
const listData = ref([]);
const selectPic = ref("");
const modalInfo = reactive({
  type: "UPLOAD",
  showModal: false,
});

const emits = defineEmits(["onClose", "onEnter"]);

// const convertedTreeData = navList.value.map(item => ({
//   ...item,
//   title: item.name, // 用于 title 插槽
//   key: item.id,
//   children: item.children?.length ? item.children.map(child => ({
//     ...child,
//     title: child.name,
//     key: child.id,
//     children: child.children // 如果有子节点递归处理
//   })) : []
// }))

const convertedTreeData = computed(() => {
  const mapTree = (nodes) =>
    nodes.map((item) => ({
      title: item.name,
      key: item.id,
      name: item.name,
      fileCnt: item.materialCnt,
      children: item.children ? mapTree(item.children) : [],
    }));
  return mapTree(navList.value || []);
});
// 获取所有未分组
const getNotGroup = () => {
  getGroupMateriallCnt({ groupId: 0 }).then((res) => {
    if (res.code === 0) {
      notNums.value = res.data || 0;
    }
  });
};

// 获取文件目录列表
const getGroup = () => {
  navLoading.value = true;
  getGroupQuery({ flatFlag: false })
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data)) {
        navList.value = res.data;
      }
    })
    .finally(() => {
      navLoading.value = false;
    });
};

// 获取所有图片
const getPics = () => {
  const params = {
    page: pagintion.current,
    size: pagintion.pageSize,
    ownerGroupId: activeNode.value,
  };
  if (!isEmptyValue(materialName.value)) {
    params.materialName = materialName.value;
  }
  listLoading.value = true;
  getPageSearch(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        listData.value = res.data.records;
        pagintion.total = res.data.total;
      } else {
        listData.value = [];
      }
    })
    .catch(() => {
      listData.value = [];
    })
    .finally(() => {
      listLoading.value = false;
    });
};

// 左侧节点选中时逻辑
const handleClick = (context) => {
  if (context === 'not') {
    activeNode.value = 0;
  } 

  pagintion.current = 1;
  getPics();
};

//  点击当前树
const treeClick = (node) => {
  console.log(node, "node");
  activeNode.value = node.key;
  pagintion.current = 1;
  getPics();
};

// 输入框搜索逻辑
const handleEnter = () => {
  pagintion.current = 1;
  getPics();
};

// 分页逻辑
const pageChange = (current, pageSize) => {
  pagintion.current = current;
  pagintion.pageSize = pageSize;
  getPics();
};

// 关闭上传图片弹框
const handleUploadCancel = () => {
  modalInfo.showModal = false;
};

// 点图片也能选中
const selectFunc = (item) => {
  selectPic.value = item.url;
};

// 打开上传图片弹框
const handleUpload = () => {
  modalInfo.showModal = true;
  modalInfo.type = "UPLOAD";
};

// 上传图片确认逻辑
const handleUploadConfirm = () => {
  modalInfo.showModal = false;
  modalInfo.type = "";
  pagintion.current = 1;
  getNotGroup();
  getGroup();
  getPics();
};

// 新开窗口到素材中心
const handleOpenWindow = () => {
  const { VITE_CURRENT_URL } = import.meta.env;
  const url = `${VITE_CURRENT_URL}/#/market/index?market=/#/cms/materialCenter`;
  window.open(url, "_blank");
};

// 关闭弹框
const handleClose = () => {
  notNums.value = 0;
  navList.value = [];
  activeNode.value = 0;
  materialName.value = "";
  listData.value = [];
  selectPic.value = "";
  emits("onClose");
};

// 确认逻辑
const handleConfirm = () => {
  if (isEmptyValue(selectPic.value)) {
    message.error("请选择图片");
    return;
  }
  emits("onEnter", selectPic.value);
  handleClose();
};

watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      getNotGroup();
      getGroup();
      getPics();
    }
  }
);
</script>
<style lang="less" scoped>
.list {
  height: 500px;
  overflow-y: auto;
}
:deep(.ant-tree-list-holder-inner){
  line-height: 40px;
}
:deep(.ant-tree .ant-tree-node-content-wrapper){
    display:inline-block;
    width:170px;
    line-height: 40px;
  }
  :deep(.ant-tree .ant-tree-switcher){
    line-height: 40px;
  }
  // :deep(.ant-tree-title){
  //   margin-left:8px;
  // }
.tree-container {
  height: 450px !important;
  overflow-y: auto;
  :deep(.ant-tree .ant-tree-treenode) {
    height: 40px;
    line-height: 40px;
    width:170px;
  }
  :deep(.ant-tree-node-selected) {
    background-color: #e6f7e6 !important; /* 浅绿色背景 */
  }
  .num-css{
    position: absolute;
    right:0px;
  }
}
</style>
