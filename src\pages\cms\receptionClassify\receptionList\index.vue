<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <page-wrap class="mt16">
    <div class="add-box">
      <div class="total"></div>
      <a-button type="primary" @click="handleAdd"> 新建分类 </a-button>
    </div>
    <a-table
      class="mt24"
      :columns="columns"
      :rowKey="(record) => record.id"
      :data-source="tableData"
      :pagination="pagination"
      @change="handlePageChange"
      :row-expandable="rowExpandable"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'categoryType'">
          <div v-if="record.level === 1">
            {{ record.categoryType === 1 ? "单层" : "双层" }}
          </div>
          <div v-else></div>
        </template>
        <template v-if="column.key === 'categoryOrder'">
          <div v-if="record.categoryType !== 1 && record.level > 1">
            {{ record.categoryOrder }}
          </div>
        </template>
        <template v-if="column.key === 'operate'">
          <a-button type="link" @click="handleEdit($event, record)">
            编辑
          </a-button>
          <a-button
            type="link"
            v-if="isAdminBtn(record)"
            @click="handleAdmin($event, record)"
          >
            管理
          </a-button>
          <a-button type="link" @click="handleCopy(record)">复制</a-button>
        </template>
      </template>
    </a-table>
  </page-wrap>
  <dialog-view />
  <copy-dialog
    :visible="copyVisible"
    :data-obj="dialogDataObj"
    :maxlength="5"
    @on-close="handleCopyClose"
    @on-confirm="handleCopyConfirm"
  />
</template>

<script setup>
import SearchAntd from "@/components/SearchAntd/index.vue";
import AddCircle from "@/assets/add-circle.svg";
import DialogView from "./components/DialogView.vue";
import CopyDialog from "@/components/CopyDialog/index.vue";
import { isEmptyValue } from "@/utils";
import "./styled.less";
import {
  loading,
  isAdminBtn,
  formList,
  tableListRef,
  tableData,
  totalNums,
  columns,
  treeConfig,
  dialogDataObj,
  copyVisible,
  handleSearch,
  handleAdd,
  handleEdit,
  handleCopy,
  handleAdmin,
  handleCopyClose,
  handleCopyConfirm,
  pagination,
  handlePageChange,
} from "./setData";
</script>

<style lang="less" scoped>
.mt8 {
  margin-top: 8px;
}

.mt16 {
  margin-top: 16px;
  flex: 1;
}

.add-box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  svg {
    margin-right: 3px;
  }
}

// .btn-css {
//   color: var(--td-brand-color);
// }

.t-table {
  margin-top: 16px;
}
.mt24 {
  margin-top: 24px;
}
</style>
