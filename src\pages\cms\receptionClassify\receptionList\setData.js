import { reactive, ref, watch } from "vue";
import { message } from "woody-ui";
import router from "@/router";
import {
  getFeCategoryPage,
  getSysUserPage,
  copyFeCategory,
} from "@/api/cms/reception";
import { isEmptyValue } from "@/utils";
import { removeEmptyChildren } from "@/utils/utils";

export const loading = ref(false);
export const queryParams = ref({});
export const tableListRef = ref(null);
export const tableData = ref([]);
export const userData = ref([]);

export const totalNums = ref(0);
export const dialogVisible = ref(false);
export const dialogDataObj = ref({});
export const copyVisible = ref(false);
const remoteMethod = (search) => {
  setTimeout(() => {
    getSysUserData(search);
  }, 1000);
};
export const formList = [
  {
    type: "input",
    label: "分类名称",
    name: "categoryName",
    maxlength: 5,
    span: 6,
  },
  // {
  //   type: 'select',
  //   label: '创建人',
  //   name: 'createUserId',
  //   labelKey: 'username',
  //   valueKey: 'userId',
  //   filterable: true,
  //   loading: false,
  //   searchFn: getSysUserData,
  //   options: [],
  //   span:6,
  // },
  {
    label: "创建人",
    name: "createUserId",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: userData,
    showSearch: true,
    needFilter: true,
    labelKey: "username",
    valueKey: "userId",
    remoteMethod,
    span: 6,
  },

  {
    type: "datePicker",
    label: "创建日期",
    name: "createTime",
    showTime: true,
    span: 6,
  },
  {
    type: "datePicker",
    label: "更新日期",
    name: "updateTime",
    showTime: true,
    span: 6,
  },
  {
    type: "select",
    label: "分类类型",
    name: "categoryType",
    options: [
      { label: "全部", value: "" },
      { label: "单层", value: "1" },
      { label: "双层", value: "2" },
    ],
    span: 6,
  },
];

export const columns = [
  {
    width: "46px",
  },
  {
    key: "categoryName",
    dataIndex: "categoryName",
    title: "分类名称",
  },
  {
    key: "id",
    dataIndex: "id",
    title: "分类ID",
  },
  {
    key: "createUserName",
    dataIndex: "createUserName",
    title: "创建人",
  },
  {
    key: "createTime",
    dataIndex: "createTime",
    title: "创建时间",
    width: "200px",
  },
  {
    key: "updateTime",
    dataIndex: "updateTime",
    title: "更新时间",
    width: "200px",
  },
  {
    key: "categoryOrder",
    dataIndex: "categoryOrder",
    title: "排序",
  },
  {
    key: "categoryType",
    dataIndex: "categoryType",
    title: "分类类型",
  },
  {
    key: "operate",
    dataIndex: "operate",
    title: "操作",
    width: "220px",
    className: "operation",
  },
];

// 初始化数据（这种通过import导入到setup里的数库，需要在onMounted里初始化下数据，否则有缓存）
export const initData = () => {
  loading.value = false;
  queryParams.value = {};
  tableData.value = [];
  totalNums.value = 0;
  dialogVisible.value = false;
  dialogDataObj.value = {};
  copyVisible.value = false;
};

export const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

// 分页逻辑
export const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  getData();
};

// 是否展示管理按钮
export const isAdminBtn = (row) => {
  if (row.level !== 1) {
    return true;
  }
  if (row.categoryType === 1) {
    return true;
  }
  return false;
};

// 查询功能
export const handleSearch = (formData) => {
  queryParams.value = formData;
  // tableListRef.value.clearExpandedTreeNodes();
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  getData();
};

// 统计总条数
// export const getTotal = () => {
//   function add(arr) {
//     arr.forEach((item) => {
//       totalNums.value++;
//       if (Array.isArray(item.children)) {
//         add(item.children);
//       }
//     });
//   }
//   add(tableData.value);
// };

// 获取表格数据
export const getData = () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    ...queryParams.value,
  };
  loading.value = true;
  getFeCategoryPage(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        tableData.value = removeEmptyChildren(res.data.records);
        pagination.value.total = res.data.total;
        // getTotal();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 编辑功能
export const handleEdit = (e, row) => {
  e.stopPropagation();
  Object.keys(row).forEach((key) => {
    dialogDataObj.value[key] = row[key];
  });
  dialogVisible.value = true;
};

// 打开复制弹框
export const handleCopy = (row) => {
  Object.keys(row).forEach((key) => {
    dialogDataObj.value[key] = row[key];
  });
  copyVisible.value = true;
};

// 关闭复制弹框
export const handleCopyClose = () => {
  copyVisible.value = false;
};

// 复制确认逻辑
export const handleCopyConfirm = (obj) => {
  const params = {
    id: obj.id,
    categoryName: obj.categoryName,
    createUserId: obj.createUserId,
  };
  copyFeCategory(params).then((res) => {
    if (res.code === 0) {
      message.success("复制成功");
      copyVisible.value = false;
      getData();
    } else {
      message.error(res.message);
    }
  });
};

// 管理功能
export const handleAdmin = (e, row) => {
  e.stopPropagation();
  router.push({
    path: "/cms/receptionClassify/admin",
    query: {
      id: row.id,
      categoryName: row.categoryName,
      categoryType: row.categoryType,
      parentName: row.parentName,
    },
  });
};

// 打开新建弹框
export const handleAdd = () => {
  dialogVisible.value = true;
  dialogDataObj.value = {};
};

// 关闭新建和编辑
export const handleDialogClosed = () => {
  dialogVisible.value = false;
  dialogDataObj.value = {};
};

// 新建和编辑的确认逻辑
export const handleDialogConfirm = async () => {
  handleDialogClosed();
  getData();
};

export const treeConfig = {
  expandTreeNodeOnClick: true,
  indent: 12,
};

// 获取创建人列表数据
export function getSysUserData(username) {
  return new Promise((resolve) => {
    const params = {
      page: 1,
      size: 50,
    };
    if (!isEmptyValue(username)) {
      params.username = username;
    }
    getSysUserPage(params).then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        userData.value = res.data.records;
        resolve(res.data.records);
      }
    });
  });
}

watch(
  () => router.currentRoute.value,
  (newValue) => {
    getSysUserData();
    initData();
    getData();
  },
  {
    immediate: true,
  }
);
