.image-custom_dialog {
  .t-dialog__header {
    background-color: #f1f6f8;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
    padding: 15px 32px;
    border-bottom: 0 none;
  }
  .t-dialog__body {
    padding: 24px 15px 0 32px !important;
    border-bottom-color: #f2f5f9;
    overflow: visible;
  }
  .t-dialog__footer {
    padding: 16px 32px 32px;
  }
  .content {
    display: flex;
    justify-content: space-between;
    height: calc(100vh / 1.77);
    .nav-wrap {
      width: 200px;
      height: 100%;
      border-right: 1px solid #f2f5f9;
      .title {
        font-size: 16px;
        line-height: 24px;
        color: #05082c;
        font-weight: 600;
        padding: 0 10px;
      }
      .not {
        margin-top: 13px;
        color: #05082c;
        padding: 0 19px 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        line-height: 48px;
        &.active {
          background-color: #f0f9ff;
          color: #1a7af8;
        }
        span {
          color: #8990a0;
        }
      }
      .t-tree {
        margin: 0 0 0 15px;
      }
      .t-tree__item {
        padding: 0;
      }
      .t-is-active .t-tree__label {
        background-color: #f0f9ff;
        border-right-color: #1a7af8;
        .label {
          color: #1a7af8;
        }
      }
      .t-tree__label {
        padding: 13px 0;
        color: #495366;
        border-right-width: 3px;
        border-right-style: solid;
        border-right-color: transparent;
        > span {
          display: flex;
          justify-content: space-between;
          padding: 0 16px 0;
        }
        .label {
          width: 50px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .main-wrap {
      width: calc(100% - 160px);
      padding: 0 0 10px 24px;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .t-input__wrap {
          width: 237px;
        }
        .t-input {
          .t-input__prefix-icon {
            margin-right: 12px;
          }
        }
        .btn {
          display: flex;
          align-items: center;
          > div {
            cursor: pointer;
            line-height: 32px;
            background-color: #f2f5f9;
            text-align: center;
            color: #05082c;
            border-radius: 3px;
            padding: 0 16px;
            margin-left: 8px;
            svg {
              margin-right: 10px;
            }
          }
        }
      }
      .t-radio-group {
        width: 100%;
        margin-top: 24px;
        height: calc(100% - 32px - 32px - 28px - 24px);
      }
      .list {
        display: flex;
        flex-wrap: wrap;
        gap: 13px;
        height: 480px;
        overflow-y: auto;
        .item {
          width: 180px;
          overflow: hidden;
        }
        .image-box {
          width: 180px;
          height: 180px;
          background-color: #f6f6f6;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 8px;
          position: relative;
          overflow: hidden;
          &:hover {
            .tips {
              display: block;
            }
          }
          img {
            max-width: 125px;
          }
          .tips {
            background-color: rgba(0, 0, 0, 0.8);
            line-height: 40px;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            color: #fff;
            text-align: center;
            display: none;
          }
        }
        .t-radio {
          margin-top: 12px;
        }
        .t-radio__label {
          width: 132px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .t-loading__parent {
      height: 100%;
    }
  }
  .empty-tips {
    text-align: center;
    flex: 1;
  }
  .t-pagination {
    color: @pagination-color;
    margin-top: 28px;
    .t-pagination__total {
      color: @pagination-total-color;
    }
    .t-input {
      border-color: @pagination-border-color;
      color: @pagination-color;
    }
    .t-pagination__jump {
      margin-left: 16px;
    }
    .t-pagination__btn-prev, .t-pagination__btn-next {
      justify-content: center;
    }
  }
}
