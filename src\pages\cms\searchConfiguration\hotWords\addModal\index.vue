<template>
  <a-modal
    v-model:open="isOpen"
    width="520px"
    title="新增热搜词"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="热搜词"
            name="searchVal"
            :rules="[{ required: true, message: '请输入热搜词' }]"
          >
            <a-input
              v-model:value="formModalData.searchVal"
              allow-clear
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="排序系数"
            name="sort"
            :rules="[{ required: true, message: '请输入排序系数' }]"
          >
            <a-input
              v-model:value="formModalData.sort"
              allow-clear
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="是否展示" name="isShow">
            <a-switch v-model:checked="formModalData.isShow" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import { getSaveHost } from "@/api/cms/searchHot";

const isOpen = ref(false);
const formModalData = reactive({ searchVal: "", sort: "", isShow: true });
const loading = ref(false);
const formModalRef = ref(null);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
});

const emit = defineEmits(["isModalOpen"]);

// 新增
const getAdd = async () => {
  const params = {
    ...formModalData,
    isShow: formModalData.isShow ? 1 : 0,
  };
  try {
    const res = await getSaveHost(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("新增成功");
    formModalRef.value.resetFields();
    isOpen.value = false;
    emit("isModalOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getAdd();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped></style>
