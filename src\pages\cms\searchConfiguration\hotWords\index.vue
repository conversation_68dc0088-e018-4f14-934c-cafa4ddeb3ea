<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="6">
            <a-form-item label="名称" name="searchVal">
              <a-input
                v-model:value="formData.searchVal"
                allow-clear
                style="width: 100%"
                placeholder="请输入名称"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="排序系数" name="sort">
              <a-input
                v-model:value="formData.sort"
                allow-clear
                style="width: 100%"
                placeholder="请输入排序系数"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="创建时间" name="createStart">
              <a-range-picker
                v-model:value="formData.createStart"
                :locale="locale"
                :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('23:59:59', 'HH:mm:ss'),
                  ],
                }"
                allow-clear
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="更新时间" name="updateStart">
              <a-range-picker
                v-model:value="formData.updateStart"
                :locale="locale"
                :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('23:59:59', 'HH:mm:ss'),
                  ],
                }"
                allow-clear
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <a-popconfirm
        title="确定删除？"
        ok-text="确定"
        cancel-text="取消"
        @confirm="handleBatchDel()"
      >
        <a-button style="float: left"> 批量作废 </a-button>
      </a-popconfirm>
      <div class="table-operate-box">
        <a-button type="primary" @click="() => handleCreate()">
          <plus-circle-outlined />
          新增
        </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        rowKey="id"
        :row-selection="{ onChange: onSelectChange }"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 是否展示 -->
          <template v-if="column.key == 'isShow'">
            <a-switch
              @change="handleSwitch(record)"
              :checked="record.isShow === 1"
            />
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css"
              @click="handleEdit(record)"
            >
              编辑
            </a-button>

            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelte(record.id)"
            >
              <a-button type="link" class="btn-css"> 作废 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <add-modal :open="isOpen" @is-modal-open="handleOk"></add-modal>
    <edit-modal
      :open="isEditOpen"
      :data="isData"
      @is-modal-open="handleEditOk"
    ></edit-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { PlusCircleOutlined } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getCoinPage, getSetSave, getDelHotList } from "@/api/cms/searchHot";
import addModal from "./addModal/index.vue";
import editModal from "./editModal/index.vue";
import dayjs from "dayjs";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({
  searchVal: undefined,
  sort: undefined,
  createStart: undefined,
  updateStart: undefined,
});
const isDetail = ref(false);
onMounted(async () => {
  getPageList();
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
      createStart: formData.createStart ? formData.createStart[0] : "",
      createEnd: formData.createStart ? formData.createStart[1] : "",
      updateStart: formData.updateStart ? formData.updateStart[0] : "",
      updateEnd: formData.updateStart ? formData.updateStart[1] : "",
    };
    const res = await getCoinPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
// 新增
const isOpen = ref(false);
const handleCreate = () => {
  isOpen.value = !isOpen.value;
};
const handleOk = (e) => {
  isOpen.value = e;
  getPageList();
};
//编辑
const isEditOpen = ref(false);
const isData = ref();
const handleEdit = (data) => {
  isData.value = data;
  isEditOpen.value = !isEditOpen.value;
};
const handleEditOk = (e) => {
  isEditOpen.value = e;
  getPageList();
};
// 删除
const getDelete = async (id) => {
  try {
    const res = await getDelHotList(id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("删除成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
const handleDelte = async (id) => {
  const ids = [id];
  getDelete(ids);
};

//批量删除
const isSelectRowKey = ref();
const onSelectChange = (selectedRowKeys) => {
  isSelectRowKey.value = selectedRowKeys;
};

const handleBatchDel = () => {
  if (!isSelectRowKey.value) {
    message.error("请选择要删除的数据");
    return;
  }
  getDelete(isSelectRowKey.value);
};

//是否展示
const handleSwitch = async (e) => {
  const params = {
    id: e.id,
    isShow: e.isShow == 1 ? 0 : 1,
  };
  try {
    const res = await getSetSave(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
