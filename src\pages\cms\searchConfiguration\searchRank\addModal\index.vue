<template>
  <a-modal
    v-model:open="isOpen"
    width="520px"
    title="新增"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="选择榜单  数据来源为专区管理-榜单专区"
            name="zoneId"
            :rules="[{ required: true, message: '请输入选择榜单' }]"
          >
            <!-- <p style="color: #878C94;">数据来源为专区管理-榜单专区</p> -->
            <a-select
              v-model:value="formModalData.zoneId"
              show-search
              option-filter-prop="name"
              placeholder="请选择专区"
              :options="zoneOptions"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="排序" name="rankShowSort">
            <a-input
              v-model:value="formModalData.rankShowSort"
              allow-clear
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import { getZone, getAddOrEdit } from "@/api/cms/searchRank";

const isOpen = ref(false);
const formModalData = reactive({
  zoneId: undefined,
  searchShowRankId: undefined,
  rankShowSort: undefined,
});
const loading = ref(false);
const formModalRef = ref(null);
const zoneOptions = ref();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  if (isOpen.value) {
    getZoneList();
  }
});

//榜单
const getZoneList = async () => {
  try {
    const params = {
      size: 10000,
      zoneType: "RANK",
    };
    const res = await getZone(params as any);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    zoneOptions.value = res.data.records;
  } catch (error) {
    message.error(error.message);
  }
};
const emit = defineEmits(["isModalOpen"]);

// 新增
const getAdd = async () => {
  const params = {
    ...formModalData,
  };
  try {
    const res = await getAddOrEdit(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isOpen.value = false;
    emit("isModalOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getAdd();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped></style>
