<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="6">
            <a-form-item label="榜单名称" name="zoneName">
              <a-input
                v-model:value="formData.zoneName"
                allow-clear
                style="width: 100%"
                placeholder="请输入榜单名称"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <div class="table-operate-box">
        <a-button type="primary" @click="() => handleCreate()">
          <plus-circle-outlined />
          新增
        </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #headerCell="{ column }">
          <!-- 分类状态 -->
          <template v-if="column.key == 'status'"
            ><div>
              状态&nbsp;
              <a-tooltip placement="bottom" title="上下架请去专区管理操作">
                <question-circle-filled />
              </a-tooltip></div
          ></template>
        </template>
        <template #bodyCell="{ column, record }">
          <!-- 分类状态 -->
          <template v-if="column.key == 'status'">
            {{
              record.status === "0"
                ? "下架"
                : record.status === "1"
                ? "上架"
                : ""
            }}
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css"
              @click="handleEdit(record)"
            >
              编辑
            </a-button>
            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelte(record.searchShowRankId)"
            >
              <a-button type="link" class="btn-css"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <add-modal :open="isOpen" @is-modal-open="handleOk"></add-modal>
    <edit-modal
      :open="isEditOpen"
      :data="isData"
      @is-modal-open="handleEditOk"
    ></edit-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS } from "./constants";
import { getRankPage, getRemoveRank } from "@/api/cms/searchRank";
import addModal from "./addModal/index.vue";
import editModal from "./editModal/index.vue";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({ zoneName: undefined });
const isDetail = ref(false);
onMounted(async () => {
  getPageList();
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
    };
    const res = await getRankPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
// 新增
const isOpen = ref(false);
const handleCreate = () => {
  isOpen.value = !isOpen.value;
};
const handleOk = (e) => {
  isOpen.value = e;
  getPageList();
};
//编辑
const isEditOpen = ref(false);
const isData = ref();
const handleEdit = (record) => {
  isData.value = record;
  isEditOpen.value = !isEditOpen.value;
};
const handleEditOk = (e) => {
  isEditOpen.value = e;
  getPageList();
};
// 删除
const handleDelte = async (id) => {
  try {
    const res = await getRemoveRank(id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("删除成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
.table-columns {
  height: calc(100vh - 242px);
}
</style>
