<template>
  <a-modal
    v-model:open="isOpen"
    width="520px"
    title="新增专区"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="专区名称"
            name="name"
            :rules="[{ required: true, message: '请输入专区名称' }]"
          >
            <a-input
              v-model:value="formModalData.name"
              allow-clear
              maxlength="32"
              show-count
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import { getAddSection } from "@/api/goodsCenter/newZone";
import router from "@/router";

const isOpen = ref(false);
const formModalData = reactive({ name: "" });
const loading = ref(false);
const formModalRef = ref(null);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
});

const emit = defineEmits(["isModalOpen"]);

// 新增
const getAdd = async () => {
  const params = {
    name: formModalData.name,
  };
  try {
    const res = await getAddSection(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isOpen.value = false;
    emit("isModalOpen", false);
    router.push({
      path: "/cms/shopPreferred/newZone/newZoneEdit",
      query: {
        id: res.data,
      },
    });
  } catch (error) {
    message.error(error.message);
  }
};

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getAdd();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped></style>
