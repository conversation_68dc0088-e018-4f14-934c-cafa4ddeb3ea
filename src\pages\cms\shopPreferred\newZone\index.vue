<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="6">
            <a-form-item label="专区名称" name="name">
              <a-input
                v-model:value="formData.name"
                allow-clear
                style="width: 100%"
                placeholder="请输入专区名称"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="创建时间" name="time">
              <a-range-picker
                v-model:value="formData.time"
                :locale="locale"
                :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('23:59:59', 'HH:mm:ss'),
                  ],
                }"
                allow-clear
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <div class="table-operate-box">
        <a-button
          style="margin: 0 15px 0 0"
          type="primary"
          @click="() => handleCreate()"
        >
          <plus-circle-outlined />
          新增
        </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button
              type="link"
              class="btn-css"
              @click="handleEdit(record)"
            >
              编辑
            </a-button>

            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelte(record.id)"
            >
              <a-button type="link" class="btn-css"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <add-modal :open="isOpen" @is-modal-open="handleOk"></add-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { PlusCircleOutlined } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import { ref, onMounted, reactive, watch } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getPage, getSectionDelete } from "@/api/goodsCenter/newZone";
import addModal from "./addModal/index.vue";
import router from "@/router";
import dayjs from "dayjs";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({ name: undefined, time: undefined });
const isDetail = ref(false);
onMounted(async () => {
  getPageList();
});

watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (newVal.path === "/cms/shopPreferred/newZone/newZoneEdit") {
      isDetail.value = true;
    } else {
      isDetail.value = false;
    }
  },
  {
    immediate: true,
  }
);

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      startTime: formData.time ? formData.time[0] : null,
      endTime: formData.time ? formData.time[1] : null,
      ...formData,
    };
    delete params.time;
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
// 新增
const isOpen = ref(false);
const handleCreate = () => {
  isOpen.value = !isOpen.value;
};
const handleOk = (e) => {
  isOpen.value = e;
  getPageList();
};
//编辑
const handleEdit = (record) => {
  router.push({
    path: "/cms/shopPreferred/newZone/newZoneEdit",
    query: {
      id: record.id,
    },
  });
};
// 删除
const handleDelte = async (id) => {
  try {
    const res = await getSectionDelete(id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("删除成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
