<template>
  <a-modal
    v-model:open="isOpen"
    width="520px"
    title="新增分类"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="外显分类名称"
            name="displayName"
            :rules="[{ required: true, message: '请输入外显分类名称' }]"
          >
            <a-input
              v-model:value="formModalData.displayName"
              allow-clear
              maxlength="32"
              show-count
              style="width: 100%"
              placeholder="请输入外显分类名称"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="上/下架"
            name="status"
            :rules="[{ required: true, message: '请选择上/下架' }]"
          >
            <a-radio-group v-model:value="formModalData.status">
              <a-radio :value="1">上架</a-radio>
              <a-radio :value="0">下架</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import { getAddCategory } from "@/api/goodsCenter/newZone";

const isOpen = ref(false);
const formModalData = reactive({ displayName: "", status: 0 });
const loading = ref(false);
const formModalRef = ref(null);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },

  id: {
    type: String,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
});

const emit = defineEmits(["isModalOpen", "isModalShowOpen"]);

// 新增
const getAdd = async () => {
  const params = {
    sectionId: props.id,
    ...formModalData,
  };
  try {
    const res = await getAddCategory(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("新增成功");
    isOpen.value = false;
    formModalRef.value.resetFields();
    emit("isModalShowOpen", res.data);
  } catch (error) {
    message.error(error.message);
  }
};

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getAdd();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped></style>
