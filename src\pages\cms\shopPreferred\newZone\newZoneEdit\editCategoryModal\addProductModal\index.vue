<template>
  <a-drawer
    v-model:open="isOpen"
    width="80%"
    title="选择商品"
    :destroy-on-close="true"
    :footer-style="{ textAlign: 'right' }"
    @close="handleCancel"
  >
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
        <a-col class="gutter-row" :span="6">
          <a-form-item label="商品名称" name="prodName">
            <a-input
              v-model:value="formData.prodName"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品名称"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="后台分类" name="platCategoryIdList">
            <a-cascader
              v-model:value="formData.platCategoryIdList"
              :options="platCategoryIdOptions"
              change-on-select
              :field-names="{
                label: 'categoryName',
                value: 'categoryId',
                children: 'newCategoryModelDtos',
              }"
              placeholder="请选择平台分类"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="三方分类" name="thirdPlatCategoryIdList">
            <a-cascader
              v-model:value="formData.thirdPlatCategoryIdList"
              :options="thirdPlatCategoryIdOptions"
              :load-data="loadThirdData"
              :change-on-select="true"
              :field-names="{
                label: 'thirdCategoryName',
                value: 'id',
                children: 'children',
              }"
              placeholder="请选择三方分类"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="供应链" name="prodSource">
            <a-select
              v-model:value="formData.prodSource"
              show-search
              option-filter-prop="name"
              placeholder="请选择供应链"
              :options="prodSourceOptions"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="供货仓" name="supplierId">
            <a-select
              v-model:value="formData.supplierId"
              show-search
              option-filter-prop="supplierName"
              placeholder="请选择供货仓"
              :options="supplierIdOptions"
              :field-names="{ label: 'supplierName', value: 'supplierId' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="三方商品ID" name="thirdProdId">
            <a-input
              v-model:value="formData.thirdProdId"
              allow-clear
              style="width: 100%"
              placeholder="请输入三方商品ID"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item
            :wrapper-col="{ span: 24, offset: 0 }"
            style="align-self: flex-end; text-align: left"
            label="&nbsp;"
          >
            <a-button type="primary" @click="onSubmit">搜索</a-button>
            <a-button style="margin-left: 10px" @click="reset">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="table-columns">
      <div class="table-operate-box">
        <a-button type="primary" @click="handleAddProduct">
          上传全部筛选结果
        </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        rowKey="productId"
        :row-selection="{ onChange: onSelectChange }"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 供应链 -->
          <template v-if="column.key == 'prodSource'">
            <span v-for="(item, index) in prodSourceOptions" :key="index">{{
              item.id === record.prodSource ? item.name : ""
            }}</span>
          </template>
          <!-- 售价 -->
          <template v-if="column.key == 'oriPrice'">
            {{
              record.minPrice === record.maxPrice
                ? record.maxPrice
                : record.maxPrice + "-" + record.minPrice
            }}
          </template>
          <!-- 商品信息 -->
          <template v-if="column.key == 'productName'">
            <a-space>
              <div v-if="record.picUrl.includes('https')">
                <a-image :src="record?.picUrl" width="60px" height="60px" />
              </div>
              <div v-else>
                <a-image
                  :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${record?.picUrl}`"
                  width="50px"
                  height="50px"
                />
              </div>
              <a-space direction="vertical">
                <div style="text-align: left">{{ record.prodName }}</div>
                <div style="text-align: left">{{ record.spuNumber }}</div>
              </a-space>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleOk"
        >确定</a-button
      >
    </template>
  </a-drawer>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getThCategoryList,
  getSelectSrction,
  getSupplierPage,
  getByParams,
  getAllBatch,
  getBatchInsert,
} from "@/api/goodsCenter/newZone";
// import { queryCategory } from "@/api/common";
import { fetchCategory } from "@/utils";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const formData = reactive({
  id: undefined,
  prodName: undefined,
  platCategoryIdList: undefined,
  thirdPlatCategoryIdList: undefined,
  prodSource: undefined,
  supplierId: undefined,
  thirdProdId: undefined,
});
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const isOpen = ref(false);
const loading = ref(false);
const platCategoryIdOptions = ref([{ value: "", label: "", children: [] }]); //平台分类
const thirdPlatCategoryIdOptions = ref([]); //三方分类
const prodSourceOptions = ref([]); //供应链
const supplierIdOptions = ref([]); //供货仓
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    defaule: "",
  },
  sectionid: {
    type: String,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  if (props.sectionid) {
    getPageList();
    getCategory();
    getThirdCategory();
    getProdSourceOptions();
    getSupplier();
  }
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params: any = {
      page: pagination.current,
      size: pagination.pageSize,
      ...formData,
    };
    if (formData.platCategoryIdList?.length) {
      params.firstCategoryId = formData.platCategoryIdList[0];
      params.secondCategoryId = formData.platCategoryIdList[1];
      params.threeCategoryId = formData.platCategoryIdList[2];
      delete params.platCategoryIdList;
    } else {
      params.firstCategoryId = undefined;
      params.secondCategoryId = undefined;
      params.threeCategoryId = undefined;
    }
    const res = await getByParams(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

//获取平台分类
const getCategory = async () => {
  platCategoryIdOptions.value = await fetchCategory();
  // fetchCategory().then((res) => {
  //   if (res.data) {
  //     // res.data.forEach((item) => {
  //     //   item.isLeaf = false;
  //     // });
  //     platCategoryIdOptions.value = res.data;
  //   }
  // });
};
// //获取二级分类
// const loadSelectData = (selectedOptions: any) => {

//   const targetOption = selectedOptions[selectedOptions.length - 1];
//   targetOption.loading = true;
//   getCategoryList(targetOption.categoryId)
//     .then((res) => {
//       targetOption.children = res.data;
//     })
//     .finally(() => {
//       targetOption.loading = false;
//     });
// };

//获取三方分类
const getThirdCategory = () => {
  getThCategoryList("0").then((res) => {
    if (res.data) {
      res.data.forEach((item) => {
        item.isLeaf = false;
      });
      thirdPlatCategoryIdOptions.value = res.data;
    }
  });
};
//获取获取三方分类二级分类
const loadThirdData = (selectedOptions: any) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  getThCategoryList(targetOption.id)
    .then((res) => {
      targetOption.children = res.data;
    })
    .finally(() => {
      targetOption.loading = false;
    });
};

//获取供应链

const getProdSourceOptions = async () => {
  try {
    const res = await getSelectSrction();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    prodSourceOptions.value = res.data;
  } catch (error) {
    message.error(error.message);
  }
};

//获取供货仓

const getSupplier = async () => {
  try {
    const res = await getSupplierPage();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    supplierIdOptions.value = res.data.records;
  } catch (error) {
    message.error(error.message);
  }
};

//上传全部筛选结果

const handleAddProduct = async () => {
  const params = {
    sectionId: props.sectionid,
    sectionCategoryId: props.id,
    ...formData,
  };
  try {
    const res = await getAllBatch(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    reset();
    isOpen.value = false;
    emit("isAddOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const isSelectRowKey = ref();
const isSelectedRows = ref();
const onSelectChange = (selectedRowKeys, selectedRows) => {
  isSelectRowKey.value = selectedRows;
  let parmas: any[] = [];
  let list = {};
  isSelectRowKey.value.map((item: any, i: number) => {
    list = {
      sectionId: props.sectionid,
      sectionCategoryId: props.id,
      prodId: item.productId,
      supplierId: item.supplierId,
      prodSource: item.prodSource,
    };
    parmas.push(list);
  });
  isSelectedRows.value = parmas;
};

//多选
const handleAddInsert = async () => {
  try {
    const res = await getBatchInsert(isSelectedRows.value);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    reset();
    isOpen.value = false;
    emit("isAddOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const emit = defineEmits(["isAddOpen"]);
const handleCancel = () => {
  isOpen.value = false;
  emit("isAddOpen", false);
};
const handleOk = () => {
  if (!isSelectedRows.value) {
    return message.error("请选择商品");
  }
  handleAddInsert();
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
