<template>
  <a-drawer
    v-model:open="isOpen"
    width="80%"
    title="编辑分类"
    :destroy-on-close="true"
    :footer-style="{ textAlign: 'right' }"
    @close="handleCancel"
  >
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
        <a-col class="gutter-row" :span="6">
          <a-form-item label="商品ID" name="id">
            <a-input
              v-model:value="formData.id"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品ID"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="商品名称" name="prodName">
            <a-input
              v-model:value="formData.prodName"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品名称"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="后台分类" name="platCategoryIdList">
            <a-cascader
              v-model:value="formData.platCategoryIdList"
              change-on-select
              :options="platCategoryIdOptions"
              :field-names="{
                label: 'categoryName',
                value: 'categoryId',
                children: 'newCategoryModelDtos',
              }"
              placeholder="请选择平台分类"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="三方分类" name="thirdPlatCategoryIdList">
            <a-cascader
              v-model:value="formData.thirdPlatCategoryIdList"
              :options="thirdPlatCategoryIdOptions"
              :load-data="loadThirdData"
              :change-on-select="true"
              :field-names="{
                label: 'thirdCategoryName',
                value: 'id',
                children: 'children',
              }"
              placeholder="请选择平台分类"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="供应链" name="prodSource">
            <a-select
              v-model:value="formData.prodSource"
              show-search
              option-filter-prop="name"
              placeholder="请选择专区"
              :options="prodSourceOptions"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="供货仓" name="supplierId">
            <a-select
              v-model:value="formData.supplierId"
              show-search
              option-filter-prop="supplierName"
              placeholder="请选择专区"
              :options="supplierIdOptions"
              :field-names="{ label: 'supplierName', value: 'supplierId' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>

        <a-col class="gutter-row" :span="6">
          <a-form-item
            :wrapper-col="{ span: 24, offset: 0 }"
            style="align-self: flex-end; text-align: left"
            label="&nbsp;"
          >
            <a-button type="primary" @click="onSubmit">搜索</a-button>
            <a-button style="margin-left: 10px" @click="reset">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="table-columns">
      <div class="table-operate">
        <div class="table-operate-box-left">
          <span style="margin: 0 15px 0 0"
            >外显明细：{{ isDetail?.displayName }}</span
          >
          <span>状态：{{ isDetail?.statusName }}</span>
        </div>
        <div class="table-operate-box">
          <a-popconfirm
            title="确定清空全部商品？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleAllDelete()"
          >
            <a-button type="primary" ghost style="margin: 0 15px 0 0">
              清空全部商品
            </a-button>
          </a-popconfirm>
          <a-popconfirm
            title="确定删除？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleBatchDelete()"
          >
            <a-button type="primary" ghost style="margin: 0 15px 0 0">
              批量删除
            </a-button>
          </a-popconfirm>
          <a-button
            type="primary"
            @click="handleEditExplicit"
            ghost
            style="margin: 0 15px 0 0"
          >
            修改外显属性
          </a-button>
          <a-button type="primary" @click="handleAddProduct">
            添加商品
          </a-button>
        </div>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        rowKey="id"
        :row-selection="{ onChange: onSelectChange }"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 商品信息 -->
          <template v-if="column.key == 'productName'">
            <a-space>
              <div v-if="record.productUrl.includes('https')">
                <a-image :src="record?.productUrl" width="60px" height="60px" />
              </div>
              <div v-else>
                <a-image
                  :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${record?.productUrl}`"
                  width="50px"
                  height="50px"
                />
              </div>
              <a-space direction="vertical">
                <div style="text-align: left">{{ record.productName }}</div>
                <div style="text-align: left">{{ record.supplierId }}</div>
              </a-space>
            </a-space>
          </template>
          <!-- 商品状态 -->
          <template v-if="column.key == 'status'">
            {{
              record.status === 0
                ? "下架"
                : record.status === 1
                ? "上架"
                : record.status === 2
                ? "违规下架"
                : record.status === 3
                ? "第三方下架"
                : ""
            }}
          </template>
          <!-- 售价 -->
          <template v-if="column.key == 'price'">
            {{
              record.minPrice === record.maxPrice
                ? record.maxPrice
                : record.maxPrice + "-" + record.minPrice
            }}
          </template>
          <!-- 排序 -->
          <template v-if="column.key == 'sortOrder'">
            <a-input-number
              v-if="isInp === record.id"
              @blur="handleSort"
              v-model:value="record.sortOrder"
              :max="999999999"
              placeholder="请输入排序"
            />
            <div v-else>
              <a-input-number
                :disabled="true"
                v-model:value="record.sortOrder"
              ></a-input-number>
              <edit-filled
                @click="handleEditSort(record.id)"
                style="color: #5599ff; cursor: pointer; margin: 0 0 0 10px"
              />
            </div>
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record.id)"
            >
              <a-button type="link" class="btn-css" danger> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <!-- <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleOk"
        >确定</a-button
      >
    </template> -->
    <!-- 修改外显属性 -->
    <edit-modal
      :open="isEditOpen"
      :id="props.isecd"
      :sectionid="props.id"
      @is-modal-open="handleEditOk"
    ></edit-modal>
    <!-- 添加商品 -->
    <add-modal
      :open="isAddOpen"
      :id="props.isecd"
      :sectionid="props.id"
      @is-add-open="handleAddOk"
    ></add-modal>
  </a-drawer>
</template>
<script setup lang="ts">
import { EditFilled } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getCategoryList,
  getThCategoryList,
  getSelectSrction,
  getSupplierPage,
  getCategoryProductList,
  getCategoryEditSort,
  getCategoryDelete,
  getCategoryAllDelete,
  getBatchDelete,
} from "@/api/goodsCenter/newZone";
// import { queryCategory } from "@/api/common";
import { fetchCategory } from "@/utils";
import editModal from "./editExplicitModal/index.vue";
import AddModal from "./addProductModal/index.vue";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const formData = reactive({
  id: undefined,
  prodName: undefined,
  platCategoryIdList: undefined,
  thirdPlatCategoryIdList: undefined,
  prodSource: undefined,
  supplierId: undefined,
});
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const isOpen = ref(false);
const loading = ref(false);
const platCategoryIdOptions = ref([{ value: "", label: "", children: [] }]); //平台分类
const thirdPlatCategoryIdOptions = ref([]); //三方分类
const prodSourceOptions = ref([]); //供应链
const supplierIdOptions = ref([]); //供货仓
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    defaule: "",
  },
  isecd: {
    type: String,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  if (newProps.isecd) {
    getPageList();
    getCategory();
    getThirdCategory();
    getProdSourceOptions();
    getSupplier();
  }
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const isDetail = ref();

const getPageList = async () => {
  try {
    isLoading.value = true;
    const params: any = {
      page: pagination.current,
      size: pagination.pageSize,
      sectionId: props.id,
      sectionCategoryId: props.isecd,
      ...formData,
    };
    if (formData.platCategoryIdList?.length) {
      params.firstCategoryId = formData.platCategoryIdList[0];
      params.secondCategoryId = formData.platCategoryIdList[1];
      params.threeCategoryId = formData.platCategoryIdList[2];
      delete params.platCategoryIdList;
    }
    const res = await getCategoryProductList(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.sectionCategoryProductVOS;
    pagination.total = res.data.total;
    isDetail.value = {
      displayName: res.data.displayName,
      statusName: res.data.status,
    };
  } catch (error) {
    message.error(error.message);
  }
};

//获取平台分类
const getCategory = async () => {
  platCategoryIdOptions.value = await fetchCategory();
  // fetchCategory().then((res) => {
  //   if (res.data) {
  //     platCategoryIdOptions.value = res.data;
  //   }
  // });
};
//获取二级分类
// const loadSelectData = (selectedOptions: any) => {
//   const targetOption = selectedOptions[selectedOptions.length - 1];
//   targetOption.loading = true;
//   getCategoryList(targetOption.categoryId)
//     .then((res) => {
//       targetOption.children = res.data;
//     })
//     .finally(() => {
//       targetOption.loading = false;
//     });
// };

//获取三方分类
const getThirdCategory = () => {
  getThCategoryList("0").then((res) => {
    if (res.data) {
      res.data.forEach((item) => {
        item.isLeaf = false;
      });
      thirdPlatCategoryIdOptions.value = res.data;
    }
  });
};
//获取获取三方分类二级分类
const loadThirdData = (selectedOptions: any) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  getThCategoryList(targetOption.id)
    .then((res) => {
      targetOption.children = res.data;
    })
    .finally(() => {
      targetOption.loading = false;
    });
};

//获取供应链

const getProdSourceOptions = async () => {
  try {
    const res = await getSelectSrction();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    prodSourceOptions.value = res.data;
  } catch (error) {
    message.error(error.message);
  }
};

//获取供货仓

const getSupplier = async () => {
  try {
    const res = await getSupplierPage();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    supplierIdOptions.value = res.data.records;
  } catch (error) {
    message.error(error.message);
  }
};

//排序
const getSort = async () => {
  const params = {
    id: isSortId.value,
    sortNum: isSortText.value,
  };
  try {
    const res = await getCategoryEditSort(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

const isInp = ref();
const isSortText = ref();
const isSortId = ref();
const handleSort = (e) => {
  isSortText.value = e.target.value;
  isInp.value = "-1";
  getSort();
};

const handleEditSort = (e) => {
  isInp.value = e;
  isSortId.value = e;
};

//删除列表

const handleDelete = async (e) => {
  try {
    const res = await getCategoryDelete(e);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
//删除全部
const handleAllDelete = async () => {
  try {
    const res = await getCategoryAllDelete(props.isecd);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

//批量删除
const isSelectRowKey = ref();
const onSelectChange = (selectedRowKeys) => {
  isSelectRowKey.value = selectedRowKeys;
};
const handleBatchDelete = async () => {
  if (!isSelectRowKey.value) {
    message.error("请选择要删除的商品");
    return;
  }
  try {
    const res = await getBatchDelete(isSelectRowKey.value);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
//修改外显属性
const isEditOpen = ref(false);
const handleEditExplicit = () => {
  isEditOpen.value = !isEditOpen.value;
};
const handleEditOk = (e) => {
  isEditOpen.value = e;
  getPageList();
};

//添加商品
const isAddOpen = ref(false);
const handleAddProduct = () => {
  isAddOpen.value = !isAddOpen.value;
};
const handleAddOk = (e) => {
  isAddOpen.value = e;
  getPageList();
};
const emit = defineEmits(["isModalOpen"]);
const handleCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
// const handleOk = () => {
//   isOpen.value = false;
//   emit("isModalOpen", false);
// };
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
