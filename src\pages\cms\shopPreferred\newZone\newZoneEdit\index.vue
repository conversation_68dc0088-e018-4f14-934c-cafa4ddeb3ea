<template>
  <div v-if="!isDetail">
    <div class="table-columns">
      <div class="table-operate">
        <div class="table-operate-box-left">
          <div>
            专区名称：{{ isInfo?.name
            }}<a-button
              ghost
              style="margin: 0 15px 0 20px"
              type="primary"
              @click="() => handleEditName()"
            >
              修改
            </a-button>
          </div>
          <div>分类列表</div>
        </div>
        <div class="table-operate-box">
          <span>专区内搜索：</span>
          <a-switch
            @click="handleSwitch"
            v-model:checked="isSwitch"
            checked-children="开启"
            un-checked-children="关闭"
          />
          <a-button
            ghost
            style="margin: 0 15px 0 15px"
            type="primary"
            @click="() => handleSetBanner()"
          >
            设置Banner
          </a-button>
          <a-button type="primary" @click="() => handleCreateClass()">
            <plus-circle-outlined />
            新增分类
          </a-button>
        </div>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 排序 -->
          <template v-if="column.key == 'sortOrder'">
            <a-input-number
              v-if="isInp === record.id"
              @blur="handleSort"
              v-model:value="record.sortOrder"
              :max="999999999"
              placeholder="请输入排序"
            />
            <div v-else>
              <a-input-number
                :disabled="true"
                v-model:value="record.sortOrder"
              ></a-input-number>
              <edit-filled
                @click="handleEditSort(record.id)"
                style="color: #5599ff; cursor: pointer; margin: 0 0 0 10px"
              />
            </div>
          </template>
          <!-- 状态 -->
          <template v-if="column.key == 'status'">
            {{ record.status === 1 ? "上架" : "下架" }}
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css" @click="() => handleUpDown(record.id)">
              {{ record.status === 1 ? "下架" : "上架" }}
            </a-button>
            <a-button type="link" class="btn-css" @click="handleEdit(record.id)">
              编辑
            </a-button>

            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelte(record)"
            >
              <a-button type="link" class="btn-css" danger> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <edit-modal
      :open="isOpen"
      :name="isInfo?.name"
      :id="isId"
      :status="isStatus"
      @is-modal-open="handleNameOk"
    ></edit-modal>
    <banner-modal
      :open="isSetBannerOpen"
      :id="isId"
      @is-modal-open="handleBannerOk"
    ></banner-modal>
    <add-category-modal
      :open="isCategoryOpen"
      :id="isId"
      @is-modal-show-open="handleCategoryShowOk"
      @is-modal-open="handleCategoryOk"
    ></add-category-modal>
    <edit-category-modal
      :open="isEditCategoryOpen"
      :id="isId"
      :isecd="iscareId"
      @is-modal-open="handleEditCategoryOk"
    ></edit-category-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { PlusCircleOutlined, EditFilled } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS } from "./constants";
import {
  getSectionDetail,
  getSectionList,
  getEditSort,
  getUpAndDown,
  getSwitchSearch,
  getDeleteCategory,
} from "@/api/goodsCenter/newZone";
import { useRoute } from "vue-router";
import editModal from "./editModal/index.vue";
import bannerModal from "./setBanner/index.vue";
import addCategoryModal from "./addCategoryModal/index.vue";
import editCategoryModal from "./editCategoryModal/index.vue";

const isLoading = ref(true);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const isDetail = ref(false);
const route = useRoute();
const isId = ref();
const isInfo = ref();

onMounted(async () => {
  if (route.query.id) {
    isId.value = route.query.id;
    getDetail();
    getPageList();
  }
});

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      sectionId: isId.value,
    };
    const res = await getSectionList(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

const isSwitch = ref();
const isStatus = ref();
// 获取详情
const getDetail = async () => {
  try {
    const res = await getSectionDetail(isId.value);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isInfo.value = res.data;
    isSwitch.value = res.data.searchEnabled === 1 ? true : false;
    isStatus.value = res.data.status === "上架" ? 1 : 0;
  } catch (error) {
    message.error(error.message);
  }
};

//排序
const getSort = async () => {
  const params = {
    id: isSortId.value,
    sortOrder: isSortText.value,
  };
  try {
    const res = await getEditSort(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
const isInp = ref();
const isSortText = ref();
const isSortId = ref();
const handleSort = (e) => {
  isSortText.value = e.target.value;
  isInp.value = "-1";
  getSort();
};
const handleEditSort = (e) => {
  isInp.value = e;
  isSortId.value = e;
};

//上下架
const handleUpDown = async (id) => {
  try {
    const res = await getUpAndDown(id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

//修改专区名称
const isOpen = ref(false);

const handleEditName = () => {
  isOpen.value = !isOpen.value;
};

const handleNameOk = (e) => {
  isOpen.value = e;
  getDetail();
};

//专区搜索开关
const handleSwitch = async (e) => {
  isSwitch.value = e;
  try {
    const res = await getSwitchSearch(isId.value);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getDetail();
  } catch (error) {
    message.error(error.message);
  }
};

//设置bannner属性
const isSetBannerOpen = ref(false);
const handleSetBanner = () => {
  isSetBannerOpen.value = !isSetBannerOpen.value;
};
const handleBannerOk = (e) => {
  isSetBannerOpen.value = e;
};

//新增分类
const isCategoryOpen = ref();
const handleCreateClass = () => {
  isCategoryOpen.value = !isCategoryOpen.value;
};
const handleCategoryShowOk = (e) => {
  isCategoryOpen.value = false;
  iscareId.value = e;
  getPageList();
  isEditCategoryOpen.value = true;
};
const handleCategoryOk = (e) => {
  isCategoryOpen.value = e;
};
//编辑
const iscareId = ref();
const isEditCategoryOpen = ref(false);
const handleEdit = (e) => {
  isEditCategoryOpen.value = !isEditCategoryOpen.value;
  iscareId.value = e;
};
const handleEditCategoryOk = (e) => {
  isEditCategoryOpen.value = e;
};
//删除
const handleDelte = async (data) => {
  if (data.statusName === "上架") {
    return message.error("上架状态不允许删除");
  }
  try {
    const res = await getDeleteCategory(data.id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("删除成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
