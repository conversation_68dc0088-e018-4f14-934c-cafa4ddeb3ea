<template>
  <a-modal
    v-model:open="isOpen"
    width="600px"
    title="编辑Banner属性"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="图片名称（对内展示）"
            name="bannerName"
            :rules="[{ required: true, message: '请输入专区名称' }]"
          >
            <a-input
              v-model:value="formModalData.bannerName"
              allow-clear
              maxlength="10"
              show-count
              style="width: 100%"
              placeholder="请输入图片名称"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item
            label="当前排序"
            name="sortOrder"
            :rules="[{ required: true, message: '请输入当前排序' }]"
          >
            <a-input-number
              v-model:value="formModalData.sortOrder"
              allow-clear
              :min="0"
              :max="999999999"
              style="width: 100%"
              placeholder="请输入数字"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="状态" name="status">
            <a-radio-group v-model:value="formModalData.status">
              <a-radio :value="1">上架</a-radio>
              <a-radio :value="0">下架</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="支持跳转" name="skip">
            <a-radio-group
              v-model:value="formModalData.skip"
              @change="handleSkipChange"
            >
              <a-radio :value="1">支持</a-radio>
              <a-radio :value="0">不支持</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col v-if="formModalData.skip === 1" class="gutter-row" :span="24">
          <a-form-item label="跳转分类" name="redirectType">
            <a-select
              v-model:value="formModalData.redirectType"
              placeholder="请选择分类"
              allow-clear
              style="width: 100%"
            >
              <a-select-option value="内部跳转">内部跳转</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col v-if="formModalData.skip === 1" class="gutter-row" :span="24">
          <a-form-item label="选择专区" name="targetSectionId">
            <a-select
              v-model:value="formModalData.targetSectionId"
              show-search
              option-filter-prop="name"
              placeholder="请选择专区"
              :options="transGroupIdOptions"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="Banner图" name="url">
            <wd-upload
              biz-type="cms"
              source="life_plat"
              :max-count="1"
              @get-url-list="afferentUrlChange"
              :file-list="fileList"
            />
            <p>
              支持格式：JPG/PNG/SVG 限制大小：200KB 尺寸：710X240
            </p></a-form-item
          >
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import WdUpload from "@/components/WdUpload/index.vue";
import {
  getBannerDetail,
  getAddBanner,
  getSelectSection,
  getUpdateBanner,
} from "@/api/goodsCenter/newZone";

const isOpen = ref(false);
const formModalData = reactive({
  bannerName: "",
  sortOrder: "",
  status: 0,
  skip: 0,
  redirectType: "",
  targetSectionId: "",
  url: "",
});
const loading = ref(false);
const formModalRef = ref(null);
const transGroupIdOptions = ref();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  name: {
    type: String,
    default: "",
  },
  sectionid: {
    type: String,
    defaule: "",
  },
  id: {
    type: String,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  getDetail();
  getSection();
});

//详情
const getDetail = async () => {
  try {
    const res = await getBannerDetail(props.id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    Object.assign(formModalData, res.data);
    fileList.value = [{ name: "", url: res.data.url.split("?wdISI")[0] }];
  } catch (error) {
    message.error(error.message);
  }
};

// 编辑
const getEdit = async () => {
  const params = {
    sectionId: props.sectionid,
    id: props.id,
    ...formModalData,
  };
  try {
    const res = await getUpdateBanner(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("编辑成功");
    isOpen.value = false;
    emit("isScrollOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

//选择专区

const getSection = async () => {
  try {
    const res = await getSelectSection("SECTIONNAME");
    if (res.code !== 0) {
      return message.error(res.message);
    }
    transGroupIdOptions.value = res.data;
  } catch (error) {
    message.error(error.message);
  }
};

//支持跳转选择
const handleSkipChange = (e) => {
  formModalData.skip = e.target.value;
};
//图片上传
const fileList = ref([]);
const afferentUrlChange = (data) => {
  if (data && data.length) {
    formModalData.url = data[0].url;
    fileList.value = [{ name: "", url: data[0].url }];
  } else {
    formModalData.url = "";
    fileList.value = [];
  }
};

const emit = defineEmits(["isScrollOpen"]);

const handleModalCancel = () => {
  isOpen.value = false;
  emit("isScrollOpen", false);
};
const handleModalOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getEdit();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped></style>
