<template>
  <a-modal
    v-model:open="isScrollOpen"
    width="520px"
    title="Banner轮播属性"
    :destroy-on-close="true"
    @cancel="handleCancel"
  >
    <a-form ref="formModalRef" :model="formModalData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item label="轮播属性" name="bannerCarouselEnabled">
            <a-radio-group v-model:value="formModalData.bannerCarouselEnabled">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="12">
          <a-form-item label="滚动间隔" name="bannerScrollInterval">
            <a-input-number
              v-model:value="formModalData.bannerScrollInterval"
              :min="0.1"
              addon-after="秒"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="12" style="margin: 33px 0 0 0"
          ><span>精确到0.1s</span></a-col
        >
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { FROM_DATA } from "./constants";
import { getSetBannerSection } from "@/api/goodsCenter/newZone";
import { id } from "@/pages/imageConfiguration/components/setData";

const isScrollOpen = ref(false);
const formModalData = reactive({
  bannerCarouselEnabled: "",
  bannerScrollInterval: "",
});
const loading = ref(false);
const formModalRef = ref(null);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  sectionid: {
    type: String,
    defaule: "",
  },
  info: {
    type: Object,
    default: "",
  },
});
watch(props, (newProps) => {
  isScrollOpen.value = newProps.open;
  Object.assign(formModalData, newProps.info);
});
const emit = defineEmits(["isScrollOpen"]);

// 新增
const getAdd = async () => {
  const params = {
    id: props.sectionid,
    ...formModalData,
  };
  try {
    const res = await getSetBannerSection(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("修改成功");
    isScrollOpen.value = false;
    emit("isScrollOpen", false);
  } catch (error) {
    message.error(error.message);
  }
};

const handleCancel = () => {
  isScrollOpen.value = false;
  emit("isScrollOpen", false);
};
const handleOk = () => {
  formModalRef.value
    .validate()
    .then(() => {
      getAdd();
    })
    .catch(() => {
      message.error("请完善信息");
    });
};
</script>
<style lang="less" scoped></style>
