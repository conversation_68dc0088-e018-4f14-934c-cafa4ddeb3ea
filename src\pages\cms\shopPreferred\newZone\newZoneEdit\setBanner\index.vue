<template>
  <a-drawer
    v-model:open="isOpen"
    width="80%"
    title="设置Banner"
    :destroy-on-close="true"
    :footer-style="{ textAlign: 'right' }"
    @close="handleCancel"
  >
    <div class="table-columns">
      <div class="table-operate">
        <div class="table-operate-box-left">
          <span style="margin: 0 15px 0 0"
            >轮播属性：{{
              isInfo?.bannerCarouselEnabled === 0 ? "关闭" : "开启"
            }}</span
          >
          <span>滚动间隔：{{ isInfo?.bannerScrollInterval }}</span>
        </div>
        <div class="table-operate-box">
          <a-button
            @click="handleEditScroll"
            type="primary"
            ghost
            style="margin: 0 15px 0 0"
          >
            编辑轮播属性
          </a-button>
          <a-button type="primary" @click="handleAddImg"> 新增图片 </a-button>
        </div>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 图片预览 -->
          <template v-if="column.key == 'url'">
            <a-image
              :width="80"
              :height="80"
              :src="record?.url ? record?.url.split('?wdISI')[0] : 'error'"
              alt=""
            />
          </template>
          <!-- 状态 -->
          <template v-if="column.key == 'status'"
            >{{ record.status === 1 ? "上架" : "下架" }}
          </template>
          <!-- 排序 -->
          <template v-if="column.key == 'sortOrder'">
            <a-input-number
              v-if="isInp === record.id"
              @blur="handleSort"
              v-model:value="record.sortOrder"
              :max="999999999"
              placeholder="请输入排序"
            />
            <div v-else>
              <a-input-number
                :disabled="true"
                v-model:value="record.sortOrder"
              ></a-input-number>
              <edit-filled
                @click="handleEditSort(record.id)"
                style="color: #5599ff; cursor: pointer; margin: 0 0 0 10px"
              />
            </div>
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css" @click="handleUpDwon(record.id)">
              {{ record.status === 1 ? "下架" : "上架" }}
            </a-button>
            <a-button type="link" class="btn-css" @click="handleEditImg(record.id)">
              编辑
            </a-button>

            <a-popconfirm
              title="确定删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="btn-css" danger> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleOk"
        >确定</a-button
      >
    </template>
    <edit-scroll-modal
      :open="isScrollOpen"
      :info="isInfo"
      :sectionid="props.id"
      @is-scroll-open="handleScrollOk"
    ></edit-scroll-modal>
    <add-attribute-modal
      :open="isAttributeOpen"
      :info="isInfo"
      :sectionid="props.id"
      @is-scroll-open="handleAttributeOk"
    ></add-attribute-modal>
    <edit-attribute-modal
      :open="isEditAttributeOpen"
      :info="isInfo"
      :sectionid="props.id"
      :id="isId"
      @is-scroll-open="handleEditAttributeOk"
    ></edit-attribute-modal>
  </a-drawer>
</template>
<script setup lang="ts">
import { EditFilled } from "@ant-design/icons-vue";
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { COLUMNS } from "./constants";
import {
  getSectionDetail,
  getBannerList,
  getEditBannerSort,
  getBannerUpAndDown,
  getDeleteBanner,
} from "@/api/goodsCenter/newZone";
import editScrollModal from "./editScrollModal/index.vue";
import addAttributeModal from "./addAttributeModal/index.vue";
import editAttributeModal from "./editAttributeModal/index.vue";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const isOpen = ref(false);
const loading = ref(false);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  getDetail();
  getPageList();
});

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      sectionId: props.id,
    };
    const res = await getBannerList(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

// 获取详情
const isInfo = ref();
const getDetail = async () => {
  try {
    const res = await getSectionDetail(props.id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isInfo.value = res.data;
  } catch (error) {
    message.error(error.message);
  }
};
//编辑轮播属性
const isScrollOpen = ref(false);

const handleEditScroll = () => {
  isScrollOpen.value = !isScrollOpen.value;
};
const handleScrollOk = (e) => {
  isScrollOpen.value = e;
  getDetail();
};
//新增图片
const isAttributeOpen = ref(false);
const handleAddImg = () => {
  isAttributeOpen.value = !isAttributeOpen.value;
};

const handleAttributeOk = (e) => {
  isAttributeOpen.value = e;
  getPageList();
};

//编辑图片
const isId = ref();
const isEditAttributeOpen = ref(false);
const handleEditImg = (e) => {
  isId.value = e;
  isEditAttributeOpen.value = !isEditAttributeOpen.value;
};

const handleEditAttributeOk = (e) => {
  isEditAttributeOpen.value = e;
  getPageList();
};

//删除

const handleDelete = async (data) => {
  if (data.status === 1) {
    return message.error("上架状态不允许删除");
  }
  try {
    const res = await getDeleteBanner(data.id);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("删除成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
//排序
const getSort = async () => {
  const params = {
    id: isSortId.value,
    sortOrder: isSortText.value,
  };
  try {
    const res = await getEditBannerSort(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

const isInp = ref();
const isSortText = ref();
const isSortId = ref();
const handleSort = (e) => {
  isSortText.value = e.target.value;
  isInp.value = "-1";
  getSort();
};
const handleEditSort = (e) => {
  isInp.value = e;
  isSortId.value = e;
};
//上下架

const handleUpDwon = async (e) => {
  try {
    const res = await getBannerUpAndDown(e);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

const emit = defineEmits(["isModalOpen"]);
const handleCancel = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
const handleOk = () => {
  isOpen.value = false;
  emit("isModalOpen", false);
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
