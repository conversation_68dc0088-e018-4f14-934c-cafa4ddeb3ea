<template>
  <div class="edit-main">
    <a-row>
      <a-col :span="7">
        <div class="left-box">
          <div class="iphone-box" v-if="iphoneData">
            <img
              v-if="iphoneData.zoneAttr"
              :src="iphoneData?.zoneAttr?.banner"
              alt=""
            />
            <img
              v-else-if="iphoneData.zoneCardDoList.length"
              :src="iphoneData.zoneCardDoList[0].imgUrl"
            />
            <p class="title1-css">{{ iphoneData?.zoneAttr?.title }}</p>
            <p class="title2-css">{{ iphoneData?.zoneAttr?.rankName }}</p>
            <div
              class="card-box"
              v-if="
                iphoneData.zoneCardDoList && iphoneData.zoneCardDoList.length
              "
            >
              <div
                class="card-css"
                v-for="item in iphoneData.zoneCardDoList"
                :key="item.id"
              >
                <div v-if="item.jumpParam != ''" style="display: flex">
                  <img v-if="item.imgUrl" :src="item.imgUrl" />
                  <div class="text-css" v-if="item.jumpParam">
                    <p>{{ JSON.parse(item.jumpParam).prodName }}</p>
                    <p style="color: red; margin: 10px 0 15px 0">
                      ¥ {{ JSON.parse(item.jumpParam).costPrice }}
                    </p>
                    <s style="color: #eee"
                      >¥ {{ JSON.parse(item.jumpParam).oriPrice || "0.00" }}</s
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="17">
        <div class="right-box">
          <div class="opt-css">
            <!-- 专区搜索功能
            <a-switch
              v-model:checked="checked"
              :disabled="route.query.isEdit === '0' ? true : false"
              @change="switchChange"
            /> -->
            <a-button
              class="ml10"
              @click="editClick"
              :disabled="route.query.isEdit === '0' ? true : false"
              >编辑专区</a-button
            >
            <a-button
              type="primary"
              class="ml10"
              @click="addClick"
              :disabled="route.query.isEdit === '0' ? true : false"
              >新增</a-button
            >
          </div>
          <a-table
            :dataSource="dataSource"
            :columns="columns"
            bordered
            :pagination="false"
            class="table-css"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'jumpParam'">
                <div
                  style="display: flex; align-items: center"
                  v-if="record.id"
                >
                  <img
                    v-if="record.imgUrl"
                    :src="record.imgUrl"
                    style="width: 80px; height: 80px"
                  />
                  <p class="ml10">{{ prodFn(record) }}</p>
                </div>
                <div
                  v-else-if="record.imgUrl && !record.id"
                  style="display: flex; align-items: center"
                >
                  <img :src="record.imgUrl" style="width: 80px; height: 80px" />
                  <p class="ml10">{{ prodFn(record) }}</p>
                </div>
                <div v-else>
                  <a-button @click="addBtn(index)">+</a-button>
                </div>
              </template>
              <template v-if="column.key === 'sort'">
                <a-input-number
                  v-model:value="record.sort"
                  :min="1"
                  :max="100000"
                ></a-input-number>
              </template>
              <template v-if="column.key === 'actions'">
                <a-popconfirm
                  :disabled="route.query.isEdit === '0' ? true : false"
                  ok-text="确定"
                  cancel-text="取消"
                  title="确定执行此操作？"
                  class="btn-css"
                  @confirm="delClick(index)"
                >
                  <a-button type="link" class="btn-css"
                    :disabled="route.query.isEdit === '0' ? true : false"
                    >删除</a-button
                  >
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </div>
      </a-col>
    </a-row>
    <div class="btn-box">
      <a-button @click="backBtn">返回</a-button>
      <a-button
        type="primary"
        class="ml10"
        @click="saveBtn"
        v-if="route.query.isEdit === '1'"
        >保存</a-button
      >
    </div>
  </div>
  <a-modal
    v-model:open="editVisible"
    title="编辑专区属性"
    :destroy-on-close="true"
    width="40%"
    @cancel="editVisible = false"
  >
    <a-form
      :model="formState"
      name="horizontal_login"
      class="mt20"
      v-bind="layout"
    >
      <a-form-item label="页面标题" name="title">
        <a-input v-model:value="formState.title" placeholder="请输入"></a-input>
      </a-form-item>

      <a-form-item label="排行榜名称" name="rankName">
        <a-input
          v-model:value="formState.rankName"
          placeholder="请输入"
        ></a-input>
      </a-form-item>
      <a-form-item label="主题色" name="color">
        <color-picker
          v-model:pureColor="formState.color"
          @change="colorChange"
        />
      </a-form-item>
      <a-form-item label="Banner图" name="banner">
        <!-- <a-input
            v-model:value="formState.banner"
            placeholder="请输入"
          ></a-input> -->
        <wd-upload
          biz-type="in_coming"
          :max-count="1"
          @get-url-list="afferentUrlChange"
          :file-list="fileList"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="editVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="addVisible"
    title="添加内容"
    :destroy-on-close="true"
    width="35%"
    @cancel="addVisible = false"
  >
    <a-form name="horizontal_login" class="mt20" v-bind="layout">
      <img src="../../../assets/images/sptu.png" alt="" style="width: 240px" />
      <p style="margin-top: 20px">商品卡片-横版</p>
      <a-form-item label="添加数量" name="title" style="margin-top: 20px">
        <a-input-number
          v-model:value="addNumber"
          :min="1"
          :max="10"
        ></a-input-number>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="addSubmit">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="addProdShow"
    title="选择商品"
    :destroy-on-close="true"
    width="60%"
    @cancel="addProdShow = false"
  >
    <search-antd :form-list="propdFormList" @on-search="handerSearch" />
    <a-table
      :dataSource="prodSource"
      :columns="prodColumns"
      :pagination="pagination"
      @change="handleTableChange"
      :row-selection="rowSelection"
      :rowKey="(record) => record"
      :scroll="{ x: 1000 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'prodName'">
          <div class="proinfo-css">
            <img class="con-style" :src="record.picPath" />
            <div>
              <p class="ml10">{{ record.prodName }}</p>
              <p class="ml10" v-if="record.prodNumber">
                {{ record.prodNumber }}
              </p>
            </div>
          </div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button @click="addProdShow = false">取消</a-button>
      <a-button type="primary" @click="addSubmitClick">确定</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed, toRaw } from "vue";
import {
  getZoneCardList,
  GetCategory,
  saveZoneCard,
  GetSearchStatus,
  GetEditZoneAttr,
  getSpuList,
  getCategoryList,
} from "@/api/cms/zoneManagement/index";
import { useRoute, useRouter } from "vue-router";
// import router from '@/router';
import { message } from "woody-ui";
import WdUpload from "@/components/WdUpload/index.vue";

import SearchAntd from "@/components/SearchAntd/index.vue";

const route = useRoute();
const router = useRouter();
const dataSource = ref([]);
// const tableData = ref([]);
const editVisible = ref(false);
const addProdShow = ref(false);
const addVisible = ref(false);
// const supplyId = ref("");
const fileList = ref([]);
const categoryList = ref([]);
const iphoneData = ref(null);
const checked = ref(true);
const itemIndex = ref(0);
const addNumber = ref("");
const cardType = ["小图", "商品卡片", "大图", "商品卡片横版"];
const prodSource = ref([]);
const formState = ref({
  color: "",
  banner: "",
  rankName: "",
  title: "",
});
const propdFormList = [
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "后台分类",
    name: "firstCategoryId",
    type: "select", // 输入框
    span: 6,
    options: categoryList,
    labelKey: "categoryName",
    valueKey: "categoryId",
  },
];
const prodColumns = [
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    width: 260,
    fixed: "left",
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
    key: "categoryName",
    width: 120,
  },
  {
    title: "售价",
    dataIndex: "price",
    key: "price",
    width: 120,
    // customRender: ({ record }) => {
    //   return `¥ ${record.price} + ¥ ${record.goldCoin} 金币 `
    // },
  },
  {
    title: "划线价",
    dataIndex: "oriPrice",
    key: "oriPrice",
    width: 120,
  },
  {
    title: "榜单",
    dataIndex: "rankName",
    key: "rankName",
    width: 150,
  },
];
const selectedRowKeys = ref([]);
const rowSelection = computed(() => ({
  type: "radio",
  selectedRowKeys,
  onChange: onSelectChange,
  getCheckboxProps: (record) => ({
    disabled: record.rankName, // Column configuration not to be checked
  }),
}));

const prodFn = (data) => {
  console.log(data, "data123");
  if (data.jumpParam) {
    return JSON.parse(data.jumpParam).prodName;
  }
};

const getProdCategory = async () => {
  const res = await getCategoryList({});
  if (res.code === 0) {
    categoryList.value = res.data;
  }
};
// 列表复选框 事件
const onSelectChange = (newSelectedRowKeys) => {
  console.log(newSelectedRowKeys, "newSelectedRowKeys");
  selectedRowKeys.value = newSelectedRowKeys;
  //   const rowId = newSelectedRowKeys.map((v) => {
  //     return v.prodId;
  //   });
};
const formData = ref(null);
const handerSearch = (params) => {
  console.log(params, "params");
  formData.value = params;
  getListApi();
};
const addSubmitClick = async () => {
  console.log(selectedRowKeys.value[0], "selectedRowKeys.value");
  let obj = {
    jumpParam: JSON.stringify(toRaw(selectedRowKeys.value[0])),
    cardType: "4",
    imgUrl: toRaw(selectedRowKeys.value[0]).picPath,
    jumpType: 1,
    jumpUrl: "",
    sort: 99,
  };
  dataSource.value[itemIndex.value] = obj;
  addProdShow.value = false;

  console.log(dataSource.value, "dataSource.value");
  //   if (data.length > 1) {
  //     message.error("只能选择一个商品");
  //     return;
  //   }
  //   proDetail.value = data[0];
  //   selectData.value = data;
  //   addProdVisible.value = true;
  // const ids = data.map((v)=>{
  //   return v.prodId
  // })
  // const params = {
  //   prodIdList:ids,
  //   seckillId:route.query.coinSeckillId
  // }
  // const res = await GetProdSave(params);
  // if(res.code === 0){
  //   addShow.value = false;
  //   getList();
  // }
};
const addBtn = async (index) => {
  itemIndex.value = index;
  addProdShow.value = true;
  getListApi();
};
const getListApi = async () => {
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    isGetRankInfo: true,
    prodSourceType: 0,
    ...formData.value,
  };
  const res = await getSpuList(params);
  if (res.code === 0) {
    prodSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};
const handleTableChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getListApi();
};
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const columns = [
  {
    title: "展示内容",
    dataIndex: "jumpParam",
    key: "jumpParam",
    fixed: true,
    align: "center",
    width: 360,
  },
  {
    title: "内容类型",
    dataIndex: "cardType",
    key: "cardType",
    align: "center",
    width: 200,
    customRender: ({ record }) => {
      return cardType[Number(record.cardType) - 1];
    },
  },
  {
    title: "排序",
    dataIndex: "sort",
    key: "sort",
    align: "center",
    width: 200,
  },
  //   {
  //     title: "跳转链接",
  //     dataIndex: "jumpUrl",
  //     key: "jumpUrl",
  //     align: "center",
  //     width: 200,
  //   },
  {
    title: "操作",
    dataIndex: "actions",
    key: "actions",
    fixed: true,
    align: "center",
    width: 200,
  },
];
const backBtn = () => {
  router.go(-1);
};
const addSubmit = () => {
  console.log(addNumber.value, "890");
  console.log(dataSource.value, "890890");
  for (let i = 0; i < Number(addNumber.value); i++) {
    let obj = {
      jumpParam: "",
      cardType: "4",
      imgUrl: "",
      jumpType: "",
      sort: 99,
    };
    dataSource.value.push(obj);
  }
  console.log(dataSource.value, "chakan ");
  addVisible.value = false;
};
const colorChange = () => {};
const afferentUrlChange = (data) => {
  if (data && data.length) {
    formState.value.banner = data[0].url;
    fileList.value = [{ name: "", url: data[0].url }];
  }
};
const switchChange = async (data) => {
  console.log(checked, data, "data");
  const params = {
    searchStatus: data ? 1 : 0,
    zoneId: Number(route.query.id),
  };
  const res = await GetSearchStatus(params);
  if (res.code === 0) {
    message.success("操作成功");
    getDetail();
    return;
  }
  message.error(res.message);
};
const saveBtn = async () => {
  // for (let i of dataSource.value) {
  //   i.jumpParam = JSON.stringify(i.jumpParam);
  // }
  const params = {
    zoneType: "RANK",
    zoneId: Number(route.query.id),
    list: dataSource.value,
  };
  const res = await saveZoneCard(params);
  if (res.code === 0) {
    message.success("保存成功!");
    getDetail();
    // setTimeout(() => {
    //   window.location.reload();
    // }, 100);
    return;
  }
  message.error(res.message);
};
const addClick = () => {
  addNumber.value = "";
  addVisible.value = true;
};
const editClick = () => {
  console.log(iphoneData.value, "iphoneData.value");
  if (iphoneData.value && iphoneData.value.zoneAttr) {
    formState.value = iphoneData.value?.zoneAttr;
    fileList.value = [
      {
        name: "",
        url: iphoneData.value.zoneAttr?.banner,
      },
    ];
  }

  editVisible.value = true;
};
const submitClick = async () => {
  const params = {
    zoneId: Number(route.query.id),
    ...formState.value,
  };
  const res = await GetEditZoneAttr(params);
  if (res.code === 0) {
    message.success("操作成功");
    editVisible.value = false;
    getDetail();
    return;
  }
  message.error(res.message);
};
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const delClick = (index) => {
  dataSource.value.splice(index, 1);
  // tableData.value.splice(index, 1);
};

// 记录已添加的 id
const addedIds = new Set();
const getDetail = async () => {
  const params = {
    zoneId: route.query.id,
  };
  const res = await getZoneCardList(params);
  if (res.code === 0) {
    iphoneData.value = res.data;
    dataSource.value = res.data.zoneCardDoList;
    checked.value = res.data.searchStatus === 0 ? false : true;
    // tableData.value = res.data.zoneCardDoList;
    // dataSource.value = [];
    // for (let i of res.data.zoneCardDoList) {
    //     if (i.jumpParam != "") {
    //       let obj = {
    //         jumpParam: i.jumpParam ? JSON.parse(i.jumpParam) : "",
    //         sort: i.sort,
    //         jumpUrl: i.imgUrl,
    //         id: i.id,
    //         cardType: i.cardType,
    //         prodName:JSON.parse(i.jumpParam).prodName
    //       };
    //       // 判断当前 id 是否已经存在于 dataSource 中
    //       if (!dataSource.value.some(item => item.id === i.id)) {
    //         dataSource.value.push(obj);
    //       }
    //     } else {
    //       let obj = {
    //         jumpParam: {
    //           pic: i.imgUrl,
    //         },
    //         sort: i.sort,
    //         jumpUrl: i.jumpUrl,
    //         id: i.id,
    //         cardType: i.cardType,
    //       };
    //       // 将该 id 标记为已添加
    //       addedIds.add(i.id);
    //       // 判断当前 id 是否已经存在于 dataSource 中
    //       if (!dataSource.value.some(item => item.id === i.id)) {
    //         dataSource.value.push(obj);
    //       }
    //     }
    //   }
  }
};
onMounted(() => {
  getDetail();
  getProdCategory();
});
</script>
<style scoped lang="less">
.edit-main {
  // display: flex;
  padding: 20px;
  border-radius: 16px;
  background: #ffffff;
  height: 100%;
  .left-box {
    width: 420px;
    // height: 800px;
    padding: 10px;
    // margin:30px 0;
    background: url("https://life-plat-temp.shwoody.com/static/media/iPhone.0b9e24bb.png")
      no-repeat rgb(245, 245, 245);
    background-size: cover;
    border-radius: 70px;
    position: relative;

    .iphone-box {
      padding: 20px;
      height: 800px;
      width: 382px;
      margin: 10px;
      // position: relative;
      .title1-css {
        position: absolute;
        top: 80px;
        left: 0;
        color: #ffffff;
        text-align: center;
        width: 100%;
        line-height: 40px;
        font-size: 17px;
      }
      .title2-css {
        position: absolute;
        top: 120px;
        left: 0;
        font-size: 20px;
        color: #ffffff;
        text-align: center;
        width: 100%;
        line-height: 40px;
      }
    }
    .card-box {
      position: absolute;
      top: 230px;
      width: 82%;
      height: 570px;
      overflow: auto;
      background: rgb(245, 245, 245);
      .card-css {
        background: #ffffff;
        border-radius: 16px;
        margin-bottom: 10px;
        height: 120px;
        align-content: center;
        width: 100%;
        img {
          width: 80px;
          height: 80px;
          margin: 20px 8px;
        }
        .text-css {
          margin-top: 20px;
          margin-left: 10px;
        }
      }
    }

    img {
      width: 100%;
      height: 200px;
      margin-top: 20px;
    }
  }
  .right-box {
    margin-left: 50px;
    .opt-css {
      height: 40px;
      float: right;
      margin-bottom: 10px;
    }
    .table-css {
      height: 780px;
      overflow: auto;
    }
  }
  .btn-box {
    margin-top: 10px;
    width: 100%;
    text-align: center;
  }
}
.proinfo-css {
  display: flex;
  align-items: center;
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */

  white-space: nowrap;
  // width: 300px; /* 设置容器的宽度，根据需要调整 */
  img {
    width: 80px;
    height: 80px;
  }
}
</style>
