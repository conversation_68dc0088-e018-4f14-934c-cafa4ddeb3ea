<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button type="primary" @click="addClick" class="ml10">新增</a-button>
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      :is-checkbox="false"
      :is-Look="true"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="addVisible"
    placement="center"
    title="新增专区"
    :destroy-on-close="true"
    width="40%"
    @cancel="addVisible = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="formState"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item
        label="专区名称"
        name="name"
        :rules="[{ required: true, message: '请选择专区名称' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入内容" />
      </a-form-item>
      <a-form-item label="专区类型" name="zoneType">
        <a-radio-group v-model:value="formState.zoneType" name="radioGroup">
          <!-- <a-radio value="ORDINARY">普通</a-radio> -->
          <a-radio value="RANK">榜单</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="展示类型" name="showType">
        <a-radio-group v-model:value="formState.showType" name="radioGroup">
          <a-radio value="X">横版</a-radio>
          <a-radio value="Y" v-if="formState.zoneType === 'ORDINARY'"
            >竖版</a-radio
          >
        </a-radio-group>
      </a-form-item>
      <a-form-item label="上/下架" name="status">
        <a-radio-group v-model:value="formState.status" name="radioGroup">
          <a-radio :value="1">上架</a-radio>
          <a-radio :value="0">下架</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import { getGoldCategoryDrop } from "@/api/activityCenter/goldCoin";
import {
  getZone,
  deleteZone,
  updateZoneStatus,
  saveZone,
} from "@/api/cms/zoneManagement/index";
import { message } from "woody-ui";
import router from "@/router";
const addVisible = ref(false);
const formData = ref({
  zoneType: "RANK",
});

const classfityOpt = ref([]);
import type { FormInstance } from "woody-ui";
const formRef = ref<FormInstance>();
const formState = reactive({
  name: "",
  showType: "X",
  zoneType: "RANK",
  status: 0,
});
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const formList = [
  {
    label: "名称",
    name: "name",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "创建时间",
    name: "addTime",
    type: "rangePicker",
    span: 6,
  },
];

const handleSearch = (param) => {
  const { addTime, zoneType } = param;
  formData.value = param;
  formData.value["startTime"] = Array.isArray(addTime)
    ? addTime[0] + " 00:00:00"
    : "";
  formData.value["endTime"] = Array.isArray(addTime)
    ? addTime[1] + " 23:59:59"
    : "";
  formData.value["zoneType"] = "RANK";
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const submitClick = () => {
  formRef.value
    .validate()
    .then(async () => {
      const params = {
        plateShow: "wd",
        ...formState,
      };
      const res = await saveZone(params);
      if (res.code === 0) {
        addVisible.value = false;
        message.success("新增成功");
        getList();
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

//table表头数据
const columns = [
  {
    title: "专区名称",
    dataIndex: "name",
    key: "name",
    fixed: true,
    align: "left",
    width: 200,
  },
  {
    title: "创建人",
    dataIndex: "createUser",
    key: "createUser",
    align: "left",
    width: 150,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 150,
  },
  {
    title: "专区类型",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      if (record.zoneType === "ORDINARY") {
        return "普通";
      } else if (record.zoneType === "RANK") {
        return "榜单";
      } else {
        return "全部";
      }
    },
  },
  {
    title: "上/下架",
    dataIndex: "status",
    key: "status",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      if (record.status === 1) {
        return "上架";
      } else if (record.status === 0) {
        return "下架";
      }
    },
  },
  {
    title: "操作",
    key: "dynamicsAct",
    align: "left",
    fixed: "right",
    width: 300,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const zoneId = ref("");
const getList = async () => {
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    plateShow: "wd",
    ...formData.value,
  };
  const res = await getZone(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const getClassfity = async () => {
  const res = await getGoldCategoryDrop({});
  if (res.code === 0) {
    classfityOpt.value = res.data;
  }
};

const addClick = () => {
  zoneId.value = "";
  addVisible.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  if (type === "delete") {
    deleteApi(data.id);
  } else if (type === "edit") {
    router.push({
      path: "/cms/editZone",
      query: { id: data.id, isEdit: 1 },
    });
  } else if (type === "off") {
    offApi(data);
  } else if (type === "look") {
    router.push({
      path: "/cms/editZone",
      query: { id: data.id, isEdit: 0 },
    });
  }
};

const offApi = async (data) => {
  const params = {
    id: data.id,
    status: data.status === 0 ? 1 : 0,
  };
  const res = await updateZoneStatus(params);
  if (res.code === 0) {
    message.success("操作成功");
    getList();
  } else {
    message.error(res.message);
  }
};

const deleteApi = async (id) => {
  const res = await deleteZone({ id });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  } else {
    message.error(res.message);
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  console.log(newPagination, "newPagination");
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
  console.log(pagination, "pagination");
};

onMounted(() => {
  getClassfity();
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
