<template>
  <div class="commodity-information-wrapper">
    <a-table
      row-key="id"
      :data-source="listData"
      :columns="columns"
      :pagination="pagination"
      @update:pagination="tableChange"
    >
      <template #bodyCell="{ column, record }">
        <div v-if="column.key === 'operate'">
          <a-button type="link" class="btn-css" @click="handleShowDrawerClick($event, record)">编辑</a-button>
        </div>
        
      </template>
    </a-table>
    <a-drawer
      v-model:open="visible"
      :title="headerTitle"
      :footer="true"
      size="large"
      :close-on-overlay-click="false"
      :close-btn="true"
      @close="() => (visible = false)"
    >
      <template #extra>
        <div class="drawer-footer-btn">
          <a-button @click="onClickClose()" class="mr10">取消</a-button>
          <a-button type="primary" @click="onClickConfirm()">{{ confirmOrNot ? '编辑' : '保存' }}</a-button>
        </div>
      </template>
      <div class="drawer-title-wrapper">
        <div class="drawer-title">信息配置</div>
        <a-checkbox-group v-model:value="checked" :options="options" :disabled="confirmOrNot" class="mt20"></a-checkbox-group>
      </div>
    </a-drawer>
    <a-modal
      v-model:open="visibleDialog"
      title="确定要退出吗？"
      :body="true"
      @ok="onClickConfirmDialog"
    >
      <div class="dialog-body-text">当前页面内容尚未发布，如若退出会丢失编辑的内容！</div>
      <template #footer>
        <div class="drawer-footer-btn">
          <a-button @click="onClickConfirmDialog()">确认退出</a-button>
          <a-button type="primary" @click="onClickCloseDialog()">取消</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'woody-ui';
import { queryList, infoDetail, saveConfig } from '@/api/cms/productInfo';

const listData = ref([]);
const visible = ref(false);
const confirmOrNot = ref(true);
const visibleDialog = ref(false);
const headerTitle = ref('编辑');
const rowData = ref();
const checked = ref([]);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

const columns = [
  {
    title: "页面名称",
    dataIndex: "configName",
    key: "configName",
    align: "left",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    align: "left",
    width: 200,
  }
];

const options = [
  { value: 'sales_volume', label: '销量' },
  { value: 'underlined_price', label: '划线价' },
  { value: 'supplier_info', label: '供货仓信息' },
  { value: 'selling_point_label', label: '卖点标签' },
];

const handleShowDrawerClick = (e, param) => {
  e.stopPropagation();
  visible.value = true;
  rowData.value = param;
  queryConfig(param.id);
};

const onClickConfirmDialog = () => {
  visibleDialog.value = false;
  defaultValue();
};

const onClickCloseDialog = () => {
  visibleDialog.value = false;
};

const onClickConfirm = () => {
  if (confirmOrNot.value) {
    confirmOrNot.value = false;
    headerTitle.value = '配置';
  } else {
    const obj = {
      sales_volume: checked.value.includes('sales_volume'),
      underlined_price: checked.value.includes('underlined_price'),
      supplier_info: checked.value.includes('supplier_info'),
      selling_point_label: checked.value.includes('selling_point_label'),
    };
    saveConfig({
      ...rowData.value,
      configJsonValue: JSON.stringify({ prod_info_config: obj }),
    }).then((res) => {
      if (res.code === 0) {
        message.success('保存成功');
        defaultValue();
      }
    });
  }
};

const onClickClose = () => {
  if (confirmOrNot.value) {
    defaultValue();
  } else {
    visibleDialog.value = true;
  }
};

const defaultValue = () => {
  visible.value = false;
  confirmOrNot.value = true;
  headerTitle.value = '编辑';
};

// 表格切换
const tableChange = (e) => {
  // pagination.value = { ...pagination.value, ...newPagination };
  pagination.value.current = pagination.value.pageSize === e.pageSize ? e.current : 1;
  pagination.value.pageSize = e.pageSize;
};

onMounted(() => {
  queryData();
});

// 差表格数据
const queryData = () => {
  queryList({ page: 1, size: 10 }).then((res) => {
    if (res.code === 0) {
      listData.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  });
};
// 配置详情
const queryConfig = (configId) => {
  infoDetail({ configId }).then((res) => {
    if (res.code === 0) {
      const obj = JSON.parse(res.data.configJsonValue).prod_info_config;
      const arr = [];
      Object.keys(obj).map((item) => {
        if (obj[item]) {
          arr.push(item);
        }
      });
      checked.value = arr;
    }
  });
};
</script>
<style lang="less" scoped>
.commodity-information-wrapper {
  width: 100%;
  flex: 1;
  background: #fff;
  border-radius: 16px;
  padding: 32px;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;

  .drawer-footer-btn {
    display: flex;
    justify-content: flex-end;
  }

  .drawer-title-wrapper {
    font-weight: 600;
    font-size: 14px;
    color: #05082c;
    line-height: 22px;
    text-align: left;
    margin-top: 10px;
    margin-left: 10px;

    .drawer-title {
      margin-bottom: 20px;
    }
  }

  .dialog-body-text {
    font-weight: 400;
    font-size: 14px;
    color: #495366;
    text-align: left;
    margin-left: 18px;
  }
}
</style>
