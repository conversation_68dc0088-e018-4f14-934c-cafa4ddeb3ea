<template>
  <a-modal
    :title="editId ? '修改' : '新增'"
    :open="visible"
    @cancel="handleCancel"
    @ok="handleOk"
    ok-text="确定"
    cancel-text="取消"
    :destroy-on-close="true"
  >
    <a-form
      :form="form"
      name="updateBrand"
      :wrapper-col="{ span: 19 }"
      :label-col="{ span: 5 }"
      layout="vertical"
    >
      <a-form-item name="name" label="品牌名称" v-bind="validateInfos.name">
        <a-input
          v-model:value="form.name"
          placeholder="请输入品牌名称"
          :maxlength="30"
          show-count
        />
      </a-form-item>
      <a-form-item
        name="categoryIds"
        label="分类"
        v-bind="validateInfos.categoryIds"
      >
        <a-select
          v-model:value="form.categoryIds"
          mode="multiple"
          style="width: 100%"
          placeholder="请选择"
          allow-clear
          :options="categoryConfig"
        />
      </a-form-item>
      <a-form-item
        name="firstLetter"
        label="品牌首字母"
        v-bind="validateInfos.firstLetter"
      >
        <a-input
          v-model:value="form.firstLetter"
          placeholder="请输入品牌首字母"
          :maxlength="1"
          show-count
        />
      </a-form-item>
      <a-form-item
        name="imgUrlId"
        label="品牌logo"
        v-bind="validateInfos.imgUrlId"
      >
        <wd-upload
          biz-type="in_coming"
          :action="BUSINESS_LICENSE_DISCERN_OLDUPLOAD_URL"
          :file-list="bussFileList"
          request-type="action"
          :max-count="1"
          btn-text="请上传分类LOGO"
          @success-change="handleUploadSuccess"
        />
      </a-form-item>
      <a-form-item
        name="seq"
        label="顺序"
        :initial-value="0"
        v-bind="validateInfos.seq"
      >
        <a-input-number v-model:value="form.seq" />
      </a-form-item>
      <a-form-item
        name="status"
        label="状态"
        :initial-value="1"
        v-bind="validateInfos.status"
      >
        <a-radio-group v-model:value="form.status">
          <a-radio :value="1">正常</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch, reactive } from "vue";
import { getCategoryList } from "@/api/goodsCenter/ecommerceBrand";
import { Form } from "woody-ui";
import { message } from "woody-ui";
import {
  addBrand,
  updateBrand,
  getBrandById,
} from "@/api/goodsCenter/ecommerceBrand";
import { BUSINESS_LICENSE_DISCERN_OLDUPLOAD_URL } from "./constants";
import WdUpload from "@/components/WdUpload3/index.vue";

interface Props {
  isOpenModal: boolean;
  editId: number;
}

const props = defineProps<Props>();
const emits = defineEmits(["updateSuccess", "cancel", "refresh"]);

const visible = ref(false);
const categoryList = ref([]);
const bussFileList = ref([]);

// 表单数据
let form = ref({
  name: "",
  categoryIds: [],
  firstLetter: "",
  imgUrlId: "",
  seq: null,
  status: 1,
});
const rules = reactive({
  name: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  categoryIds: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  firstLetter: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  imgUrlId: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  seq: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  status: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

const categoryConfig = computed(() =>
  categoryList?.value?.map((item) => ({
    label: item.categoryName,
    value: item.categoryId,
  }))
);

watch(props, (newProps) => {
  visible.value = newProps.isOpenModal;
  if (visible.value) {
    fetchCategoryListData();
    if (props.editId) {
      getBrandDetail();
    }
  } else {
    // 关闭做重置初始化
    resetFields();
    bussFileList.value = [];
  }
});

const handleOk = async () => {
  try {
    await validate();
    if (props.editId) {
      await doUpdateBrand();
    } else {
      await doCreateNews();
    }
    emits("refresh");
  } catch (error) {
    console.error("提交失败", error);
  }
};

// 图片上传
const handleUploadSuccess = (context) => {
  if (context?.path?.length > 0) {
    bussFileList.value = [{ uid: context.fileId, url: context.url }];
    form.value.imgUrlId = context.fileId;
  } else {
    bussFileList.value = [];
    form.value.imgUrlId = "";
  }
};

const doCreateNews = async () => {
  const result = await addBrand(form.value);
  if (result.code === 0) {
    message.success("创建成功");
    return;
  }
  return message.error(result.message || "创建失败");
};

const doUpdateBrand = async () => {
  const result = await updateBrand({ brandId: props.editId, ...form.value });
  if (result.code === 0) {
    message.success("更新成功");
    return;
  }
  return message.error(result.message || "更新失败");
};

const fetchCategoryListData = async () => {
  try {
    const result = await getCategoryList();
    if (result.code === 0) {
      return (categoryList.value = result.data);
    }
    return (categoryList.value = []);
  } catch (error) {
    console.error(error);
  }
};

const handleCancel = () => {
  emits("cancel");
};

// 获取编辑回显信息
const getBrandDetail = async () => {
  const data = await getBrandById(props.editId);
  if (data && data?.code !== 0) {
    message.error(data?.message);
  }
  if (data?.data) {
    form.value = {
      name: (data.data as any).brandName,
      categoryIds: data.data.categoryBrandInfo?.map((item) => item.categoryId),
      firstLetter: data.data.firstLetter,
      imgUrlId: data.data.imgUrlId,
      seq: data.data.seq,
      status: data.data.status,
    };
    bussFileList.value = [{ uid: data.data.imgUrlId, url: data.data.imgUrl }];
  }
};
</script>

<style scoped></style>
