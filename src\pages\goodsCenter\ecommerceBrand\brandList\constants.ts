export const STATUS_MAP = {
  0: "禁用",
  1: "正常",
};

export const STATUS_OPTIONS = [
  { value: "", label: "全部" },
  ...Object.entries(STATUS_MAP).map(([value, label]) => ({
    value: Number(value),
    label,
  })),
];

export const formList = [
  {
    label: "品牌名称",
    name: "brandName",
    type: "input",
    placeholder: "请输入",
    span: 6,
  },
  {
    label: "状态",
    name: "status",
    type: "select",
    placeholder: "请选择",
    options: STATUS_OPTIONS,
    span: 6,
  },
];

//table表头数据
export const columns = [
  {
    dataIndex: "brandId",
    title: "序号",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "name",
    title: "品牌名称",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "imgUrl",
    title: "品牌logo",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "firstLetter",
    title: "品牌首字母",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "seq",
    title: "序号",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "status",
    title: "状态",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "operation",
    title: "操作",
    fixed: "right",
    width: 150,
    ellipsis: true,
  },
];
