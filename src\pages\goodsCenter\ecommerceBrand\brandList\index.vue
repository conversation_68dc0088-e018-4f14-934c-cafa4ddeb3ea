<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button
        type="primary"
        @click="
          () => {
            editId = '';
            isOpenModal = true;
          }
        "
        class="ml10"
        >新增</a-button
      >
    </div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      class="mt10 table-list"
      :scroll="{ x: 1000 }"
      @change="handlePageChange($event)"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'imgUrl'">
          <a-image :width="50" :height="50" :src="record.imgUrl" />
        </template>
        <template v-if="column.dataIndex === 'status'">
          <div>{{ STATUS_MAP[text] }}</div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <a-button type="link" class="btn-css"
              @click="() => handleEdit(record.brandId)"
            >
              修改
            </a-button>
            <a-button type="link" class="btn-css"
              @click="
                () => {
                  if (record.brandId) {
                    handleUpdateStatus(
                      record.brandId,
                      record.status === 1 ? 0 : 1
                    );
                  }
                }
              "
            >
              {{ record.status === 0 ? "启用" : "禁用" }}
            </a-button>
            <a-popconfirm
              title="确定删除？"
              @confirm="
                () => {
                  if (record.brandId) {
                    return onDelete(record.brandId);
                  }
                  return message.warn('参数缺失');
                }
              "
            >
              <a-button type="link" class="btn-css">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <UpdateBrandModal
    :isOpenModal="isOpenModal"
    :editId="editId"
    @cancel="
      () => {
        isOpenModal = false;
      }
    "
    @refresh="
      () => {
        isOpenModal = false;
        fetchListData();
      }
    "
  />
</template>
<script lang="ts" setup>
import SearchAntd from "@/components/SearchAntd/index.vue";
import { formList, columns, STATUS_MAP } from "./constants";
import { ref, onMounted } from "vue";
import { message } from "woody-ui";
import {
  getShopBrandList,
  updateBrandStatus,
  deleteBrand,
} from "@/api/goodsCenter/ecommerceBrand";
import UpdateBrandModal from "../brandList/components/UpdateBrandModal.vue";

const loading = ref(false);
const dataSource = ref([]);
const queryParams = ref({});
const editId = ref();
const isOpenModal = ref(false);

const handleSearch = (formData) => {
  queryParams.value = {
    ...formData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    ...queryParams.value,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  try {
    const res = await getShopBrandList(params);
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handleEdit = (id: number) => {
  if (id) {
    editId.value = id;
    isOpenModal.value = true;
    return;
  }
  message.warn("参数缺失");
};

const handleUpdateStatus = async (id: number, status: number) => {
  try {
    const params = {
      brandId: id,
      status,
    };
    const res = await updateBrandStatus(params);
    if (res.code === 0) {
      message.success("更新状态成功！");
      fetchListData();
      return;
    }
    return message.error("更新状态失败！");
  } catch (error) {
    message.error("更新状态失败！");
    console.error(error);
  }
};

const onDelete = async (brandId) => {
  try {
    const res = await deleteBrand(brandId);
    if (res.code === 0) {
      message.success("删除成功！");
      handleSearch(queryParams.value);
      return;
    }
    return message.error("删除失败！");
  } catch (error) {
    message.error("删除失败！");
  }
};

onMounted(() => {
  fetchListData();
});
</script>
<style scoped lang="scss">
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
</style>
