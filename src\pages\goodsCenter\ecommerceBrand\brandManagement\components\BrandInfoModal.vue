<template>
  <a-modal
    title="品牌信息"
    :open="visible"
    @cancel="handleCancel"
    cancel-text="关闭"
    :destroy-on-close="true"
    :ok-button-props="{ style: { display: 'none' } }"
  >
    <a-form
      :form="form"
      name="brandInfoForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="品牌名称" name="brand" v-bind="validateInfos.brand">
        <a-input v-model:value="form.brand" disabled />
      </a-form-item>
      <a-form-item
        name="brandUrl"
        label="品牌logo"
        v-bind="validateInfos.brandUrl"
      >
        <a-image width="80px" :src="form.brandUrl" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import { Form } from "woody-ui";

interface Props {
  isOpenModal: boolean;
  modalData: any; // 回显数据
}

const props = defineProps<Props>();
const emits = defineEmits(["cancel"]);

const visible = ref(false);

// 表单数据
let form = ref({
  brand: null,
  brandUrl: "",
});
const rules = reactive({
  brand: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  brandUrl: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

watch(props, (newProps) => {
  visible.value = newProps.isOpenModal;
  if (visible.value) {
    if (newProps.modalData) {
      form.value = newProps.modalData;
    }
  }
});

const handleCancel = () => {
  emits("cancel");
  resetFields();
};
</script>

<style scoped></style>
