//table表头数据
export const columns = [
  {
    dataIndex: "brandUrl",
    title: "品牌logo",
    align: "center",
    fixed: "left",
    width: 100,
    ellipsis: true,
  },
  {
    dataIndex: "brand",
    title: "品牌名称",
    align: "center",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "thirdCategoryName",
    title: "供应链分类",
    align: "center",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "activityCount",
    title: "关联在售活动数",
    align: "center",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "productCount",
    title: "关联在售商品数",
    align: "center",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "sort",
    title: '品牌排序',
    align: "center",
    width: 170,
    ellipsis: true,
  },
  {
    dataIndex: "operation",
    title: "操作",
    fixed: "right",
    align: "center",
    width: 150,
    ellipsis: true,
  }
];

export const formList = [
  {
    label: "平台分类",
    name: "brandCategory",
    type: "custom",
    span: 6,
  },
  {
    label: "品牌名称",
    name: "brand",
    type: "input",
    placeholder: "请输入",
    span: 6,
  },
];