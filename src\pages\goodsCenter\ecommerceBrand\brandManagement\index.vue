<template>
  <search-antd :form-list="formList" @on-search="handleSearch">
    <template #brandCategory>
      <slot name="brandCategory">
        {{ categoryName }}
      </slot>
    </template>
  </search-antd>
  <div class="table-css">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      class="mt10 table-list"
      :scroll="{ x: 1000 }"
      @change="handlePageChange($event)"
    >
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'sort'">
          <span>
            品牌排序
            <a-tooltip placement="bottom">
              <template #title> 默认排序按照关联在售商品数正序 </template>
              <QuestionCircleFilled />
            </a-tooltip>
          </span>
        </template>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'brandUrl'">
          <a-image width="80px" height="80px" :src="record.brandUrl" />
        </template>
        <template v-if="column.dataIndex === 'sort'">
          <a-space v-if="isInp === record.thirdBrandId">
            <a-input-number
              v-model:value="record.sort"
              :min="0"
              :max="999999999"
              @blur="(e: any) => handleSortBlur(record, e.target.value)"
            />
          </a-space>
          <a-space v-else>
            <a-input-number
              v-model:value="record.sort"
              :min="0"
              :max="999999999"
              disabled
            />
            <EditFilled
              @click="
                () => {
                  isInp = record.thirdBrandId;
                }
              "
              style="color: #5599ff; cursor: pointer"
            />
          </a-space>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <span
              class="operate-text"
              @click="
                () => {
                  isOpenModal = true;
                  modalData = { ...record };
                }
              "
            >
              查看
            </span>
          </a-space>
        </template>
      </template>
    </a-table>
    <BrandInfoModal
      :isOpenModal="isOpenModal"
      :modalData="modalData"
      @cancel="
        () => {
          isOpenModal = false;
        }
      "
    />
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { columns, formList } from "./constants";
import { ref, onMounted } from "vue";
import { message } from "woody-ui";
import {
  GetCategoryManage,
  GetEditSort,
} from "@/api/goodsCenter/ecommerceBrand";
import { EditFilled, QuestionCircleFilled } from "@ant-design/icons-vue";
import BrandInfoModal from "../brandManagement/components/BrandInfoModal.vue";

const route = useRoute();
const { categoryId, categoryName } = route.params;
const loading = ref(false);
const dataSource = ref([]);
const queryParams = ref({});
const isOpenModal = ref(false);
const modalData = ref({});
const isInp = ref("");

const handleSearch = (formData) => {
  queryParams.value = {
    ...formData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    ...queryParams.value,
    categoryId: categoryId as string,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  try {
    const res = await GetCategoryManage(params);
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handleSortBlur = async (record, newValue) => {
  isInp.value = "-1";
  if (Number(newValue) && newValue !== record.sort) {
    await fetchEditSortListData(
      Number(newValue) >= 1000000000 ? "999999999" : newValue,
      record.thirdBrandId
    );
  }
};

//排序GetEditSort
const fetchEditSortListData = async (newSort: number, tid: string) => {
  loading.value = true;
  try {
    const result = await GetEditSort(categoryId as string, tid, newSort);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    fetchListData();
  } catch (error) {
    message.error((error as any).message);
  }
  loading.value = false;
};

onMounted(() => {
  fetchListData();
});
</script>
<style scoped lang="scss">
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
</style>
