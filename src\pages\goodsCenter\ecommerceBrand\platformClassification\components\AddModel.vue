<template>
  <a-modal
    :title="editId ? '编辑' : '新增'"
    :open="visible"
    @cancel="handleCancel"
    @ok="handleOk"
    ok-text="确定"
    cancel-text="取消"
    :destroy-on-close="true"
    :confirmLoading="confirmLoading"
  >
    <a-form
      :form="form"
      name="addForm"
      :wrapper-col="{ span: 19 }"
      :label-col="{ span: 6 }"
    >
      <a-form-item
        name="categoryName"
        label="分类名称"
        v-bind="validateInfos.categoryName"
      >
        <a-input
          v-model:value="form.categoryName"
          placeholder="请输入分类名称"
          :maxlength="4"
          show-count
          allowClear
        />
      </a-form-item>
      <a-form-item
        name="status"
        label="分类状态"
        :initial-value="1"
        v-bind="validateInfos.status"
      >
        <a-radio-group v-model:value="form.status">
          <a-radio :value="1">启用</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item name="seq" label="排序" v-bind="validateInfos.seq">
        <a-input-number
          v-model:value="form.seq"
          :min="0"
          :max="999999999"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item
        name="thirdCategoryIds"
        label="关联供应链分类"
        v-bind="validateInfos.thirdCategoryIds"
      >
        <a-select
          v-model:value="form.thirdCategoryIds"
          mode="multiple"
          style="width: 100%"
          placeholder="请选择"
          allow-clear
          :options="isCategory"
          :fieldNames="{
            label: 'name',
            value: 'thirdCategoryId',
          }"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import {
  GetCategorySave,
  GetCategoryEdit,
} from "@/api/goodsCenter/ecommerceBrand";
import { Form } from "woody-ui";
import { message } from "woody-ui";

interface Props {
  isOpenModal: boolean;
  editId: string;
  isCategory: any;
  editData: any; // 编辑时的回显数据
}

const props = defineProps<Props>();
const emits = defineEmits(["updateSuccess", "cancel", "refresh"]);

const visible = ref(false);
const confirmLoading = ref(false);

// 表单数据
let form = ref({
  categoryName: "",
  seq: 999,
  status: 1,
  thirdCategoryIds: [],
});
const rules = reactive({
  categoryName: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  seq: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  status: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  thirdCategoryIds: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

watch(props, (newProps) => {
  visible.value = newProps.isOpenModal;
  if (visible.value) {
    if (newProps.editId) {
      form.value = props.editData;
      form.value.thirdCategoryIds = newProps.editData.supplyChainIds;
    }
  }
});

const handleOk = async () => {
  try {
    await validate();
    if (props.editId) {
      await fetchEditListData();
    } else {
      await fetchSaveListData();
    }
    emits("refresh");
    resetFields();
  } catch (error) {
    console.error("提交失败", error);
  }
};

//新增平台分类
const fetchSaveListData = async () => {
  confirmLoading.value = true;
  let params = { ...form.value };
  try {
    const result = await GetCategorySave(params);
    confirmLoading.value = false;
    if (result.code === 0) {
      message.success("创建成功");
      return;
    }
    return message.error(result.message || "创建失败");
  } catch (error) {
    confirmLoading.value = false;
    message.error((error as any).message || "创建失败");
  }
};

//编辑平台分类GetCategoryEdit
const fetchEditListData = async () => {
  confirmLoading.value = true;
  let params = {
    categoryId: props.editId,
    ...form.value,
  };
  try {
    const result = await GetCategoryEdit(params);
    confirmLoading.value = false;
    if (result.code === 0) {
      message.success("编辑成功");
      return;
    }
    return message.error(result.message || "编辑失败");
  } catch (error) {
    confirmLoading.value = false;
    message.error((error as any).message || "编辑失败");
  }
};

const handleCancel = () => {
  emits("cancel");
  resetFields();
};
</script>

<style scoped></style>
