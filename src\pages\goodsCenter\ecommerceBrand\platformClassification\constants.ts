//table表头数据
export const columns = [
  {
    dataIndex: "categoryName",
    title: "平台分类",
    fixed: "left",
    width: 200,
    ellipsis: true,
  },
  {
    dataIndex: "supplyChainNames",
    title: "供应链分类",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "brandNum",
    title: "关联品牌数",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "productNum",
    title: "关联商品数",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "seq",
    title: "分类排序",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "createTime",
    title: "添加时间",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "status",
    title: "状态",
    width: 100,
    ellipsis: true,
    customRender: ({ record }) => {
      if (record.status === 0) {
        return "禁用";
      } else {
        return "正常";
      }
    },
  },
  {
    dataIndex: "operation",
    title: "操作",
    fixed: "right",
    width: 200,
    ellipsis: true,
  }
];
