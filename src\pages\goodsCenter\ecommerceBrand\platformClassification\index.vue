<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button
        type="primary"
        @click="
          () => {
            editId = '';
            isOpenModal = true;
            fetchLinkCategoryListData(editId);
          }
        "
        class="ml10"
        >添加平台分类</a-button
      >
    </div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      class="mt10 table-list"
      :scroll="{ x: 1000 }"
      @change="handlePageChange($event)"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'supplyChainNames'">
          <div class="wrap-text">
            {{ record.supplyChainNames.join(", ") }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <a-button
              type="link"
              class="btn-css"
              @click="() => handleEdit(record)"
            >
              编辑
            </a-button>
            <a-button
              type="link"
              class="btn-css"
              @click="() => gotoBrandManagement(record)"
            >
              管理
            </a-button>
            <a-popconfirm
              title="确定删除？"
              @confirm="
                () => {
                  if (record.categoryId) {
                    return onDelete(record.categoryId);
                  }
                  return message.warn('参数缺失');
                }
              "
            >
              <a-button type="link" class="btn-css">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <AddModel
    :isCategory="isCategory"
    :isOpenModal="isOpenModal"
    :editId="editId"
    :editData="editData"
    @cancel="
      () => {
        isOpenModal = false;
      }
    "
    @refresh="
      () => {
        isOpenModal = false;
        fetchListData();
      }
    "
  ></AddModel>
</template>
<script lang="ts" setup>
import { useRouter } from "vue-router";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { columns } from "./constants";
import { ref, onMounted, watch } from "vue";
import { message } from "woody-ui";
import {
  GetProdCategory,
  GetCategoryPage,
  GetCategoryList,
  GetCategoryDelList,
} from "@/api/goodsCenter/ecommerceBrand";
import AddModel from "../platformClassification/components/AddModel.vue";

const router = useRouter();
const loading = ref(false);
const dataSource = ref([]);
const queryParams = ref({});
const editId = ref();
const isOpenModal = ref(false);
const listCaData = ref([]);
const isCategory = ref();
const editData = ref({});

const formList = ref([]);

watch(
  listCaData,
  (listCaData) => {
    formList.value = [
      {
        type: "select",
        label: "平台分类",
        name: "categoryId",
        span: 6,
        // isLoad: true,
        labelKey: "label",
        valueKey: "value",
        placeholder: "全部",
        changeOnSelect: true,
        options: listCaData.map((item) => {
          return {
            label: item.categoryName,
            value: item.categoryId,
          };
        }),
      },
      {
        type: "select",
        label: "分类状态",
        name: "status",
        span: 6,
        needFilter: true,
        options: [
          {
            label: "全部",
            value: "",
          },
          {
            label: "启用",
            value: "1",
          },
          {
            label: "禁用",
            value: "0",
          },
        ],
      },
    ];
  },
  {
    immediate: true,
  }
);

const handleSearch = (formData) => {
  queryParams.value = {
    ...formData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
});

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    ...queryParams.value,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  try {
    const res = await GetCategoryPage(params);
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handleEdit = async (record) => {
  editId.value = record.categoryId;
  editData.value = { ...record };
  if (record) {
    await fetchLinkCategoryListData(record.categoryId);
    isOpenModal.value = true;
    return;
  }
  message.warn("参数缺失");
};

const onDelete = async (categoryId) => {
  loading.value = true;
  try {
    const res = await GetCategoryDelList(categoryId);
    if (res.code === 0) {
      message.success("删除成功！");
      handleSearch(queryParams.value);
      return;
    }
    return message.error("删除失败！");
  } catch (error) {
    message.error("删除失败！");
  }
  loading.value = false;
};

const fetchCategoryListData = async () => {
  loading.value = true;
  let params = {
    name: "",
    sourceType: "",
  };
  try {
    const result = await GetProdCategory(params);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    listCaData.value = result.data;
  } catch (error) {
    message.error((error as any).message);
  }
  loading.value = false;
};

// 获取弹窗内的关联供应链分类下拉数据
const fetchLinkCategoryListData = async (e: any) => {
  loading.value = true;
  let params = {
    categoryId: e,
  };
  try {
    const result = await GetCategoryList(params);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    isCategory.value = result.data;
    // setOpen(true);
  } catch (error) {
    message.error((error as any).message);
  }
  loading.value = false;
};

const gotoBrandManagement = (record) => {
  router.push({
    path: `/product/ecommerceBrand/brandManagement/${record.categoryId}/${record.categoryName}`,
  });
};

onMounted(() => {
  fetchCategoryListData();
  fetchListData();
});
</script>
<style scoped lang="scss">
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
  margin-right: 20px;
}
.wrap-text {
  white-space: normal; /* 允许文本换行 */
  word-wrap: break-word; /* 允许长单词换行 */
  overflow-wrap: break-word; /* 兼容性 */
}
</style>
