<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button type="primary" @click="addClick" class="ml10"
        >新增分类</a-button
      >
    </div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :children-column-name="treeConfig.childrenKey"
      :indent-size="treeConfig.indent"
      :pagination="false"
      row-key="categoryId"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'actions'">
          <a-button type="link" class="btn-css" @click="actionsClick('edit', record)"
            >修改</a-button
          >
        </template>
      </template>
      <template #emptyText>
        <!-- <div>
          <no-data />
        </div> -->
      </template>
    </a-table>
  </div>
  <a-modal
    v-model:open="addVisible"
    placement="center"
    :title="isDisabled ? '编辑' : '新增'"
    :destroy-on-close="true"
    width="35%"
    @cancel="closeClick"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="formState"
      v-bind="layout"
      class="add-form-css"
    >
      <a-form-item
        has-feedback
        label="分类名称"
        name="categoryName"
        :rules="[{ required: true, message: '请输入分类名称' }]"
      >
        <a-input
          v-model:value="formState.categoryName"
          type="input"
          autocomplete="off"
          placeholder="请输入分类名称"
          show-count
          :maxlength="5"
        />
      </a-form-item>
      <a-form-item
        v-if="isDisabled"
        has-feedback
        label="上级分类"
        name="parentId"
      >
        <a-select
          v-model:value="formState.parentId"
          :options="classiftyData"
          :disabled="isDisabled"
          :fieldNames="{
            label: 'categoryName',
            value: 'categoryId',
          }"
          placeholder="请选择"
        >
        </a-select>
      </a-form-item>
      <a-form-item v-else has-feedback label="上级分类" name="parentId">
        <a-cascader
          v-model:value="formState.parentId"
          :disabled="isDisabled"
          :options="categoryIdOptions"
          :change-on-select="true"
          :field-names="{
            label: 'categoryName',
            value: 'categoryId',
            children: 'children',
          }"
          placeholder="全部"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import {
  getPaginationCategoryList,
  GetCommodityList,
  saveCategory,
  updateCategory,
  getCategoryInfo,
} from "@/api/goodsCenter/ecommerceClassifty";
import { queryCategory } from "@/api/common";
// import { fetchCategory } from "@/utils";
import { message } from "woody-ui";
const addVisible = ref(false);
import type { FormInstance } from "woody-ui";
const formRef = ref<FormInstance>();
const formState = reactive({
  categoryName: "",
  parentId: "",
});
let formData = {};
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const formList = [
  {
    label: "分类名称",
    name: "categoryName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "创建时间",
    name: "time",
    type: "rangePicker", // 输入框
    span: 6,
  },
];

const handleSearch = (param) => {
  // console.log(param, "pram");
  if (param.time && param.time.length) {
    param.startTime = param.time[0] + " 00:00:00";
    param.endTime = param.time[1] + " 23:59:59";
  }
  formData = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const closeClick = () => {
  formState.categoryName = "";
  formState.parentId = "";
  addVisible.value = false;
  categoryId.value = undefined;
};

const submitClick = async () => {
  const { parentId } = formState;
  if (parentId === "无上级分类") {
    formState.parentId = "0";
  }
  const res = categoryId.value
    ? await updateCategory({ ...formState, categoryId: categoryId.value })
    : await saveCategory({
        ...formState,
        parentId: formState.parentId
          ? formState.parentId[formState.parentId.length - 1]
          : "",
      });
  if (res.code === 0) {
    message.success(categoryId.value ? "编辑成功" : "新增成功");
    addVisible.value = false;
    getList();
    getCategoryList();
    formState.categoryName = "";
    formState.parentId = "";
  }
};

//table表头数据
const columns = [
  {
    title: "分类ID",
    dataIndex: "categoryId",
    key: "categoryId",
    fixed: true,
    align: "left",
    width: 150,
  },
  {
    title: "分类名称",
    dataIndex: "categoryName",
    key: "categoryName",
    align: "left",
    width: 200,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    width: 150,
  },
];
const treeConfig = {
  childrenKey: "newCategoryModelDtos",
  treeNodeColumnIndex: 0,
  indent: 25,
  expandTreeNodeOnClick: true,
};
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const isDisabled = ref(false);
const loading = ref(false);
const classiftyData = ref([]);
const categoryId = ref("");
const categoryIdOptions = ref([]);
const getList = async () => {
  loading.value = true;
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    ...formData,
  };
  const res = await getPaginationCategoryList(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data;
    // pagination.value.total = res.data.total;
  }
};

const addClick = () => {
  categoryId.value = "";
  addVisible.value = true;
  isDisabled.value = false;
  // getClassifty();
};

// const getClassifty = async () => {
//   const res = await GetCommodityList({});
//   if (res.code === 0) {
//     classiftyData.value = res.data;
//   }
// };

//上级分类
const getCategoryList = () => {
  // let res = await queryCategory();
  // let res = await queryCategory();
  // categoryIdOptions.value = res.data || [];
  queryCategory().then((res) => {
    if (res.data) {
      res.data.forEach((item) => {
        item.children = item.newCategoryModelDtos;
      });
      categoryIdOptions.value = res.data;
    }
  });
};

// 操作回调
const actionsClick = async (type, data) => {
  // console.log(data, "data123");
  categoryId.value = data.categoryId;

  if (type === "edit") {
    isDisabled.value = true;
    const res = await getCategoryInfo({ categoryId: data.categoryId });
    if (res.code === 0) {
      formState.categoryName = res.data.categoryName;
      classiftyData.value = [
        {
          categoryName: res.data.categoryParentName,
          categoryId: res.data.parentId,
        },
      ];
      formState.parentId = res.data.parentId;
      res.data.parentId !== "0"
        ? (formState.parentId = res.data.parentId)
        : (formState.parentId = "无上级分类");
      addVisible.value = true;
    }
  }
};

// 分页变化
// const onPaginationChange = (newPagination) => {
//   console.log(newPagination, "newPagination");
//   pagination.value = { ...pagination.value, ...newPagination };
//   getList();
// };

onMounted(() => {
  // getSupplyList();
  // getSource();
  // getStatus();
  //   getShopListFunc();
  getList();
  getCategoryList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
