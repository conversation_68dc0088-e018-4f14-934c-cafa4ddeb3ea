<template>
  <a-modal
    title="编辑"
    :open="visible"
    @cancel="handleCancel"
    @ok="handleOk"
    ok-text="确定"
    cancel-text="取消"
    :destroy-on-close="true"
    :confirmLoading="confirmLoading"
  >
    <a-form
      :form="form"
      name="editForm"
      :wrapper-col="{ span: 19 }"
      :label-col="{ span: 6 }"
    >
      <a-form-item
        name="backgroundCategory"
        label="后台分类"
        v-bind="validateInfos.backgroundCategory"
      >
        <a-input v-model:value="form.backgroundCategory" disabled />
      </a-form-item>
      <a-form-item name="akcIds" label="关联供应链分类">
        <a-select
          v-model:value="form.akcIds"
          mode="multiple"
          showSearch
          allowClear
          placeholder="请选择"
          :filterOption="
            (input, option) =>
              (option?.categoryName ?? '')
                .toLowerCase()
                .includes(input.toLowerCase())
          "
          :options="isCategory"
          :fieldNames="{
            label: 'categoryName',
            value: 'id',
          }"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import {
  GetAkcCategoryUpdate,
  GetAkcCategoryDetail,
} from "@/api/goodsCenter/ecommerceClassifty";
import { Form, message } from "woody-ui";

interface Props {
  isOpenModal: boolean;
  isCategory: any;
  editData: any; // 编辑时的回显数据
  editId: any; // 编辑时的id
}

const props = defineProps<Props>();
const emits = defineEmits(["updateSuccess", "cancel", "refresh"]);

const visible = ref(false);
const confirmLoading = ref(false);

// 表单数据
let form = ref({
  backgroundCategory: "",
  akcIds: [],
  categoryId: "",
});
const rules = reactive({
  backgroundCategory: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

watch(props, (newProps) => {
  visible.value = newProps.isOpenModal;
  if (visible.value) {
    fetchCategoryDetailListData();
  }
});

const handleOk = async () => {
  try {
    await validate();
    await fetchEditListData();
    emits("refresh");
    resetFields();
  } catch (error) {
    console.error("提交失败", error);
  }
};

//编辑数据回显
const fetchCategoryDetailListData = async () => {
  let params = {
    id: props.editId,
  };
  try {
    const result = await GetAkcCategoryDetail(params);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    form.value = result.data;
    form.value.akcIds = JSON.parse(result.data.ids).map(String) ?? "";
  } catch (error) {
    message.error((error as any).message);
  }
};

//编辑平台分类GetCategoryEdit
const fetchEditListData = async () => {
  confirmLoading.value = true;
  let params = {
    // ...form.value,
    akcIds: form.value.akcIds,
    categoryId: form.value.categoryId,
  };
  try {
    const result = await GetAkcCategoryUpdate(params);
    confirmLoading.value = false;
    if (result.code === 0) {
      message.success("编辑成功");
      return;
    }
    return message.error(result.message || "编辑失败");
  } catch (error) {
    confirmLoading.value = false;
    message.error((error as any).message || "编辑失败");
  }
};

const handleCancel = () => {
  emits("cancel");
  resetFields();
};
</script>

<style scoped></style>
