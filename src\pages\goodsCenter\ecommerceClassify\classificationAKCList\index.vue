<template>
  <search-antd
    :form-list="formList"
    @on-search="handleSearch"
    @onReset="
      () => {
        formData.categoryIdList = [];
      }
    "
  >
    <template #categoryIdList>
      <slot name="categoryIdList">
        <a-cascader
          v-model:value="formData.categoryIdList"
          :options="cascaderOptions"
          :load-data="loadData"
        />
      </slot>
    </template>
  </search-antd>
  <div class="table-css">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :children-column-name="treeConfig.childrenKey"
      :indent-size="treeConfig.indent"
      :pagination="false"
      :loading="loading"
      row-key="categoryId"
      class="mt10 table-list"
      :scroll="{ x: 1000 }"
      @change="handlePageChange($event)"
    >
      <template #bodyCell="{ column, record, text, index }">
        <template v-if="column.dataIndex === 'supplyChainCategoryName'">
          <div :key="record.categoryName + index">
            <div
              v-for="(items, j) in record.info"
              :key="items.supplyChainCategoryName + j"
            >
              {{ items.supplyChainCategoryName }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'productCount'">
          <div :key="column?.categoryId || column?.id">
            <div
              v-for="(items, j) in record.info"
              :key="items.productCount + j"
            >
              {{ items.productCount }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space v-if="record.parentId !== 0 && record.grade === 2">
            <a-button type="link" class="btn-css" @click="() => onEdit(record.categoryId)">
              编辑
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <edit-modal
    :isOpenModal="editVisible"
    :editId="currentId"
    :isCategory="isCategory"
    @cancel="
      () => {
        editVisible = false;
      }
    "
    @refresh="
      () => {
        editVisible = false;
        currentId = undefined;
        fetchListData();
      }
    "
  />
</template>

<script setup>
import { ref, onMounted } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import {
  GetAkcCategoryPage,
  GetSupplyChainCategoryList,
  GetCommodityList,
} from "@/api/goodsCenter/ecommerceClassifty";
import { message } from "woody-ui";
import { columns, treeConfig } from "./constants";
import editModal from "./components/EditModel.vue";

const queryParams = ref({});
const currentId = ref();
const editVisible = ref(false);
const cascaderOptions = ref([]);
const formData = ref({
  categoryIdList: [],
});

const handleSearch = (param) => {
  // console.log(param, "formData.value");
  // const categoryIdList = formData.value.categoryIdList;
  if (Array.isArray(param.categoryIdList) && param.categoryIdList.length > 0) {
    queryParams.value = {
      categoryId: param.categoryIdList[param.categoryIdList.length - 1]
        ? param.categoryIdList[param.categoryIdList.length - 1]
        : param.categoryIdList[param.categoryIdList.length - 2],
    };
  } else {
    queryParams.value = {};
  }
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const isCategory = ref();
const formList = ref([
  {
    type: "cascader",
    label: "后台分类",
    name: "categoryIdList",
    labelKey: "categoryName",
    valueKey: "categoryId",
    childrenKey: "newCategoryModelDtos",
    hideAll: true,
    searchFn: "common",
    span: 6,
  },
]);

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    ...queryParams.value,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  try {
    const res = await GetAkcCategoryPage(params);
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

const onEdit = async (categoryId) => {
  currentId.value = categoryId;
  await fetchCategoryListData();
  editVisible.value = true;
};

const fetchCategoryListData = async (data) => {
  loading.value = true;
  try {
    const result = await GetSupplyChainCategoryList();
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    result.data.map((item) => {
      item["disabled"] = item.ifBound;
    });
    isCategory.value = result.data;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const getDropdownCategory = async () => {
  try {
    const result = await GetCommodityList({});
    if (result.code === 0) {
      const categoryList = result.data || [];
      if (categoryList.length > 0) {
        let res = categoryList.map((item) => {
          return {
            label: item.categoryName,
            value: item.categoryId,
            isLeaf: false,
          };
        });
        cascaderOptions.value = res;
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const loadData = async (selectedOptions) => {
  let targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  const res = await GetCommodityList({ categoryId: targetOption.value });
  targetOption.loading = false;

  if (res.code === 0) {
    targetOption.children = res.data.map((item) => {
      return {
        label: item.categoryName,
        value: item.categoryId,
      };
    });
    cascaderOptions.value = [...cascaderOptions.value];
  }
};

onMounted(() => {
  fetchListData();
  getDropdownCategory();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  // height:100%;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
</style>
