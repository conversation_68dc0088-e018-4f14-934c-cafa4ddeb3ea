const columns = [
  {
    title: "分类ID",
    dataIndex: "categoryId",
    key: "categoryId",
    fixed: true,
    align: "left",
    width: 200,
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
    key: "categoryName",
    align: "left",
    width: 200,
  },
  {
    title: "供应链分类",
    dataIndex: "thirdProductCategoryList",
    key: "thirdProductCategoryList",
    align: "left",
    width: 200,
    customRender: ({ record }) => {
      console.log(record);
      if (record.thirdProductCategoryList) {
        for (let i of record.thirdProductCategoryList) {
          return i.thirdCategoryName;
        }
      }
    },
  },
  {
    title: "关联商品数",
    dataIndex: "productCount",
    key: "productCount",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "smallShopAct",
    fixed: "right",
    align: "left",
    width: 200,
  },
];

const treeConfig = {
  childrenKey: "children",
  treeNodeColumnIndex: 0,
  indent: 25,
  expandTreeNodeOnClick: true,
};
export { treeConfig, columns };
