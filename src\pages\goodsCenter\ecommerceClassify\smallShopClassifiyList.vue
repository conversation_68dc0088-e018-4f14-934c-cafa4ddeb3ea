<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <page-wrap class="mt16 mb60">
    <a-table
      :columns="columns"
      :data-source="tableData"
      :children-column-name="treeConfig.childrenKey"
      :indent-size="treeConfig.indent"
      :pagination="pagination"
      @change="onPaginationChange"
      row-key="categoryId"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'show'">
          <div>{{ record.show === 1 ? "显示" : "不显示" }}</div>
        </template>
        <template v-if="column.key === 'operation'">
          <a-space>
            <a-button type="link" class="btn-css"
              @click="handleEdit($event, record)"
            >
              设置
            </a-button>
          </a-space>
        </template>
        <template v-if="column.key === 'smallShopAct'">
          <a-button type="link" class="btn-css"
            v-if="record.grade === 2"
            @click="actionsClick('edit', record)"
            >编辑</a-button
          >
        </template>
      </template>
      <template #emptyText>
        <!-- <div>
          <no-data />
        </div> -->
      </template>
    </a-table>
  </page-wrap>
  <a-modal
    v-model:open="editVisible"
    placement="center"
    title="编辑"
    :destroy-on-close="true"
    width="30%"
  >
    <a-form
      :model="editForm"
      name="horizontal_login"
      v-bind="layout"
      layout="vertical"
      autocomplete="off"
      style="margin: 30px 0"
    >
      <a-form-item label="后台分类" name="categoryName">
        <a-input
          v-model:value="editForm.categoryName"
          placeholder="请输入"
          style="width: 300px"
        ></a-input>
      </a-form-item>

      <a-form-item label="关联供应链分类" name="openShare" class="mt20">
        <a-select
          v-model:value="editForm.thirdProductCategoryIds"
          :options="options"
          mode="multiple"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          placeholder="请选择"
          style="width: 300px"
        ></a-select>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="editVisible = false">取消</a-button>
      <a-button type="primary" @click="editSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { treeConfig, columns } from "./const";
import { message } from "woody-ui";
import {
  GetThirdCategory,
  GetListByThird,
  GetSaveCategoryRef,
  //   GetCommodityList,
  //   saveCategory,
  //   updateCategory,
  //   getCategoryInfo,
} from "@/api/goodsCenter/ecommerceClassifty";

// import { getShopCategoryList, editShopCategory } from "@/api/shopCategory";
import { setEmptyArraysToNull } from "@/utils/utils";
// import noData from "@/components/Nodata/index.vue";

const props = defineProps({
  shopCategoryId: {
    type: Number,
    default: 0,
  },
});
const editForm = ref({
  categoryName: "",
  thirdProductCategoryIds: [],
});
const editVisible = ref(false);
const settingDialogVisible = ref(false);
const queryParams = ref({});
const dialogDataObj = ref({});
const platCateGoryId = ref("");
let formData = {};
const tableData = ref([]);
const options = ref([]);

const loading = ref(false);

// const handleEdit = (e, row) => {
//   e.stopPropagation();
//   dialogDataObj.value = { shopCategoryId: row.shopCategoryId };
//   settingDialogVisible.value = true;
// };
const handleEdit = (e, row) => {
  console.log("handleEdit e === ", e);
  console.log("handleEdit row === ", row);
  dialogDataObj.value = row;
  settingDialogVisible.value = true;
};
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const actionsClick = async (type, data) => {
  console.log(data, "data123");
  platCateGoryId.value = data.categoryId;
  if (type === "edit") {
    let params = {
      level: 1,
      thirdType: "XIAO_DIAN",
    };
    const res = await GetListByThird(params);
    if (res.code === 0) {
      // message.success(result.message || "请求失败");
      options.value = res.data;
      editForm.value.categoryName = data.categoryName;
      if (
        data.thirdProductCategoryList &&
        Array.isArray(data.thirdProductCategoryList)
      ) {
        editForm.value.thirdProductCategoryIds =
          data.thirdProductCategoryList.map((v) => {
            return v.id;
          });
      } else {
        editForm.value.thirdProductCategoryIds = [];
      }

      editVisible.value = true;
    }
  }
};

const editSubmit = async () => {
  const params = {
    platCateGoryId: platCateGoryId.value,
    thirdProductCategoryIds: editForm.value.thirdProductCategoryIds,
  };
  const res = await GetSaveCategoryRef(params);
  if (res.code === 0) {
    message.success("操作成功");
    editVisible.value = false;
    getList();
  } else {
    message.error(res.message);
  }
};

const formList = [
  {
    label: "后台分类",
    name: "platFirstCategoryName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "分类状态",
    name: "categoryStatus",
    type: "select", // 输入框
    options: [
      { label: "启用", value: "ENABLE" },
      { label: "禁用", value: "DISABLE" },
    ],
    span: 6,
  },
];

// 获取表格数据
const getList = () => {
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    productSource: "XIAO_DIAN",
    ...formData,
  };

  loading.value = true;
  GetThirdCategory(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data.records)) {
        tableData.value = res.data.records;
        tableData.value = setEmptyArraysToNull(tableData.value);
        pagination.value.total = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleSearch = (param) => {
  console.log(param, "pram");
  formData = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

// 分页变化
const onPaginationChange = (newPagination) => {
  console.log(newPagination, "newPagination");
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
  console.log(pagination, "pagination");
};

onMounted(() => {
  getList();
});
</script>

<style lang="less" scoped>
.mt8 {
  margin-top: 8px;
}
.mt16 {
  margin-top: 16px;
  flex: 1;
}
.t-table {
  margin-top: 16px;
  :deep(.parent-td-style) {
    padding-left: 0;
  }
}
.t-button--variant-text {
  padding: 0;
}
:deep(.indent-level-2) {
  padding-left: 24px !important;
}
</style>
