<template>
  <a-modal
    v-model:open="lookVisible"
    placement="center"
    title="新增商品"
    :destroy-on-close="true"
    width="60%"
    @cancel="cancelClick"
  >
    <a-form
      :model="formState"
      name="horizontal_login"
      layout="inline"
      autocomplete="off"
      class="mt20"
    >
      <a-form-item label="商品名称" name="skuName">
        <a-input
          v-model:value="formState.skuName"
          placeholder="请输入"
        ></a-input>
      </a-form-item>

      <a-form-item label="商品编码" name="skuNumber">
        <a-input
          v-model:value="formState.skuNumber"
          placeholder="请输入"
        ></a-input>
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="getSkuListApi">查询</a-button>
        <a-button @click="resetClick" class="ml10">重置</a-button>
      </a-form-item>
    </a-form>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      row-key="skuId"
      @update:pagination="handleTableChange"
      @select-change="selectChange"
      class="mt20 add-table"
    >
    </table-list-antd>
    <template #footer>
      <a-button @click="emits('close-click')">取消</a-button>
      <a-button type="primary" @click="submitDisAgree">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { getSkuList } from "@/api/goodsCenter/ecommerceGoods";
import TableListAntd from "@/components/TableListAntd/index.vue";
import { message } from "woody-ui";

// const props = defineProps({
//   itemData: {
//     type: Object,
//     default: () => ({}),
//   },
//   auditList: {
//     type: Array,
//     default: () => [],
//   },
// });
const selectData = ref([]);
const formState = ref({
  skuName: "",
  skuNumber: "",
});
const columns = [
  {
    title: "商品信息",
    dataIndex: "skuName",
    key: "skuName",
    width: 260,
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
    key: "categoryName",
    width: 150,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    width: 150,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
});

const handleTableChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getSkuListApi();
};

const emits = defineEmits([
  "onSucceed",
  "reject-click",
  "close-click",
  "add-click",
]);
const dataSource = ref([]);
const loading = ref(false);

// 获取列表数据
const getSkuListApi = async () => {
  loading.value = true;
  const { skuName, skuNumber } = formState.value;
  const params = {
    current: pagination.value.current,
    size: 10,
    skuName,
    skuNumber,
  };
  const res = await getSkuList(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const selectChange = (data) => {
  selectData.value = data;
};
const resetClick = () => {
  formState.value.skuName = "";
  formState.value.skuNumber = "";
  getSkuListApi();
};
const lookVisible = ref(true);

const cancelClick = () => {
  lookVisible.value = false;
  emits("close-click");
};
const open = (code: String, isAudit: Boolean) => {};
const submitDisAgree = () => {
  if (!selectData.value.length) {
    message.error("请勾选商品");
    return;
  }
  emits("add-click", selectData.value);
};

// 驳回成功
const onSucceed = () => {
  emits("onSucceed");
};

defineExpose({
  open,
});
onMounted(() => {
  getSkuListApi();
});
</script>

<style lang="less" scoped>
// .dia-box {
//   margin: 16px;
//   .title {
//     font-weight: bold;
//     font-size: 16px;
//     color: #05082c;
//     margin-bottom: 16px;
//   }
//   .sub-title {
//     font-size: 14px;
//     color: #495366;
//     white-space: nowrap;
//   }
// }
// .f-item {
//   .item-name {
//     font-size: 14px;
//     color: #05082c;
//     height: 40px;
//     line-height: 40px;
//     padding-left: 10px;
//     background: rgb(242, 242, 242);
//     .require {
//       font-size: 14px;
//       color: #ff436a;
//       margin-left: 3px;
//     }
//   }
//   p {
//     line-height: 40px;
//     padding: 20px 0;
//   }
// }
.img-css {
  width: 80px;
  height: 80px;
}
.flex-box {
  display: flex;
  align-items: center;
}
.add-table {
  max-height: 600px;
  overflow: auto;
}
</style>
<style lang="less">
.t-dialog__header {
  border-bottom: 1px solid var(--td-border-level-1-color);
}
</style>
