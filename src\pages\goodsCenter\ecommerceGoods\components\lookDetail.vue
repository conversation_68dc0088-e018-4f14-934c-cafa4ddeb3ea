<template>
  <a-modal
    v-model:open="lookVisible"
    placement="center"
    title="查看"
    :destroy-on-close="true"
    width="70%"
    @cancel="cancelClick"
    class="model-css"
  >
    <div class="dia-box">
      <div class="f-item">
        <div class="item-name">商品类型</div>
        <p>实物商品（物流发货）</p>
      </div>
      <div class="f-item">
        <div class="item-name">配送方式</div>
        <p>{{ props.itemData.deliveryModelDesc }}</p>
      </div>
      <div class="f-item">
        <div class="item-name">后台分类</div>
        <p>
          后台分类：
          {{ props.itemData.topPlatCategoryName }}/
          {{ props.itemData.platParentCategoryName }}/{{
            props.itemData.platCategoryName
          }}
        </p>
      </div>
      <div class="f-item">
        <div class="item-name">基本信息</div>
        <div>
          <div class="val-item">
            <p class="lable">商品名称:</p>
            <p>{{ props.itemData.prodName }}</p>
          </div>
          <div class="val-item">
            <p class="lable">卖点标签:</p>
            <p>{{ props.itemData.buyingPointTags }}</p>
          </div>
          <div class="val-item">
            <p class="lable">商品卖点:</p>
            <p style="width: 90%">{{ props.itemData.brief }}</p>
          </div>
          <div class="img-dox">
            <span style="width: 80px">商品图片:</span>
            <div style="width: 90%">
              <!-- <img
                v-for="(item, index) in props.itemData.picInfos"
                :key="index"
                :src="item.path"
                class="img-info ant-image-mask"
              /> -->
              <a-image
                style="width: 80px; height: 80px; margin: 5px"
                v-for="(item, index) in props.itemData.picInfos"
                :key="index"
                :src="item.path"
                fallback="data:image/png;base64,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"
              />
            </div>
          </div>
          <div class="val-item">
            <p class="lable">商品视频:</p>
            <video
              v-if="props.itemData?.videoInfo?.path"
              :src="props.itemData?.videoInfo?.path"
              controls
              style="width: 200px; height: 150px"
            ></video>
          </div>
          <div class="val-item">
            <p class="lable">商品品牌:</p>
            <p style="width: 90%">{{ props.itemData.brandName }}</p>
          </div>
          <div class="val-item">
            <p class="lable">售后期限:</p>
            <p v-if="props.itemData.afterSalePeriod != null">
              {{
                props.itemData.afterSalePeriod === 0
                  ? "不支持售后"
                  : props.itemData.afterSalePeriod + "天内可发起售后"
              }}
            </p>
          </div>
          <div class="val-item">
            <p class="lable">
              近义词:<a-tag
                class="ml10"
                v-for="item in props.itemData.prodTags"
                :key="item.id"
                >{{ item.prodTag }}</a-tag
              >
            </p>
          </div>
        </div>
      </div>
      <div class="f-item">
        <div class="item-name">规格库存</div>
        <div>
          <p>
            商品规格：规格名：<span
              v-if="
                props.itemData.skuList && props.itemData.skuList[0].properties
              "
            ></span
            >{{ props.itemData.skuList[0].properties.split(":")[0] }}
          </p>
          <p v-for="item in props.itemData.skuList" :key="item.skuId">
            规格值：<span v-if="item.properties"></span
            >{{ item.properties.split(":")[1] }}：{{
              item.properties.split(":")[2]
            }}
            <!-- <img :src="item.picInfo.path" style="width: 100px; height: 100px" /> -->
            <a-image
              :src="item.picInfo?.path"
              style="width: 100px; height: 100px"
              class="ml10"
              fallback="data:image/png;base64,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"
            />
          </p>
          <p>价格及库存</p>
        </div>
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          :pagination="false"
        />
      </div>
      <div class="f-item">
        <div class="item-name">商品详情</div>
        <div v-html="props.itemData.content" class="content-css"></div>
      </div>
    </div>
    <template #footer>
      <a-button @click="cancelClick">返回</a-button>
      <a-button type="primary" @click="bohuiClick" v-if="props.isBohui"
        >驳回</a-button
      >
      <a-button type="primary" @click="submitDisAgree" v-if="props.isAgree"
        >同意</a-button
      >
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const props = defineProps({
  itemData: {
    type: Object,
    default: () => {},
  },
  auditList: {
    type: Array,
    default: () => [],
  },
  isBohui: {
    type: Boolean,
    default: true,
  },
  isAgree: {
    type: Boolean,
    default: true,
  },
});
const columns = [
  {
    title: "成本价（元）",
    dataIndex: "costPrice",
    key: "costPrice",
  },
  {
    title: "市场价（元）",
    dataIndex: "oriPrice",
    key: "oriPrice",
  },
  {
    title: "销售价（元）",
    dataIndex: "price",
    key: "price",
  },
  {
    title: "库存",
    dataIndex: "stocks",
    key: "stocks",
  },
  {
    title: "规格",
    dataIndex: "prodName",
    key: "prodName",
  },
  {
    title: "商品体积(m³)",
    dataIndex: "volume",
    key: "volume",
  },
  {
    title: "商品重量(kg)",
    dataIndex: "weight",
    key: "weight",
  },
  {
    title: "单位",
    dataIndex: "unit",
    key: "unit",
  },
  {
    title: "国标码",
    dataIndex: "skuNumber",
    key: "skuNumber",
  },
];

console.log(props.itemData, "0099");
// 整理成 Table 组件所需的 dataSource

const dataSource = props.itemData.skuList.map((item) => ({
  key: item.skuId, // 必须为每个数据项指定一个唯一的 key
  skuId: item.skuId,
  oriPrice: item.oriPrice,
  properties: item.properties,
  price: item.price,
  costPrice: item.costPrice,
  stocks: item.stocks,
  prodName: item.prodName,
  weight: item.weight,
  volume: item.volume,
  skuNumber: item.skuNumber,
  unit: item.unit,
}));

const emits = defineEmits([
  "onSucceed",
  "reject-click",
  "close-click",
  "bohui-click",
  "agree-click",
]);

const lookVisible = ref(true);

const cancelClick = () => {
  emits("close-click");
};
const bohuiClick = () => {
  emits("bohui-click", props.itemData);
};
const open = (code: String, isAudit: Boolean) => {
  // disAgreeDia.value = {
  //   visible: true,
  //   data: {
  //     rejectReason: "",
  //     rejectNote: "",
  //     rejectImageUrl: "",
  //   },
  //   loading: false,
  // };
  // isBatchAudit.value = isAudit;
  // console.log('测试？')
  // uploadRef.value.handleClear();
};
const updates = ref("");
const timer = ref(null);
const submitDisAgree = () => {
  // if (!disAgreeDia.value.data.rejectReason)
  //   return MessagePlugin.error("请选择驳回原因");
  // if (!disAgreeDia.value.data.rejectNote)
  //   return MessagePlugin.error("请输入驳回原因");

  // disAgreeDia.value.loading = true;
  emits("agree-click", props.itemData);
  // if (isBatchAudit.value) {
  //   const list = [];
  //   props.auditList.forEach((item) => {
  //     const info = {
  //       auditId: item.auditId,
  //       nodeId: item.nodeId,
  //       rejectReason: disAgreeDia.value.data.rejectReason,
  //       rejectNote: disAgreeDia.value.data.rejectNote,
  //       rejectImageUrl: disAgreeDia.value.data.rejectImageUrl,
  //     };
  //     list.push(info);
  //   });
  //   httpBatchReject(list)
  //     .then((res) => {
  //       if (res.code === 0) {
  //         disAgreeDia.value.loading = false;
  //         disAgreeDia.value.visible = false;
  //         MessagePlugin.success("提交成功");
  //         fileList.value = [];
  //         onSucceed();
  //       }
  //     })
  //     .catch((err) => {
  //       console.log(err);
  //     })
  //     .finally(() => {
  //       disAgreeDia.value.loading = false;
  //     });
  // } else {
  //   const { auditId, nodeId } = props.disagreeData;
  //   httpReject({ ...disAgreeDia.value.data, auditId, nodeId })
  //     .then((res) => {
  //       if (res.code === 0) {
  //         disAgreeDia.value.loading = false;
  //         disAgreeDia.value.visible = false;
  //         MessagePlugin.success("提交成功");
  //         fileList.value = [];
  //         onSucceed();
  //       }
  //     })
  //     .finally(() => {
  //       disAgreeDia.value.loading = false;
  //     });
  // }
};

// 驳回成功
const onSucceed = () => {
  emits("onSucceed");
};

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.dia-box {
  margin: 20px 0;
  max-height: 600px;
  overflow-y: auto;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #05082c;
    margin-bottom: 16px;
  }
  .sub-title {
    font-size: 14px;
    color: #495366;
    white-space: nowrap;
  }
}
.f-item {
  .item-name {
    font-size: 16px;
    color: #05082c;
    font-weight: 500;
    height: 50px;
    line-height: 50px;
    padding-left: 10px;
    background: rgb(242, 242, 242);
    .require {
      font-size: 14px;
      color: #ff436a;
      margin-left: 3px;
    }
  }
  .val-item {
    line-height: 30px;
    display: flex;
    .lable {
      width: 80px;
    }
  }
  p {
    line-height: 30px;
    padding: 10px 0;
  }
  .img-info {
    width: 80px;
    height: 80px;
    margin: 5px;
  }
  // .img-info:hover{
  //   position: absolute;
  //   top: 0;
  //   right: 0;
  //   bottom: 0;
  //   left: 0;
  //   // display: flex;
  //   // align-items: center;
  //   // justify-content: center;
  //   // color: #fff;
  //   background: rgba(0, 0, 0, .5);
  //   cursor: pointer;
  //   opacity: 0;
  //   // transition: opacity .3s;
  // }
  .img-dox {
    width: 100%;
    display: flex;
    overflow: auto;
    margin: 10px 0;
  }
  .content-css {
    margin-top: 10px;
    width: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
<style lang="less">
.t-dialog__header {
  border-bottom: 1px solid var(--td-border-level-1-color);
}
</style>
