<template>
  <div class="header-btn">
    <span>全局积分</span>
    <div style="float: right">
      <a-button danger @click="shareClick" class="ml10"
        >分享商品主要事项</a-button
      >
      <a-button type="primary" @click="openClick" class="ml10"
        >{{ globalPv.pvRatio }}%（{{
          globalPv.status === 1 ? "开" : "关"
        }}）</a-button
      >
    </div>
  </div>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button @click="logClick" class="ml10">操作日志</a-button>
      <a-button type="primary" @click="addClick" class="ml10">新增</a-button>
    </div>
    <table-list-antd
      :is-checkbox="false"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      class="mt10"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="openVisible"
    placement="center"
    title="商品积分"
    :destroy-on-close="true"
    width="30%"
  >
    <div class="msg-css">
      用户购买商品是否享受积分，开启后除调整比例商品
      其余商品按全局比例计算积分，如若关闭，则根据供货仓店铺的签约比例取商品积分值。
    </div>
    <a-form
      :model="openForm"
      name="horizontal_login"
      layout="vertical"
      autocomplete="off"
      style="margin-bottom: 30px"
    >
      <a-form-item label="全局比例" name="status">
        <a-radio-group v-model:value="openForm.status" name="radioGroup">
          <a-radio :value="1">开</a-radio>
          <a-radio :value="0">关</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="积分比例" name="pvRatio" class="mt20">
        <a-input-number
          class="wth300"
          v-model:value="openForm.pvRatio"
          :min="3"
          :max="20"
          placeholder="请输入"
        >
          <template #addonAfter>
            <div>%</div>
          </template>
        </a-input-number>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="openVisible = false">取消</a-button>
      <a-button type="primary" @click="openSubmit">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="editVisible"
    placement="center"
    title="修改比例"
    :destroy-on-close="true"
    width="35%"
  >
    <a-form
      :model="editForm"
      name="horizontal_login"
      layout="vertical"
      autocomplete="off"
      style="margin: 30px 0"
    >
      <a-form-item label="商品积分比例" name="pvRatio">
        <a-input-number
          v-model:value="editForm.pvRatio"
          placeholder="请输入"
          :min="3"
          :max="20"
          style="width: 300px"
        >
          <template #addonAfter>
            <div>%</div>
          </template>
        </a-input-number>
        <div class="jifen-css">
          商品积分比例=平台服务费抽成比例=购买人积分比例=供货仓积分比例
        </div>
      </a-form-item>

      <a-form-item label="是否开启商品分享功能" name="openShare" class="mt20">
        <a-radio-group
          v-model:value="editForm.openShare"
          name="radioGroup"
          @change="radioChange"
        >
          <a-radio value="Y">开</a-radio>
          <a-radio value="N">关</a-radio>
        </a-radio-group>
      </a-form-item>
      <!-- <a-form-item
        label="分享人积分比例"
        name="sharePvRatio"
        v-if="editForm.openShare === 'Y'"
        class="mt20"
      >
        <a-input
          v-model:value="editForm.sharePvRatio"
          placeholder="请输入"
          style="width: 300px"
        ></a-input>
        <div class="jifen-css">
          通过分享人销售的商品会额外扣除供货仓对应比例的平台服务费抽成
        </div>
        <div class="jifen-css">分享人会获得该比例的积分</div>
        <div class="jifen-css">购买人和供货仓获得的积分比例不变</div>
      </a-form-item> -->
    </a-form>
    <template #footer>
      <a-button @click="editVisible = false">取消</a-button>
      <a-button type="primary" @click="editSubmit">确定</a-button>
    </template>
  </a-modal>
  <add-goods
    v-if="addVisible"
    @add-click="addSubmit"
    @close-click="addVisible = false"
  ></add-goods>
</template>

<script setup lang="tsx">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import addGoods from "./components/addGoods.vue";
import {
  getIntegralPaginationList,
  setGlobalPv,
  getGlobalPv,
  deleteIntegralItem,
  updateIntegralItemPv,
  addIntegralItem,
} from "@/api/goodsCenter/ecommerceGoods";
import { message } from "woody-ui";

const editVisible = ref(false);
const openVisible = ref(false);
const addVisible = ref(false);

const router = useRouter();

let formData = {};

const editForm = ref({
  openShare: "",
  pvRatio: "",
  skuId: "",
  sharePvRatio: "",
});

const openForm = ref({
  status: 1,
  pvRatio: "20",
});

import type { Rule } from "woody-ui/es/form";

const addSubmit = async (data) => {
  const skuIds = data.map((v) => {
    return v.skuId;
  });
  const res = await addIntegralItem({ skuIds });
  if (res.code === 0) {
    message.success(res.message);
    addVisible.value = false;
    getList();
  }
};

//radio事件--供应仓接入新支付需求
const radioChange = (e) => {
  const { value } = e.target;
  console.log(value, "value");
  if (value === "Y") {
    message.warning("抱歉，暂不支持开启商品分享");
    editForm.value.openShare = "N";
  }
};
const openClick = () => {
  openForm.value.status = globalPv.value.status;
  openForm.value.pvRatio = globalPv.value.pvRatio;
  openVisible.value = true;
};
const logClick = () => {
  router.push({
    path: "/product/ecommerceGoods/pointsLog",
  });
};
const shareClick = () => {
  window.open(
    "https://doc.weixin.qq.com/doc/w3_AT8AEgY2AG0BhxqLzJ5RNuObEtMDI?scode=AKcAAAfmAAcpsYRAm5AT8AEgY2AG0"
  );
};
const globalPv = ref({
  pvRatio: "20",
  status: 0,
});
const openSubmit = async () => {
  const { status, pvRatio } = openForm.value;
  const params = {
    pvRatio,
    status,
  };
  const res = await setGlobalPv(params);
  if (res.code === 0) {
    message.success("操作成功");
    openVisible.value = false;
    getList();
    getGlobalPvApi();
  }
};

const getGlobalPvApi = async () => {
  const res = await getGlobalPv({});
  if (res.code === 0) {
    const { pvRatio, status } = res.data;
    globalPv.value.status = status;
    globalPv.value.pvRatio = pvRatio;
  }
};
const formList = [
  {
    label: "商品类型",
    name: "openShare",
    type: "select", // 输入框
    span: 6,
    options: [
      { label: "全部", value: "" },
      { label: "分享商品", value: "Y" },
    ],
  },
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品编码",
    name: "skuNumber",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "积分比例",
    name: "pvRatio",
    type: "input", // 输入框
    span: 6,
    isSuffix: "%",
  },
  {
    label: "分享人积分比例",
    name: "sharePvRatio",
    type: "input", // 输入框
    span: 6,
    isSuffix: "%",
  },
  {
    label: "添加时间",
    name: "addTime",
    type: "rangePicker", // 输入框
    span: 6,
  },
];

const handleSearch = (param) => {
  const { addTime } = param;
  formData = param;
  formData["startTime"] = Array.isArray(addTime)
    ? addTime[0] + " 00:00:00"
    : "";
  formData["endTime"] = Array.isArray(addTime) ? addTime[1] + " 23:59:59" : "";
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const editSubmit = async () => {
  const params = {
    ...editForm.value,
  };
  const res = await updateIntegralItemPv(params);
  if (res.code === 0) {
    message.success("修改成功");
    editVisible.value = false;
    getList();
    return;
  }
  message.success(res.message);
};

//table表头数据
const columns = [
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    fixed: true,
    align: "left",
    width: 300,
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
    key: "categoryName",
    align: "left",
    width: 150,
  },
  {
    title: "商品积分比例",
    dataIndex: "pvRatio",
    key: "pvRatio",
    align: "left",
    width: 150,
  },
  {
    title: "分享人积分比例",
    dataIndex: "sharePvRatio",
    key: "sharePvRatio",
    align: "left",
    width: 150,
  },
  {
    title: "添加时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 150,
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    width: 200,
    align: "left",
    actionType: [
      {
        type: "delete",
        title: "移除",
        isPop: true,
      },
      {
        type: "edit",
        title: "修改比例",
        isPop: false,
      },
    ],
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const skuId = ref("");
const getList = async () => {
  loading.value = true;
  const params = {
    current: pagination.value.current,
    size: pagination.value.pageSize,
    ...formData,
  };
  const res = await getIntegralPaginationList(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const addClick = () => {
  skuId.value = "";
  addVisible.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  console.log(data, "data123");
  const { openShare, pvRatio, skuId, sharePvRatio } = data;
  editForm.value.openShare = openShare;
  editForm.value.skuId = skuId;
  editForm.value.pvRatio = pvRatio;
  editForm.value.sharePvRatio = sharePvRatio;
  if (type === "delete") {
    deleteApi(skuId);
  } else if (type === "edit") {
    editVisible.value = true;
  }
};

const deleteApi = async (id) => {
  const res = await deleteIntegralItem({ skuIds: [id] });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  console.log(newPagination, "newPagination");
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
  console.log(pagination, "pagination");
};

onMounted(() => {
  getList();
  getGlobalPvApi();
});
</script>
<style lang="less" scoped>
.header-btn {
  margin-bottom: 10px;
  line-height: 40px;
}
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.msg-css {
  color: rgb(230, 162, 60);
  background: rgb(253, 246, 236);
  padding: 15px;
  margin-bottom: 20px;
}
.jifen-css {
  width: 300px;
  color: rgba(136, 141, 141, 0.839);
  margin-top: 10px;
}
.add-form-css {
  padding: 30px 0;
}
</style>
