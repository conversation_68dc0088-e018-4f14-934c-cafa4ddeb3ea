<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button type="primary" @click="exportVisible = true">导出</a-button>
      <a-button @click="offClick" class="ml10">违规下架</a-button>
      <a-button @click="logClick" style="float: right">操作日志</a-button>
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      row-key="productId"
      @update:pagination="onPaginationChange"
      @fetchData="fetchTableData"
      @actions-click="actionsClick"
      @select-change="selectChange"
      @blur-change="blurChange"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="offVisible"
    placement="center"
    title="违规信息"
    :destroy-on-close="true"
    width="30%"
    @cancel="closeClick"
  >
    <a-form ref="formRef" name="custom-validation" class="mt20">
      <a-form-item has-feedback label="下架原因" name="propName">
        <a-textarea
          v-model:value="reason"
          type="input"
          autocomplete="off"
          show-count
          :maxlength="200"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="offVisible = false">取消</a-button>
      <a-button type="primary" @click="upSubmit">确定</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="exportVisible"
    placement="center"
    title="导出商品数据"
    :destroy-on-close="true"
    width="40%"
    @cancel="exportClose"
  >
    <a-form
      ref="exportFormRef"
      name="custom-validation"
      :model="exportFormData"
      class="mt20"
      :rules="rules"
      v-bind="layout"
    >
      <a-form-item has-feedback label="供货仓" name="supplierId">
        <a-select
          v-model:value="exportFormData.supplierId"
          placeholder="请选择供货仓"
          allowClear
          show-search
          :show-arrow="false"
          :filter-option="false"
          mode="multiple"
          :options="sourceArr"
          @search="remoteMethod"
          @blur="selectBlur"
        />
      </a-form-item>
      <a-form-item has-feedback label="商品状态" name="status">
        <a-select
          v-model:value="exportFormData.status"
          placeholder="请选择商品状态"
          mode="multiple"
          allowClear
          :options="statusArr"
        />
      </a-form-item>
      <a-form-item has-feedback label="电子邮箱" name="emailAddress">
        <a-input
          v-model:value="exportFormData.emailAddress"
          type="input"
          autocomplete="off"
          placeholder="请输入电子邮箱"
        />
        <p class="mt10">
          导出完成文件会发送至邮箱，请勿多次发送 请确保邮箱正确
        </p>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="exportVisible = false">取消</a-button>
      <a-button @click="exportReset">重置</a-button>
      <a-button type="primary" @click="exportSubmit">导出</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:open="resVisible"
    placement="center"
    title="违规信息"
    :destroy-on-close="true"
    width="30%"
    @cancel="resVisible = false"
  >
    <a-form
      name="custom-validation"
      :model="detailObj"
      class="mt20"
      :label-col="{ span: 4 }"
    >
      <a-form-item has-feedback label="操作人" name="operatorUserName">
        <a-input v-model:value="detailObj.operatorUserName" :disabled="true" />
      </a-form-item>
      <a-form-item has-feedback label="下架原因" name="status">
        <a-textarea
          v-model:value="detailObj.violationReason"
          :disabled="true"
        />
      </a-form-item>
      <a-form-item has-feedback label="下架时间" name="emailAddress">
        <a-input v-model:value="detailObj.operateTime" :disabled="true" />
      </a-form-item>
    </a-form>
    <template #footer> </template>
  </a-modal>
  <a-modal
    v-model:open="bohuiVisible"
    placement="center"
    title="驳回原因"
    :destroy-on-close="true"
    width="30%"
    @cancel="bohuiVisible = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      class="mt20"
      :label-col="{ span: 4 }"
      :model="reasonObj"
    >
      <a-form-item has-feedback label="审核人" name="propName">
        <a-input
          v-model:value="reasonObj.operatorUserName"
          type="input"
          :disabled="true"
          autocomplete="off"
        />
      </a-form-item>
      <a-form-item has-feedback label="驳回原因" name="propName">
        <a-select
          :value="reasonObj.manualAuditRejectReasonVO?.rejectReason ?? ''"
          type="input"
          :disabled="true"
          :options="reasonList"
          :fieldNames="{ label: 'name', value: 'value' }"
        />
      </a-form-item>
      <a-form-item has-feedback label="备注" name="propName">
        <a-textarea
          :disabled="true"
          :value="reasonObj.manualAuditRejectReasonVO?.rejectNote ?? ''"
          type="input"
          autocomplete="off"
          show-count
          :maxlength="200"
        />
      </a-form-item>
      <a-form-item has-feedback label="图片" name="propName">
        <img
          :src="reasonObj.manualAuditRejectReasonVO?.rejectImageUrl || noImg"
          style="width: 100px; height: 100px"
        />
      </a-form-item>
    </a-form>
    <template #footer> </template>
  </a-modal>
  <look-detail
    v-if="lookShow"
    :isBohui="false"
    :isAgree="false"
    :item-data="itemData"
    @close-click="lookShow = false"
  ></look-detail>
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import noImg from "@/assets/images/no-img.png";
import {
  GetProductPage,
  GetDictItems,
  GetDictItem,
  GetSupplierPage,
  GetCommodityList,
  violationOffSale,
  getCommodityInfoById,
  ExportInterface,
  GetViolationDetail,
  GetNewViolationOffSale,
  GetWaterSoldNum,
  GetAuditDetails,
} from "@/api/goodsCenter/ecommerceGoods";
import { httpSupplierList } from "@/api/product";
import { getQueryDictItems } from "@/api/common";
import lookDetail from "./components/lookDetail.vue";
import { useRoute, useRouter } from "vue-router";
import { message } from "woody-ui";

const prodAuditData = ref([]);
const sourceData = ref([]);
const selectId = ref([]);
const lookShow = ref(false);
const reason = ref("");
const router = useRouter();
const exportFormRef = ref();
const resVisible = ref(false);
const detailObj = ref(null);
const reasonObj = ref(null);
const bohuiVisible = ref(false);
const randomKey = ref(null);
const rules = {
  emailAddress: [{ required: true, message: "邮箱地址必填", trigger: "blur" }],
};
let formData = {};
const exportFormData = ref({
  emailAddress: "",
  supplierId: [],
  status: [],
});
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const statusArr = [
  {
    label: "上架",
    value: 1,
  },
  {
    label: "下架",
    value: 0,
  },
  {
    label: "违规下架",
    value: 2,
  },
];
const itemData = ref({});
const productId = ref("");
const offVisible = ref(false);
const exportVisible = ref(false);
const sourceArr = ref([]);
const remoteMethod = (search) => {
  setTimeout(() => {
    searchSource(search);
  }, 500);
};
const formList = [
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    type: "cascader",
    label: "后台分类",
    name: "cascader",
    span: 6,
    isLoad: true,
    labelKey: "categoryName",
    valueKey: "categoryId",
    childrenKey: "newCategoryModelDtos",
    searchFn: "common",
    changeOnSelect: true,
    // searchFn: async (id, obj, isLeaf = true) => {
    //   let res = await GetCommodityList({ categoryId: id });
    //   console.log(res, "resss");
    //   let curr = res.data[0]; // 目前只有2级
    //   let res1 = await GetCommodityList({ categoryId: curr.categoryId });
    //   if (res1.data.length) {
    //     res.data.forEach((item) => {
    //       item.isLeaf = false;
    //     });
    //   }
    //   return res.data;
    // },
  },
  {
    type: "select",
    label: "商品状态",
    name: "status",
    span: 6,
    needFilter: true,
    showSearch: true,
    options: [
      {
        label: "上架",
        value: 1,
      },
      {
        label: "下架",
        value: 0,
      },
      {
        label: "违规下架",
        value: 2,
      },
    ],
  },
  {
    type: "rangePicker",
    label: "创建时间",
    name: "date",
    // showTime: true,
    span: 6,
  },
  {
    label: "国标码",
    name: "skuNumber",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "SPU ID",
    name: "prodIds",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "供货仓",
    name: "supplierId",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: sourceArr,
    showSearch: true,
    needFilter: true,
    remoteMethod,
    span: 6,
  },
  {
    label: "商品来源",
    name: "prodSource",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: sourceData,
    span: 6,
  },
  {
    label: "审核状态",
    name: "prodAuditStatusDictCode",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: prodAuditData,
    mode: "multiple",
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  const { date, cascader, prodIds, status } = param;
  formData = param;
  formData["createStartTime"] = Array.isArray(date)
    ? date[0] + " 00:00:00"
    : "";
  formData["firstCategoryId"] = Array.isArray(cascader) ? cascader[0] : "";
  formData["secondCategoryId"] = Array.isArray(cascader) ? cascader[1] : "";
  if (cascader && cascader.length === 3) {
    formData["threeCategoryId"] = cascader[2];
  }
  formData["createEndTime"] = Array.isArray(date) ? date[1] + " 23:59:59" : "";
  if (prodIds) formData["prodIds"] = [prodIds];
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  formData["status"] = status;
  getList();
};
const sourceLoading = ref(false);
const searchSource = (keyword: string) => {
  sourceLoading.value = true;
  httpSupplierList({ name: keyword })
    .then((res) => {
      if (res.code === 0) {
        const tempArr = [];
        res.data.forEach((item) => {
          tempArr.push({
            value: item.supplierId,
            label: item.supplierName,
          });
        });
        sourceArr.value = tempArr;
        // randomKey.value = Math.random().toString(36).substring(2, 15)
      }
      console.log(sourceArr, "sourceArr");
    })
    .finally(() => {
      sourceLoading.value = false;
    });
};

const selectBlur = async () => {
  searchSource("");
};

const selectChange = (data, idArr) => {
  selectId.value = idArr;
};

const logClick = () => {
  router.push({
    path: "/product/ecommerceGoods/pointsLog",
  });
};

const reasonList = ref([]);
// 获取驳回原因
const getReasonList = async (code: String) => {
  const params = {
    dictCode: code,
  };

  const res = await getQueryDictItems(params);
  if (res.code === 0) {
    reasonList.value = res.data;
  }
};

const blurChange = async (id, data) => {
  const params = {
    productId: id,
    seq: data,
  };
  const res = await GetWaterSoldNum(params);
  if (res.code === 0) {
    message.success("操作成功");
    getList();
  }
};

const exportReset = () => {
  exportFormData.value.emailAddress = "";
  exportFormData.value.supplierId = [];
  exportFormData.value.status = [];
};

const exportSubmit = () => {
  exportFormRef.value
    .validate()
    .then(async () => {
      const { emailAddress, supplierId, status } = exportFormData.value;
      const params = {
        taskName: "new_platform_product_export",
        informWay: "email",
        inform: JSON.stringify({ emailAddress: [emailAddress] }),
        taskParam: JSON.stringify({ supplierId, status }),
      };
      const res = await ExportInterface(params);
      if (res.code === 0) {
        message.success(res.message);
        exportVisible.value = false;
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

const offClick = () => {
  if (!selectId.value.length) {
    message.error("请选择要违规下架的商品");
    return;
  }
  offVisible.value = true;
};

//table表头数据
const columns = [
  {
    title: "SPU ID",
    dataIndex: "productId",
    key: "productId",
    fixed: "left",
    align: "left",
    width: 200,
  },
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    align: "left",
    width: 350,
  },
  {
    title: "后台分类",
    dataIndex: "platParentCategoryName",
    key: "platParentCategoryName",
    align: "left",
    width: 150,
  },
  {
    title: "配送方式",
    dataIndex: "deliveryModelDesc",
    key: "deliveryModelDesc",
    align: "left",
    width: 150,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 200,
  },
  {
    title: "操作时间",
    dataIndex: "updateTime",
    key: "updateTime",
    align: "left",
    width: 200,
  },
  {
    title: "售价",
    dataIndex: "price",
    key: "price",
    align: "left",
    width: 150,
    customRender: ({ record }) => {
      return record.prodSource === 7
        ? record.minPrice === record.maxPrice
          ? record.maxPrice
          : record.minPrice + "-" + record.maxPrice
        : record.price;
    },
  },
  {
    title: "划线价",
    dataIndex: "oriPrice",
    key: "oriPrice",
    align: "left",
    width: 150,
  },
  {
    title: "注水销量",
    dataIndex: "waterSoldNum",
    key: "waterSoldNum",
    align: "left",
    width: 150,
  },
  {
    title: "商品状态",
    dataIndex: "status",
    key: "status",
    align: "left",
    width: 150,
  },
  {
    title: "审核状态",
    dataIndex: "productAuditStatusDesc",
    key: "productAuditStatusDesc",
    align: "left",
    width: 150,
  },
  {
    title: "审核角色",
    dataIndex: "nodeTypeDesc",
    key: "nodeTypeDesc",
    align: "left",
    width: 150,
  },
  {
    title: "操作人",
    dataIndex: "operatorUserName",
    key: "operatorUserName",
    align: "left",
    width: 150,
  },
  {
    title: "供货仓",
    dataIndex: "supplierName",
    key: "supplierName",
    align: "left",
    width: 150,
  },
  {
    title: "商品来源",
    dataIndex: "prodSource",
    key: "prodSource",
    align: "left",
    width: 150,
    customRender: (text) => {
      const matchedItem = sourceData.value.find(
        (item) => text.value === Number(item.value)
      );
      if (matchedItem) {
        return matchedItem.label;
      } else {
        // 可以返回一个默认值或者空字符串
        return "未知来源";
      }
    },
  },
  {
    title: "操作",
    key: "npGoodsList",
    fixed: "right",
    width: 300,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);

const upSubmit = async () => {
  const prodIds =
    selectId.value.length &&
    selectId.value.map((v) => {
      return v;
    });
  const params = {
    reason: reason.value,
    productIds: prodIds.length ? prodIds : [productId.value],
  };
  const res = await violationOffSale(params);
  if (res.code === 0) {
    message.success(res.message || "操作成功");
    offVisible.value = false;
    getList();
  }
};

const closeClick = () => {
  offVisible.value = false;
  reason.value = "";
};
const exportClose = () => {
  exportReset();
  exportVisible.value = false;
};
const getList = async () => {
  loading.value = true;
  const params = {
    page: pagination.value.current,
    size: pagination.value.pageSize,
    sort: "create_time,desc",
    ...formData,
  };
  const res = await GetProductPage(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

// 操作回调
const actionsClick = (type, data) => {
  productId.value = data.productId;
  if (type === "look") {
    getDetail(data);
  } else if (type === "shenhe") {
    console.log(data.productId, "data");
    router.push({
      path: `/product/ecommerceGoods/reviewHistory`,
      query: { productId: data.productId },
    });
  } else if (type === "weiguiRes") {
    offResApi(data);
  } else if (type === "weiguiOff") {
    offVisible.value = true;
  } else if (type === "bohui") {
    getReasonList("OPERATOR_REJECT_CAUSE");
    bohuiDetail(data);
  }
};

const bohuiDetail = async (data) => {
  console.log(productId.value, "productId.value");
  const res = await GetAuditDetails({ productAuditId: data.productAuditId });
  if (res.code === 0) {
    reasonObj.value = res.data;
    bohuiVisible.value = true;
  }
};

const offResApi = async (data) => {
  const res = await GetViolationDetail({ productId: data.productId });
  if (res.code === 0) {
    detailObj.value = res.data;
    resVisible.value = true;
    console.log(detailObj, "detailObj");
  }
};

const getDetail = async (data) => {
  const res = await getCommodityInfoById({ productId: data.productId });
  if (res.code === 0) {
    itemData.value = res.data;
    lookShow.value = true;
  }
  console.log(itemData, "itemData");
};

const getStatus = async () => {
  const params = {
    dictCode: "PRODUCT_AUDIT_STATE",
  };
  const res = await GetDictItems(params);
  if (res.code === 0) {
    prodAuditData.value = res.data.map((v) => {
      return {
        label: v.name,
        value: v.value,
      };
    });
  }
};

const getSource = async () => {
  const params = {
    dictCode: "SUPPLY",
  };
  const res = await GetDictItem(params);
  if (res.code === 0) {
    sourceData.value = res.data.map((v) => {
      return {
        label: v.name,
        value: v.value,
      };
    });
  }
};

// const getSupplyList = async () => {
//   const params = {
//     page: 1,
//     size: 10000,
//   };
//   const res = await GetSupplierPage(params);
//   if (res.code === 0) {
//     supplierData.value = res.data.records.map((v: any) => {
//       return {
//         label: v.supplierName,
//         value: v.supplierId,
//       };
//     });
//   }
// };

// 获取表格数据
const fetchTableData = ({ pagination, filters, sorter }) => {
  loading.value = true;
  getList();
};

// 分页变化
const onPaginationChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  console.log(pagination.value, "667");
  getList();
};

onMounted(() => {
  getList();
  searchSource("");
  // getSupplyList();
  getSource();
  getStatus();
  //   getShopListFunc();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    height: 40px;
    margin-bottom: 10px;
  }
}
</style>
