<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button @click="bohuiClick">驳回</a-button>
      <a-popconfirm
        ok-text="确定"
        cancel-text="取消"
        title="确定执行此操作？"
        class="btn-css"
        @confirm="agreeClick"
      >
        <a-button type="primary" class="ml10" style="float: right"
          >同意</a-button
        >
      </a-popconfirm>
    </div>
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      row-key="productId"
      :pagination="pagination"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      @select-change="selectChange"
      class="mt10"
    >
    </table-list-antd>
  </div>

  <DisAgreeDia
    v-if="isShow"
    @reject-click="rejectClick"
    @close-click="isShow = false"
  />
  <look-detail
    v-if="lookShow"
    :item-data="itemData"
    @close-click="lookShow = false"
    @bohui-click="lookBohuiClick"
    @agree-click="lookAgreeClick"
  ></look-detail>
</template>

<script setup>
import { ref, onMounted } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import DisAgreeDia from "@/components/DisAgreeDia/index.vue";
import lookDetail from "./components/lookDetail.vue";
import {
  GetAuditProductPage,
  GetSupplierPage,
  GetDictItem,
  GetAuditApproval,
  GetNewRejectPutOn,
  GetNewBatchReject,
  getCommodityInfoById,
  GetAuditBatchApproval,
} from "@/api/goodsCenter/ecommerceGoods";
import { httpSupplierList } from "@/api/product";
import { message } from "woody-ui";
// const supplierData = ref([]);
const sourceData = ref([]);
const selectData = ref([]);
const isShow = ref(false);
const lookShow = ref(false);
const itemData = ref({});
let rowData = {};
let formData = {};
const sourceArr = ref([]);
const remoteMethod = (search) => {
  setTimeout(() => {
    searchSource(search);
  }, 1000);
};
const formList = [
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    type: "cascader",
    label: "后台分类",
    name: "cascader",
    span: 6,
    isLoad: true,
    changeOnSelect: true,
    labelKey: "categoryName",
    valueKey: "categoryId",
    childrenKey: "newCategoryModelDtos",
    searchFn: "common",
  },
  {
    type: "rangePicker",
    label: "申请时间",
    name: "applyTime",
    // showTime: true,
    span: 6,
  },
  {
    label: "供货仓",
    name: "supplierId",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: sourceArr,
    showSearch: true,
    needFilter: true,
    remoteMethod,
    span: 6,
  },
  {
    label: "供应链",
    name: "prodSource",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: sourceData,
    span: 6,
  },
  {
    label: "国际码",
    name: "skuNumber",
    type: "input", // 输入框
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  const { applyTime, cascader } = param;
  formData = param;
  formData["applyStartTime"] = Array.isArray(applyTime)
    ? applyTime[0] + " 00:00:00"
    : "";
  formData["applyEndTime"] = Array.isArray(applyTime)
    ? applyTime[1] + " 23:59:59"
    : "";
  formData["firstCategoryId"] = Array.isArray(cascader) ? cascader[0] : "";
  formData["secondCategoryId"] = Array.isArray(cascader) ? cascader[1] : "";
  if (cascader && cascader.length === 3) {
    formData["threeCategoryId"] = cascader[2];
  }
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  getList();
};
//table表头数据
const columns = [
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    fixed: "left",
    align: "left",
    width: 320,
  },
  {
    title: "后台分类",
    dataIndex: "platParentCategoryName",
    key: "platParentCategoryName",
    align: "left",
    width: 200,
  },
  {
    title: "配送方式",
    dataIndex: "deliveryModelDesc",
    key: "deliveryModelDesc",
    align: "left",
    width: 200,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
    width: 200,
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
    key: "applyTime",
    align: "left",
    width: 200,
  },
  {
    title: "售价",
    dataIndex: "price",
    key: "price",
    align: "left",
    width: 150,
  },
  {
    title: "划线价",
    dataIndex: "oriPrice",
    key: "oriPrice",
    align: "left",
    width: 150,
  },
  {
    title: "商品状态",
    dataIndex: "status",
    key: "status",
    align: "left",
    width: 150,
  },
  {
    title: "审核角色",
    dataIndex: "nodeTypeDesc",
    key: "nodeTypeDesc",
    align: "left",
    width: 200,
  },
  {
    title: "供货仓",
    dataIndex: "supplierName",
    key: "supplierName",
    align: "left",
    width: 150,
  },
  {
    title: "供应链",
    dataIndex: "prodSourceName",
    key: "prodSourceName",
    align: "left",
    width: 120,
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    width: 220,
    actionType: [
      {
        type: "look",
        title: "查看",
        color: "",
        isPop: false,
      },
      {
        type: "agree",
        title: "同意",
        color: "",
        isPop: true,
      },
      {
        type: "bohui",
        title: "驳回",
        color: "red",
        isPop: false,
      },
    ],
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const getList = async () => {
  loading.value = true;
  const { current, pageSize } = pagination.value;
  const params = {
    page: current,
    size: pageSize,
    prodAuditStatusDictCode: ["PULL_OFF_SHELVES:AUDITING"],
    nodeType: 4,
    sort: "paudit.apply_time,desc",
    ...formData,
  };
  const res = await GetAuditProductPage(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};
//批量驳回
const bohuiClick = () => {
  if (!selectData.value.length) {
    message.error("请选择要驳回的商品");
    return;
  }
  isShow.value = true;
};

// 批量同意
const agreeClick = async () => {
  if (!selectData.value.length) {
    message.error("请选择要同意的商品");
    return;
  }
  const params = selectData.value.map((v) => {
    return {
      nodeId: v.nodeId,
      auditId: v.productAuditId,
    };
  });
  const res = await GetAuditBatchApproval(params);
  if (res.code === 0) {
    message.success(res.message);
    getList();
  }
};
// 操作回调
const actionsClick = (type, data) => {
  rowData = data;
  if (type === "bohui") {
    isShow.value = true;
  } else if (type === "agree") {
    agreeApi(data);
  } else if (type === "look") {
    getDetail(data);
  }
};

const getDetail = async (data) => {
  const res = await getCommodityInfoById({ productId: data.productId });
  if (res.code === 0) {
    itemData.value = res.data;
    lookShow.value = true;
  }
};

const lookAgreeClick = (data) => {
  agreeApi(data);
};

const agreeApi = async (data) => {
  const res = await GetAuditApproval({
    auditId: data.productAuditId,
    nodeId: data.nodeId,
  });
  console.log(res, "res789");
  if (res.code === 0) {
    message.success(res.message);
    lookShow.value = false;
    getList();
  } else {
    message.error(res.message);
  }
};

// const getSupplyList = async () => {
//   const params = {
//     page: 1,
//     size: 10000,
//   };
//   const res = await GetSupplierPage(params);
//   if (res.code === 0) {
//     supplierData.value = res.data.records.map((v) => {
//       return {
//         label: v.supplierName,
//         value: v.supplierId,
//       };
//     });
//   }
// };
const sourceLoading = ref(false);
const searchSource = (keyword) => {
  sourceLoading.value = true;
  httpSupplierList({ name: keyword })
    .then((res) => {
      if (res.code === 0) {
        const tempArr = [];
        res.data.forEach((item) => {
          tempArr.push({
            value: item.supplierId,
            label: item.supplierName,
          });
        });
        sourceArr.value = tempArr;
      }
      console.log(sourceArr, "sourceArr");
    })
    .finally(() => {
      sourceLoading.value = false;
    });
};

// 分页变化
const onPaginationChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
};

//
const selectChange = (data) => {
  selectData.value = data;
};

const lookBohuiClick = (data) => {
  rowData = data;
  isShow.value = true;
};

// 驳回
const rejectClick = async (data) => {
  if (selectData.value.length) {
    const list = [];
    selectData.value.forEach((item) => {
      const info = {
        auditId: item.productAuditId,
        nodeId: item.nodeId,
        rejectReason: data.data.rejectReason,
        rejectNote: data.data.rejectNote,
        rejectImageUrl: data.data.rejectImageUrl,
      };
      list.push(info);
    });
    const res = await GetNewBatchReject(list);
    if (res.code === 0) {
      if (res.code === 0) {
        isShow.value = false;
        lookShow.value = false;
        message.success(res.message);
        getList();
      }
    }
  } else {
    const { productAuditId, nodeId } = rowData;
    const res = await GetNewRejectPutOn({
      ...data.data,
      auditId: productAuditId,
      nodeId,
    });
    if (res.code === 0) {
      if (res.code === 0) {
        isShow.value = false;
        lookShow.value = false;
        message.success("提交成功");
        getList();
      }
    }
  }
};
const getSource = async () => {
  const params = {
    dictCode: "SUPPLY",
  };
  const res = await GetDictItem(params);
  if (res.code === 0) {
    sourceData.value = res.data.map((v) => {
      return {
        label: v.name,
        value: v.value,
      };
    });
  }
};

onMounted(() => {
  getSource();
  searchSource("");
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    height: 40px;
    margin-bottom: 10px;
  }
}
</style>
