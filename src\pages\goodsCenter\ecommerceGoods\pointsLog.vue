<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <table-list-antd
    :columns="columns"
    :dataSource="dataSource"
    :pagination="pagination"
    :is-checkbox="false"
    row-key="commProcessLogId"
    @update:pagination="handleTableChange"
    class="table-css"
  >
  </table-list-antd>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { GetCommProcessLog } from "@/api/goodsCenter/ecommerceGoods";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
const formState = ref({
  userNickNme: "",
  addTime: [],
});
const dataSource = ref([]);
const formList = ref([
  {
    label: "操作人",
    name: "userNick<PERSON><PERSON>",
    type: "input", // 输入框
    span: 6,
  },
  {
    type: "rangePicker",
    label: "添加时间",
    name: "addTime",
    // showTime: true,
    span: 6,
  },
]);
const columns = [
  {
    title: "操作时间",
    dataIndex: "createTime",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作人",
    dataIndex: "userNickName",
    key: "userNickName",
    align: "left",
    width:300
  },
  {
    title: "日志",
    dataIndex: "bizDesc",
    key: "bizDesc",
    align: "left",
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  formState.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getLogList();
};
const getLogList = async () => {
  const { addTime, userNickNme } = formState.value;
  const params = {
    bizTypes: ["MERCHANT_PRODUCT_LOG", "PLATFORM_PRODUCT_LOG"],
    commLogStatus: ["FINISH"],
    createTimeBgn: addTime && addTime.length ? addTime[0]+' 00:00:00' : "",
    createTimeEnd: addTime && addTime.length ? addTime[1]+' 23:59:59' : "",
    current: pagination.value.current,
    size: pagination.value.pageSize,
    userNickNme,
  };
  const res = await GetCommProcessLog(params);
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal:total=>`共${total}条数据`
});
const handleTableChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getLogList();
};
onMounted(() => {
  getLogList();
});
</script>
<style scoped lang="scss">
.table-css {
  background: #ffffff;
  padding: 20px;
  border-radius: 16px;
  margin-top:16px;
  margin-bottom: 16px;
}
</style>
