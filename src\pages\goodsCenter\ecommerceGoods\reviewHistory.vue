<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <table-list-antd
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
      class="mt10"
    >
      <!-- 操作列自定义插槽 -->
      <template #actions="{ record }">
        <a-button @click="editRecord(record)">编辑</a-button>
        <a-button danger @click="deleteRecord(record)">删除</a-button>
      </template>
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="resVisible"
    placement="center"
    title="违规信息"
    :destroy-on-close="true"
    width="30%"
    @cancel="resVisible = false"
  >
    <a-form
      name="custom-validation"
      :model="detailObj"
      class="mt20"
      :label-col="{ span: 4 }"
    >
      <a-form-item has-feedback label="操作人" name="operatorUserName">
        <a-input v-model:value="detailObj.operatorUserName" :disabled="true" />
      </a-form-item>
      <a-form-item has-feedback label="下架原因" name="status">
        <a-textarea
          v-model:value="detailObj.violationReason"
          :disabled="true"
        />
      </a-form-item>
      <a-form-item has-feedback label="下架时间" name="emailAddress">
        <a-input v-model:value="detailObj.operateTime" :disabled="true" />
      </a-form-item>
    </a-form>
    <template #footer> </template>
  </a-modal>
  <a-modal
    v-model:open="bohuiVisible"
    placement="center"
    title="驳回原因"
    :destroy-on-close="true"
    width="30%"
    @cancel="bohuiVisible = false"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      class="mt20"
      :label-col="{ span: 4 }"
      :model="reasonObj"
    >
      <a-form-item has-feedback label="审核人" name="propName">
        <a-input
          v-model:value="reasonObj.operatorUserName"
          type="input"
          :disabled="true"
          autocomplete="off"
        />
      </a-form-item>
      <a-form-item has-feedback label="驳回原因" name="propName">
        <a-select
          :value="reasonObj.manualAuditRejectReasonVO?.rejectReason ?? ''"
          type="input"
          :disabled="true"
          :options="reasonList"
          :fieldNames="{ label: 'name', value: 'value' }"
        />
      </a-form-item>
      <a-form-item has-feedback label="备注" name="propName">
        <a-textarea
          :disabled="true"
          :value="reasonObj.manualAuditRejectReasonVO?.rejectNote ?? ''"
          type="input"
          autocomplete="off"
          show-count
          :maxlength="200"
        />
      </a-form-item>
      <a-form-item has-feedback label="图片" name="propName">
        <img
          :src="reasonObj.manualAuditRejectReasonVO?.rejectImageUrl"
          style="width: 100px; height: 100px"
        />
      </a-form-item>
    </a-form>
    <template #footer> </template>
  </a-modal>
  <look-detail
    v-if="lookShow"
    :isBohui="false"
    :isAgree="false"
    :item-data="itemData"
    @close-click="lookShow = false"
  ></look-detail>
</template>

<script setup lang="tsx">
import { ref, onMounted, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import {
  GetAuidHistory,
  GetDictItems,
  GetOperatorUserList,
  GetViolationDetail,
  GetAuditDetails,
  getCommodityInfoById,
} from "@/api/goodsCenter/ecommerceGoods";
import { getQueryDictItems } from "@/api/common";
import lookDetail from "./components/lookDetail.vue";

import { useRoute } from "vue-router";

const prodAuditData = ref([]);
const operatorList = ref([]);
const addVisible = ref(false);
const lookShow = ref(false);
const itemData = ref({});
const route = useRoute();
const detailObj = ref(null);
const reasonObj = ref(null);
const bohuiVisible = ref(false);
const resVisible = ref(false);
let formData = {};

import type { FormInstance } from "woody-ui";
// interface FormState {
//   propName: string;
//   prodPropValues: Array;
// }
const formRef = ref<FormInstance>();
const getOperatorUser = async () => {
  const res = await GetOperatorUserList({});
  if (res.code === 0) {
    const tempArr = [];
    res.data.forEach((item) => {
      tempArr.push({
        value: item.operatorUserId,
        label: item.operatorUserName,
      });
    });
    operatorList.value = tempArr;
  }
};
const formList = [
  {
    label: "审核状态",
    name: "auditStatusList",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: prodAuditData,
    mode: "multiple",
    span: 6,
  },
  {
    label: "操作人",
    name: "operatorUserId",
    options: operatorList,
    type: "select", // 输入框
    span: 6,
  },
  {
    label: "审核时间",
    name: "auditTime",
    type: "rangePicker", // 输入框
    span: 6,
  },
  {
    label: "申请时间",
    name: "applyTime",
    type: "rangePicker", // 输入框
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  const { applyTime, auditTime } = param;
  formData = param;
  formData["auditStartTime"] = Array.isArray(auditTime)
    ? auditTime[0] + " 00:00:00"
    : "";
  formData["auditEndTime"] = Array.isArray(auditTime)
    ? auditTime[1] + " 23:59:59"
    : "";
  formData["applyStartTime"] = Array.isArray(applyTime)
    ? applyTime[0] + " 00:00:00"
    : "";
  formData["applyEndTime"] = Array.isArray(applyTime)
    ? applyTime[1] + " 23:59:59"
    : "";
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const getStatus = async () => {
  const params = {
    dictCode: "PRODUCT_AUDIT_STATE",
  };
  const res = await GetDictItems(params);
  if (res.code === 0) {
    prodAuditData.value = res.data.map((v) => {
      return {
        label: v.name,
        value: v.value,
      };
    });
  }
};

//table表头数据
const columns = [
  {
    title: "商品ID",
    dataIndex: "prodId",
    key: "prodId",
    fixed: true,
    align: "center",
    width: 150,
  },
  {
    title: "商品来源",
    dataIndex: "prodSourceName",
    key: "prodSourceName",
    align: "center",
    width: 200,
  },
  {
    title: "供货仓",
    dataIndex: "supplierName",
    key: "supplierName",
    align: "center",
    width: 150,
  },
  {
    title: "商品信息",
    dataIndex: "platProductSnapshotInfoVO",
    key: "platProductSnapshotInfoVO",
    align: "center",
    width: 300,
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
    key: "applyTime",
    align: "center",
    width: 150,
  },
  {
    title: "审核状态",
    dataIndex: "auditStatusDesc",
    key: "auditStatusDesc",
    align: "center",
    width: 150,
  },
  {
    title: "审核动作",
    dataIndex: "operationTypeDesc",
    key: "operationTypeDesc",
    align: "center",
    width: 150,
  },
  {
    title: "操作时间",
    dataIndex: "operationTime",
    key: "operationTime",
    align: "center",
    width: 150,
  },
  {
    title: "审核角色",
    dataIndex: "nodeTypeName",
    key: "nodeTypeName",
    align: "center",
    width: 150,
  },
  {
    title: "取消原因",
    dataIndex: "operationReasonDesc",
    key: "operationReasonDesc",
    align: "center",
    width: 150,
  },
  {
    title: "操作人",
    dataIndex: "operatorUserName",
    key: "operatorUserName",
    align: "center",
    width: 150,
  },
  {
    title: "操作",
    key: "reviewHistory",
    fixed: "right",
    width: 200,
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const prodId = ref("");
const getList = async () => {
  const params = {
    prodId: route.query.productId,
    page: pagination.value.current,
    size: pagination.value.pageSize,
    sort: "id,desc",
    ...formData,
  };
  const res = await GetAuidHistory(params);
  if (res.code === 0) {
    loading.value = false;
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const reasonList = ref([]);
// 获取驳回原因
const getReasonList = async (code: String) => {
  const params = {
    dictCode: code,
  };

  const res = await getQueryDictItems(params);
  if (res.code === 0) {
    reasonList.value = res.data;
  }
};

const addClick = () => {
  prodId.value = "";
  addVisible.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  prodId.value = data.prodId;
  if (type === "look") {
    getDetail(data);
  } else if (type === "weiguiRes") {
    offResApi(data);
  } else if (type === "bohui") {
    getReasonList("OPERATOR_REJECT_CAUSE");
    bohuiDetail(data);
  }
};

const getDetail = async (data) => {
  const res = await getCommodityInfoById({ productId: data.prodId });
  if (res.code === 0) {
    itemData.value = res.data;
    lookShow.value = true;
  }
};

const bohuiDetail = async (data) => {
  const res = await GetAuditDetails({ productAuditId: data.auditId });
  if (res.code === 0) {
    reasonObj.value = res.data;
    bohuiVisible.value = true;
  }
};

const offResApi = async (data) => {
  const res = await GetViolationDetail({ productId: data.prodId });
  if (res.code === 0) {
    detailObj.value = res.data;
    resVisible.value = true;
    console.log(detailObj, "detailObj");
  }
};

// 获取表格数据
// const fetchTableData = ({ pagination, filters, sorter }) => {
//   loading.value = true;
//   getList();
// };

// 分页变化
const onPaginationChange = (newPagination) => {
  console.log(newPagination, "newPagination");
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
  console.log(pagination, "pagination");
};

// 编辑记录
const editRecord = (record) => {
  console.log("编辑记录", record);
};

// 删除记录
const deleteRecord = (record) => {
  console.log("删除记录", record);
};

onMounted(() => {
  // getSupplyList();
  // getSource();
  getOperatorUser();
  getStatus();
  //   getShopListFunc();
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
