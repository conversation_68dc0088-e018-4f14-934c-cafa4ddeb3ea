<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button type="primary" @click="addClick" class="ml10">新增</a-button>
    </div>
    <table-list-antd
      :is-checkbox="false"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :pagination="pagination"
      @update:pagination="onPaginationChange"
      @actions-click="actionsClick"
    >
    </table-list-antd>
  </div>
  <a-modal
    v-model:open="addVisible"
    placement="center"
    :title="propId === '' ? '新增' : '编辑'"
    :destroy-on-close="true"
    width="30%"
    @cancel="closeClick"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      :model="formState"
      v-bind="layout"
      layout="vertical"
      @finish="handleFinish"
      @validate="handleValidate"
      @finishFailed="handleFinishFailed"
      class="add-form-css"
    >
      <a-form-item
        has-feedback
        label="规格中文名"
        name="propName"
        :rules="[{ required: true, message: '请输入规格中文名' }]"
      >
        <a-input
          v-model:value="formState.propName"
          type="input"
          autocomplete="off"
          placeholder="请输入规格中文名"
          show-count
          :maxlength="30"
        />
      </a-form-item>
      <a-form-item
        v-for="(domain, index) in formState.prodPropValues"
        :key="index"
        v-bind="formItemLayout"
        label="属性值"
        :name="['prodPropValues', index, 'propValue']"
        :rules="{
          required: true,
          message: '请输入属性值',
          trigger: 'change',
        }"
      >
        <a-input
          v-model:value="domain.propValue"
          placeholder="属性值"
          style="width: 70%; margin-right: 8px"
        />
        <MinusCircleOutlined
          v-if="formState.prodPropValues.length > 0"
          class="dynamic-delete-button"
          @click="removeDomain(domain)"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="dashed" style="width: 100%" @click="addDomain">
          <PlusOutlined />
          添加
        </a-button>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="addVisible = false">取消</a-button>
      <a-button type="primary" @click="submitClick">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableListAntd from "@/components/TableListAntd/index.vue";
import {
  getStandardPaginationList,
  addStandard,
  deleteStandardById,
  updateStandard,
} from "@/api/goodsCenter/ecommerceGoods";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons-vue";
import { message } from "woody-ui";

const addVisible = ref(false);
const formData = ref({
  propName: "",
});

import type { Rule } from "woody-ui/es/form";
import type { FormInstance } from "woody-ui";
// interface FormState {
//   propName: string;
//   prodPropValues: Array;
// }
const formRef = ref<FormInstance>();
const formState = ref({
  propName: "",
  prodPropValues: [
    {
      propValue: "",
      propValueEn: "",
    },
  ],
});
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const rules: Record<string, Rule[]> = {
  propName: [{ required: true, trigger: "blur", message: "请输入规格中文名" }],
};
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};
const handleFinish = (values) => {
  console.log(values, formState);
};
const handleFinishFailed = (errors) => {
  console.log(errors);
};
const resetForm = () => {
  formRef.value.resetFields();
};
const handleValidate = (...args) => {
  console.log(args);
};
const removeDomain = (item) => {
  const index = formState.value.prodPropValues.indexOf(item);
  if (index !== -1) {
    formState.value.prodPropValues.splice(index, 1);
  }
};
const addDomain = () => {
  formState.value.prodPropValues.push({
    propValue: "",
    propValueEn: "",
  });
};
const formList = [
  {
    label: "属性名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
];

const handleSearch = (param) => {
  console.log(param, "pram");
  formData.value.propName = param.prodName;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;

  getList();
};

const closeClick = () => {
  formState.value.propName = "";
  formState.value.prodPropValues = [
    {
      propValue: "",
      propValueEn: "",
    },
  ];
  addVisible.value = false;
};

const submitClick = () => {
  formRef.value
    .validate()
    .then(async () => {
      const formRes = formState.value.prodPropValues.map((v: any) => {
        return {
          propValue: v.propValue,
          propValueEn: v.propValue,
        };
      });
      const params = {
        propName: formState.value.propName,
        prodPropValues: formRes,
        propId: propId.value,
      };
      const res = propId.value
        ? await updateStandard(params)
        : await addStandard(params);
      if (res.code === 0) {
        addVisible.value = false;
        message.success(propId.value ? "编辑成功" : "新增成功");
        getList();
      } else {
        message.success(res.message);
      }
    })
    .catch((error) => {
      console.log("error", error);
    });
};

//table表头数据
const columns = [
  {
    title: "id",
    dataIndex: "propId",
    key: "propId",
    fixed: true,
    align: "left",
    width: 200,
  },
  {
    title: "属性名称",
    dataIndex: "propName",
    key: "propName",
    align: "left",
    width: 200,
  },
  {
    title: "属性值",
    dataIndex: "prodPropValues",
    key: "prodPropValues",
    align: "left",
    width: 250,
  },
  {
    title: "操作",
    key: "actions",
    fixed: "right",
    align: "left",
    width: 200,
    actionType: [
      {
        type: "edit",
        title: "编辑",
        isPop: false,
      },
      {
        type: "delete",
        title: "删除",
        isPop: true,
      },
    ],
  },
];
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});

const dataSource = ref([]);
const loading = ref(false);
const propId = ref("");
const getList = async () => {
  loading.value = true;
  const params = {
    propName: formData.value.propName,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  const res = await getStandardPaginationList(params);
  loading.value = false;
  if (res.code === 0) {
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  }
};

const addClick = () => {
  formState.value.propName = "";
  formState.value.prodPropValues = [
    {
      propValue: "",
      propValueEn: "",
    },
  ];
  propId.value = "";
  addVisible.value = true;
};

// 操作回调
const actionsClick = (type, data) => {
  propId.value = data.propId;
  if (type === "delete") {
    deleteApi(data.propId);
  } else if (type === "edit") {
    formState.value.propName = data.propName;
    for (let v of data.prodPropValues) {
      v["value"] = v.propValue || v.propValueEn;
    }
    formState.value.prodPropValues = data.prodPropValues;
    addVisible.value = true;
  }
};

const deleteApi = async (id) => {
  const res = await deleteStandardById({ id });
  if (res.code === 0) {
    message.success("删除成功");
    getList();
  }
};

// 分页变化
const onPaginationChange = (newPagination) => {
  pagination.value = { ...pagination.value, ...newPagination };
  getList();
};

onMounted(() => {
  getList();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.add-form-css {
  padding: 30px 0;
}
</style>
