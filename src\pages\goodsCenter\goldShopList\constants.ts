export const COLUMNS = [
  {
    title: "店铺名称",
    dataIndex: "shopName",
    key: "shopName",
    fixed: "left",
    width: 200,
    align: "left",
    ellipsis: true,
  },
  {
    title: "商品信息",
    dataIndex: "prodName",
    key: "prodName",
    width: 500,
    ellipsis: true,
    align: "left",
  },
  {
    title: "平台分类",
    dataIndex: "secondPlatCategoryName",
    key: "secondPlatCategoryName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "商家分类",
    dataIndex: "shopCategoryName",
    key: "shopCategoryName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
    key: "updateTime",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "销售价",
    dataIndex: "price",
    key: "price",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "市场价",
    dataIndex: "oriPrice",
    key: "oriPrice",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "第三方库存量",
    dataIndex: "totalStocks",
    key: "totalStocks",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "供应链名称",
    dataIndex: "supplyChainName",
    key: "supplyChainName",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "创建来源",
    dataIndex: "prodSource",
    key: "prodSource",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "注水销量",
    dataIndex: "waterSoldNum",
    key: "waterSoldNum",
    width: 200,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    key: "operate",
    fixed: "right",
    width: 260,
    ellipsis: true,
    align: "left",
  },
];

export const FROM_DATA = {
  prodName: undefined,
  secondPlatCategoryId: undefined,
  prodCreateTimeStart: undefined,
  prodCreateTimeEnd: undefined,
  status: undefined,
  shopId: undefined,
};
