<template>
  <a-modal
    v-model:open="isOpen"
    width="900px"
    title="详情"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      layout="vertical"
      style="margin-top: 16px"
    >
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col class="gutter-row" :span="24">
          <a-form-item label="商品名称" name="prodName">
            {{ formData.prodName }}
            <!-- <a-input
              v-model:value="formData.prodName"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品名称"
              disabled="true"
            /> -->
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="商品卖点" name="brief">
            {{ formData.brief }}
            <!-- <a-input
              v-model:value="formData.brief"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品卖点"
              disabled="true"
            /> -->
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item label="商品图片" name="pic">
            <a-image :src="formData.pic?.path" width="80px" height="80px" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <p style="margin: 0 0 20px 0">规格</p>
    <div class="table-columns">
      <a-table
        :rowKey="(record) => record.prodId"
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :pagination="false"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <!-- 商品图片 -->
          <template v-if="column.key == 'pic'">
            <a-space>
              <div v-if="formData.pic.path.includes('https')">
                <a-image
                  :src="formData?.pic?.path"
                  width="60px"
                  height="60px"
                />
              </div>
              <div v-else>
                <a-image
                  :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${formData.pic.path}`"
                  width="50px"
                  height="50px"
                />
              </div>
            </a-space>
          </template>
          <!-- 商品图片 -->
          <template v-if="column.key == 'price'">
            ￥{{ record.price }}+{{ record.goldCoin }}金币
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { reactive, ref, watch, defineEmits } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getProdInfo } from "@/api/goodsCenter/goldShopList";

const formRef = ref(null);
const formData = reactive({
  prodName: undefined,
  brief: undefined,
  pic: { path: "" },
});
const isOpen = ref(false);
const tableData = ref([]);
const isLoading = ref(true);
const loading = ref(true);
const shopColumns = reactive(COLUMNS);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    defaule: "",
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  getPageList();
});

//详情
const getPageList = async () => {
  isLoading.value = true;
  loading.value = true;
  try {
    const res = await getProdInfo(props.id);
    isLoading.value = false;
    loading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    Object.assign(formData, res.data);
    tableData.value = res.data.skuList;
  } catch (error) {
    isLoading.value = false;
    loading.value = false;
    message.error(error.message);
  }
};

const emit = defineEmits(["isModalOpen"]);

const handleModalCancel = () => {
  emit("isModalOpen", false);
};
const handleModalOk = () => {
  emit("isModalOpen", false);
};
</script>
<style lang="less" scoped></style>
