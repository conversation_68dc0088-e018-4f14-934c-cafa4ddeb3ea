<template>
  <div class="search-form">
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
        <a-col class="gutter-row" :span="6">
          <a-form-item label="商品名称" name="prodName">
            <a-input
              v-model:value="formData.prodName"
              allow-clear
              style="width: 100%"
              placeholder="请输入商品名称"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="商品分类" name="secondPlatCategoryId">
            <a-select
              v-model:value="formData.secondPlatCategoryId"
              show-search
              option-filter-prop="name"
              placeholder="请选择商品分类"
              :options="prodSourceOptions"
              :field-names="{ label: 'categoryName', value: 'categoryId' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="创建时间" name="prodCreateTimeStart">
            <a-range-picker
              v-model:value="formData.prodCreateTimeStart"
              value-format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              :show-time="{
                hideDisabledOptions: true,
                defaultValue: [
                  dayjs('00:00:00', 'HH:mm:ss'),
                  dayjs('23:59:59', 'HH:mm:ss'),
                ],
              }"
              allowClear
              style="width: 100%"
              :placeholder="['开始时间', '结束时间']"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="状态" name="status">
            <a-select
              v-model:value="formData.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 100%"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">上架</a-select-option>
              <a-select-option value="0">下架</a-select-option>
              <a-select-option value="2">云仓下架</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item label="绑定店铺" name="shopId">
            <a-select
              v-model:value="formData.shopId"
              show-search
              :show-arrow="false"
              option-filter-prop="name"
              placeholder="请选择绑定店铺"
              :options="shopOptions"
              :field-names="{ label: 'shopName', value: 'shopId' }"
              allow-clear
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="6">
          <a-form-item
            :wrapper-col="{ span: 24, offset: 0 }"
            style="align-self: flex-end; text-align: left"
            label="&nbsp;"
          >
            <a-button type="primary" @click="onSubmit">搜索</a-button>
            <a-button style="margin-left: 10px" @click="reset">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div class="table-columns">
    <div class="table-operate-box-left">
      <a-popconfirm
        title="确定操作？"
        ok-text="确定"
        cancel-text="取消"
        @confirm="handleUpdate()"
      >
        <a-button>
          <plus-circle-outlined />
          下架选择
        </a-button>
      </a-popconfirm>
    </div>
    <a-table
      :row-selection="{ onChange: onSelectChange }"
      :rowKey="(record) => record.prodId"
      :data-source="tableData"
      :loading="isLoading"
      :columns="shopColumns"
      :scroll="{ x: 1500 }"
      :pagination="pagination"
      @change="pageChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 商品信息 -->
        <template v-if="column.key == 'prodName'">
          <a-space>
            <div v-if="record?.pic.includes('https')">
              <a-image :src="record?.pic" width="60px" height="60px" />
            </div>
            <div v-else>
              <a-image
                :src="`https://oss-hxq-prod.szbaoly.com/bjsc/goods/${record?.pic}`"
                width="50px"
                height="50px"
              />
            </div>
            <a-space direction="vertical">
              <div style="text-align: left">{{ record.prodName }}</div>
              <div style="text-align: left">{{ record.spuNumber }}</div>
            </a-space>
          </a-space>
        </template>
        <!-- 销售价 -->
        <template v-if="column.key == 'price'">
          ￥ {{ record.price + "+" + record.goldCoin }}金币
        </template>
        <!-- 市场价 -->
        <template v-if="column.key == 'oriPrice'">
          ￥ {{ record.oriPrice ? record.oriPrice : 0 }}
        </template>
        <!-- 状态 -->
        <template v-if="column.key == 'status'">
          {{
            record.status === 0
              ? "下架"
              : record.status === 1
              ? "上架"
              : record.status === 2
              ? "违规下架"
              : ""
          }}
        </template>
        <!-- 创建来源 -->
        <template v-if="column.key == 'prodSource'">
          {{ record.prodSource === 2 ? "供应链" : "平台" }}
        </template>
        <!-- 创建时间 -->
        <template v-if="column.key == 'createTime'">
          {{ dayjs(record.createTime * 1000).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
        <!-- 更新时间 -->
        <template v-if="column.key == 'updateTime'">
          {{ dayjs(record.updateTime * 1000).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
        <!-- 操作 -->
        <template v-if="column.key == 'operate'">
          <a-button type="link" class="btn-css"
            @click="handleDetail(record.prodId)"
          >
            查看
          </a-button>

          <a-popconfirm
            :title="`确定${record.status === 0 ? '上架' : '下架'}`"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleUpDown(record.prodId, record.status)"
          >
            <a-button type="link" class="btn-css">
              {{ record.status === 1 ? "违规下架" : "重新上架" }}
            </a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <d-modal
      :open="isOpen"
      :id="isId"
      @is-modal-open="handleModalOpen"
    ></d-modal>
  </div>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getPage,
  getCategoryList,
  getShopPageList,
  getUpdateStatus,
} from "@/api/goodsCenter/goldShopList";
import dayjs from "dayjs";
import dModal from "./detailModal/index.vue";

const isLoading = ref(true);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({
  prodName: undefined,
  secondPlatCategoryId: undefined,
  prodCreateTimeStart: undefined,
  prodCreateTimeEnd: undefined,
  status: undefined,
  shopId: undefined,
});
const prodSourceOptions = ref();
const shopOptions = ref();
onMounted(async () => {
  getPageList();
  getCategory();
  getCategoryPage();
});
// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  getPageList();
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
      prodCreateTimeStart: formData.prodCreateTimeStart
        ? new Date(formData.prodCreateTimeStart[0]).getTime() / 1000
        : "",
      prodCreateTimeEnd: formData.prodCreateTimeStart
        ? new Date(formData.prodCreateTimeStart[1]).getTime() / 1000
        : "",
    };
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};
//商品分类

const getCategory = async () => {
  try {
    const res = await getCategoryList();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    prodSourceOptions.value = res.data;
  } catch (error) {
    message.error(error.message);
  }
};

//绑定店铺

const getCategoryPage = async () => {
  try {
    const params = {
      current: 1,
      size: 10000,
      shopType: "LIFE",
    };
    const res = await getShopPageList(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    shopOptions.value = res.data.records;
  } catch (error) {
    message.error(error.message);
  }
};

//下架选择
const isSelectRowKey = ref();
const onSelectChange = (selectedRowKeys) => {
  isSelectRowKey.value = selectedRowKeys;
};

const handleUpdate = async () => {
  if (!isSelectRowKey.value) {
    message.error("请选择要下架的商品");
    return;
  }
  const params = {
    prodIds: isSelectRowKey.value,
    status: 0,
  };
  try {
    const res = await getUpdateStatus(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("下架成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

//上下架

const handleUpDown = async (id, sta) => {
  try {
    const params = {
      prodIds: [id],
      status: sta === 1 ? 0 : 1,
    };
    const res = await getUpdateStatus(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("操作成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};

// 查看
const isOpen = ref(false);
const isId = ref();
const handleDetail = (e) => {
  isId.value = e;
  isOpen.value = !isOpen.value;
};
const handleModalOpen = (e) => {
  isOpen.value = e;
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
