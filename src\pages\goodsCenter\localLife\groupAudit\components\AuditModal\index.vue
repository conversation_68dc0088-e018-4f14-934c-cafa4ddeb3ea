<template>
  <a-modal
    :open="visible"
    :width="600"
    :title="modalTitle"
    okText="确定"
    cancelText="取消"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <div class="audit-modal-container">
      <a-form
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        label-align="left"
      >
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="wraps">
              <div
                v-for="(item, idx) in modalProdInfo"
                :key="idx"
                class="com-info"
              >
                <a-image
                  :width="60"
                  :height="60"
                  :src="item.masterPicture || 'error'"
                  alt=""
                  class="prod-img"
                />
                <div class="content">
                  <div class="items-1">{{ item.productName }}</div>
                  <div class="items-2">{{ item.productId }}</div>
                </div>
              </div>
            </div>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="审核结果"
              name="auditResult"
              v-bind="validateInfos.auditType"
            >
              <a-radio-group v-model:value="modelRef.auditType">
                <a-radio :value="20">审核通过</a-radio>
                <a-radio :value="30">审核驳回</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col v-if="modelRef.auditType === 30" :span="24">
            <a-form-item
              label="审核备注"
              name="auditDesc"
              class="remarks"
              :wrapper-col="{ span: 18 }"
              v-bind="validateInfos.auditRemark"
            >
              <a-textarea
                v-model:value="modelRef.auditRemark"
                :show-count="true"
                :maxlength="200"
                rows="6"
                placeholder="请输入审核备注"
              />
            </a-form-item>
          </a-col>

          <a-col v-if="modelRef.auditType === 30" :span="24">
            <a-form-item label="审核图片：" :wrapper-col="{ span: 20 }">
              <div class="upload-flex">
                <wd-upload
                  biz-type="in_coming"
                  :file-list="modelRef.fileList"
                  :accept="accept"
                  :max-count="5"
                  :file-size="1"
                  @get-url-list="afferentUrlChange"
                />
                <p>图片大小不超过1M，最多上传5张，格式支持jpg、png</p>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import "./styled.less";

import { ref, watch, computed } from "vue";
import WdUpload from "@/components/WdUpload/index.vue";
// Props
interface ModalProdInfo {
  masterPicture?: string;
  productName: string;
  productId: string | number;
}

const props = defineProps({
  modalType: String,
  isOpenAuditModal: Boolean,
  modelRef: Object,
  validateInfos: Object,
  modalProdInfo: Array as () => ModalProdInfo[],
});

const emits = defineEmits(["onSubmit", "changeFileList", 'cancel']);

// Data
const visible = ref(false);
const accept = ref(".jpg,.png");

// Computed
const modalTitle = computed(() => {
  return props.modalType === "single" ? "审核" : "批量审核";
});
// watch
watch(props, (newProps) => {
  visible.value = newProps.isOpenAuditModal;
});
// Methods
// 图片上传
const afferentUrlChange = (data) => {
  emits("changeFileList", data);
};

const handleCancel = () => {
  visible.value = false;
  emits("cancel");
};

const handleOk = () => {
  emits("onSubmit");
};
</script>

<style scoped></style>
