.audit-modal-container {
  padding: 0 20px;
  min-height: 200px;
  max-height: 450px;
  overflow: auto;
  .wraps {
    display: flex;
    flex-wrap: wrap;
    .com-info {
      display: flex;
      margin: 0 10px 15px 0;
      .prod-img {
        border-radius: 5px;
      }
      .content {
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        .items-1 {
          width: 160px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .items-2 {
          width: 160px;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .remarks {
    margin-bottom: 35px;
  }
  .red {
    color: red;
  }
  .mb-15 {
    margin-bottom: 15px;
  }
  .mb-5 {
    margin-bottom: 5px;
  }
  .ml-10 {
    margin-left: 10px;
  }
  .mr-10 {
    margin-left: 10px;
  }
  .audits {
    display: flex;
    flex-direction: column;
  }
  .reject-pic {
    display: flex;
  }
  .reject {
    display: flex;
    flex-direction: column;
    .its {
      display: flex;
      .textarea {
        width: 80%;
      }
    }
    .pl-10 {
      padding-left: 80px;
    }
  }
}
