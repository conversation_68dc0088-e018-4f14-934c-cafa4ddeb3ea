<template>
  <a-tabs :items="tabsDataList" :activeKey="activeKey" @change="onTabsChange">
    <a-tab-pane
      v-for="item in tabsDataList"
      :key="item.key"
      :tab="item.label"
    ></a-tab-pane>
  </a-tabs>
  <search-antd
    ref="searchAntdRef"
    :form-list="formList"
    @on-search="handleSearch"
  />

  <div class="table-css">
    <div class="btn-box">
      <div>
        <span style="margin-right: 10px">
          {{ `已选 ${selectedRowKeys.length} 项` }}
        </span>
        <a-button
          type="default"
          @click="() => handlePublicModal('batch', selectedRowKeys)"
        >
          批量审核
        </a-button>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :rowKey="(record) => record"
      :rowSelection="rowSelection"
      :scroll="{ x: 1000 }"
      @change="handlePageChange($event)"
    >
      <template #bodyCell="{ column, record, text }">
        <template
          v-if="['oriPrice', 'sellingPrice'].includes(column.dataIndex)"
        >
          <div>{{ text ? "¥" + text : null }}</div>
        </template>
        <template v-if="column.dataIndex === 'goodsInfo'">
          <a-space>
            <a-image
              width="50px"
              height="50px"
              style="border-radius: 3px"
              :src="record?.masterPicture ? record?.masterPicture : 'error'"
              alt=""
            />
            <a-space direction="vertical" style="text-align: 'left'">
              <a-tooltip :title="record?.productName">
                <span
                  style="
                    width: 170px;
                    font-weight: 500;
                    cursor: pointer;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: inline-block;
                  "
                >
                  {{ record?.productName }}
                </span>
              </a-tooltip>
              <span
                style="
                  color: #999;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: inline-block;
                "
              >
                {{ record?.productId }}
              </span>
              <div
                v-if="record?.activityFlag === 'GROUPON_SHARE'"
                class="promotion"
              >
                推
              </div>
            </a-space>
          </a-space>
        </template>
        <template v-if="column.dataIndex === 'quantitySold'">
          <div>{{ text === -1 ? "不限库存" : text }}</div>
        </template>
        <template v-if="column.dataIndex === 'buyTime'">
          <div>
            <div>{{ record?.buyStartTime }}</div>
            <div>{{ record?.buyEndTime }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'useTime'">
          <div>
            <div>{{ record?.useStartDate }}</div>
            <div>{{ record?.useEndDate }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'stock'">
          <div>{{ text === -1 ? "不限库存" : text }}</div>
        </template>
        <template v-if="column.dataIndex === 'auditStatus'">
          <div>{{ STATUS_MAP[text] }}</div>
        </template>
        <template v-if="column.dataIndex === 'isCategoryValid'">
          <div>{{ text ? "是" : "否" }}</div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <span
              class="operate-text"
              @click="
                () => {
                  currentRecord = record;
                  handleOpenGroupDetailModal(record.productId);
                }
              "
            >
              查看
            </span>
            <span
              class="operate-text"
              v-if="record?.auditStatus === '10'"
              @click="() => handlePublicModal('single', record)"
            >
              审核
            </span>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <!-- 团购详情弹窗 -->
  <GroupDetailModal
    :isOpenGroupDetailModal="isOpenGroupDetailModal"
    :groupDetailModalData="groupDetailModalData"
    @refreshGroupDetailData="handleOpenGroupDetailModal"
    @handlePublicModal="handlePublicModal"
    :currentRecord="currentRecord"
  />
  <!-- 审核 & 批量审核 弹窗 -->
  <AuditModal
    :isOpenAuditModal="isOpenAuditModal"
    :modalProdInfo="modalProdInfo"
    :modelRef="modelRef"
    :validateInfos="validateInfos"
    :modalType="modalType"
    @onSubmit="handleAuditSubmit"
    @change-file-list="handleChangeFileList"
    @cancel="handleCancel"
  />
  <!-- 审核通过或驳回成功弹窗 -->
  <a-modal :open="isOpen" :footer="null" :closable="false">
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
      "
    >
      <CheckCircleFilled
        style="font-size: 30px; color: rgb(96, 171, 8); margin-right: 30px"
      />
      <div style="font-size: 25px">{{ isOptionTxt }}</div>
    </div>
  </a-modal>
  <!-- 审核 或者 批量审核时 错误弹窗提示 -->
  <a-modal
    :width="600"
    :open="isOpenErrorMsg"
    :footer="null"
    @cancel="handleCloseErrorModal"
  >
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        flex-direction: column;
      "
    >
      <div style="font-size: 25px; margin-bottom: 40px">
        {{ isShowError || "-" }}
      </div>
      <a-button
        type="primary"
        style="width: 250px; height: 40px"
        @click="handleCloseErrorModal"
      >
        确定
      </a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, watch, reactive } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import {
  GetGrouponProductList,
  GeNumByAuditStatus,
  QueryGroupPurchaseDetail,
  ReviewProduct,
} from "@/api/goodsCenter/localLife";
import { useShopCategoryListV2 } from "@/hooks/useShopCategoryListV2";
import { STATUS_MAP, columns } from "./constants";
import { Form, message } from "woody-ui";
import GroupDetailModal from "@/components/GroupDetailModal/index.vue";
import AuditModal from "../groupAudit/components/AuditModal/index.vue";
import { CheckCircleFilled } from "@ant-design/icons-vue";

const queryParams = ref({
  auditStatus: "ALL",
});
const isOpenGroupDetailModal = ref(false);
const groupDetailModalData = ref(undefined);

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

const dataSource = ref([]);
const loading = ref(false);

const { cascaderOptions } = useShopCategoryListV2();
const formListTemp = ref([]);
const formList = ref([]);
const tabsDataList = ref([]);
const activeKey = ref("0");
const checkedStatus = ref(true);
const sort = ref("gpa.create_time,desc");
const searchAntdRef = ref(null);
const checkCount = ref(0); // 批量 已选数量
const modalProdInfo = ref([]); // 弹窗头部详情
const selectedRowKeys = ref([]); // 列表复选框
const isOpenErrorMsg = ref(false); // 点击审核错误弹窗 显隐
const isShowError = ref(""); // 点击审核错误弹窗文案
const modalType = ref(""); // 弹窗类型
const isOpenAuditModal = ref(false); // 审批 & 批量审批弹窗显隐
const isOpen = ref(false); // 确认审核后弹窗提示 显隐
const isOptionTxt = ref(""); // 确认审核后弹窗提示文案
const currentRecord = ref();

//审核弹窗里的表单相关的几个变量
const useForm = Form.useForm;
const modelRef = reactive({
  auditType: 20,
  auditRemark: "",
  fileList: [],
});
const rulesRef = reactive({
  auditType: [
    {
      required: true,
      message: "审核结果必选!",
    },
  ],
  auditRemark: [
    {
      required: true,
      message: "审核备注必填!",
    },
  ],
});
const {
  resetFields: resetAuditModalFields,
  validate,
  validateInfos,
} = useForm(modelRef, rulesRef, {
  onValidate: (...args) => console.log(...args),
});

watch(
  [cascaderOptions, checkedStatus],
  ([newCascaderOptions, newCheckedStatus]) => {
    formListTemp.value = [
      {
        label: "店铺名称",
        name: "shopName",
        type: "input",
        placeholder: "请选择店铺名称",
        span: 6,
      },
      {
        label: "商品名称",
        name: "prodName",
        type: "input",
        placeholder: "请输入商品名称",
        span: 6,
      },
      {
        label: "商品ID",
        name: "prodId",
        type: "input",
        placeholder: "请输入商品ID",
        span: 6,
      },
      {
        type: "cascader",
        label: "商品分类",
        name: "cascader",
        span: 6,
        // isLoad: true,
        labelKey: "label",
        valueKey: "value",
        placeholder: "全部",
        changeOnSelect: true,
        options: newCascaderOptions,
      },
      {
        type: "rangePicker",
        label: "提交时间",
        name: "createTime",
        // showTime: true,
        span: 6,
      },
      {
        type: "rangePicker",
        label: "审核时间",
        name: "auditTime",
        // showTime: true,

        span: 6,
        show: newCheckedStatus, // 根据变量 checkedStatus 控制是否显示
      },
      {
        type: "select",
        label: "营销推广",
        name: "activityFlagList",
        span: 6,
        needFilter: true,
        placeholder: "全部",
        options: [
          {
            label: "全部",
            value: "ALL",
          },
          {
            label: "是",
            value: "GROUPON_SHARE",
          },
          {
            label: "否",
            value: "NOT",
          },
        ],
      },
      {
        type: "select",
        label: "绑定分类",
        name: "isCategoryValid",
        span: 6,
        needFilter: true,
        placeholder: "全部",
        options: [
          {
            label: "全部",
            value: null,
          },
          {
            label: "是",
            value: true,
          },
          {
            label: "否",
            value: false,
          },
        ],
      },
    ];

    // 动态过滤表单项
    formList.value = formListTemp.value.filter(
      (item) => item.show === undefined || item.show
    );
  },
  {
    immediate: true,
  }
);

const SwitchTabs = (status) => {
  switch (status) {
    case null:
      return "全部";
    case "10":
      return "审核中";
    case "20":
      return "审核通过";
    case "30":
      return "审核驳回";
  }
};

// 列表复选框 事件
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
  checkCount.value = newSelectedRowKeys.length;
};
const rowSelection = {
  selectedRowKeys,
  onChange: onSelectChange,
};

// 查询头部tab切换列表数据
const fetchAuditNumList = async () => {
  try {
    const res = await GeNumByAuditStatus();
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    let dataL = res.data.map((item, index) => {
      return {
        key: `${index}`,
        label: SwitchTabs(item.auditStatus) + "（" + item.number + "）",
        isShow: item.auditStatus === "10" ? false : true,
        auditStatus: item.auditStatus === null ? "ALL" : item.auditStatus,
      };
    });
    tabsDataList.value = dataL;
  } catch (error) {
    message.error(error.message);
  }
};

// Tab切换
const onTabsChange = (activeKeyVal) => {
  activeKey.value = activeKeyVal;
  checkedStatus.value = tabsDataList.value[activeKeyVal]?.isShow;
  // 重置搜索表单
  searchAntdRef.value?.resetOnlyFunc();
  // form.resetFields();
  if (
    tabsDataList.value[activeKeyVal]?.auditStatus === "ALL" ||
    tabsDataList.value[activeKeyVal]?.auditStatus === "10"
  ) {
    sort.value = "gpa.create_time,desc";
  } else {
    sort.value = "gp.audit_time,desc";
  }
  handleSearch();
  fetchAuditNumList();
};

const handleSearch = (formData) => {
  formData = {
    ...formData,
    auditStatus:
      tabsDataList.value.length > 0
        ? tabsDataList.value[activeKey.value].auditStatus
        : "ALL",
  };
  const { cascader, auditTime, createTime, activityFlagList, ...restFormData } =
    formData;
  const [firstCategoryId, secondCategoryId, thirdCategoryId] = cascader || [];
  const [auditStartTime, auditEndTime] = auditTime || [];
  const [createStartTime, createEndTime] = createTime || [];
  queryParams.value = {
    firstCategoryId,
    secondCategoryId,
    thirdCategoryId,
    activityFlagList:
      activityFlagList && activityFlagList !== "ALL" ? [activityFlagList] : [],
    auditStartTime: auditStartTime ? `${auditStartTime} 00:00:00` : undefined,
    auditEndTime: auditEndTime ? `${auditEndTime} 23:59:59` : undefined,
    createStartTime: createStartTime
      ? `${createStartTime} 00:00:00`
      : undefined,
    createEndTime: createEndTime ? `${createEndTime} 23:59:59` : undefined,
    ...restFormData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

// 列表数据
const fetchListData = async () => {
  loading.value = true;

  const params = {
    ...queryParams.value,
  };
  try {
    const res = await GetGrouponProductList(
      pagination.value.current,
      pagination.value.pageSize,
      params,
      sort.value
    );
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

// 打开查看详情弹窗
const handleOpenGroupDetailModal = async (productId) => {
  try {
    const res = await QueryGroupPurchaseDetail(productId);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    groupDetailModalData.value = res.data;
    isOpenGroupDetailModal.value = true;
    return;
  } catch (error) {
    return message.error(error.message);
  }
};

// 打开审核 & 批量审核 弹窗
const handlePublicModal = (type, records) => {
  if (type === "single") {
    checkCount.value = 1;
    isOpenGroupDetailModal.value = false;
    modalProdInfo.value = Array.of(records); // 头部商品的信息展示
    selectedRowKeys.value = Array.of(records.productId); // 选中的商品ID放进数组
  }
  if (type === "batch") {
    if (selectedRowKeys.value.length === 0)
      return message.warning("请选择团购!");
    if (selectedRowKeys.value.length > 10) {
      isOpenErrorMsg.value = true;
      isShowError.value = "批量审核的商品数量上限为10件，请分批审核!";
      return;
    }
    const pendingRecord = records.find((item) => item.auditStatus !== "10");
    if (pendingRecord) {
      isOpenErrorMsg.value = true;
      isShowError.value = "包含不能批量审核的商品，请重新选择!";
      return;
    }
    modalProdInfo.value = selectedRowKeys.value; // 头部商品的信息展示
    selectedRowKeys.value = (records ?? []).map((it) => it.productId); // 选中的商品ID放进数组
  }
  modalType.value = type; // 设置弹窗类型（审核 还是 批量审核）
  isOpenAuditModal.value = true; // 打开审核弹窗
};

const clearForm = () => {
  checkCount.value = 0;
  selectedRowKeys.value = [];
  modelRef.fileList = [];
  modelRef.auditType = 1;
  resetAuditModalFields();
};

// 审核 & 批量审核 弹窗 确认
const handlePublicModalOk = async (e) => {
  const isCategoryValidFlag = modalProdInfo.value.every(
    (item) => item.isCategoryValid
  );
  if (!isCategoryValidFlag && e?.auditResult === 20) {
    if (modalProdInfo.value.length > 1) {
      return message.error("个别商品的商品分类不存在");
    } else if (modalProdInfo.value.length === 1) {
      return message.error("商品分类不存在");
    }
  }
  let data = {
    productIds:
      modalProdInfo.value.length > 0
        ? modalProdInfo.value.map((it) => it.productId)
        : null,
    auditResult: modelRef.auditType,
    auditDesc: modelRef.auditRemark || undefined,
    auditImages:
      modelRef.fileList.length > 0
        ? modelRef.fileList.map((item) => item.url)
        : null,
  };

  ReviewProduct(data)
    .then((res) => {
      if (res.code !== 0) {
        return message.error(res.message || "请求失败!");
      }
      activeKey.value = "0";
      queryParams.value.auditStatus = "ALL";
      fetchAuditNumList();
      isOpenAuditModal.value = false; // 关闭审核弹窗
      isOpen.value = true; // 打开消息提示弹窗
      isOptionTxt.value = `审核${
        modelRef?.auditType === 20 ? "通过" : "驳回"
      }成功!`; // 设置message信息
      clearForm();
      setTimeout(() => {
        isOpen.value = false;
      }, 2000); // 2秒后自动关闭
      fetchListData();
      return;
    })
    .catch((err) => {
      message.error(err.message);
      clearForm();
      activeKey.value = "0";
      fetchAuditNumList();
      isOpenAuditModal.value = false; // 关闭审核弹窗
      fetchListData();
      return;
    });
};

const handleAuditSubmit = () => {
  // 审核通过时不需要验证auditRemark，validate的入参里处理下
  validate(
    modelRef.auditType === 30 ? ["auditType", "auditRemark"] : ["auditType"]
  )
    .then((res) => {
      handlePublicModalOk();
    })
    .catch((err) => {
      console.log("error", err);
    });
};

// 关闭审核错误提示弹窗
const handleCloseErrorModal = () => {
  isOpenErrorMsg.value = false;
  clearForm();
};

const handleChangeFileList = (data) => {
  if (data && data.length) {
    modelRef.fileList = (data || []).map((item) => {
      return {
        name: "",
        url: item.url,
      };
    });
  } else {
    modelRef.fileList = [];
  }
};

const handleCancel = () => {
  clearForm();
};

onMounted(() => {
  fetchAuditNumList();
  fetchListData();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    height: 40px;
    margin-bottom: 10px;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
.promotion {
  width: 20px;
  padding: 0 4px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
  background: #de4141;
  font-size: 12px;
  border-radius: 3px;
  margin-right: 3px;
}
</style>
