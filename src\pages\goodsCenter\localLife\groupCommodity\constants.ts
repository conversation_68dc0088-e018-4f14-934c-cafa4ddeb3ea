export const PROD_STATUS_TEXT_MAP = {
  30: '已上架',
  40: '未上架',
  50: '已下架',
  60: '待提交',
  70: '封禁中'
}

export const PROD_STATUS_OPTIONS = [
  { value: '', label: "全部" },
  ...Object.entries(PROD_STATUS_TEXT_MAP).map(([value, label]) => ({
    value: Number(value),
    label,
  })),
];

//table表头数据
export const columns = [
  {
    dataIndex: "shopName",
    title: "店铺名称",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "goodsInfo",
    title: "商品信息",
    width: 280,
    ellipsis: true,
  },
  {
    dataIndex: "oriPrice",
    title: "市场价",
    width: 100,
    ellipsis: true,
  },
  {
    dataIndex: "sellingPrice",
    title: "团购价",
    width: 100,
    ellipsis: true,
  },
  {
    dataIndex: "quantitySold",
    title: "售卖数量",
    width: 100,
    ellipsis: true,
  },
  {
    dataIndex: "buyTime",
    title: "售卖时间",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "useTime",
    title: "使用时间",
    width: 150,
    ellipsis: true,
  },
  {
    dataIndex: "productStatus",
    title: "状态",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "isCategoryValid",
    title: "绑定分类",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "waterSoldNum",
    title: "注水量",
    width: 120,
    ellipsis: true,
  },
  {
    dataIndex: "operation",
    title: "操作",
    fixed: "right",
    width: 150,
    ellipsis: true,
  },
];