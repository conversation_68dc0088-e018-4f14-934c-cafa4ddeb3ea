<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <a-table
    :columns="columns"
    :data-source="dataSource"
    :pagination="pagination"
    :loading="loading"
    :scroll="{ x: 1000 }"
    class="mt10 table-list"
    @change="handlePageChange($event)"
  >
    <template #bodyCell="{ column, record, text }">
      <template v-if="['oriPrice', 'sellingPrice'].includes(column.dataIndex)">
        <div>{{ text ? "¥" + text : null }}</div>
      </template>
      <template v-if="column.dataIndex === 'goodsInfo'">
        <a-space>
          <a-image
            width="50px"
            height="50px"
            style="border-radius: 3px"
            :src="record?.masterPicture ? record?.masterPicture : 'error'"
            alt=""
          />
          <a-space direction="vertical" style="text-align: 'left'">
            <a-tooltip :title="record?.productName">
              <span
                style="
                  width: 170px;
                  font-weight: 500;
                  cursor: pointer;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: inline-block;
                "
              >
                {{ record?.productName }}
              </span>
            </a-tooltip>
            <span
              style="
                color: #999;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
              "
            >
              {{ record?.productId }}
            </span>
          </a-space>
        </a-space>
      </template>
      <template v-if="column.dataIndex === 'quantitySold'">
        <div>{{ text === -1 ? "不限库存" : text }}</div>
      </template>
      <template v-if="column.dataIndex === 'buyTime'">
        <div>
          <div>{{ record?.buyStartTime }}</div>
          <div>{{ record?.buyEndTime }}</div>
        </div>
      </template>
      <template v-if="column.dataIndex === 'useTime'">
        <div>
          <div>{{ record?.useStartDate }}</div>
          <div>{{ record?.useEndDate }}</div>
          <div v-if="record?.afterUseDay" style="padding-top: 10px">
            购买后{{ record?.afterUseDay }}天可使用
          </div>
        </div>
      </template>
      <template v-if="column.dataIndex === 'productStatus'">
        <div>{{ PROD_STATUS_TEXT_MAP[text] }}</div>
      </template>
      <template v-if="column.dataIndex === 'isCategoryValid'">
        <div>{{ text ? "是" : "否" }}</div>
      </template>
      <template v-if="column.dataIndex === 'waterSoldNum'">
        <div>
          <span style="margin-right: 15px; color: #000; cursor: pointer">
            {{ text }}
          </span>
          <EditOutlined
            style="cursor: pointer"
            @click="() => onPublicModal('wts', record)"
          />
        </div>
      </template>
      <template v-if="column.dataIndex === 'operation'">
        <a-space>
          <span
            class="operate-text"
            @click="() => handleOpenGroupDetailModal(record.productId)"
          >
            查看
          </span>
          <span
            class="operate-text"
            v-if="record.productStatus !== 70"
            @click="() => onPublicModal('down', record)"
          >
            违规下架
          </span>
        </a-space>
      </template>
    </template>
  </a-table>
  <BasicModal
    ref="waterSalesModalRef"
    :title="modalType === 'wts' ? '注水销量设置' : '违规下架'"
    :footerStyle="modalType === 'down' ? { marginTop: '30px' } : null"
    @ok="handleModalOk"
    @cancel="resetModal"
  >
    <div v-if="modalType === 'down'" class="mb-20">
      确定违规下架商品？下架后将停止售卖
    </div>
    <div class="com-info">
      <a-image
        width="60"
        height="60"
        :src="modalInfo?.masterPicture"
        alt=""
        class="prod-img"
      />
      <div class="content">
        <div class="items-1">{{ modalInfo?.productName }}</div>
        <div class="items-2">{{ modalInfo?.productId }}</div>
      </div>
    </div>
    <div v-if="modalType === 'wts'" class="wts">
      <div>注水数量：</div>
      <a-input-number
        :min="0"
        :max="99999999"
        :maxlength="8"
        :precision="0"
        class="ipt"
        :value="waterSoldNum"
        @change="
          (value) => {
            waterSoldNum = value;
          }
        "
      />
    </div>
    <div v-if="modalType === 'down'">
      <div class="mb-10">
        <b>下架原因：</b>
        <span class="red">下架原因必填</span>
      </div>
      <a-textarea
        showCount
        :maxlength="200"
        :rows="4"
        placeholder="..."
        :value="reason"
        @change="
          (e) => {
            reason = e.target.value;
          }
        "
      />
    </div>
  </BasicModal>
  <!-- 团购详情弹窗 -->
  <GroupDetailModal
    :isOpenGroupDetailModal="isOpenGroupDetailModal"
    :groupDetailModalData="groupDetailModalData"
    @refreshGroupDetailData="handleOpenGroupDetailModal"
  />
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import {
  GetGrouponProductLists,
  UpdateWaterSoldNum,
  ViolationCommodity,
  QueryGroupPurchaseDetail,
} from "@/api/goodsCenter/localLife";
import { message } from "woody-ui";
import {
  PROD_STATUS_TEXT_MAP,
  PROD_STATUS_OPTIONS,
  columns,
} from "./constants";
import { EditOutlined } from "@ant-design/icons-vue";
import BasicModal from "@/components/BasicModal.vue";
import GroupDetailModal from "@/components/GroupDetailModal/index.vue";
import { useShopCategoryListV2 } from "@/hooks/useShopCategoryListV2";

const queryParams = ref({});
const waterSalesModalRef = ref(null);
const modalInfo = ref(null);
const modalType = ref(null);
const waterSoldNum = ref(null);
const reason = ref(null);
const isOpenGroupDetailModal = ref(false);
const groupDetailModalData = ref(undefined);

const { cascaderOptions } = useShopCategoryListV2();
const formList = ref([]);

watch(
  cascaderOptions,
  (cascaderOptions) => {
    formList.value = [
      {
        label: "店铺名称",
        name: "shopName",
        type: "input", // 输入框
        placeholder: "请选择店铺名称",
        span: 6,
      },
      {
        label: "商品名称",
        name: "prodName",
        type: "input",
        placeholder: "请输入商品名称",
        span: 6,
      },
      {
        label: "商品ID",
        name: "prodId",
        type: "input",
        placeholder: "请输入商品ID",
        span: 6,
      },
      {
        type: "cascader",
        label: "商品分类",
        name: "cascader",
        span: 6,
        // isLoad: true,
        labelKey: "label",
        valueKey: "value",
        placeholder: "全部",
        changeOnSelect: true,
        options: cascaderOptions,
      },
      {
        type: "select",
        label: "状态",
        name: "status",
        span: 6,
        needFilter: true,
        placeholder: "全部",
        options: PROD_STATUS_OPTIONS,
      },
      {
        type: "select",
        label: "绑定分类",
        name: "isCategoryValid",
        span: 6,
        needFilter: true,
        placeholder: "全部",
        options: [
          {
            label: "全部",
            value: null,
          },
          {
            label: "是",
            value: true,
          },
          {
            label: "否",
            value: false,
          },
        ],
      },
      {
        type: "rangePicker",
        label: "售卖时间",
        name: "buyTime",
        // showTime: true,
        span: 6,
      },
      {
        type: "rangePicker",
        label: "使用时间",
        name: "useTime",
        // showTime: true,
        span: 6,
      },
    ];
  },
  {
    immediate: true,
  }
);

const handleSearch = (formData) => {
  const { cascader, useTime, buyTime, ...restFormData } = formData;
  const [firstCategoryId, secondCategoryId, thirdCategoryId] = cascader || [];
  const [useStartTime, useEndTime] = useTime || [];
  const [buyStartTime, buyEndTime] = buyTime || [];
  queryParams.value = {
    firstCategoryId,
    secondCategoryId,
    thirdCategoryId,
    useStartTime,
    useEndTime,
    buyStartTime: buyStartTime ? `${buyStartTime} 00:00:00` : undefined,
    buyEndTime: buyEndTime ? `${buyEndTime} 23:59:59` : undefined,
    ...restFormData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

const dataSource = ref([]);
const loading = ref(false);

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    activityFlagList: ["NOT"],
    ...queryParams.value,
  };
  try {
    const res = await GetGrouponProductLists(
      pagination.value.current,
      pagination.value.pageSize,
      "gpa.create_time,desc",
      params
    );
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

const onPublicModal = (type, record) => {
  modalType.value = type;
  modalInfo.value = record;
  if (modalType.value === "wts") {
    //回显
    waterSoldNum.value = modalInfo.value.waterSoldNum;
  }
  waterSalesModalRef.value.open();
};

const resetModal = () => {
  reason.value = null;
  waterSoldNum.value = null;
};

const handleModalOk = async () => {
  try {
    if (modalType.value === "wts") {
      const res = await UpdateWaterSoldNum(
        modalInfo.value?.productId,
        waterSoldNum.value
      );
      if (res.code !== 0) {
        return message.success(res.message || "请求失败!");
      }
      waterSalesModalRef.value.close();
      resetModal();
      fetchListData();
      return message.success("注水销量修改成功!");
    }

    if (!reason.value) return message.error("下架原因不能为空!");
    const res = await ViolationCommodity({
      productId: modalInfo.value?.productId,
      productStatus: modalInfo.value?.productStatus,
      reason: reason.value,
    });
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    message.success("违规下架成功!");
  } catch (error) {
    message.error(error.message);
  }
  waterSalesModalRef.value.close();
  resetModal();
  fetchListData();
};

// 打开查看详情弹窗
const handleOpenGroupDetailModal = async (productId) => {
  try {
    const res = await QueryGroupPurchaseDetail(productId);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    groupDetailModalData.value = res.data;
    isOpenGroupDetailModal.value = true;
    return;
  } catch (error) {
    return message.error(error.message);
  }
};

onMounted(() => {
  fetchListData();
});
</script>
<style lang="less" scoped>
.table-list {
  padding: 30px 20px;
  background: #ffffff;
  border-radius: 16px;
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
.com-info {
  display: flex;
  align-items: center;
  margin: 0 15px 15px 0;
  .prod-img {
    border-radius: 5px;
  }
  .content {
    margin-left: 15px;
    display: flex;
    flex-direction: column;
    .items-1 {
      width: 300px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .items-2 {
      width: 160px;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.red {
  color: red;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-10 {
  margin-bottom: 10px;
}
.wts {
  display: flex;
  align-items: center;
  .ipt {
    width: 80%;
  }
}
</style>
