<template>
  <a-modal
    :title="currentId ? '编辑' : '新增'"
    :open="visible"
    :confirmLoading="addIsSubmitting"
    @ok="handleSubmitAdd"
    @cancel="handleCancel"
    :destroyOnClose="true"
    okText="确定"
    cancelText="取消"
  >
    <a-form
      ref="formRef"
      :labelCol="{ span: 7 }"
      :wrapperCol="{ span: 19 }"
      layout="vertical"
    >
      <a-form-item
        name="categoryName"
        label="分类名称"
        v-bind="validateInfos.categoryName"
      >
        <a-input
          v-model:value="form.categoryName"
          placeholder="请输入名称"
          :maxlength="5"
          showCount
        />
      </a-form-item>

      <a-form-item name="status" label="应用类型" v-bind="validateInfos.status">
        <a-radio-group v-model:value="form.status">
          <a-radio :value="1">启用</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item name="seq" label="排序" v-bind="validateInfos.seq">
        <a-input-number v-model:value="form.seq" :min="0" />
      </a-form-item>

      <a-form-item name="picId" label="分类LOGO" v-bind="validateInfos.picId">
        <wd-upload
          biz-type="in_coming"
          :action="BUSINESS_LICENSE_DISCERN_OLDUPLOAD_URL"
          :file-list="bussFileList"
          request-type="action"
          :max-count="1"
          btn-text="请上传分类LOGO"
          @success-change="handleUploadSuccess"
        />
      </a-form-item>

      <a-form-item
        name="thirdId"
        label="关联供应商分类"
        v-bind="validateInfos.thirdId"
      >
        <a-select
          v-model:value="form.thirdId"
          showSearch
          allowClear
          placeholder="请选择"
          :options="categoryList"
          :filterOption="filterOption"
          :fieldNames="{ label: 'thirdName', value: 'thirdId' }"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { Form, message } from "woody-ui";
import {
  GetQueryAll,
  GetEdit,
  getById,
  GetAdd,
} from "@/api/goodsCenter/localLife";
import WdUpload from "@/components/WdUpload3/index.vue";
import { BUSINESS_LICENSE_DISCERN_OLDUPLOAD_URL } from "./constants";

// Props
const props = defineProps({
  addOrEditVisible: Boolean,
  currentId: Number,
  refresh: Function,
});
const emits = defineEmits(["update:visible", "refresh", "cancel"]);

// data
const visible = ref(false);
const categoryList = ref([]);
const bussFileList = ref([]);

// 表单数据
let form = ref({
  categoryName: "",
  status: 1,
  seq: 0,
  picId: "",
  thirdId: null,
});
const formRef = ref(null);
const addIsSubmitting = ref(false);
const useForm = Form.useForm;
const rules = reactive({
  categoryName: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  status: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  seq: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  picId: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
  thirdId: [
    {
      required: true,
      message: "请填写该字段",
    },
  ],
});
// 校验规则
// const rules = ref([{ required: true, message: "请填写该字段" }]);
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

watch(props, (newProps) => {
  visible.value = newProps.addOrEditVisible;
  if (visible.value) {
    fetchQueryAllListData();
    if (props.currentId) {
      getDetail();
    }
  }
});

// 提交表单
const handleSubmitAdd = async () => {
  validate()
    .then(async () => {
      if (props.currentId) {
        const res = await doUpdate(form.value);
        if (res) {
          form.value.categoryName = "";
          form.value.status = 1;
          form.value.seq = 0;
          form.value.thirdId = null;
          bussFileList.value = [];
          resetFields();
          emits("refresh");
        }
      } else {
        const res = await doCreateNews(form.value);
        if (res) {
          emits("refresh");
        }
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};

// 关闭弹窗
const handleCancel = () => {
  emits("cancel");
  bussFileList.value = [];
  resetFields();
};

// 过滤选项
const filterOption = (input, option) => {
  return (option?.thirdName ?? "").toLowerCase().includes(input.toLowerCase());
};

const fetchQueryAllListData = async () => {
  try {
    const res = await GetQueryAll();
    if (res.code === 0) {
      categoryList.value = res.data;
    }
  } catch (error) {
    message.error(error.message);
  }
};

const doCreateNews = async (formData) => {
  try {
    const result = await GetAdd(formData);
    if (result.code === 0) {
      message.success("创建成功");
      return true;
    }
    // message.error(result.message || "创建失败");
    // return false;
  } catch (error) {
    // return message.error(error || "创建失败");
  }
};

const doUpdate = async (formData) => {
  if (props.currentId) {
    formData.categoryId = props.currentId;
  }

  try {
    const result = await GetEdit(formData);
    if (result.code === 0) {
      message.success("更新成功");
      return true;
    }
  } catch (error) {}
};

// 图片上传
const handleUploadSuccess = (context) => {
  if (context?.path?.length > 0) {
    bussFileList.value = [{ uid: context.fileId, url: context.url }];
    form.value.picId = context.fileId;
  } else {
    bussFileList.value = [];
    form.value.picId = "";
  }
};

const getDetail = async () => {
  let params = {
    categoryId: props.currentId,
  };
  try {
    if (props.currentId) {
      const result = await getById(params);
      if (result.code === 0) {
        const { categoryName, status, seq, picId, thirdId, pic } = result.data;
        form.value = {
          categoryName,
          status,
          seq,
          picId,
          thirdId,
        };
        bussFileList.value = [{ uid: picId, url: pic }];
        return true;
      }
      message.error(result.message || "查询信息失败");
      return false;
    }
    return message.error("参数缺失");
  } catch (error) {
    return message.error(error || "查询信息失败");
  }
};
</script>

<style scoped>
/* 添加必要的样式 */
</style>
