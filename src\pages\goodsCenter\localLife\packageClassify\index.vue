<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="btn-box">
      <a-button
        type="primary"
        @click="
          () => {
            currentId = '';
            addOrEditVisible = true;
          }
        "
        class="ml10"
        >新增平台分类</a-button
      >
    </div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      class="mt10 table-list"
      :scroll="{ x: 1000 }"
      @change="handlePageChange($event)"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'pic'">
          <a-image
            :width="70"
            :height="70"
            :src="text ? text : 'error'"
            :fallback="fallBackUrl"
          />
        </template>
        <template v-if="column.dataIndex === 'status'">
          <div>{{ ORDER_TYPE_MAP[text] }}</div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <span class="operate-text" @click="() => onEdit(record.categoryId)">
              编辑
            </span>
            <a-popconfirm
              title="确定删除？"
              @confirm="() => onDelete(record.categoryId)"
            >
              <span class="operate-text">删除</span>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <add-modal
    :addOrEditVisible="addOrEditVisible"
    :currentId="currentId"
    @cancel="
      () => {
        addOrEditVisible = false;
      }
    "
    @refresh="
      () => {
        addOrEditVisible = false;
        currentId = undefined;
        fetchListData();
      }
    "
  />
</template>

<script setup>
import { ref, onMounted } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { GetPageList, GetDel } from "@/api/goodsCenter/localLife";
import { message } from "woody-ui";
import { formList, ORDER_TYPE_MAP, fallBackUrl, columns } from "./constants";
import addModal from "./components/addModal.vue";

const queryParams = ref({});
const currentId = ref();
const addOrEditVisible = ref(false);

const handleSearch = (formData) => {
  queryParams.value = {
    ...formData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

const dataSource = ref([]);
const loading = ref(false);

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    ...queryParams.value,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  try {
    const res = await GetPageList(params);
    if (res.code === 0) {
      dataSource.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  } catch (error) {
    // message.error(error.message);
  }
  loading.value = false;
};

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

const onEdit = (categoryId) => {
  currentId.value = categoryId;
  addOrEditVisible.value = true;
};

const onDelete = async (categoryId) => {
  let params = {
    categoryId,
  };
  try {
    const res = await GetDel(params);
    if (res.code === 0) {
      message.success("删除成功！");
      handleSearch();
      return true;
    }
    message.error(res.message || "删除失败");
  } catch (error) {
    message.error(error.message);
  }
};

onMounted(() => {
  fetchListData();
});
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  flex: 1;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
</style>
