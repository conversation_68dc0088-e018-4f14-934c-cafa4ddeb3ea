<template>
  <div class="create-wrap">
    <SubTitle :title="isEdit ? '编辑楼盘信息' : '创建楼盘信息'" />
    <a-spin :spinning="detailLoading">
      <a-form
        :model="form"
        ref="formRef"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 10 }"
        layout="vertical"
        style="margin-top: 32px"
      >
        <a-form-item label="楼盘名称" name="projectName" required>
          <a-input
            v-model:value="form.projectName"
            maxlength="50"
            show-count
            placeholder="请输入内容"
          />
        </a-form-item>
        <a-form-item label="价格" name="unitPrice" required>
          <a-input-number
            v-model:value="form.unitPrice"
            :min="0"
            :max="9999999"
            :parser="parseNumber"
            placeholder="请输入内容"
            addon-after="元/m²"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="面积区间" required>
          <a-row :gutter="8">
            <a-col :span="11">
              <a-form-item name="areaMin" :colon="false" class="no-bottom-margin">
                <a-input-number
                  v-model:value="form.areaMin"
                  :parser="parseNumber"
                  :min="0"
                  :keyboard="false"
                  :max="99999999"
                  controls-position="right"
                  placeholder="请输入最小面积"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="2" style="text-align: center; line-height: 32px">
              至
            </a-col>
            <a-col :span="11">
              <a-form-item name="areaMax" :colon="false" class="no-bottom-margin">
                <a-input-number
                  v-model:value="form.areaMax"
                  :parser="parseNumber"
                  :min="0"
                  :max="99999999"
                  :keyboard="false"
                  controls-position="right"
                  placeholder="请输入最大面积"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="居室户型" name="layout">
          <a-input v-model:value="form.layout" :maxlength="20" show-count placeholder="请输入内容" />
        </a-form-item>
        <a-form-item label="所在地区" name="region" required>
          <a-cascader
            v-model:value="form.region"
            :options="areaList"
            :field-names="fieldArea"
            :autofocus="true"
            placeholder="请选择省市区"
          />
        </a-form-item>
        <a-form-item label="详细地址" name="address" required>
          <a-input v-model:value="form.address" :maxlength="50" show-count placeholder="请输入内容" />
        </a-form-item>
        <a-form-item label="联系电话" name="contactPhone" required>
          <a-input
            v-model:value="form.contactPhone"
            placeholder="请输入内容"
            maxlength="15"
            @input="handlePhoneInput"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="积分比例" name="pvRatio" required>
          <a-input-number
            v-model:value="form.pvRatio"
            placeholder="请输入内容"
            :min="0"
            :max="100"
            :parser="parseNumber"
            addon-after="%"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          label="楼盘图片"
          name="newProjectPics"
          :rules="[
            { required: true, message: '请上传楼盘图片', trigger: 'change' },
          ]"
        >
          <wd-upload
            biz-type="life_estate"
            :file-list="form.newProjectPics"
            :max-count="10"
            :btn-text="'上传图片'"
            :file-size="2"
            :accept="'image/jpeg,image/png,image/jpg'"
            @getUrlList="handleProjectPicsChange"
            @remove="handleProjectPicDelete"
          />
          <div class="upload-tip">
            建议图片尺寸为800*600像素，支持jpg/jpeg/png，建议图片大小不超过2M
          </div>
        </a-form-item>
        <a-form-item
          label="项目销(预)售许可证"
          name="newProjectSalesPermits"
          :rules="[
            { required: true, message: '请上传项目销(预)售许可证图片', trigger: 'change' },
          ]"
          >
          <wd-upload
            biz-type="life_estate"
            :file-list="form.newProjectSalesPermits"
            :max-count="10"
            :btn-text="'上传图片'"
            :file-size="2"
            :accept="'image/jpeg,image/png,image/jpg'"
            @getUrlList="handleSalesPermitsChange"
            @remove="handleSalesPermitDelete"
          />
          <div class="upload-tip">
            建议图片尺寸为800*600像素，支持jpg/jpeg/png，建议图片大小不超过2M
          </div>
        </a-form-item>
        <a-form-item label="详细描述" name="description">
          <a-textarea
            v-model:value="form.description"
            :maxlength="200"
            show-count
            :auto-size="{ minRows: 3, maxRows: 8 }"
            placeholder="请输入内容"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
  <div class="footer-wrapper">
    <a-button @click="onCancel">取消</a-button>
    <a-button
      type="primary"
      :disabled="btnLoading"
      class="ml10"
      @click="onSubmit"
    >
      {{ isEdit ? '确认修改' : '确认新建' }}
    </a-button>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "woody-ui";
import SubTitle from "@/components/SubTitle.vue";
import WdUpload from "@/components/WdUpload/index.vue";
import { createProject, getProjectDetail, editProject } from "@/api/goodsCenter/localLife";
import { getAreaList } from "@/api/common";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
const formRef = ref(null);
const areaList = ref([]);
const projectId = ref(null);
const isEdit = ref(false);
const fieldArea = reactive({
  label: "areaName",
  value: "areaCode",
  children: "areas",
});
const form = ref({
  projectName: "",
  unitPrice: "",
  areaMin: "",
  areaMax: "",
  layout: "",
  region: "",
  address: "",
  contactPhone: "",
  pvRatio: "",
  newProjectPics: [],
  newProjectSalesPermits: [],
  description: "",
  delProjectPicIds: [],
  delProjectSalesPermitIds: [],
});

// 自定义验证函数，确保最小面积小于等于最大面积
const validateAreaRange = async (rule, value) => {
  // 验证必填
  if (value === '' || value === null || value === undefined) {
    return Promise.reject(rule.field === 'areaMin' ? '请输入最小面积' : '请输入最大面积');
  }
  
  // 只有当两个字段都有值时才进行范围验证
  if (form.value.areaMin !== '' && form.value.areaMin !== null && form.value.areaMax !== '' && form.value.areaMax !== null) {
    if (rule.field === 'areaMin' && Number(form.value.areaMin) > Number(form.value.areaMax)) {
      return Promise.reject('最小面积不能大于最大面积');
    }
    if (rule.field === 'areaMax' && Number(form.value.areaMin) > Number(form.value.areaMax)) {
      return Promise.reject('最大面积不能小于最小面积');
    }
    // 如果范围验证通过，清除两个面积字段的错误提示
    formRef.value?.clearValidate(['areaMin', 'areaMax']);
  }
  return Promise.resolve();
};

// 电话号码格式验证
const validatePhone = async (rule, value) => {
  if (!value) return Promise.resolve();
  
  // 验证手机号码格式（11位数字，以1开头）
  const mobilePattern = /^1\d{10}$/;
  // 验证座机号码格式（区号-号码，区号3-4位，号码7-8位）
  const telPattern = /^0\d{2,3}-\d{7,8}$/;
  // 验证座机号不带连字符（以0开头的10-12位数字）
  const telNoHyphenPattern = /^0\d{9,11}$/;
  
  if (!mobilePattern.test(value) && !telPattern.test(value) && !telNoHyphenPattern.test(value)) {
    return Promise.reject('请输入正确的联系电话格式');
  }
  
  return Promise.resolve();
};

const rules = {
  projectName: [{ required: true, message: "请输入楼盘名称" }],
  unitPrice: [{ required: true, message: "请输入价格" }],
  areaMin: [
    { validator: validateAreaRange, trigger: 'change' }
  ],
  areaMax: [
    { validator: validateAreaRange, trigger: 'change' }
  ],
  region: [{ required: true, message: "请选择所在地区" }],
  address: [{ required: true, message: "请输入详细地址" }],
  contactPhone: [
    { required: true, message: "请输入联系电话" },
    { validator: validatePhone, trigger: 'change' }
  ],
  pvRatio: [{ required: true, message: "请输入积分比例" }],
};
const btnLoading = ref(false);
const detailLoading = ref(false);

onMounted(async () =>{
  // 获取省市区数据
  await getArea();
  
  // 判断是否为编辑模式
  if (route.query.projectId) {
    projectId.value = route.query.projectId;
    isEdit.value = true;
    await getProjectData();
  }
});

// 获取省市区
const getArea = async () => {
  try {
    const res = await getAreaList();
    if (res.code === 0) {
      areaList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};

// 获取楼盘详情，回显表单数据
const getProjectData = async () => {
  detailLoading.value = true;
  try {
    const res = await getProjectDetail({ projectId: projectId.value });
    if (res.code === 0 && res.data) {
      const detailData = res.data;
      
      // 回显表单数据
      form.value.projectName = detailData.projectName;
      form.value.unitPrice = detailData.unitPrice;
      form.value.areaMin = detailData.areaMin;
      form.value.areaMax = detailData.areaMax;
      form.value.layout = detailData.layout;
      form.value.address = detailData.address;
      form.value.contactPhone = detailData.contactPhone;
      form.value.pvRatio = detailData.pvRatio;
      form.value.description = detailData.description;
      
      // 处理省市区数据
      if (detailData.provinceCode && detailData.cityCode && detailData.districtCode) {
        form.value.region = [
          detailData.provinceCode,
          detailData.cityCode,
          detailData.districtCode
        ];
      }
      
      // 处理图片数据
      if (detailData.projectPics && detailData.projectPics.length > 0) {
        form.value.newProjectPics = detailData.projectPics.map(item => ({
          url: item.url,
          id: item.id,
          key: item.url // 保持原有的key字段用于上传(图片域名后端拼接处理，key值如："test/image/life.Estate/life_plat/2025/05/22/1747898593282_1747911200420.jpg")
        }));
      }

      if (detailData.projectSalesPermits && detailData.projectSalesPermits.length > 0) {
        form.value.newProjectSalesPermits = detailData.projectSalesPermits.map(item => ({
          url: item.url,
          id: item.id,
          key: item.url // 保持原有的key字段用于上传
        }));
      }
    } else {
      message.error(res.message || "获取楼盘详情失败");
    }
  } catch (error) {
    console.error("获取楼盘详情失败:", error);
    message.error("获取楼盘详情失败");
  } finally {
    setTimeout(() => {
      detailLoading.value = false;
    }, 100);
  }
};

const onSubmit = async () => {
  try {
    await formRef.value.validate();
    
    // 处理area数据
    const [provinceCode, cityCode, districtCode] = form.value.region || [];

    btnLoading.value = true;
    
    // 构建请求参数
    const params = {
      projectName: form.value.projectName,
      unitPrice: Number(form.value.unitPrice),
      areaMin: form.value.areaMin ? Number(form.value.areaMin) : 0,
      areaMax: form.value.areaMax ? Number(form.value.areaMax) : 0,
      layout: form.value.layout,
      provinceCode,
      provinceName: getAreaName(areaList.value, provinceCode),
      cityCode,
      cityName: getAreaName(
        getAreaChildren(areaList.value, provinceCode),
        cityCode
      ),
      districtCode,
      districtName: getAreaName(
        getAreaChildren(
          getAreaChildren(areaList.value, provinceCode),
          cityCode
        ),
        districtCode
      ),
      address: form.value.address,
      contactPhone: form.value.contactPhone,
      pvRatio: Number(form.value.pvRatio),
      description: form.value.description,
    };
    
    if (isEdit.value) {
      // 编辑模式
      params.projectId = projectId.value;
      // 添加新增的图片
      const newPics = form.value.newProjectPics
        .filter(item => !item.id) // 只取新上传的图片
        .map(item => item.key);
      const newPermits = form.value.newProjectSalesPermits
        .filter(item => !item.id)
        .map(item => item.key);
      
      // 只有在有新增图片时才添加参数
      if (newPics.length > 0) {
        params.newProjectPics = newPics;
      }
      if (newPermits.length > 0) {
        params.newProjectSalesPermits = newPermits;
      }
      
      // 添加删除的图片ID
      if (form.value.delProjectPicIds.length > 0) {
        params.delProjectPicIds = form.value.delProjectPicIds;
      }
      if (form.value.delProjectSalesPermitIds.length > 0) {
        params.delProjectSalesPermitIds = form.value.delProjectSalesPermitIds;
      }
    } else {
      // 创建模式
      const newPics = form.value.newProjectPics.map(item => item.key);
      const newPermits = form.value.newProjectSalesPermits.map(item => item.key);
      
      if (newPics.length > 0) {
        params.newProjectPics = newPics;
      }
      if (newPermits.length > 0) {
        params.newProjectSalesPermits = newPermits;
      }
    }
    
    let res;
    if (isEdit.value) {
      res = await editProject(params);
    } else {
      res = await createProject(params);
    }
    
    if (res.code === 0) {
      message.success(isEdit.value ? "编辑成功" : "创建成功");
      // 返回列表页
      router.push("/product/localLife/propertyProjectManage");
    } else {
      message.error(res.message || (isEdit.value ? "编辑失败" : "创建失败"));
    }
  } catch (error) {
    console.error(isEdit.value ? "编辑失败:" : "创建失败:", error);
    
    // 获取第一个错误元素并滚动至屏幕中央
    setTimeout(() => {
      const errorElement = document.querySelector('.ant-form-item-has-error');
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  } finally {
    btnLoading.value = false;
  }
};

// 根据areaCode获取areaName
const getAreaName = (areas = [], code) => {
  const area = areas.find((item) => item.areaCode === code);
  return area ? area.areaName : "";
};

// 获取子区域
const getAreaChildren = (areas = [], code) => {
  const area = areas.find((item) => item.areaCode === code);
  return area && area.areas ? area.areas : [];
};

// 处理楼盘图片上传回调
const handleProjectPicsChange = (fileList) => {
  form.value.newProjectPics = fileList;
  // 手动触发表单验证
  formRef.value?.validateFields('newProjectPics');
};

// 处理销售许可证上传回调
const handleSalesPermitsChange = (fileList) => {
  form.value.newProjectSalesPermits = fileList;
  // 手动触发表单验证
  formRef.value?.validateFields('newProjectSalesPermits');
};

// 处理图片删除
const handleProjectPicDelete = (file) => {
  if (file.id) {
    form.value.delProjectPicIds.push(file.id);
  }
};

// 处理销售许可证删除
const handleSalesPermitDelete = (file) => {
  if (file.id) {
    form.value.delProjectSalesPermitIds.push(file.id);
  }
};

const onCancel = () => {
  router.back();
};

// 处理电话号码输入，允许输入数字和特定格式
const handlePhoneInput = (e) => {
  const value = e.target.value;
  // 允许数字、连字符（用于座机）
  const filteredValue = value.replace(/[^\d-]/g, "");
  
  // 如果过滤后的值与原值不同，则更新输入框的值
  if (filteredValue !== value) {
    form.value.contactPhone = filteredValue;
  } else {
    form.value.contactPhone = value;
  }
};

// 将字符串解析回数字
const parseNumber = (value) => {
  if (value === null || value === undefined || value === "") return null;
  // 确保只有数字和最多一个小数点
  value = String(value).replace(/[^\d.]/g, "");
  // 处理多个小数点的情况，只保留第一个
  const parts = value.split(".");
  if (parts.length > 2) {
    value = parts[0] + "." + parts.slice(1).join("");
  }
  // 如果包含小数点，则保留两位小数
  if (value.includes('.')) {
    return parseFloat(parseFloat(value).toFixed(2));
  }
  // 如果是整数，直接返回
  return parseInt(value) || null;
};
</script>

<style lang="less" scoped>
.create-wrap {
  background: #ffffff;
  border-radius: 12px;
  padding: 32px 40px;
  margin-bottom: 65px;
  width: 100%;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.footer-wrapper {
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  padding: 12px 0;
  border-top: 1px solid #f2f5f9;
  position: fixed;
  bottom: 0;
  z-index: 100;
  margin: 0;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
  width: calc(100% - 290px);
}

.no-bottom-margin {
  margin-bottom: 0 !important;
}
</style>
