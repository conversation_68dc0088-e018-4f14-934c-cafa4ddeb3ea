<template>
  <search-antd :form-list="formList" @on-search="handleSearch" />
  <div class="table-css">
    <div class="table-header-bar">
      <div></div>
      <div class="btn-box">
        <a-button
          type="primary"
          @click="handleCreate"
          class="ml10"
        >创建楼盘信息</a-button>
      </div>
    </div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :scroll="{ x: 1000 }"
      class="mt10 table-list"
      @change="handlePageChange($event)"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'projectName'">
          <div>{{ text }}</div>
        </template>
        <template v-if="column.dataIndex === 'unitPrice'">
          <div>{{ text ? text + '/m²' : '-' }}</div>
        </template>
        <template v-if="column.dataIndex === 'state'">
          <div :style="{ color: text === 1 ? '#1a7af8' : '#ff8000' }">
            {{ statusTextMap[text] }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <span class="operate-text" @click="() => handleEdit(record)">编辑</span>
            <a-popconfirm
              v-if="record.state === 1"
              title="确认下架吗，下架后将不再C端展示"
              ok-text="确定"
              cancel-text="取消"
              @confirm="() => handleDown(record)"
            >
              <span class="operate-text">下架</span>
            </a-popconfirm>
            <a-popconfirm
              v-else
              title="确认上架吗，上架后将在C端展示"
              ok-text="确定"
              cancel-text="取消"
              @confirm="() => handleUp(record)"
            >
              <span class="operate-text">上架</span>
            </a-popconfirm>
            <a-popconfirm
              v-if="record.state === 2"
              title="确认删除吗，删除后将不可恢复"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <span class="operate-text"> 删除 </span>
            </a-popconfirm>

          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SearchAntd from '@/components/SearchAntd/index.vue';
import { message } from 'woody-ui';
import { queryProjectPageByCondition, publishProject, unPublishProject, deleteProject } from "@/api/goodsCenter/localLife";

const router = useRouter();

const statusTextMap = {
  1: '已上架',
  2: '已下架',
};

const columns = [
  { dataIndex: 'projectName', title: '楼盘名称', width: 200, ellipsis: true },
  { dataIndex: 'unitPrice', title: '价格', width: 120, ellipsis: true },
  { dataIndex: 'state', title: '状态', width: 120, ellipsis: true },
  { dataIndex: 'operation', title: '操作', width: 200, ellipsis: true },
];

const formList = ref([
  {
    label: '楼盘名称',
    name: 'projectName',
    type: 'input',
    placeholder: '请输入',
    span: 6,
  },
]);

const dataSource = ref([]);
const loading = ref(false);
const formData = ref({});
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});

const fetchListData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.value.current,
      size: pagination.value.pageSize,
      ...formData.value
    };
    const res = await queryProjectPageByCondition(params);
    if (res.code === 0 && res.data) {
      dataSource.value = res.data.records || [];
      pagination.value.total = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
};
const handleSearch = (param) => {
  formData.value = param;
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};
const handlePageChange = (page) => {
  pagination.value.current = page.current;
  pagination.value.pageSize = page.pageSize;
  fetchListData();
};

const handleEdit = (record) => {
  router.push({ 
    path: '/product/localLife/createPropertyInfo',
    query: { projectId: record.projectId }
  });
};
const handleDown = async (record) => {
  try {
    loading.value = true;
    const res = await unPublishProject({
      projectId: record.projectId
    });
    if (res.code === 0) {
      message.success('下架成功');
      fetchListData();
    }
  } catch (error) {
    console.error('下架失败:', error);
    message.error('下架失败');
  } finally {
    loading.value = false;
  }
};
const handleUp = async (record) => {
  try {
    loading.value = true;
    const res = await publishProject({
      projectId: record.projectId
    });
    if (res.code === 0) {
      message.success('上架成功');
      fetchListData();
    }
  } catch (error) {
    console.error('上架失败:', error);
    message.error('上架失败');
  } finally {
    loading.value = false;
  }
};
const handleDelete = async (record) => {
  try {
    loading.value = true;
    const res = await deleteProject({
      projectId: record.projectId
    });
    if (res.code === 0) {
      message.success('删除成功');
      pagination.value.current = 1;
      fetchListData();
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  } finally {
    loading.value = false;
  }
};

const handleCreate = () => {
  router.push({ path: '/product/localLife/createPropertyInfo' });
};

onMounted(() => {
  fetchListData();
});
</script>

<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  flex: 1;
  .table-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  .btn-box {
    height: 40px;
    margin-bottom: 0;
    float: none;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
  margin-right: 10px;
}
.pagination-bar {
  margin-top: 20px;
  text-align: right;
}
</style> 