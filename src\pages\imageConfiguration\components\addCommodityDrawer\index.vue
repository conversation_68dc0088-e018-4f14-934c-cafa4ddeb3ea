<template>
  <drawer-dialog
    :visible="addCommodityDrawerFlag"
    size="800px"
    title="添加商品"
    @on-close="handleClose"
    @on-confirm="handleConfirm"
  >
    <search-antd
      ref="searchPageRef"
      :form-list="formList"
      @on-search="handleSearch"
      @on-reset="handleReset"
    />
  <!-- <table-list
      ref="tableListRef"
      row-key="productId"
      :table-data="tableData"
      :columns="columns"
      max-height="calc(100vh - 382px)"
      :pagination="pagination"
      :loading="loading"
      class="mt17"
      @on-select-change="handleSelectChange"
      @on-page-change="handlePageChange"
    >
      <template #prductName="{ row }">
        <span v-if="isEmptyValue(row.prductName) && isEmptyValue(row.picUrl)">
          -
        </span>
        <div v-else className="info-style">
          <div v-if="!isEmptyValue(row.picUrl)" className="pic">
            <img :src="row.picUrl" />
          </div>
          {{ row.prductName }}
        </div>
      </template>
      <template #picUrl="{ row }">
        <img
          v-if="!isEmptyValue(useMapList[row.productId]?.cornerMarkImg)"
          class="current-pic"
          :src="useMapList[row.productId].cornerMarkImg"
        />
        <span v-else>-</span>
      </template>
    </table-list> -->
    <a-table
      class="mt20 add-table"
      ref="tableListRef"
      :data-source="tableData"
      :columns="columns"
      max-height="calc(100vh - 382px)"
      :pagination="pagination"
      :loading="loading"
      @change="handlePageChange"
      :row-selection="rowSelection"
      :rowKey="(record) => record"
      :scroll="{ x: 1000 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'prductName'">
          <span v-if="isEmptyValue(record.prductName) && isEmptyValue(record.picUrl)">
          -
        </span>
        <div v-else className="info-style">
          <div v-if="!isEmptyValue(record.picUrl)" className="pic">
            <img :src="record.picUrl" />
          </div>
          {{ record.prductName }}
        </div>
        </template>
        <template v-if="column.dataIndex === 'picUrl'">
          <img
            v-if="!isEmptyValue(useMapList[record.productId]?.cornerMarkImg)"
            class="current-pic"
            :src="useMapList[record.productId].cornerMarkImg"
          />
          <span v-else>-</span>
        </template>
      </template>
    </a-table>
    <template #customBtn>
      <a-button
        type="primary"
        ghost
        style="margin:0 10px"
        @click="handleAddAll"
      >
        添加所有筛选
      </a-button>
    </template>
  </drawer-dialog>
</template>
<script setup>
import { ref, reactive, watch,computed } from "vue";
import { message } from "woody-ui";
import DrawerDialog from "@/components/DrawerDialog/index.vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import {
  id,
  useType,
  addCommodityDrawerFlag,
  handleAddCloseDialog,
  handleAddConfirm,
} from "../setData";
import { getBrandList, getAddList } from "@/api/cms/reception";
import { isEmptyValue, isObj } from "@/utils";
import {
  markUseBatchSave,
  getUseMap,
  getBindUseList,
} from "@/api/cms/imageConfiguration";

const formList = reactive([
  {
    type: "input",
    label: "SPU ID",
    name: "spuId",
    maxlength: 19,
    span: 12,
  },
  {
    type: "input",
    label: "商品名称",
    name: "productName",
    maxlength: 30,
    span: 12,
  },
  {
    type: "cascader",
    label: "后台分类",
    name: "categoryId",
    labelKey: "categoryName",
    valueKey: "categoryId",
    childrenKey: "newCategoryModelDtos",
    searchFn: "common",
    hideAll: true, // 不添加"全部"选项
    options: [],
    getPopupContainer: (triggerNode) => triggerNode.parentNode || document.body,
    span: 12,
  },
  {
    type: "select",
    label: "商品品牌",
    name: "brandId",
    labelKey: "name",
    valueKey: "brandId",
    showSearch: true,
    options: [],
    span: 12,
  },
]);

const columns = [
  {
    dataIndex: "row-select",
    type: "multiple",
  },
  {
    dataIndex: "productId",
    title: "SPU ID",
  },
  {
    title: "商品信息",
    width: 180,
    dataIndex: "prductName",
  },
  {
    title: "后台分类",
    dataIndex: "categoryName",
  },
  {
    dataIndex: "brandName",
    title: "商品品牌",
    width: 130,
  },
  {
    title: "当前贴图",
    dataIndex: "picUrl",
  },
];

const pagination = {
  current: 1,
  pageSize: 10,
  total: 0,
  size: "small",
  showPreviousAndNextBtn: false,
  pageEllipsisMode: "both-ends",
};

const selectedRowKeys = ref([]);
const rowSelection = computed(() => ({
  type: "checkout",
  selectedRowKeys,
  onChange: onSelectChange,
  getCheckboxProps: (record) => ({
    disabled: record.checked === true, // Column configuration not to be checked
  }),
}));
const useInfoMapList = ref([]);
// 列表复选框 事件
const onSelectChange = (newSelectedRowKeys,ctx) => {
  console.log(newSelectedRowKeys,ctx, "newSelectedRowKeys");
  selectedRowKeys.value = newSelectedRowKeys;
  const obj = newSelectedRowKeys.map((v) => {
    return {
      useId:v.productId,
      supplierId:v.supplierId
    };
  });
  
  useInfoMapList.value = obj;
  console.log(useInfoMapList,'useInfoMapList')
  // emits("select-change", selectedRowKeys.value, rowId);
  // checkCount.value = newSelectedRowKeys.length;
};



const loading = ref(false);
const searchPageRef = ref(null);
const searchParams = ref({});
const tableListRef = ref(null);
const tableData = ref([]);
const useMapList = ref({});

// 获取商品品牌数据
const getBrandData = () => {
  getBrandList().then((res) => {
    if (res.code === 0 && Array.isArray(res.data)) {
      res.data.unshift({ name: "全部", brandId: "" });
      formList[3].options = res.data;
    }
  });
};
// 获取所有已绑定商品
const getBindList = () => {
  // getBindUseList(id.value).then((res) => {
  //   if (res.code === 0 && Array.isArray(res.data)) {
  //     tableListRef.value.handleSelectChange(null, null, () => {
  //       const value = res.data.reduce((prev, cur) => {
  //         useInfoMapList.set(cur.productId, {
  //           useId: cur.productId,
  //           supplierId: cur.supplierId,
  //         });
  //         prev.push(cur.productId);
  //         return prev;
  //       }, []);
  //       return value;
  //     });
  //   }
  // });
};

// 获取表格数据
const getData = () => {
  const params = {
    page: pagination.current,
    size: pagination.pageSize,
    ...searchParams.value,
  };
  loading.value = true;
  getAddList(params).then((res) => {
    if (res.code === 0 && Array.isArray(res.data?.records)) {
      tableData.value = res.data.records;
      pagination.total = res.data.total;
      getPics();
    }
  });
};

// 获取所有角标图片
const getPics = () => {
  const useSupplierIdQueryList = tableData.value.reduce((prev, cur) => {
    prev.push({
      useId: cur.productId,
      supplierId: cur.supplierId,
    });
    return prev;
  }, []);

  const params = {
    useSupplierIdQueryList,
    useType: useType.value,
  };
  getUseMap(params)
    .then((res) => {
      if (res.code === 0 && isObj(res.data)) {
        useMapList.value = res.data;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 表单查询
const handleSearch = (formData) => {
  // 如果 categoryId 是数组，取最后一个值（叶子节点）
  if (Array.isArray(formData.categoryId) && formData.categoryId.length > 0) {
    formData.categoryId = formData.categoryId[formData.categoryId.length - 1];
  }
  searchParams.value = formData;
  pagination.current = 1;
  getData();
};

// 表单重置
const handleReset = () => {
  searchParams.value = {};
  pagination.current = 1;
  getData();
};

// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  getData();
};

// 选中值发生变化时的逻辑
const handleSelectChange = (selectValues) => {
  console.log(selectValues,'chakan')
  if (isObj(ctx)) {
    if (isObj(ctx.currentRowData)) {
      const { productId, supplierId } = ctx.currentRowData;
      if (useInfoMapList.has(productId)) {
        useInfoMapList.delete(productId);
      } else {
        useInfoMapList.set(productId, { useId: productId, supplierId });
      }
    } else if (ctx.currentRowKey === "CHECK_ALL_BOX") {
      if (0 in ctx.selectedRowData) {
        ctx.selectedRowData.forEach((item) => {
          if (!useInfoMapList.has(item.productId)) {
            const { productId, supplierId } = item;
            useInfoMapList.set(item.productId, {
              useId: productId,
              supplierId,
            });
          }
        });
      } else {
        tableData.value.forEach((item) => {
          useInfoMapList.delete(item.productId);
        });
      }
    }
  }
};

// 关闭弹框
const handleClose = () => {
  searchParams.value = {};
  searchPageRef.value.resetOnlyFunc();
  // tableListRef.value.handleClearSelectedRowKeys();
  // useInfoMapList.clear();
  pagination.current = 1;
  pagination.pageSize = 10;
  pagination.total = 0;
  handleAddCloseDialog();
};

// 请求添加接口
const submitAdd = (params) => {
  params = {
    ...params,
    id: id.value,
  };
  markUseBatchSave(params).then((res) => {
    if (res.code === 0) {
      message.success("添加成功");
      handleAddConfirm();
      handleClose();
    } else {
      message.error(res.message);
    }
  });
};

// 添加所有
const handleAddAll = () => {
  const { categoryId, productName, ...reset } = searchParams.value;
  const params = {
    operateType: 3,
    prodSearchParam: {
      ...reset,
      prodName: productName,
      secondCategoryIds: categoryId,
    },
  };
  submitAdd(params);
};

// 确认添加逻辑
const handleConfirm = () => {
  const params = {
    operateType: 1,
    useInfoList: useInfoMapList.value,
  };
  submitAdd(params);
};

watch(
  () => addCommodityDrawerFlag.value,
  (newValue) => {
    if (newValue) {
      getBindList();
      getBrandData();
      getData();
    }
  }
);
</script>
<style lang="less" scoped>
.mt17 {
  margin-top: 17px;
}
.current-pic {
  width: 60px;
}
.form-wrap {
  padding: 8px 0 7px 0;
}
.info-style {
  display: flex;
  align-items: center;
  .pic {
    padding: 5px 10px 1px 12px;
    background-color: #f0f9ff;
    border-radius: 8px;
    margin-right: 8px;
  }
  img {
    width: 38px;
  }
}
</style>
