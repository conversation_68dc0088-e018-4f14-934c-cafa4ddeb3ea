<template>
  <drawer-dialog
    :visible="boundCommodityScopeDrawerFlag"
    size="800px"
    title="绑定商品范围"
    @on-close="handleClose"
    @on-confirm="handleConfirm"
  >
    <a-tabs default-active-key="3">
      <a-tab-pane key="3" tab="供货仓" />
    </a-tabs>
    <div class="supply-warehouse">
      <search-antd ref="searchPageRef" :form-list="formList" @on-search="handleSearch" @on-reset="handleReset" />
      <div class="btn-page">
        <a-button :disabled="!(0 in checkedWarehouseValues)" @click="handleClear">
          清除
        </a-button>
        <a-button class="ml16" @click="checkedAll">全部选择</a-button>
      </div>
      <a-table
        ref="tableRef"
        :row-key="'supplierId'"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        :rowSelection="{ 
          selectedRowKeys: checkedWarehouseValues, 
          onChange: onTableSelectChange,
          preserveSelectedRowKeys: true // 跨页勾选
        }"
        class="mt16"
        @change="handleTableChange"
      >
      </a-table>
    </div>
  </drawer-dialog>
</template>
<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'woody-ui';
import DrawerDialog from '@/components/DrawerDialog/index.vue';
import SearchAntd from '@/components/SearchAntd/index.vue';
import { id, boundCommodityScopeDrawerFlag, boundCommodityScopeCloseDialog } from '../setData';
import { getSupplierPage } from '@/api/cms/reception';
import { isEmptyValue, isObj } from '@/utils';
import { getBindSupplierList, markUseBatchSave } from '@/api/cms/imageConfiguration';

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

const formList = reactive([
  {
    type: 'input',
    label: '店铺名称',
    name: 'shopName',
    placeholder: '请输入店铺名称',
  },
  {
    type: 'input',
    label: '绑定电话',
    name: 'tel',
    placeholder: '请输入绑定电话',
  },
]);

const columns = [
  {
    title: '店铺名称',
    dataIndex: 'shopName',
    key: 'shopName',
    customRender: ({ text }) => text || '-'
  },
  {
    title: '绑定电话',
    dataIndex: 'tel',
    key: 'tel',
    customRender: ({ text }) => text || '-'
  },
];


const loading = ref(false);
const searchPageRef = ref(null);
const searchParams = ref({});
const tableRef = ref(null);
const checkedWarehouseValues = ref([]); // 供货仓已选择值
const tableData = ref([]);
const excludeSupplierIdList = ref([]);
// 全选状态标记
const isAllSelected = ref(false);

// 获取所有已绑定数据
const getBindList = () => {
  getBindSupplierList(id.value).then((res) => {
    if (res.code === 0 && Array.isArray(res.data)) {
      checkedWarehouseValues.value = res.data;
    }
  });
};

// 获取供货仓数据
const getData = () => {
  const params = {
    current: pagination.current,
    size: pagination.pageSize,
    ...searchParams.value,
  };
  loading.value = true;
  getSupplierPage(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        tableData.value = res.data.records;
        pagination.total = res.data.total;
        
        // 修改这里：在全选状态下，当数据加载后应用排除列表
        if (isAllSelected.value) {
          // 先添加所有当前页的ID
          const currentPageIds = tableData.value.map(item => item.supplierId);
          
          // 然后从选中值中移除被排除的ID
          checkedWarehouseValues.value = currentPageIds.filter(id => 
            !excludeSupplierIdList.value.includes(id)
          );
          
          // 如果不是全部排除的情况，保留其他页面已选中的ID
          const otherPagesSelectedIds = checkedWarehouseValues.value.filter(id => 
            !currentPageIds.includes(id)
          );
          
          checkedWarehouseValues.value = [
            ...otherPagesSelectedIds,
            ...currentPageIds.filter(id => !excludeSupplierIdList.value.includes(id))
          ];
        }
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  getData();
};

// 供货仓搜索逻辑
const handleSearch = (formData) => {
  searchParams.value = formData;
  pagination.current = 1;
  getData();
};

// 表格选择回调
const onTableSelectChange = (selectedRowKeys, selectedRows) => {
  // 如果是全选状态下的操作
  if (isAllSelected.value) {
    // 计算当前页的所有ID
    const currentPageIds = tableData.value.map(item => item.supplierId);
    
    // 找出当前页中哪些记录被取消选中
    const deselectedIds = currentPageIds.filter(id => !selectedRowKeys.includes(id));
    
    // 将取消选中的ID添加到排除列表中
    deselectedIds.forEach(id => {
      if (!excludeSupplierIdList.value.includes(id)) {
        excludeSupplierIdList.value.push(id);
      }
    });

    // 找出当前页中哪些记录被新选中(之前在排除列表中，现在又被选中)
    const newSelectedIds = selectedRowKeys.filter(id => 
      currentPageIds.includes(id) && excludeSupplierIdList.value.includes(id)
    );
    
    // 从排除列表中移除新选中的ID
    newSelectedIds.forEach(id => {
      const index = excludeSupplierIdList.value.indexOf(id);
      if (index !== -1) {
        excludeSupplierIdList.value.splice(index, 1);
      }
    });
  }

  // 更新选中值
  checkedWarehouseValues.value = selectedRowKeys;
  
  // 如果没有选中项，取消全选状态
  if (selectedRowKeys.length === 0) {
    isAllSelected.value = false;
    excludeSupplierIdList.value = [];
  }
};

// 重置
const handleReset = () => {
  searchParams.value = {};
  pagination.current = 1;
  getData();
};

// 改变是否为全选的状态
const changeIsAllState = (isAllState = false) => {
  isAllSelected.value = isAllState;
};

// 清空
const handleClear = () => {
  checkedWarehouseValues.value = [];
  excludeSupplierIdList.value = [];
};

// 全选逻辑
const checkedAll = () => {
  isAllSelected.value = true;
  excludeSupplierIdList.value = [];
  
  // 首先选中当前页的所有数据
  checkedWarehouseValues.value = tableData.value.map(item => item.supplierId);
};

// 取消按钮
const handleClose = () => {
  searchPageRef.value.resetOnlyFunc();
  searchParams.value = {};
  tableData.value = [];
  excludeSupplierIdList.value = [];
  pagination.current = 1;
  pagination.pageSize = 10;
  pagination.total = 0;
  checkedWarehouseValues.value = [];
  changeIsAllState();
  boundCommodityScopeCloseDialog();
};

// 确定逻辑
const handleConfirm = () => {
  const params = {
    id: id.value,
  };
  if (isAllSelected.value) {
    params.operateType = 4;
    params.supplierSearchParam = {
      ...searchParams.value,
      excludeSupplierIdList: excludeSupplierIdList.value,
    };
  } else {
    params.operateType = 2;
    params.supplierIdList = checkedWarehouseValues.value;
  }

  markUseBatchSave(params).then((res) => {
    if (res.code === 0) {
      message.success('添加成功');
      handleClose();
    } else {
      message.error(res.message);
    }
  });
};

watch(
  () => boundCommodityScopeDrawerFlag.value,
  (newValue) => {
    if (newValue) {
      getBindList();
      getData();
    }
  },
);
</script>
<style lang="less" scoped>
.mt16 {
  margin-top: 16px;
}
.ml16 {
  margin-left: 16px;
}
.supply-warehouse {
  .form-wrap {
    padding: 0;
  }
  .btn-page {
    margin-top: 24px;
  }
}
</style>
