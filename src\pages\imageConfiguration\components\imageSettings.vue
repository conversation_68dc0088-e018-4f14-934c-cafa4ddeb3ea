<template>
  <div class="section-top">
    <div class="scroll-name-wrapper">
      <div class="scroll-title">贴图icon</div>
      <div class="scroll-title-text">
        <img class="icon-hot-img" :src="info.cornerMarkImg" />
      </div>
    </div>
    <div class="scroll-name-wrapper">
      <div class="scroll-title">贴图名称</div>
      <div class="scroll-title-text">{{ info.cornerMarkName }}</div>
    </div>
    <div class="scroll-name-wrapper">
      <div class="scroll-title">贴图类型</div>
      <div class="scroll-title-text">{{ info.useTypeDesc }}</div>
    </div>
  </div>
  <div class="settings-wrapper">
    <div style="display: flex; justify-content: space-between">
      <confirm-btn text="全部删除" :on-confirm="handleAllDelete">
        <a-button>全部删除</a-button>
      </confirm-btn>
      <div class="table-bottom">
        <a-button type="primary" ghost @click="commodityCoverage()">绑定商品范围</a-button>
        <a-button type="primary" style="margin-left: 14px" @click="handleAddCommodity()">添加商品</a-button>
      </div>
    </div>
    <a-table
      row-key="id"
      :data-source="tableData"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      class="mt24"
      @change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'prodName'">
          <div class="icon-img-wrapper">
            <div v-if="!isEmptyValue(record.pic)" class="pic">
              <img :src="record.pic" />
            </div>
            <span>{{ record.prodName }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <confirm-btn text="删除" :params="record" :on-confirm="handleDelete" />
        </template>
      </template>
    </a-table>

    <add-commodity-drawer />
    <bound-commodity-scope />
  </div>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import AddCommodityDrawer from './addCommodityDrawer/index.vue';
import boundCommodityScope from './boundCommodityScope/index.vue';
import { isEmptyValue } from '@/utils';
import {
  loading,
  tableData,
  columns,
  pagination,
  handleAllDelete,
  getId,
  getUseType,
  getData,
  handleDelete,
  handlePageChange,
  commodityCoverage,
  handleAddCommodity,
} from './setData.js';

const route = useRoute();

const info = ref({});

onMounted(() => {
  info.value = route.query || {};
  getId(route.query?.id || 1);
  getUseType(route.query?.useType || '');
  getData();
});
</script>
<style lang="less" scoped>
.section-top {
  width: 100%;
  height: 116px;
  background: #ffffff;
  border-radius: 16px 16px 16px 16px;
  display: flex;
  align-items: center;
  padding: 32px;
  .scroll-name-wrapper {
    margin-right: 72px;
    .scroll-title {
      font-weight: 400;
      font-size: 14px;
      color: #636d7e;
    }
    .scroll-title-text {
      height: 30px;
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
      margin-top: 16px;
      .icon-hot-img {
        // width: 15px;
        height: 30px;
        width: auto;
        max-width: 30px;
        margin-left: 8px;
      }
    }
  }
}

.settings-wrapper {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 16px;
  margin-top: 16px;
  padding: 32px;
  .mt24 {
    margin-top: 24px;
  }
}

.t-table__content {
  flex: 1;
}

.icon-img-wrapper {
  display: flex;
  align-items: center;
  .pic {
    flex: none;
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f9ff;
    border-radius: 8px;
    margin-right: 8px;
  }
  img {
    width: 38px;
  }
}
</style>
