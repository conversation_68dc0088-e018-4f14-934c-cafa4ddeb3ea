import { ref } from 'vue';
import { message } from 'woody-ui';
import { getMarkUseList, delAllMarkUse, delMarkUse } from '@/api/cms/imageConfiguration';

export const id = ref(1);
export const useType = ref('');
export const loading = ref(false);
export const tableData = ref([]);
export const selectedRowKeys = ref([]);
export const addCommodityDrawerFlag = ref(false);
export const boundCommodityScopeDrawerFlag = ref(false);

export const columns = [
  {
    dataIndex: 'prodName',
    title: '商品信息',
  },
  {
    dataIndex: 'operation',
    title: '操作',
    className: 'operation',
    width: 300,
  },
];

export const pagination = {
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
};

// 获取当前角标id
export const getId = (currentId) => {
  id.value = currentId;
};

// 获取应用类型
export const getUseType = (type) => {
  useType.value = type;
};

// 获取表格数据
export const getData = () => {
  const params = {
    page: pagination.current,
    size: pagination.pageSize,
    id: id.value,
  };
  loading.value = true;
  getMarkUseList(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        tableData.value = res.data.records;
        pagination.total = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 全部删除
export const handleAllDelete = () => {
  delAllMarkUse(id.value).then((res) => {
    if (res.code === 0) {
      message.success('删除成功');
      pagination.current = 1;
      getData();
    } else {
      message.error(res.message);
    }
  });
};

// 删除单个
export const handleDelete = (row) => {
  delMarkUse(row.id).then((res) => {
    if (res.code === 0) {
      message.success('删除成功');
      getData();
    } else {
      message.error('删除失败');
    }
  });
};

// 绑定商品范围
export const commodityCoverage = () => {
  boundCommodityScopeDrawerFlag.value = true;
};

// 打开添加商品弹框
export const handleAddCommodity = () => {
  addCommodityDrawerFlag.value = true;
};

// 关闭添加商品弹框
export const handleAddCloseDialog = () => {
  addCommodityDrawerFlag.value = false;
};

// 添加商品确认逻辑
export const handleAddConfirm = () => {
  getData();
};

// 分页逻辑
export const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  getData();
};

// 绑定商品范围确定
export const boundCommodityScopeConfirmDialog = () => {
  boundCommodityScopeDrawerFlag.value = false;
};

// 绑定商品范围取消
export const boundCommodityScopeCloseDialog = () => {
  boundCommodityScopeDrawerFlag.value = false;
};
