<template>
  <div class="section-top">
    <search-antd :form-list="formList" @on-search="onSubmit" />
  </div>
  <div class="image-configuration-wrapper">
    <div class="table-bottom">
      <a-button type="primary" @click="handleEdit('', 'add')">新建贴图</a-button>
    </div>

    <a-table
      row-key="id"
      :data-source="tableData"
      :columns="columns"
      :pagination="pagination"
      @change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <div
          v-if="column.key === 'cornerMarkImg'"
          style="display: flex; width: 60px; height: 60px; justify-content: center"
        >
          <img class="img-wrapper" :src="record.cornerMarkImg" />
        </div>
        <div v-if="column.key === 'statusDesc'">
          <div v-if="record.statusDesc === '下架'" class="status-wrapper">
            <div class="status-radius"></div>
            {{ record.statusDesc }}
          </div>
          <div v-else class="status-wrapper" :class="'status-wrapper-green'">
            <div class="status-radius" :class="'status-radius-green'"></div>
            {{ record.statusDesc }}
          </div>
        </div>
        <div v-if="column.key === 'operate'">
          <a-button type="link" class="btn-css" @click="handleEdit(record, 'edit')">修改</a-button>
          <a-button type="link" class="btn-css" @click="handleImageStatus(record)">
            {{ record.statusDesc === '上架' ? '下架' : '上架' }}
          </a-button>
          <a-button type="link" class="btn-css" @click="jumpSettings(record)">设置</a-button>
        </div>
      </template>
    </a-table>
  </div>
  <a-modal
    v-model:open="visibleDialog"
    :title="headerText"
    cancel-text="取消"
    ok-text="确认"
    @ok="onClickConfirm"
    :destroy-on-close="true"
  >
    <a-form
      ref="formHandleRef"
      label-align="top"
      layout="vertical"
      size="large"
      :model="formData"
      :colon="true"
      :rules="FORM_RULES"
    >
      <a-row>
        <a-col>
          <a-form-item label="贴图名称" name="cornerMarkName">
            <a-input
              v-model:value="formData.cornerMarkName"
              placeholder="请输入贴图名称"
              style="width: 420px"
              maxlength="30"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item label="应用类型" name="useType">
            <a-select
              v-model:value="formData.useType"
              style="width: 420px"
              placeholder="请输入应用类型"
              :field-names="{ label: 'useTypeDesc', value: 'useType' }"
              :options="groupOptions"
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item label="贴图图片" name="cornerMarkImg">
            <div class="upload-flex">
              <wd-upload
                biz-type="in_coming"
                :file-list="fileList"
                :accept="accept"
                :max-count="1"
                :file-size="1"
                :max-width="maxWidth"
                @before-upload="beforeUpload"
                @get-url-list="afferentUrlChange"
              />
              <p>
                {{
                  formData.useType === 'prod_title'
                    ? '请上传宽度小于300px，高度36px的png或gif图片'
                    : '展示350*350的尺寸，格式为png或gif的图片'
                }}
              </p>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
<script setup>
  import { onMounted, ref, computed } from 'vue';
  import SearchAntd from '@/components/SearchAntd/index.vue';
  import WdUpload from '@/components/WdUpload/index.vue';
  import {
    tableData,
    columns,
    pagination,
    getData,
    handleEdit,
    headerText,
    visibleDialog,
    FORM_RULES,
    handlePageChange,
    formHandleRef,
    onClickConfirm,
    onSubmit,
    formData,
    groupOptions,
    handleImageStatus,
    jumpSettings,
    formList,
    afferentUrlChange,
    userTypeList,
    fileList,
  } from './setData.js';

  const accept = ref('.png,.gif');
  const maxWidth = computed(() => {
    return formData.useType === 'prod_title' ? 300 : 350;
  });

  onMounted(() => {
    pagination.current = 1;
    pagination.pageSize = 10;
    getData();
    userTypeList();
  });
</script>
<style lang="less" scoped>
  .upload-flex {
    display: flex;
    flex-direction: column;
    color: #666;
    font-size: 12px;
  }
  .section-top {
    border-radius: 16px;
    background: #fff;
  }
  :deep(.ant-message) {
    /* 在你的全局 CSS 文件中增加以下样式 */
    z-index: 10000 !important; /* 使用更高的 z-index，并确保该规则优先级更高 */
  }

  :deep(.img-wrapper) {
    width: auto;
    height: 58px;
    max-width: 60px;
  }

  :deep(.status-wrapper) {
    display: flex;
    align-items: center;
    color: #ff436a;
  }

  :deep(.status-wrapper-green) {
    display: flex;
    align-items: center;
    color: #1bb599;
  }

  :deep(.status-radius) {
    width: 6px;
    height: 6px;
    background: #ff436a;
    margin-right: 4px;
    border-radius: 50%;
  }

  :deep(.status-radius-green) {
    background: #1bb599;
  }

  :deep(.operate-btn) {
    margin-right: 20px;
  }

  .image-configuration-wrapper {
    width: 100%;
    // min-height: 660px;
    background: #fff;
    border-radius: 16px 16px 16px 16px;
    margin-top: 8px;
    padding: 32px;

    .table-bottom {
      padding-bottom: 24px;
      text-align: right;
      svg {
        margin-right: 3px;
      }
    }

    .page-title {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }
  }
</style>
