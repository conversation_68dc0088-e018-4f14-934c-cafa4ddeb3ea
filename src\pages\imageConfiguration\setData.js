import { ref, reactive } from 'vue';
import { message } from 'woody-ui';
import { cornerMarkList, updateStatus, saveCornerMark, queryUseType } from '@/api/cms/chartlet';
import router from '../../router/index.ts';

export const tableData = ref([]);
export const visibleDialog = ref(false);
export const selectedRowKeys = ref([]);
export const headerText = ref('');
export const formHandleRef = ref(null);
export const uploadRef = ref(null);
export const groupOptions = ref([]);
const searchParam = ref({});
const rowData = ref({});

export const fileList = ref([
  // {
  //   name: '',
  //   url: '',
  // }
]);
export const formData = reactive({
  cornerMarkName: '',
  cornerMarkImg: '',
  useType: '',
});

export const formList = [
  {
    type: 'input',
    label: '贴图名称',
    name: 'cornerMarkName',
    span: 6,
  },
  {
    type: 'select',
    label: '贴图状态',
    name: 'status',
    options: [
      {
        label: '上架',
        value: '1',
      },
      {
        label: '下架',
        value: '0',
      },
    ],
  },
];

export const columns = [
  {
    title: '贴图icon',
    dataIndex: 'cornerMarkImg',
    key: 'cornerMarkImg',
    align: 'left',
  },
  {
    title: '贴图名称',
    dataIndex: 'cornerMarkName',
    key: 'cornerMarkName',
    align: 'left',
  },
  {
    title: '贴图类型',
    dataIndex: 'useTypeDesc',
    key: 'useTypeDesc',
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    align: 'left',
  },
  {
    title: '贴图状态',
    dataIndex: 'statusDesc',
    key: 'statusDesc',
    align: 'left',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    align: 'left',
  },
];

// 非空校验
export const FORM_RULES = {
  cornerMarkName: [{ required: true, message: '请输入页面标题' }],
  cornerMarkImg: [{ required: true, message: '请上传贴图图片' }],
  useType: [{ required: true, message: '请选择应用类型' }],
};

export const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

// 隐藏弹框
export const closeDialog = () => {
  visibleDialog.value = false;
  formHandleRef.value.reset();
  rowData.value = {};
  // uploadRef.value.handleClear();
};

// 获取表格数据
export const getData = (param = { page: 1, size: 10 }) => {
  cornerMarkList(param).then(res => {
    if (res.code === 0) {
      tableData.value = res.data.records;
      pagination.total = Number(res.data.total);
    }
  });
};

// 确认按钮
export const onClickConfirm = () => {
  formHandleRef.value.validate().then(() => {
    const obj = {
      ...formData,
    };
    if (rowData.value.id) {
      obj.id = rowData.value.id;
    }
    saveCornerMark(obj).then(res => {
      if (res.code === 0) {
        message.success(`${rowData.value.id ? '编辑' : '新增'}成功`);
        pagination.current = 1;
        getData();
        closeDialog();
      }
    });
  });
};

// 表单查询
export const onSubmit = param => {
  searchParam.value = param;
  pagination.current = 1;
  getData({
    ...param,
    page: 1,
    size: pagination.pageSize,
  });
};

// 修改贴图状态
export const handleImageStatus = row => {
  const { id, status } = row;
  updateStatus({
    id,
    status: status === 1 ? 0 : 1,
  }).then(res => {
    if (res.code === 0) {
      message.success(`${row.status === 1 ? '下架' : '上架'}成功`);
      getData();
    }
  });
};

// 跳转设置
export const jumpSettings = row => {
  router.push({
    path: '/cms/imageSettings',
    query: row,
  });
};

// 图片上传
export const afferentUrlChange = data => {
  if (data && data.length) {
    formData.cornerMarkImg = data[0].url;
    fileList.value = [{ name: '', url: formData.cornerMarkImg }];
  } else {
    formData.cornerMarkImg = '';
    fileList.value = [];
  }
};

// 查询应用类型
export const userTypeList = () => {
  queryUseType().then(res => {
    if (res.code === 0) {
      groupOptions.value = res.data;
    }
  });
};

// 修改
export const handleEdit = (row, type) => {
  visibleDialog.value = true;
  if (type === 'edit') {
    headerText.value = '修改';
    formData.cornerMarkName = row.cornerMarkName;
    formData.cornerMarkImg = row.cornerMarkImg;
    formData.useType = row.useType;
    rowData.value = row;
    fileList.value = [{ name: '', url: formData.cornerMarkImg }];
  } else if (type === 'add') {
    headerText.value = '新建';
    fileList.value = [];
  }
};

// 分页逻辑
export const handlePageChange = pageInfo => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  const obj = {
    page: pageInfo.current,
    size: pageInfo.pageSize,
    ...searchParam.value,
  };
  getData(obj);
};
