<template>
  <div class="login-css">
    <noPage class="box"/>
  </div>
 
</template>
<script lang="ts">
export default {
  name: 'LoginIndex',
};
</script>
<script setup lang="ts">
import noPage from '@/components/noPage.vue';
import { message } from "woody-ui";
import { onMounted } from "vue";

onMounted(async () => {
  // getCategoryDropDown();
  message.warning("请通过基座登录页登录");
});
</script>

<style lang="less" scoped>
.login-css{
  position: relative;
  height: 100vh;
  width: 100%;
  background-color: #f0f2f5;
  .box{
    position: absolute;
  }
}
</style>
