<template>
  <a-modal
    v-model:open="importModal"
    title="批量导入"
    wrapClassName="modal-class"
    @cancel="handleCancel"
  >
    <a-form
      layout="vertical"
      :model="importForm"
      :rules="rules"
      ref="formRef"
      :labelCol="{ style: { width: '200px' } }"
    >
      <a-form-item label="任务名称" name="name">
        <a-input
          v-model:value="importForm.name"
          placeholder="请输入任务名称"
          :maxlength="30"
          allowClear
          showCount
        />
      </a-form-item>
      <a-form-item label="任务类型" name="type">
        <a-radio-group v-model:value="importForm.type">
          <a-radio value="1">限时特惠</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="模板下载">
        <a-button :href="templateData">
          <template #icon>
            <DownloadOutlined />
          </template>
          下载模板
        </a-button>
      </a-form-item>
      <a-form-item label="任务文件" name="fileList">
        <upload-files
          accept=".xlsx,.xls"
          btnText="上传文件"
          :files="importForm.fileList"
          bizType="cms"
          resourceType="file"
          :fileSize="2"
          :maxCount="1"
          @getUrlList="getUrlList"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="btnLoading" @click="handleAgree"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { DownloadOutlined } from "@ant-design/icons-vue";
import UploadFiles from "@/pages/marketingCenter/flashSalesImport/components/uploadFiles.vue";
import { addTask } from "@/api/flashSales";
import { message } from "woody-ui";
import { getDictionaries } from "@/api/common";

const emits = defineEmits(["onClose"]);

//弹窗
const importModal = ref<boolean>(false);

//表单类型
interface importFormType {
  //任务名称
  name: string;
  //任务类型
  type: string;
  //文件
  fileList: Array<any>;
}

//导入表单
const importForm = ref<importFormType>({
  //任务名称
  name: undefined,
  //任务类型
  type: "1",
  //文件
  fileList: [],
});

//表单校验
const rules = ref({
  //任务名称
  name: [{ required: true, message: "请输入任务名称", trigger: "blur" }],
  //文件
  fileList: [
    { required: true, message: "请选择文件", trigger: "change | blur" },
  ],
});

//获取文件
const getUrlList = (fileList: any) => {
  importForm.value.fileList = fileList;
  if (fileList && fileList.length > 0) {
    formRef.value.clearValidate("fileList");
  }
};

//打开弹窗
const openImportModal = () => {
  importModal.value = true;
  getTemplate();
};

//模板链接
const templateData = ref();
//获取模板链接
const getTemplate = () => {
  let info = {
    dictCode: "LIFE_PROMOTION_BATCH_FILE_TEMPLATE",
  };
  getDictionaries(info).then((res) => {
    templateData.value = res.data[0].attributes.url;
  });
};

//表单ref
const formRef = ref();

//按钮loading
const btnLoading = ref(false);

//保存
const handleAgree = () => {
  formRef.value.validate().then(() => {
    btnLoading.value = true;
    let info = {
      taskName: importForm.value.name,
      sourceUrl: importForm.value.fileList[0].url,
    };
    addTask(info)
      .then((res) => {
        message.success("新增成功");
        importForm.value.name = undefined;
        importForm.value.fileList = [];
        importModal.value = false;
        emits("onClose");
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
//取消
const handleCancel = () => {
  importForm.value.name = undefined;
  importForm.value.fileList = [];
  importModal.value = false;
};

defineExpose({
  openImportModal,
});
</script>

<style scoped lang="less"></style>
