<template>
  <a-upload
    v-model:file-list="fileList"
    name="file"
    list-type="text"
    class="avatar-uploader"
    :accept="accept"
    :custom-request="customUpload"
    :multiple="multiple"
    :before-upload="beforeUpload"
    @change="handleChange"
    :max-count="maxCount"
    :remove="handleDelete"
  >
    <a-button>{{ btnText }}</a-button>
  </a-upload>

  <a-modal
    :open="previewVisible"
    :title="previewTitle"
    :footer="null"
    centered
    :zIndex="10000"
    @cancel="handleCancel"
  >
    <img alt="example" style="width: 100%" :src="previewImage" />
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { message } from "woody-ui";
import type { UploadChangeParam, UploadProps } from "woody-ui";
import axios from "axios";
import { qiNiuYunTokenFile } from "@/api/flashSales";

const props = defineProps({
  files: {
    type: Array,
    default: () => {
      return [];
    },
  },
  resourceType: {
    type: String,
    default: "image",
  },
  bizType: {
    type: String,
    default: "",
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  maxWidth: {
    type: Number,
    default: 10000,
  },
  accept: {
    type: String,
    default: "",
  },
  btnText: {
    type: String,
    default: "上传",
  },
  fileSize: {
    type: Number,
    default: 1.8,
  },
  maxCount: {
    type: Number,
    default: 100,
  },
});

const fileList = ref<any>([]);

function getBase64(img: Blob, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener("load", () => callback(reader.result as string));
  reader.readAsDataURL(img);
}
function getBase642(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
const loading = ref<boolean>(false);

const tokenData = ref({
  agreement: "",
  domain: "",
  path: "",
  token: "",
});

const customUpload: UploadProps["customRequest"] = (e: any) => {
  qiNiuYunTokenFile({
    bizType: props.bizType,
    resourceType: props.resourceType,
  }).then((res) => {
    if (res.code === 0) {
      const { token, path, agreement, domain } = res.data;
      tokenData.value = {
        token,
        path,
        agreement,
        domain,
      };
      e.onSuccess(e);
    }
  });
};

const emits = defineEmits(["getUrlList", "before-upload"]);
const handleChange = (info: UploadChangeParam) => {
  if (info.file.status === "done") {
    getBase64(info.file.originFileObj, () => {
      loading.value = false;
    });

    const formData = new FormData();
    const { token, path } = tokenData.value;
    let accept = info.file.name.split(".");
    const key = path + new Date().getTime() + "." + accept[1];
    formData.append("key", key);
    formData.append("token", token);
    formData.append("file", info.file.originFileObj);
    formData.append("fname", new Date().getTime() + "." + accept[1]);
    const { agreement, domain } = tokenData.value;

    axios
      .post("https://upload.qiniup.com/", formData, {
        headers: {
          "content-type": "multipart/form-data",
        },
      })
      .then((response) => {
        if (response.data.key) {
          fileList.value = [
            {
              url: `${agreement}://${domain}/${response.data.key}`,
              name: info.file.name,
              uid: info.file.uid,
            },
          ];
          console.log(fileList.value);
          emits("getUrlList", fileList.value);
        }
      })
      .catch(() => {
        message.error("上传失败");
        fileList.value = [];
        emits("getUrlList", fileList.value);
      });
  }
  if (info.file.status === "error") {
    loading.value = false;
    message.error("upload error");
  }
};

const previewVisible = ref(false);
const previewImage = ref("");
const previewTitle = ref("");

// 上传前看图片信息
const beforeUpload = (file: any) => {
  return new Promise((resolve) => {
    if (fileList.value.length >= props.maxCount) {
      message.error(`文件上传不能超过 ${props.maxCount}张`);
      return false;
    }
    // 检查文件大小
    const maxSizeInMB = props.fileSize;
    if (file.size > maxSizeInMB * 1024 * 1024) {
      message.error(`文件大小不能超过 ${maxSizeInMB}MB`);
      return false;
    }
    return resolve(true);
  });
};
const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = "";
};
const handleDelete = (file: UploadProps["fileList"][number]) => {
  const index = fileList.value.indexOf(file.url);
  fileList.value.splice(index, 1);
  emits("getUrlList", fileList.value);
};
watch(
  () => props.files,
  (newValue) => {
    fileList.value = newValue;
  },
  { immediate: true, deep: true }
);
</script>
<style scoped>
.avatar-uploader > .ant-upload {
  width: 138px;
  height: 138px;
  overflow: hidden;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
