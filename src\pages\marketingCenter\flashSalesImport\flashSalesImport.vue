<template>
  <div class="table-search">
    <a-form
      layout="vertical"
      :model="searchTableData"
      :labelCol="{ style: { width: '150px' } }"
    >
      <a-row>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="任务名称">
            <a-input
              v-model:value="searchTableData.taskName"
              placeholder="请输入任务名称"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="创建人">
            <a-input
              v-model:value="searchTableData.createUserName"
              placeholder="请输入创建人"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="创建日期">
            <a-date-picker
              placeholder="创建日期"
              value-format="YYYY-MM-DD"
              v-model:value="searchTableData.createTime"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="任务状态">
            <a-select
              v-model:value="searchTableData.taskStatus"
              placeholder="请选择是否开启"
              style="width: 100%"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in taskStates"
                :key="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="">
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 16px"
              >重置</a-button
            >
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div class="table-main">
    <div class="table-button">
      <a-button type="primary" @click="addTask">
        <template #icon><PlusCircleOutlined /></template>
        新增任务
      </a-button>
    </div>
    <div class="table-content" ref="tableContentRef">
      <a-table
        :columns="columns"
        :data-source="tableList"
        :pagination="false"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <div class="status-content">
              <div
                class="status-icon"
                :style="{ backgroundColor: tableTaskStates(record).color }"
              ></div>
              <div :style="{ color: tableTaskStates(record).color }">
                {{ tableTaskStates(record).name }}
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <div class="table-btn">
              <a-button
                type="link"
                class="btn-css"
                :href="record.downloadUrl"
                v-if="record.downloadUrl"
                >下载结果</a-button
              >
              <a-button type="link" class="btn-css" v-else @click="handleDownload(record)"
                >下载结果</a-button
              >
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <div class="table-pagination">
      <div>共 {{ total }} 项数据</div>
      <a-pagination
        v-model:current="searchTableData.page"
        v-model:page-size="searchTableData.size"
        show-size-changer
        show-quick-jumper
        :total="total"
        @change="changePagination"
      />
    </div>
  </div>
  <!--批量导入-->
  <import-modal ref="importModalRef" @onClose="onClose" />
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from "vue";
import { PlusCircleOutlined } from "@ant-design/icons-vue";
import { FLASH_SALES_IMPORT_COLUMNS } from "@/pages/marketingCenter/flashSalesImport/constants/constants";
import ImportModal from "@/pages/marketingCenter/flashSalesImport/components/importModal.vue";
import { getTaskPage } from "@/api/flashSales";
import { message } from "woody-ui";

interface searchDataType {
  //任务名称
  taskName: string;
  //创建人
  createUserName: string;
  //创建日期
  createTime: string;
  //任务状态
  taskStatus: string;
  //页码
  page: number;
  //每页条数
  size: number;
}
//列表查询数据
const searchTableData = ref<searchDataType>({
  //任务名称
  taskName: undefined,
  //创建人
  createUserName: undefined,
  //创建日期
  createTime: undefined,
  //任务状态
  taskStatus: undefined,
  //页码
  page: 1,
  //每页条数
  size: 10,
});

//列表配置
const columns = FLASH_SALES_IMPORT_COLUMNS;

//列表数据
const tableList = ref<Array<any>>([]);
//列表总数
const total = ref(1);
//获取列表数据
const getTableList = () => {
  tableLoading.value = true;
  getTaskPage(searchTableData.value)
    .then((res) => {
      tableList.value = res.data.records;
      total.value = res.data.total;
    })
    .catch((err) => {
      tableList.value = [];
      total.value = 1;
      message.error(err.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

//任务状态
const taskStates = ref<Array<any>>([
  { label: "未开始", value: 0 },
  { label: "执行中", value: 1 },
  { label: "成功", value: 2 },
  { label: "失败", value: -1 },
]);

//列表状态展示
const tableTaskStates = computed(() => (item: any) => {
  let info = { color: "#818999", name: "未开始" };
  switch (item.status) {
    case 0:
      info = { color: "#818999", name: "未开始" };
      return info;
    case 1:
      info = { color: "#1A7AF8", name: "执行中" };
      return info;
    case 2:
      info = { color: "#1BB599", name: "成功" };
      return info;
    case -1:
      info = { color: "#FF436A", name: "失败" };
      return info;
  }
  return info;
});

//查询Loading
const tableLoading = ref<boolean>(false);

//查询数据
const handleSearch = () => {
  searchTableData.value.page = 1;
  getTableList();
};

//重置
const handleReset = () => {
  searchTableData.value.createTime = undefined;
  searchTableData.value.taskName = undefined;
  searchTableData.value.taskStatus = undefined;
  searchTableData.value.createUserName = undefined;
  searchTableData.value.page = 1;
  getTableList();
};

//切换分页
const changePagination = (page: number, pageSize: number) => {
  searchTableData.value.page = page;
  searchTableData.value.size = pageSize;
  getTableList();
};

//下载结果
const handleDownload = (record: any) => {
  message.error(record.resultDesc);
};

//批量导入弹窗ref
const importModalRef = ref();
//新建任务
const addTask = () => {
  importModalRef.value.openImportModal();
};
//关闭弹窗
const onClose = () => {
  handleSearch();
};

//table内容ref
const tableContentRef = ref();

onMounted(() => {
  getTableList();
});
</script>

<style scoped lang="less">
.status-content {
  display: flex;
  align-items: center;
  gap: 4px;
  .status-icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.table-btn {
  :deep(.ant-btn) {
    // padding: 4px 15px 4px 0;
  }
}
</style>
