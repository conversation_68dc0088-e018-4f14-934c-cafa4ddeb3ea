<template>
  <div class="back-btn" @click="goBack">
    <LeftOutlined />
    <div class="btn-title">返回上一页</div>
  </div>
  <div class="table-search">
    <a-form
      layout="vertical"
      :model="searchTableData"
      :labelCol="{ style: { width: '150px' } }"
    >
      <a-row>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="商品名称">
            <a-input
              v-model:value="searchTableData.prodName"
              placeholder="请输入商品名称"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="商品ID">
            <a-input
              v-model:value="searchTableData.prodId"
              placeholder="请输入商品ID"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="商品来源">
            <a-select
              v-model:value="searchTableData.prodSource"
              placeholder="请选择商品来源"
              style="width: 100%"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in commoditySources"
                :key="item.prodSourceCode"
                >{{ item.prodSourceDesc }}</a-select-option
              >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="所属店铺">
            <a-select
              v-model:value="searchTableData.shopId"
              placeholder="请选择所属店铺"
              style="width: 100%"
              :not-found-content="null"
              :default-active-first-option="false"
              :filter-option="false"
              :options="shopList"
              allowClear
              show-search
              @search="handleShopSearch"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="后台分类">
            <a-cascader
              v-model:value="searchTableData.classify"
              :options="classifyList"
              :load-data="loadData"
              :fieldNames="{
                label: 'categoryName',
                value: 'categoryId',
                children: 'children',
              }"
              placeholder="请选择后台分类"
              change-on-select
              @change="changeCascader"
            />
          </a-form-item>
        </a-col>
        <a-col
          :span="6"
          class="a-col-center"
          style="padding-left: 12px; padding-right: 12px"
        >
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 16px"
              >重置</a-button
            >
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div class="table-main">
    <div class="table-content" ref="tableContentRef">
      <a-table
        :columns="columns"
        :data-source="tableList"
        :scroll="{ x: tableContent.x }"
        :pagination="false"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'productId'">
            <div class="prod-main">
              <a-image :src="record.picUrl" alt="" :width="60" :height="60" />
              <div class="prod-info">
                <a-tooltip placement="top" color="#ffffff">
                  <template #title>
                    <div style="color: #05082c">{{ record.prodName }}</div>
                    <div style="color: #05082c">ID:{{ record.productId }}</div>
                  </template>
                  <div class="prod-info-name">{{ record.prodName }}</div>
                  <div class="prod-info-id">ID:{{ record.productId }}</div>
                </a-tooltip>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <div class="putaway-icon" v-if="record.status === 1">上架</div>
            <div class="sold-out-icon" v-if="record.status === 0">下架</div>
            <div class="error-icon" v-if="record.status === 2">违规下架</div>
          </template>
          <template v-if="column.dataIndex === 'minPrice'">
            <span>{{ record.minPrice }}</span>
            <span v-if="record.maxPrice && record.minPrice !== record.maxPrice"
              >--</span
            >
            <span
              v-if="record.maxPrice && record.minPrice !== record.maxPrice"
              >{{ record.maxPrice }}</span
            >
          </template>
          <template v-if="column.dataIndex === 'platParentCategoryName'">
            <span>{{ record.platParentCategoryName }}</span>
            <span v-if="record.platCategoryName">，</span>
            <span v-if="record.platCategoryName">{{
              record.platCategoryName
            }}</span>
          </template>
          <template v-if="column.dataIndex === 'prodSource'">
            <div>{{ prodSourceName(record.prodSource) }}</div>
          </template>
          <template v-if="column.dataIndex === 'simple'">
            <template v-if="record.simple && record.simple.length > 2">
              <a-popover placement="bottom">
                <template #content>
                  <div
                    v-for="(item, index) in record.simple"
                    :key="index"
                    class="describe-main"
                  >
                    <div class="describe-name">{{ item.promotionName }}</div>
                    <div class="describe-content">
                      （ID：{{ item.promotionCode }}，{{ item.bizTypeStr }}）
                    </div>
                  </div>
                </template>
                <div class="describe-main">
                  <div class="describe-name">
                    {{ record.simple[0].promotionName }}
                  </div>
                  <div class="describe-content">
                    （ID：{{ record.simple[0].promotionCode }}，{{
                      record.simple[0].bizTypeStr
                    }}）
                  </div>
                </div>
                <div class="describe-main">
                  <div class="describe-name">
                    {{ record.simple[1].promotionName }}
                  </div>
                  <div class="describe-content">
                    （ID：{{ record.simple[1].promotionCode }}，{{
                      record.simple[1].bizTypeStr
                    }}）...
                  </div>
                </div>
              </a-popover>
            </template>
            <template v-else>
              <div
                class="describe-main"
                v-if="record.simple && record.simple.length > 0"
              >
                <div class="describe-name">
                  {{ record.simple[0].promotionName }}
                </div>
                <div class="describe-content">
                  （ID：{{ record.simple[0].promotionCode }}，{{
                    record.simple[0].bizTypeStr
                  }}）
                </div>
              </div>
              <div
                class="describe-main"
                v-if="record.simple && record.simple.length > 1"
              >
                <div class="describe-name">
                  {{ record.simple[1].promotionName }}
                </div>
                <div class="describe-content">
                  （ID：{{ record.simple[1].promotionCode }}，{{
                    record.simple[1].bizTypeStr
                  }}）
                </div>
              </div>
            </template>
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <a-button
              type="link"
              class="btn-css"
              v-if="record.select === 1"
              @click="handleAdd(record)"
              >添加商品</a-button
            >
            <a-button
              type="link"
              class="btn-css"
              v-if="record.select === 0"
              :disabled="true"
              >不可选</a-button
            >
          </template>
        </template>
      </a-table>
    </div>
    <div class="table-pagination">
      <div>共 {{ total }} 项数据</div>
      <a-pagination
        v-model:current="searchTableData.page"
        v-model:page-size="searchTableData.size"
        show-size-changer
        show-quick-jumper
        :total="total"
        @change="changePagination"
      />
    </div>
  </div>
  <!--添加商品-->
  <add-or-edit-shop ref="addOrEditShopRef" type="add" @onClose="onClose" />
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import {
  ADD_COMMODITY_COLUMNS,
  searchAddCommodityType,
  tableContentType,
} from "@/pages/marketingCenter/flashSales/constants/constants";
import addOrEditShop from "@/pages/marketingCenter/flashSales/components/addOrEditShop.vue";
import { useRoute } from "vue-router";
import { getSourceConfigList } from "@/api/common";
import {
  getCategoryList,
  getProductSelect,
  getShopPage,
} from "@/api/flashSales";
import router from "@/router";
import { message } from "woody-ui";
import { LeftOutlined } from "@ant-design/icons-vue";

//路由参数
const route = useRoute();

//列表查询数据
const searchTableData = ref<searchAddCommodityType>({
  //商品名称
  prodName: undefined,
  //商品id
  prodId: undefined,
  //商品来源
  prodSource: undefined,
  //所属店铺
  shopId: undefined,
  //后台分类
  classify: [],
  //一级分类
  firstCategoryId: undefined,
  //二级分类
  secondCategoryId: undefined,
  //活动id
  promotionId: undefined,
  //是否互斥
  excludeFullReduction: 1,
  //页码
  page: 1,
  //每页条数
  size: 10,
});

//列表配置
const columns = ADD_COMMODITY_COLUMNS;

//列表数据
const tableList = ref<Array<any>>([]);
//列表总数
const total = ref(1);
//获取列表
const getTableList = () => {
  tableLoading.value = true;
  searchTableData.value.promotionId = promotionId.value;
  getProductSelect(searchTableData.value)
    .then((res) => {
      tableList.value = res.data.records;
      total.value = res.data.total;
    })
    .catch((err) => {
      tableList.value = [];
      total.value = 1;
      message.error(err.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

//商品来源名称
const prodSourceName = computed(() => (prodSource: number) => {
  let name = undefined;
  commoditySources.value.forEach((item) => {
    if (String(item.prodSourceCode) === String(prodSource)) {
      name = item.prodSourceDesc;
    }
  });
  return name;
});

//table内容ref
const tableContentRef = ref();
//table内容宽高
const tableContent = ref<tableContentType>({
  x: undefined,
});

//商品来源数据
const commoditySources = ref<Array<any>>([]);
//获取商品来源
const getSourceList = () => {
  getSourceConfigList().then((res) => {
    commoditySources.value = res.data.prodSourceConfigList;
  });
};

//所属店铺
const shopList = ref<Array<any>>([]);
//查询所属店铺
const handleShopSearch = (val: any) => {
  if (val) {
    getShopList(val);
  } else {
    shopList.value = [];
  }
};

//定时器实例
const timeout = ref();
//获取所属店铺
const getShopList = (val: any) => {
  if (timeout.value) {
    clearTimeout(timeout.value);
    timeout.value = null;
  }
  function fake() {
    let info = {
      page: 1,
      size: 10000,
      shopName: val,
    };
    getShopPage(info).then((res) => {
      shopList.value = [];
      if (res.data.records && res.data.records.length > 0) {
        res.data.records.forEach((item: any) => {
          shopList.value.push({ value: item.shopId, label: item.shopName });
        });
      }
    });
  }
  timeout.value = setTimeout(fake, 300);
};

//后台分类
const classifyList = ref<Array<any>>([]);
//获取后台分类
const getClassifyList = () => {
  getCategoryList({}).then((res) => {
    if (res.data) {
      res.data.forEach((item) => {
        item.isLeaf = false;
      });
      classifyList.value = res.data;
    }
  });
};
//获取二级分类
const loadData = (selectedOptions: any) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  getCategoryList({ categoryId: targetOption.categoryId })
    .then((res) => {
      targetOption.children = res.data;
    })
    .finally(() => {
      targetOption.loading = false;
    });
};
//选择后台分类
const changeCascader = (value: any) => {
  if (value && value.length > 0) {
    searchTableData.value.firstCategoryId = value[0];
    searchTableData.value.secondCategoryId =
      value.length > 1 ? value[1] : undefined;
  } else {
    searchTableData.value.firstCategoryId = undefined;
    searchTableData.value.secondCategoryId = undefined;
  }
};

//切换分页
const changePagination = (page: number, pageSize: number) => {
  searchTableData.value.page = page;
  searchTableData.value.size = pageSize;
  getTableList();
};

//列表Loading
const tableLoading = ref<boolean>(false);

//查询数据
const handleSearch = () => {
  searchTableData.value.page = 1;
  getTableList();
};

//重置
const handleReset = () => {
  searchTableData.value.prodName = undefined;
  searchTableData.value.prodId = undefined;
  searchTableData.value.prodSource = undefined;
  searchTableData.value.shopId = undefined;
  searchTableData.value.classify = [];
  searchTableData.value.firstCategoryId = undefined;
  searchTableData.value.secondCategoryId = undefined;
  searchTableData.value.page = 1;
  getTableList();
};

//添加商品弹窗ref
const addOrEditShopRef = ref();
//添加
const handleAdd = (record: any) => {
  addOrEditShopRef.value.openDrawer(record.productId, promotionId.value);
};
//关闭弹窗
const onClose = () => {
  router.go(-1);
};

//返回
const goBack = () => {
  router.go(-1);
};

//活动id
const promotionId = ref<any>(undefined);

onMounted(() => {
  nextTick(() => {
    tableContent.value.x = tableContentRef.value.offsetWidth;
  });
  if (route.query.id) {
    promotionId.value = route.query.id;
  }
  getSourceList();
  getClassifyList();
  getTableList();
});
</script>

<style scoped lang="less">
.putaway-icon {
  width: 40px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #94cdff;
  background: #e6f4ff;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #1a7af8;
}
.sold-out-icon {
  width: 40px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e8ed;
  background: #f0f4f9;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #495366;
}
.error-icon {
  width: 60px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ffbdc3;
  background: #fff0f0;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #ff436a;
}
.describe-main {
  display: flex;
  align-items: center;
  .describe-name {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }
  .describe-content {
    font-weight: 400;
    font-size: 14px;
    color: #818999;
  }
}
.prod-main {
  display: flex;
  align-items: center;
  width: 100%;
  .prod-info {
    flex: 1;
    width: 1px;
    display: flex;
    flex-direction: column;
    margin-left: 8px;
    .prod-info-name {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }
    .prod-info-id {
      font-weight: 400;
      font-size: 14px;
      color: #818999;
    }
  }
}
.back-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 15px;
}
</style>
