<template>
  <div class="basic-information" :style="{ padding: '32px 32px 8px 32px' }">
    <div class="title-main">
      <div class="title-icon"></div>
      <div class="title-name">活动基本信息</div>
    </div>
    <div class="form-main">
      <a-form
        layout="vertical"
        :model="activityForm"
        :labelCol="{ style: { width: '450px' } }"
        :rules="rules"
        ref="formRef"
      >
        <a-form-item label="活动名称" name="promotionName">
          <a-input
            v-model:value="activityForm.promotionName"
            :disabled="activityForm.promotionStatus !== 0"
            placeholder="请输入活动名称"
            :maxlength="5"
            allowClear
            showCount
          />
        </a-form-item>
        <a-form-item label="业务类型" name="bizType">
          <a-select
            v-model:value="activityForm.bizType"
            placeholder="请选择活业务类型"
            :disabled="activityForm.promotionStatus !== 0"
            style="width: 100%"
            allowClear
          >
            <a-select-option
              :value="item.value"
              v-for="item in businessTypes"
              :key="item.value"
              >{{ item.name }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <a-form-item label="活动描述" name="promotionDescription">
          <a-input
            v-model:value="activityForm.promotionDescription"
            :disabled="activityForm.promotionStatus !== 0"
            placeholder="请输入活动描述"
            :maxlength="15"
            allowClear
            showCount
          />
        </a-form-item>
        <a-form-item label="活动时间" name="promotionTime">
          <a-range-picker
            v-model:value="activityForm.promotionTime"
            :locale="locale"
            show-time
            allow-clear
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled="activityForm.promotionStatus !== 0"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item name="showCountDown">
          <template #label>
            <div>是否展示倒计时（时长超过15天时自动隐藏倒计时）</div>
          </template>
          <a-radio-group
            v-model:value="activityForm.showCountDown"
            :disabled="activityForm.promotionStatus !== 0"
          >
            <a-radio :value="1">展示</a-radio>
            <a-radio :value="0">不展示</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="当前活动状态">
          <div
            :style="{
              color: activeState.color,
              border: `1px solid ${activeState.borderColor}`,
              backgroundColor: activeState.background,
            }"
            class="tag-main"
          >
            {{ activeState.name }}
          </div>
        </a-form-item>
      </a-form>
    </div>
  </div>
  <div class="basic-information" :style="{ padding: '32px 32px 8px 32px' }">
    <div class="title-main">
      <div class="title-icon"></div>
      <div class="title-name">活动规则</div>
    </div>
    <div class="rule-description">
      特惠活动，采用一口价形式，在商品上配置优惠价格、限购数量。同时参与供货仓满减、限时特惠的商品，优先命中限时特惠活动。
    </div>
    <div class="descriptions-css" style="margin-left: 12px">
      <a-descriptions
        layout="vertical"
        :column="1"
        :labelStyle="{
          color: '#05082C',
          fontSize: '14px',
          fontWeight: 400,
          paddingBottom: '8px',
        }"
        :contentStyle="{ paddingBottom: '24px' }"
      >
        <a-descriptions-item label="达到限购后">
          <a-radio v-model:checked="businessRules.noBuy" :disabled="true"
            >无法购买</a-radio
          >
        </a-descriptions-item>
        <a-descriptions-item label="互斥活动">
          <a-checkbox-group
            v-model:value="activityForm.excludeFullReduction"
            :disabled="true"
          >
            <a-checkbox :value="1">供货仓 -- 满减</a-checkbox>
            <!--<a-checkbox value="1">金币商城 &#45;&#45; 新人福利</a-checkbox>
            <a-checkbox value="2">金币商城 &#45;&#45; 限时秒杀</a-checkbox>
            <a-checkbox value="3">金币商城 &#45;&#45; 特价专区</a-checkbox>
            <a-checkbox value="4">金币商城 &#45;&#45; 金币抽奖</a-checkbox>
            <a-checkbox value="4">金币商城 &#45;&#45; 金币抽奖</a-checkbox>-->
          </a-checkbox-group>
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
  <div
    :class="
      activityForm.promotionStatus !== 2
        ? 'basic-information bottom-main'
        : 'basic-information'
    "
    ref="mainRef"
  >
    <div class="title-main">
      <div class="title-icon"></div>
      <div class="title-name">活动商品</div>
    </div>
    <div class="table-details-search">
      <a-form
        layout="vertical"
        :model="searchTableData"
        :labelCol="{ style: { width: '150px' } }"
      >
        <a-row>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品名称">
              <a-input
                v-model:value="searchTableData.prodName"
                placeholder="请输入商品名称"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品ID">
              <a-input
                v-model:value="searchTableData.prodId"
                placeholder="请输入商品ID"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品状态">
              <a-select
                v-model:value="searchTableData.prodStatus"
                placeholder="请选择商品状态"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in commodityStates"
                  :key="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="活动内状态">
              <a-select
                v-model:value="searchTableData.promotionSkuStatus"
                placeholder="请选择活动内状态"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in activateStatusList"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="skuID">
              <a-input
                v-model:value="searchTableData.skuId"
                placeholder="请输入skuID"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="sku状态">
              <a-select
                v-model:value="searchTableData.skuStatus"
                placeholder="请选择sku状态"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in skuStates"
                  :key="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品来源">
              <a-select
                v-model:value="searchTableData.prodSource"
                placeholder="请选择商品来源"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in sourceList"
                  :key="item.prodSourceCode"
                  >{{ item.prodSourceDesc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="所属店铺">
              <a-select
                v-model:value="searchTableData.shopId"
                placeholder="请选择所属店铺"
                style="width: 100%"
                :not-found-content="null"
                :default-active-first-option="false"
                :filter-option="false"
                :options="shopList"
                allowClear
                show-search
                @search="handleShopSearch"
              >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :span="6"
            class="a-col-center"
            style="padding-left: 12px; padding-right: 12px"
          >
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 16px"
              >重置</a-button
            >
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-box">
      <div class="table-btn">
        <div class="table-btn-left">
          <a-button
            v-if="activityForm.promotionStatus !== 0"
            :disabled="selectList.length === 0"
            @click="batchActivation"
            >批量激活</a-button
          >
        </div>
        <div class="table-btn-right">
          <a-button type="primary" @click="handleAddCommodity()"
            >新增商品</a-button
          >
        </div>
      </div>
      <div class="table-content" ref="tableContentRef">
        <a-table
          :columns="columns"
          :data-source="tableList"
          :scroll="{ x: tableWidth }"
          :pagination="false"
          :row-selection="rowSelection"
          row-key="id"
          :loading="tableLoading"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'prodName'">
              <div class="prod-main">
                <a-image
                  :src="record.prodPic"
                  alt=""
                  :width="60"
                  :height="60"
                />
                <div class="prod-info">
                  <a-tooltip placement="top" color="#ffffff">
                    <template #title>
                      <div style="color: #05082c">{{ record.prodName }}</div>
                      <div style="color: #05082c">ID:{{ record.prodId }}</div>
                    </template>
                    <div class="prod-info-name">{{ record.prodName }}</div>
                    <div class="prod-info-id">ID:{{ record.prodId }}</div>
                  </a-tooltip>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'prodStatus'">
              <div class="putaway-icon" v-if="record.prodStatus === 1">
                上架
              </div>
              <div class="sold-out-icon" v-if="record.prodStatus === 0">
                下架
              </div>
              <div class="error-icon" v-if="record.prodStatus === 2">
                违规下架
              </div>
            </template>
            <template v-if="column.dataIndex === 'skuName'">
              <div>规格名：{{ record.skuName }}</div>
              <div>规格ID：{{ record.skuId }}</div>
            </template>
            <template v-if="column.dataIndex === 'skuStatus'">
              <div class="putaway-icon" v-if="record.skuStatus === 1">上架</div>
              <div class="sold-out-icon" v-else>下架</div>
            </template>
            <template v-if="column.dataIndex === 'prodSource'">
              <div>{{ prodSourceName(record.prodSource) }}</div>
            </template>
            <template v-if="column.dataIndex === 'promotionSkuStatus'">
              <div
                class="status-main"
                :style="{
                  color: commodityStatus(record.promotionSkuStatus).color,
                }"
              >
                <div
                  class="status-icon"
                  :style="{
                    background: commodityStatus(record.promotionSkuStatus)
                      .background,
                  }"
                ></div>
                <div>{{ commodityStatus(record.promotionSkuStatus).name }}</div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'prodLimit'">
              <div>
                {{ record.prodLimit === -1 ? "不限购" : record.prodLimit }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'operate'">
              <a-button
                type="link"
                class="btn-css"
                v-if="![2, 3].includes(record.promotionSkuStatus)"
                @click="handleEdit(record)"
                >编辑商品</a-button
              >
              <a-button
                type="link"
                class="btn-css"
                v-if="
                  record.promotionSkuStatus === 0 &&
                  activityForm.promotionStatus !== 0
                "
                @click="handleActivate(record)"
                >激活</a-button
              >
              <a-button
                type="link"
                class="btn-css"
                v-if="
                  record.promotionSkuStatus === 1 &&
                  activityForm.promotionStatus !== 0
                "
                @click="handleFailure(record)"
                >失效</a-button
              >
            </template>
          </template>
        </a-table>
      </div>
      <div class="table-pagination">
        <div>共 {{ total }} 项数据</div>
        <a-pagination
          v-model:current="searchTableData.page"
          v-model:page-size="searchTableData.size"
          show-size-changer
          show-quick-jumper
          :total="total"
          @change="changePagination"
        />
      </div>
    </div>
  </div>
  <div
    class="bottom-button"
    :style="{ width: mainWidth + 'px' }"
    v-if="activityForm.promotionStatus == 0"
  >
    <a-button @click="goBack">取消</a-button>
    <a-button
      type="primary"
      ghost
      style="margin-left: 16px"
      :loading="btnLoading"
      @click="handleAdd"
      >保存</a-button
    >
    <a-button
      type="primary"
      style="margin-left: 16px"
      :loading="btnLoading"
      @click="handleAddAndIssue"
      >保存并发布</a-button
    >
  </div>

  <!-- 弹窗-激活/失效 -->
  <a-modal v-model:open="openModal" wrapClassName="modal-inform-class">
    <template #title>
      <div class="title-content">
        <ExclamationCircleFilled :style="{ color: '#ff436a' }" />
        <div>通知</div>
      </div>
    </template>
    <div class="back-box">
      <div class="title" v-if="modalType === PUBLISH">
        发布后将自动激活活动商品，确认发布？
      </div>
      <div class="title" v-else>
        确定{{ modalType === ACTIVATE ? "激活" : "失效" }}吗？
      </div>
    </div>
    <template #footer>
      <a-button @click="openModal = false">取消</a-button>
      <a-button type="primary" @click="handleAgree">确定</a-button>
    </template>
  </a-modal>
  <!--编辑商品-->
  <add-or-edit-shop
    ref="addOrEditShopRef"
    type="edit"
    :status="activityForm.promotionStatus"
    @onClose="onClose"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, onUnmounted } from "vue";
import router from "@/router";
import {
  ACTIVATE,
  COMMODITY_ADD_EDIT_COLUMNS,
  FAILURE,
  PUBLISH,
  searchCommodityType,
} from "@/pages/marketingCenter/flashSales/constants/constants";
import type { TableProps } from "woody-ui";
import { ExclamationCircleFilled } from "@ant-design/icons-vue";
import addOrEditShop from "@/pages/marketingCenter/flashSales/components/addOrEditShop.vue";
import { getDictionaries, getSourceConfigList } from "@/api/common";
import {
  getShopPage,
  getPromotionDetails,
  getSkuPage,
  updateSkuStatus,
  getPromotionId,
  createPromotion,
  editPromotion,
  updateStatus,
} from "@/api/flashSales";
import { message } from "woody-ui";
import { useRoute } from "vue-router";
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";

//路由传参
const route = useRoute();

//活动字段
const activityForm = ref({
  //活动名称
  promotionName: undefined,
  //业务类型
  bizType: undefined,
  //活动描述
  promotionDescription: undefined,
  //活动时间
  promotionTime: undefined,
  //开始时间
  promotionStartTime: undefined,
  //结束时间
  promotionEndTime: undefined,
  //是否展示倒计时
  showCountDown: undefined,
  //当前活动状态
  promotionStatus: 0,
  //活动类型
  promotionType: 0,
  //排斥满减
  excludeFullReduction: [1],
});

//当前活动状态
const activeState = computed(() => {
  let obj = {
    name: "未发布",
    color: "#FF436A",
    borderColor: "#FF436A",
    background: "#FFEDF0",
  };
  switch (activityForm.value.promotionStatus) {
    case 0:
      obj = {
        name: "未发布",
        color: "#FF436A",
        borderColor: "#FFBDC3",
        background: "#FFF0F0",
      };
      return obj;
    case 1:
      obj = {
        name: "未开始",
        color: "#FF9B26",
        borderColor: "#FFCF96",
        background: "#FFF9F3",
      };
      return obj;
    case 2:
      obj = {
        name: "进行中",
        color: "#1A7AF8",
        borderColor: "#94CDFF",
        background: "#E6F4FF",
      };
      return obj;
    case 9:
      obj = {
        name: "已结束",
        color: "#1BB599",
        borderColor: "#8ADBC4",
        background: "#E6F5F0",
      };
      return obj;
    case 8:
      obj = {
        name: "已下线",
        color: "#05082C",
        borderColor: "#E0E8ED",
        background: "#F0F4F9",
      };
      return obj;
  }
  return obj;
});

//商品状态
const commodityStatus = computed(() => (status: number) => {
  let obj = { name: "未激活", color: "#FF436A", background: "#FF436A" };
  switch (status) {
    case 0:
      obj = { name: "未激活", color: "#FF436A", background: "#FF436A" };
      return obj;
    case 1:
      obj = { name: "已激活", color: "#1BB599", background: "#1BB599" };
      return obj;
    case 2:
      obj = { name: "已失效", color: "#818999", background: "#818999" };
      return obj;
    case 3:
      obj = { name: "已删除", color: "#818999", background: "#818999" };
      return obj;
  }
  return obj;
});

//商品来源名称
const prodSourceName = computed(() => (prodSource: number) => {
  let name = undefined;
  sourceList.value.forEach((item) => {
    if (String(item.prodSourceCode) === String(prodSource)) {
      name = item.prodSourceDesc;
    }
  });
  return name;
});

//校验规则
const rules = ref({
  //活动名称
  promotionName: [
    { required: true, message: "请输入活动内容", trigger: "blur" },
  ],
  //业务类型
  bizType: [{ required: true, message: "请选择业务类型", trigger: "change" }],
  //活动时间
  promotionTime: [
    { required: true, message: "请选择活动时间", trigger: "change" },
  ],
  //是否展示倒计时
  showCountDown: [{ required: true, message: "请选择", trigger: "change" }],
});

//业务类型数据
const businessTypes = ref<Array<any>>([]);
//获取业务类型
const getBusinessTypes = () => {
  let info = {
    dictCode: "PROMOTION_BIZ_TYPE",
  };
  getDictionaries(info).then((res) => {
    businessTypes.value = res.data;
  });
};

//活动规则类型
interface businessRulesType {
  //达到限购后
  noBuy: string;
}

//活动规则
const businessRules = ref<businessRulesType>({
  //达到限购后
  noBuy: "1",
});

//列表查询数据
const searchTableData = ref<searchCommodityType>({
  //商品名称
  prodName: undefined,
  //商品id
  prodId: undefined,
  //商品状态
  prodStatus: undefined,
  //活动内状态
  promotionSkuStatus: undefined,
  //skuID
  skuId: undefined,
  //sku状态
  skuStatus: undefined,
  //商品来源
  prodSource: undefined,
  //所属店铺
  shopId: undefined,
  //活动id
  promotionId: undefined,
  //页码
  page: 1,
  //每页条数
  size: 10,
});

//列表配置
const columns = COMMODITY_ADD_EDIT_COLUMNS;

//列表选中
const selectList = ref<Array<any>>([]);

const rowSelection: TableProps["rowSelection"] = {
  onChange: (selectedRowKeys, selectedRows) => {
    selectList.value = selectedRows.map((item) => {
      return item.id;
    });
  },
  getCheckboxProps: (record) => ({
    name: record.name,
  }),
};

//列表数据
const tableList = ref<Array<any>>([]);
//列表总数
const total = ref<number>(1);

//定时器
const timer = ref();

//获取列表数据
const getTableList = async () => {
  tableLoading.value = true;
  if (timer.value) {
    clearTimeout(timer.value);
    timer.value = undefined;
  }
  timer.value = setTimeout(async () => {
    searchTableData.value.promotionId = promotionId.value;
    await getSkuPage(searchTableData.value)
      .then((res) => {
        tableList.value = res.data.records;
        total.value = res.data.total;
      })
      .catch((err) => {
        tableList.value = [];
        total.value = 1;
        message.error(err.message);
      })
      .finally(() => {
        tableLoading.value = false;
      });
  }, 1000);
};

//table内容ref
const tableContentRef = ref();
//table内容宽高
const tableWidth = ref<number>(0);

//商品状态
const commodityStates = ref<Array<any>>([
  { label: "上架", value: 1 },
  { label: "下架", value: 0 },
  { label: "违规下架", value: 2 },
]);
//sku状态
const skuStates = ref<Array<any>>([
  { label: "上架", value: 1 },
  { label: "下架", value: 0 },
]);

//活动内状态
const activateStatusList = ref<Array<any>>([]);
//获取活动内状态
const getActivateStatusList = () => {
  let info = {
    dictCode: "PROMOTION_SKU_STATUS",
  };
  getDictionaries(info).then((res) => {
    activateStatusList.value = res.data;
  });
};

//商品来源
const sourceList = ref<Array<any>>([]);
//获取商品来源
const getSourceList = () => {
  getSourceConfigList().then((res) => {
    sourceList.value = res.data.prodSourceConfigList;
  });
};

//所属店铺
const shopList = ref<Array<any>>([]);
//查询所属店铺
const handleShopSearch = (val: any) => {
  if (val) {
    getShopList(val);
  } else {
    shopList.value = [];
  }
};
//定时器实例
const timeout = ref();
//获取所属店铺
const getShopList = (val: any) => {
  if (timeout.value) {
    clearTimeout(timeout.value);
    timeout.value = null;
  }
  function fake() {
    let info = {
      page: 1,
      size: 10000,
      shopName: val,
    };
    getShopPage(info).then((res) => {
      shopList.value = [];
      if (res.data.records && res.data.records.length > 0) {
        res.data.records.forEach((item: any) => {
          shopList.value.push({ value: item.shopId, label: item.shopName });
        });
      }
    });
  }
  timeout.value = setTimeout(fake, 300);
};

//列表loading
const tableLoading = ref<boolean>(false);

//查询
const handleSearch = () => {
  searchTableData.value.page = 1;
  getTableList();
};

//重置
const handleReset = () => {
  searchTableData.value.page = 1;
  searchTableData.value.prodName = undefined;
  searchTableData.value.prodId = undefined;
  searchTableData.value.prodStatus = undefined;
  searchTableData.value.promotionSkuStatus = undefined;
  searchTableData.value.skuId = undefined;
  searchTableData.value.skuStatus = undefined;
  searchTableData.value.prodSource = undefined;
  searchTableData.value.shopId = undefined;
  getTableList();
};

//切换分页
const changePagination = (page: number, pageSize: number) => {
  searchTableData.value.page = page;
  searchTableData.value.size = pageSize;
  getTableList();
};

//编辑商品ref
const addOrEditShopRef = ref();
//编辑商品
const handleEdit = (item: any) => {
  addOrEditShopRef.value.openDrawer(
    item.prodId,
    promotionId.value,
    item.promotionSkuStatus
  );
};
//关闭弹窗
const onClose = () => {
  handleSearch();
};

//二次确认弹窗
const openModal = ref<boolean>(false);
//弹窗类型
const modalType = ref<string>(undefined);

//批量激活
const batchActivation = () => {
  modalType.value = ACTIVATE;
  openModal.value = true;
};

//商品id
const shopSkuId = ref<string>();
//激活
const handleActivate = (item: any) => {
  modalType.value = ACTIVATE;
  shopSkuId.value = item.id;
  openModal.value = true;
};

//失效
const handleFailure = (item: any) => {
  modalType.value = FAILURE;
  shopSkuId.value = item.id;
  openModal.value = true;
};

//弹窗确认
const handleAgree = () => {
  if (modalType.value === ACTIVATE) {
    let info = {
      flowStatus: 1,
      ids:
        selectList.value && selectList.value.length > 0
          ? selectList.value
          : [shopSkuId.value],
    };
    updateSkuStatus(info)
      .then(() => {
        message.success("激活成功");
        openModal.value = false;
        handleSearch();
      })
      .catch((err) => {
        message.error(err.message);
      });
  } else if (modalType.value === FAILURE) {
    let info = {
      flowStatus: 2,
      ids:
        selectList.value && selectList.value.length > 0
          ? selectList.value
          : [shopSkuId.value],
    };
    updateSkuStatus(info)
      .then(() => {
        message.success("失效成功");
        openModal.value = false;
        handleSearch();
      })
      .catch((err) => {
        message.error(err.message);
      });
  } else {
    updateStatus({ id: promotionId.value })
      .then(() => {
        message.success("保存并发布成功");
        goBack();
      })
      .catch((err) => {
        message.error(err.message);
      });
  }
};

//新增商品
const handleAddCommodity = () => {
  localStorage.setItem("activityForm", JSON.stringify(activityForm.value));
  localStorage.setItem("promotionId", promotionId.value);
  router.push({
    path: "/activity/activityOnlineShop/addCommodity",
    query: {
      id: promotionId.value,
    },
  });
};

//取消
const goBack = () => {
  router.go(-1);
};

//表单ref
const formRef = ref();

//按钮loading
const btnLoading = ref(false);

//保存
const handleAdd = () => {
  formRef.value.validate().then(() => {
    btnLoading.value = true;
    if (activityForm.value.promotionTime) {
      activityForm.value.promotionStartTime =
        activityForm.value.promotionTime[0];
      activityForm.value.promotionEndTime = activityForm.value.promotionTime[1];
    } else {
      activityForm.value.promotionStartTime = undefined;
      activityForm.value.promotionEndTime = undefined;
    }
    let info = {
      id: promotionId.value,
      promotionName: activityForm.value.promotionName,
      promotionDescription: activityForm.value.promotionDescription,
      bizType: activityForm.value.bizType,
      promotionType: activityForm.value.promotionType,
      showCountDown: activityForm.value.showCountDown,
      promotionStartTime: activityForm.value.promotionStartTime,
      promotionEndTime: activityForm.value.promotionEndTime,
      excludeFullReduction: activityForm.value.excludeFullReduction[0],
    };
    let mode = localStorage.getItem("mode");
    if (mode === "add") {
      createPromotion(info)
        .then((res) => {
          message.success("保存成功");
          goBack();
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => {
          btnLoading.value = false;
        });
    } else {
      editPromotion(info)
        .then((res) => {
          message.success("编辑成功");
          goBack();
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
//保存并发布
const handleAddAndIssue = () => {
  formRef.value.validate().then(() => {
    btnLoading.value = true;
    if (activityForm.value.promotionTime) {
      activityForm.value.promotionStartTime =
        activityForm.value.promotionTime[0];
      activityForm.value.promotionEndTime = activityForm.value.promotionTime[1];
    } else {
      activityForm.value.promotionStartTime = undefined;
      activityForm.value.promotionEndTime = undefined;
    }
    let info = {
      id: promotionId.value,
      promotionName: activityForm.value.promotionName,
      promotionDescription: activityForm.value.promotionDescription,
      bizType: activityForm.value.bizType,
      promotionType: activityForm.value.promotionType,
      showCountDown: activityForm.value.showCountDown,
      promotionStartTime: activityForm.value.promotionStartTime,
      promotionEndTime: activityForm.value.promotionEndTime,
      excludeFullReduction: activityForm.value.excludeFullReduction[0],
    };
    let mode = localStorage.getItem("mode");
    if (mode === "add") {
      createPromotion(info)
        .then((res) => {
          modalType.value = PUBLISH;
          openModal.value = true;
        })
        .catch((err) => {
          message.error(err.message);
          btnLoading.value = false;
        })
        .finally(() => {
          btnLoading.value = false;
        });
    } else {
      editPromotion(info)
        .then((res) => {
          modalType.value = PUBLISH;
          openModal.value = true;
        })
        .catch((err) => {
          message.error(err.message);
          btnLoading.value = false;
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

//活动id
const promotionId = ref<any>(undefined);
//获取活动id
const getActivityId = async () => {
  let id = localStorage.getItem("promotionId") || route.query.id;
  if (id) {
    promotionId.value = id;
  } else {
    await getPromotionId({}).then((res) => {
      promotionId.value = res.data;
    });
  }
};

//获取活动详情
const getDetailsData = async () => {
  let info = {
    id: promotionId.value,
  };
  await getPromotionDetails(info).then((res) => {
    activityForm.value.promotionName = res.data.promotionName;
    activityForm.value.bizType = String(res.data.bizType);
    activityForm.value.promotionDescription = res.data.promotionDescription;
    activityForm.value.promotionTime = [
      res.data.promotionStartTime,
      res.data.promotionEndTime,
    ];
    activityForm.value.promotionStartTime = res.data.promotionStartTime;
    activityForm.value.promotionEndTime = res.data.promotionEndTime;
    activityForm.value.showCountDown = res.data.showCountDown;
    activityForm.value.promotionStatus = res.data.promotionStatus;
    activityForm.value.promotionType = res.data.promotionType;
    activityForm.value.excludeFullReduction = [res.data.excludeFullReduction];
  });
};

//内容宽度
const mainWidth = ref<number>(0);
//内容ref
const mainRef = ref();

onMounted(async () => {
  await nextTick(() => {
    tableWidth.value = tableContentRef.value.offsetWidth;
    mainWidth.value = mainRef.value.offsetWidth;
  });
  await getActivityId();
  getBusinessTypes();
  getActivateStatusList();
  getSourceList();
  if (localStorage.getItem("mode") && localStorage.getItem("mode") === "edit") {
    await getDetailsData();
  }
  if (localStorage.getItem("activityForm")) {
    activityForm.value = JSON.parse(localStorage.getItem("activityForm"));
  }
  await getTableList();
});

onUnmounted(() => {
  if (timer.value) {
    clearTimeout(timer.value);
    timer.value = undefined;
  }
});
</script>

<style scoped lang="less">
.basic-information {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  .title-main {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 32px;
    height: 28px;
    .title-icon {
      width: 4px;
      height: 16px;
      border-radius: 4px;
      background: #1a7af8;
    }
    .title-name {
      font-weight: 600;
      font-size: 20px;
      color: #05082c;
    }
    .title-synopsis {
      font-weight: 400;
      font-size: 14px;
      color: #636d7e;
    }
  }
  .rule-description {
    width: 700px;
    padding: 16px;
    box-sizing: border-box;
    background: #f1f6f8;
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #000000;
    margin-left: 12px;
    margin-bottom: 24px;
  }
  .table-details-search {
    .ant-form-item {
      margin-bottom: 24px;
    }
  }
  .table-box {
    display: flex;
    flex-direction: column;
    .table-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 12px;
      padding: 12px 0;
      margin-top: 12px;
    }
    .table-content {
      margin-left: 12px;
      :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper
        .ant-table-thead
        > tr
        > th,
      :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper
        .ant-table-tbody
        > tr
        > td,
      :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper
        tfoot
        > tr
        > th,
      :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper
        tfoot
        > tr
        > td {
        padding: 12px 16px;
      }
    }
    .table-pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 12px 0px 16px;
    }
  }
  .annotation {
    font-weight: 400;
    font-size: 12px;
    color: #636d7e;
    margin-top: 5px;
  }
  .descriptions-css {
    :deep(.ant-descriptions-item) {
      padding-bottom: 8px;
    }
    :deep(.ant-descriptions-item-label)::after {
      content: "";
    }
  }
}
.basic-information + .basic-information {
  margin-top: 16px;
}
.form-main {
  width: 370px;
  margin-left: 12px;
}
.bottom-main {
  margin-bottom: 40px;
}
.bottom-button {
  height: 56px;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  left: 272px;
  z-index: 2;
  border: 1px solid #f2f5f9;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px 16px 0 0;
}
.tag-main {
  font-weight: 400;
  font-size: 12px;
  width: 50px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
.putaway-icon {
  width: 40px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #94cdff;
  background: #e6f4ff;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #1a7af8;
}
.sold-out-icon {
  width: 40px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e8ed;
  background: #f0f4f9;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #495366;
}
.error-icon {
  width: 60px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ffbdc3;
  background: #fff0f0;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #ff436a;
}
.status-main {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  gap: 4px;
  .status-icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.prod-main {
  display: flex;
  align-items: center;
  width: 100%;
  .prod-info {
    flex: 1;
    width: 1px;
    display: flex;
    flex-direction: column;
    margin-left: 8px;
    .prod-info-name {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }
    .prod-info-id {
      font-weight: 400;
      font-size: 14px;
      color: #818999;
    }
  }
}
</style>
