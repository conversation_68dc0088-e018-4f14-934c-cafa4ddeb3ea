<template>
  <a-drawer
    v-model:open="showDrawer"
    width="1000"
    :closable="false"
    :footer-style="{ textAlign: 'right' }"
    class="drawer-class"
  >
    <template #title>
      <div class="drawer-title">
        <div class="title-name">
          {{ type === "add" ? "添加商品" : "编辑商品" }}
        </div>
        <CloseOutlined @click="handleCancel" />
      </div>
    </template>
    <div class="drawer-main">
      <a-descriptions
        layout="vertical"
        :column="2"
        :labelStyle="{
          color: '#636D7E',
          fontSize: '14px',
          fontWeight: 400,
          paddingBottom: '8px',
        }"
        :contentStyle="{
          color: '#05082C',
          fontSize: '14px',
          fontWeight: 400,
          paddingBottom: '24px',
        }"
      >
        <a-descriptions-item label="商品名称">{{
          prodInfo.prodName
        }}</a-descriptions-item>
        <a-descriptions-item label="商品ID">{{
          prodInfo.prodId
        }}</a-descriptions-item>
        <a-descriptions-item label="购买限制">
          <div class="restrict-main">
            <a-radio-group
              v-model:value="restrict.value"
              :disabled="
                status !== 0 && type === 'edit' && promotionStatus !== 0
              "
              @change="changeRadio"
            >
              <a-radio value="0">不限购</a-radio>
              <a-radio value="1">每人每种商品限购</a-radio>
            </a-radio-group>
            <a-input-number
              v-model:value="restrict.number"
              placeholder="请输入"
              :min="1"
              :max="99"
              :disabled="
                restrict.value !== '1' ||
                (status !== 0 && type === 'edit' && promotionStatus !== 0)
              "
            >
              <template #addonAfter>件</template>
            </a-input-number>
          </div>
        </a-descriptions-item>
      </a-descriptions>
      <div class="transfer-main">
        <div class="transfer-main-left">
          <div class="transfer-main-left-top">
            <div class="top-name">商品规格</div>
            <div class="top-number">
              <div class="top-all-number">{{ leftTableList.length }}</div>
            </div>
          </div>
          <div class="transfer-main-left-content" ref="leftRef">
            <a-table
              :columns="leftColumns"
              :scroll="{ y: offsetHeight }"
              :data-source="leftTableList"
              :pagination="false"
              :row-selection="rowSelection"
              row-key="skuId"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'sku'">
                  <div class="sku-main">
                    <div class="sku-id">{{ record.skuId }}</div>
                    <div class="sku-name">{{ record.skuName }}</div>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
        <div class="transfer-main-right">
          <div class="transfer-main-right-top">
            <div class="top-name">已选规格 {{ rightTableList.length }}项</div>
          </div>
          <div class="transfer-main-right-content">
            <a-table
              :columns="rightColumns"
              :scroll="{ y: offsetHeight }"
              :data-source="rightTableList"
              :pagination="false"
              row-key="skuId"
            >
              <template #headerCell="{ column }">
                <template v-if="column.dataIndex === 'delete'">
                  <img
                    src="https://file.shwoody.com/prod/image/in_coming/life_plat/2025/01/02/1735784604476/minus-circle-filled@2x%20(1).png"
                    alt=""
                    width="16"
                    height="16"
                    style="cursor: pointer"
                    @click="handleDelete('all')"
                  />
                </template>
              </template>
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'sku'">
                  <div class="sku-main">
                    <div class="sku-id">{{ record.skuId }}</div>
                    <div class="sku-name">{{ record.skuName }}</div>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'delete'">
                  <img
                    src="https://file.shwoody.com/prod/image/in_coming/life_plat/2025/01/02/1735784604476/minus-circle-filled@2x%20(1).png"
                    alt=""
                    width="16"
                    height="16"
                    style="cursor: pointer"
                    v-if="record?.delete === 1"
                    @click="handleDelete(record.skuId)"
                  />
                  <view v-else></view>
                </template>
                <template v-if="column.dataIndex === 'skuPrice'">
                  <a-input-number
                    v-model:value="record.skuPrice"
                    placeholder="请输入"
                    :min="0.01"
                    :max="(record.oldrice - 0.01).toFixed(2)"
                  />
                </template>
                <template v-if="column.dataIndex === 'qualification'">
                  <a-input-number
                    v-model:value="record.qualification"
                    placeholder="请输入"
                    :min="1"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button style="margin-right: 16px" @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="btnLoading" @click="handleAgree"
        >确定</a-button
      >
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, unref } from "vue";
import { CloseOutlined } from "@ant-design/icons-vue";
import type { TableProps } from "woody-ui";
import { message } from "woody-ui";
import {
  addProductData,
  editProductData,
  getProductPage,
} from "@/api/flashSales";

const props = defineProps({
  //弹窗类型
  type: {
    type: String,
    default: "add",
  },
  //活动类型
  status: {
    type: Number,
    default: undefined,
  },
});

const emits = defineEmits(["onClose"]);

//购买限制
const restrict = ref({
  value: "0",
  number: undefined,
});

//弹窗
const showDrawer = ref<boolean>(false);

//商品id
const prodId = ref();
//活动id
const promotionId = ref();
//活动内状态
const promotionStatus = ref();

const offsetHeight = ref();

//打开弹窗
const openDrawer = (
  productId: string,
  id: any,
  promotionSkuStatus: number = 0
) => {
  leftTableList.value = [];
  rightTableList.value = [];
  prodId.value = productId;
  promotionId.value = id;
  promotionStatus.value = promotionSkuStatus;
  setTimeout(() => {
    offsetHeight.value = leftRef.value.offsetHeight - 47;
  }, 1000);
  getProductData();
  showDrawer.value = true;
};

interface prodInfoType {
  //商品名称
  prodName: string;
  //商品id
  prodId: string;
}

//商品数据
const prodInfo = ref<prodInfoType>({
  //商品名称
  prodName: undefined,
  //商品id
  prodId: undefined,
});
//获取活动商品
const getProductData = () => {
  let info = {
    promotionId: promotionId.value,
    prodId: prodId.value,
  };
  getProductPage(info)
    .then((res) => {
      prodInfo.value.prodName = res.data.prodName;
      prodInfo.value.prodId = res.data.prodId;
      restrict.value.value = res.data.prodLimit === -1 ? "0" : "1";
      if (restrict.value.value === "1") {
        restrict.value.number = res.data.prodLimit;
      }
      if (res.data.skus && res.data.skus.length > 0) {
        res.data.skus.forEach((item: any) => {
          if (item.select === 0) {
            leftTableList.value.push(item);
          } else {
            rightTableList.value.push(item);
          }
        });
      }
    })
    .catch((err) => {
      leftTableList.value = [];
      rightTableList.value = [];
      message.error(err.message);
    });
};

//按钮loading
const btnLoading = ref(false);

//确定
const handleAgree = () => {
  if (props.type === "add" && rightTableList.value.length === 0) {
    return message.error("请选择sku");
  }
  if (restrict.value.value === "1" && !restrict.value.number) {
    return message.error("每人限购不能为空");
  }
  let flag = false;
  //判断特惠价是否小于原件
  let isLess = false;
  rightTableList.value.forEach((item) => {
    if (!item.skuPrice || !item.qualification) {
      flag = true;
    }
    if (item.skuPrice >= item.oldrice) {
      isLess = true;
    }
  });
  if (flag) {
    return message.error("已添加商品的特惠价和活动库存不能为空");
  }
  if (isLess) {
    return message.error("特惠价必须小于原价");
  }
  btnLoading.value = true;
  let info = {
    promotionId: promotionId.value,
    prodId: prodId.value,
    prodLimit: restrict.value.value === "0" ? -1 : restrict.value.number,
    skus: rightTableList.value,
  };
  if (props.type === "add") {
    addProductData(info)
      .then(() => {
        message.success("添加成功");
        handleCancel();
        emits("onClose");
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => {
        btnLoading.value = false;
      });
  } else {
    editProductData(info)
      .then(() => {
        message.success("编辑成功");
        handleCancel();
        emits("onClose");
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => {
        btnLoading.value = false;
      });
  }
};

//取消
const handleCancel = () => {
  restrict.value.value = "0";
  restrict.value.number = undefined;
  showDrawer.value = false;
};

//左侧列表配置
const leftColumns = [
  {
    title: "skuID&规格值",
    dataIndex: "sku",
    key: "sku",
    ellipsis: true,
    width: 240,
  },
  {
    title: "原价",
    dataIndex: "oldrice",
    key: "oldrice",
    ellipsis: true,
    width: 80,
  },
];

//左侧列表
const leftTableList = ref<Array<any>>([]);

//选中值
const selectedRowKeys = ref([]);

const rowSelection: TableProps["rowSelection"] = {
  preserveSelectedRowKeys: false,
  selectedRowKeys: unref(selectedRowKeys),
  onChange: (selectedRowKeys, selectedRows) => {
    if (selectedRows.length === leftTableList.value.length) {
      leftTableList.value = [];
    } else {
      leftTableList.value.forEach((item, index) => {
        if (item.skuId === selectedRows[0].skuId) {
          leftTableList.value.splice(index, 1);
        }
      });
    }
    selectedRows.forEach((item) => {
      item.select = 1;
    });
    rightTableList.value.push(...selectedRows);
  },
};

//右侧列表配置
const rightColumns = [
  {
    dataIndex: "delete",
    key: "delete",
    width: 40,
  },
  {
    title: "skuID&规格值",
    dataIndex: "sku",
    key: "sku",
    ellipsis: true,
    width: 195,
  },
  {
    title: "原价",
    dataIndex: "oldrice",
    key: "oldrice",
    ellipsis: true,
    width: 80,
  },
  {
    title: "特惠价",
    dataIndex: "skuPrice",
    key: "skuPrice",
    width: 120,
  },
  {
    title: "活动库存",
    dataIndex: "qualification",
    key: "qualification",
    width: 120,
  },
];

//右侧列表
const rightTableList = ref<Array<any>>([]);

//右侧删除
const handleDelete = (id: string) => {
  if (id === "all") {
    let list = JSON.parse(JSON.stringify(rightTableList.value));
    let indexList = [];
    list.forEach((item, index) => {
      if (item.delete === 1) {
        indexList.push(index);
        leftTableList.value.push(
          JSON.parse(
            JSON.stringify({
              skuId: item.skuId,
              oldrice: item.oldrice,
              skuName: item.skuName,
              select: 0,
              delete: 1,
            })
          )
        );
        selectedRowKeys.value = [];
      }
    });
    rightTableList.value = list.filter(
      (item, index) => !indexList.includes(index)
    );
  } else {
    rightTableList.value.forEach((item, index) => {
      if (item.skuId === id) {
        rightTableList.value.splice(index, 1);
        leftTableList.value.push(
          JSON.parse(
            JSON.stringify({
              skuId: item.skuId,
              oldrice: item.oldrice,
              skuName: item.skuName,
              select: 0,
              delete: 1,
            })
          )
        );
        selectedRowKeys.value = [];
      }
    });
  }
};

//改变选择
const changeRadio = (e: any) => {
  restrict.value.value = e.target.value;
  if (e.target.value === "0") {
    restrict.value.number = undefined;
  }
};

const leftRef = ref();

onMounted(() => {});

defineExpose({
  openDrawer,
});
</script>

<style scoped lang="less">
.restrict-main {
  display: flex;
  align-items: center;
}
.drawer-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  .transfer-main {
    flex: 1;
    height: 1px;
    display: flex;
    gap: 7px;
    .transfer-main-left {
      height: 100%;
      width: 377px;
      border: 1px solid #f4f7fa;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      .transfer-main-left-top {
        padding: 12px 16px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        .top-name {
          font-weight: 400;
          font-size: 14px;
          color: #05082c;
        }
        .top-number {
          display: flex;
          align-items: center;
          .top-select-number {
            font-weight: 400;
            font-size: 14px;
            color: #05082c;
          }
          .top-all-number {
            font-weight: 400;
            font-size: 14px;
            color: #a2abbd;
          }
        }
      }
      .transfer-main-left-content {
        flex: 1;
        height: 1px;
      }
    }
    .transfer-main-right {
      height: 100%;
      flex: 1;
      width: 1px;
      border: 1px solid #f4f7fa;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      .transfer-main-right-top {
        padding: 12px 16px;
        box-sizing: border-box;
        .top-name {
          font-weight: 400;
          font-size: 14px;
          color: #05082c;
        }
      }
      .transfer-main-right-content {
        flex: 1;
        height: 1px;
      }
    }
  }
  :deep(.ant-descriptions-item) {
    padding-bottom: 8px;
    padding-right: 24px;
  }
  :deep(.ant-descriptions-item-label)::after {
    content: "";
  }
}
.sku-main {
  display: flex;
  flex-direction: column;
  .sku-id {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
    margin-bottom: 8px;
  }
  .sku-name {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }
}
</style>
