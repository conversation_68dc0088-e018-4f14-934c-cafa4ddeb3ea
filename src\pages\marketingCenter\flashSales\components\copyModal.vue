<template>
  <a-modal
    v-model:open="copyModal"
    title="复制"
    wrapClassName="modal-class"
    @cancel="handleCopyCancel"
  >
    <a-form
      layout="vertical"
      :model="copyForm"
      :rules="rules"
      ref="formRef"
      :labelCol="{ style: { width: '200px' } }"
    >
      <a-form-item label="活动名称" name="promotionName">
        <a-input
          v-model:value="copyForm.promotionName"
          placeholder="请输入活动名称"
          :maxlength="5"
          allowClear
          showCount
        />
      </a-form-item>
      <a-form-item label="活动时间" name="promotionTime">
        <a-range-picker
          v-model:value="copyForm.promotionTime"
          :locale="locale"
          show-time
          allow-clear
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item label="是否同时复制商品数据" name="copyProd">
        <a-radio-group v-model:value="copyForm.copyProd">
          <a-radio value="0">仅复制活动</a-radio>
          <a-radio value="1">同时复制商品</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCopyCancel">取消</a-button>
      <a-button type="primary" :loading="btnLoading" @click="handleCopyAgree"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { copyPromotion } from "@/api/flashSales";
import { message } from "woody-ui";
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";

//子传父
const emits = defineEmits(["onClose"]);

//复制弹窗
const copyModal = ref<boolean>(false);

//复制数据
const copyForm = ref({
  //活动名称
  promotionName: undefined,
  //活动时间
  promotionTime: [],
  //开始时间
  promotionStartTime: undefined,
  //结束时间
  promotionEndTime: undefined,
  //是否同时复制商品数据
  copyProd: undefined,
  //活动id
  copyId: undefined,
});

//校验规则
const rules = ref({
  //活动名称
  promotionName: [
    { required: true, message: "请输入活动内容", trigger: "blur" },
  ],
  //活动时间
  promotionTime: [
    { required: true, message: "请选择活动时间", trigger: "change" },
  ],
  //是否同时复制商品数据
  copyProd: [{ required: true, message: "请选择", trigger: "change" }],
});

//表单ref
const formRef = ref();

//打开弹窗
const openCopyModal = (item: any) => {
  copyForm.value.copyId = item.id;
  copyForm.value.promotionName = item.promotionName;
  copyForm.value.promotionTime = [
    item.promotionStartTime,
    item.promotionEndTime,
  ];
  copyForm.value.promotionStartTime = item.promotionStartTime;
  copyForm.value.promotionEndTime = item.promotionEndTime;
  copyModal.value = true;
};

//按钮loading
const btnLoading = ref(false);

//复制保存
const handleCopyAgree = () => {
  formRef.value.validate().then(() => {
    btnLoading.value = true;
    if (copyForm.value.promotionTime) {
      copyForm.value.promotionStartTime = copyForm.value.promotionTime[0];
      copyForm.value.promotionEndTime = copyForm.value.promotionTime[1];
    } else {
      copyForm.value.promotionStartTime = undefined;
      copyForm.value.promotionEndTime = undefined;
    }
    let info = {
      copyId: copyForm.value.copyId,
      promotionName: copyForm.value.promotionName,
      promotionStartTime: copyForm.value.promotionStartTime,
      promotionEndTime: copyForm.value.promotionEndTime,
      copyProd: copyForm.value.copyProd,
    };
    copyPromotion(info)
      .then(() => {
        message.success("复制成功");
        copyModal.value = false;
        emits("onClose");
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
//复制取消
const handleCopyCancel = () => {
  formRef.value.resetFields();
  copyModal.value = false;
};

defineExpose({
  openCopyModal,
});
</script>

<style scoped lang="less"></style>
