//限时特惠搜索类型
export interface searchDataType {
    //活动id
    promotionCode: String,
    //活动名称
    promotionName: String,
    //活动状态
    promotionStatus: String,
    //页码
    page: Number,
    //每页条数
    size: Number,
}

//限时特惠列表配置
export const FLASH_SALES_COLUMNS = [
    {
        title: '活动ID',
        dataIndex: 'promotionCode',
        key: 'promotionCode',
        ellipsis: true,
        width: 150,
        fixed: 'left'
    },
    {
        title: '活动名称',
        dataIndex: 'promotionName',
        key: 'promotionName',
        ellipsis: true,
        width: 200,
    },
    {
        title: '业务类型',
        dataIndex: 'bizType',
        key: 'bizType',
        ellipsis: true,
        width: 100,
    },
    {
        title: '活动时间',
        dataIndex: 'activityTime',
        key: 'activityTime',
        ellipsis: true,
        width: 350,
    },
    {
        title: '活动状态',
        dataIndex: 'promotionStatus',
        key: 'promotionStatus',
        ellipsis: true,
        width: 100,
    },
    {
        title: '商品数量',
        dataIndex: 'promotionProdNumber',
        key: 'promotionProdNumber',
        ellipsis: true,
        width: 100,
    },
    {
        title: '描述',
        dataIndex: 'promotionDescription',
        key: 'promotionDescription',
        ellipsis: true,
        width: 200,
    },
    {
        title: '创建人',
        dataIndex: 'createUserName',
        key: 'createUserName',
        ellipsis: true,
        width: 100,
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        ellipsis: true,
        width: 200,
    },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        ellipsis: true,
        width: 200,
    },
    {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 220,
        fixed: 'right'
    },
]

//弹窗类型-发布
export const PUBLISH:string = "1"
//弹窗类型-下线
export const DOWN_LINE:string = "2"
//弹窗类型-激活
export const ACTIVATE:string = "3"
//弹窗类型-失效
export const FAILURE:string = "4"

//商品搜索类型
export interface searchCommodityType {
    //商品名称
    prodName: string,
    //商品id
    prodId: string,
    //商品状态
    prodStatus: string,
    //活动内状态
    promotionSkuStatus: string,
    //skuID
    skuId: string,
    //sku状态
    skuStatus: string,
    //商品来源
    prodSource: string,
    //所属店铺
    shopId: string,
    //活动id
    promotionId: string,
    //页码
    page: Number,
    //每页条数
    size: Number,
}

//列表宽高类型
export interface tableContentType {
    x:number
}

//活动商品列表配置
export const COMMODITY_COLUMNS = [
    {
        title: '商品信息',
        dataIndex: 'prodName',
        key: 'prodName',
        ellipsis: true,
        width: 400,
        fixed: 'left'
    },
    {
        title: '商品状态',
        dataIndex: 'prodStatus',
        key: 'prodStatus',
        ellipsis: true,
        width: 100,
    },
    {
        title: '规格信息',
        dataIndex: 'skuName',
        key: 'skuName',
        ellipsis: true,
        width: 240,
    },
    {
        title: 'sku状态',
        dataIndex: 'skuStatus',
        key: 'skuStatus',
        ellipsis: true,
        width: 100,
    },
    {
        title: '活动内状态',
        dataIndex: 'promotionSkuStatus',
        key: 'promotionSkuStatus',
        ellipsis: true,
        width: 120,
    },
    {
        title: '原价',
        dataIndex: 'oldPrice',
        key: 'oldPrice',
        ellipsis: true,
        width: 100,
    },
    {
        title: '特惠价',
        dataIndex: 'skuPrice',
        key: 'skuPrice',
        ellipsis: true,
        width: 100,
    },
    {
        title: '每人限购',
        dataIndex: 'prodLimit',
        key: 'prodLimit',
        ellipsis: true,
        width: 100,
    },
    {
        title: '活动库存',
        dataIndex: 'qualification',
        key: 'qualification',
        ellipsis: true,
        width: 100,
    },
    {
        title: '已售',
        dataIndex: 'usedQualification',
        key: 'usedQualification',
        ellipsis: true,
        width: 100,
    },
    {
        title: '商品来源',
        dataIndex: 'prodSource',
        key: 'prodSource',
        ellipsis: true,
        width: 150,
    },
    {
        title: '所属店铺',
        dataIndex: 'shopName',
        key: 'shopName',
        ellipsis: true,
        width: 150,
    },
]

//活动商品列表配置
export const COMMODITY_ADD_EDIT_COLUMNS = [
    {
        title: '商品信息',
        dataIndex: 'prodName',
        key: 'prodName',
        ellipsis: true,
        width: 400,
        fixed: 'left'
    },
    {
        title: '商品状态',
        dataIndex: 'prodStatus',
        key: 'prodStatus',
        ellipsis: true,
        width: 100,
    },
    {
        title: '规格信息',
        dataIndex: 'skuName',
        key: 'skuName',
        ellipsis: true,
        width: 240,
    },
    {
        title: 'sku状态',
        dataIndex: 'skuStatus',
        key: 'skuStatus',
        ellipsis: true,
        width: 100,
    },
    {
        title: '活动内状态',
        dataIndex: 'promotionSkuStatus',
        key: 'promotionSkuStatus',
        ellipsis: true,
        width: 120,
    },
    {
        title: '原价',
        dataIndex: 'oldPrice',
        key: 'oldPrice',
        ellipsis: true,
        width: 100,
    },
    {
        title: '特惠价',
        dataIndex: 'skuPrice',
        key: 'skuPrice',
        ellipsis: true,
        width: 100,
    },
    {
        title: '每人限购',
        dataIndex: 'prodLimit',
        key: 'prodLimit',
        ellipsis: true,
        width: 100,
    },
    {
        title: '活动库存',
        dataIndex: 'qualification',
        key: 'qualification',
        ellipsis: true,
        width: 100,
    },
    {
        title: '已售',
        dataIndex: 'usedQualification',
        key: 'usedQualification',
        ellipsis: true,
        width: 100,
    },
    {
        title: '商品来源',
        dataIndex: 'prodSource',
        key: 'prodSource',
        ellipsis: true,
        width: 150,
    },
    {
        title: '所属店铺',
        dataIndex: 'shopName',
        key: 'shopName',
        ellipsis: true,
        width: 150,
    },
    {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 220,
        fixed: 'right'
    }
]

//新增商品搜索类型
export interface searchAddCommodityType {
    //商品名称
    prodName: String,
    //商品id
    prodId: String,
    //商品来源
    prodSource: String,
    //所属店铺
    shopId: String,
    //后台分类
    classify:Array<any>,
    //一级分类
    firstCategoryId: String,
    //二级分类
    secondCategoryId: String,
    //活动id
    promotionId:String,
    //是否互斥
    excludeFullReduction:Number,
    //页码
    page: Number,
    //每页条数
    size: Number,
}

//新增商品列表配置
export const ADD_COMMODITY_COLUMNS = [
    {
        title: '商品信息',
        dataIndex: 'productId',
        key: 'productId',
        ellipsis: true,
        width: 400,
        fixed: 'left'
    },
    {
        title: '商品状态',
        dataIndex: 'status',
        key: 'status',
        ellipsis: true,
        width: 100,
    },
    {
        title: '原价',
        dataIndex: 'minPrice',
        key: 'minPrice',
        ellipsis: true,
        width: 150,
    },
    {
        title: '后台分类',
        dataIndex: 'platParentCategoryName',
        key: 'platParentCategoryName',
        ellipsis: true,
        width: 250,
    },
    {
        title: '商品来源',
        dataIndex: 'prodSource',
        key: 'prodSource',
        ellipsis: true,
        width: 150,
    },
    {
        title: '所属店铺',
        dataIndex: 'shopName',
        key: 'shopName',
        ellipsis: true,
        width: 200,
    },
    {
        title: '已参与活动',
        dataIndex: 'simple',
        key: 'simple',
        ellipsis: true,
        width: 350,
    },
    {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 120,
        fixed: 'right'
    },
]

export const OPERATION_LOG_TYPE = [
    {
        title: '操作人ID',
        dataIndex: 'createUserId',
        key: 'createUserId',
        ellipsis: true,
        width: 180,
    },
    {
        title: '操作人',
        dataIndex: 'createUserName',
        key: 'createUserName',
        ellipsis: true,
        width: 150,
    },
    {
        title: '活动Id',
        dataIndex: 'promotionCode',
        key: 'promotionCode',
        ellipsis: true,
        width: 200,
    },
    {
        title: '活动名称',
        dataIndex: 'promotionName',
        key: 'promotionName',
        ellipsis: true,
        width: 150,
    },
    {
        title: '操作类型',
        dataIndex: 'type',
        key: 'type',
        ellipsis: true,
        width: 100,
    },
    {
        title: '操作内容',
        dataIndex: 'message',
        key: 'message',
        width: 600,
        ellipsis: true,
    },
    {
        title: '操作时间',
        dataIndex: 'createTime',
        key: 'createTime',
        ellipsis: true,
        width: 200,
    },
]