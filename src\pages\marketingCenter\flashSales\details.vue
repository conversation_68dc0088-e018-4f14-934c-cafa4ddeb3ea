<template>
  <div class="basic-information" :style="{ padding: '32px 32px 8px 32px' }">
    <div class="title-main">
      <div class="title-icon"></div>
      <div class="title-name">活动基本信息</div>
    </div>
    <div class="descriptions-css" style="margin-left: 12px">
      <a-descriptions
        layout="vertical"
        :column="4"
        :labelStyle="{
          color: '#636D7E',
          fontSize: '14px',
          fontWeight: 400,
          paddingBottom: '8px',
        }"
        :contentStyle="{
          color: '#05082C',
          fontSize: '14px',
          fontWeight: 400,
          paddingBottom: '24px',
        }"
      >
        <a-descriptions-item label="活动名称">{{
          detailsData?.promotionName
        }}</a-descriptions-item>
        <a-descriptions-item label="业务类型">{{
          detailsData?.bizTypeStr
        }}</a-descriptions-item>
        <a-descriptions-item label="活动描述">{{
          detailsData?.promotionDescription
        }}</a-descriptions-item>
        <a-descriptions-item label="活动时间"
          >{{ detailsData?.promotionStartTime }} 至
          {{ detailsData?.promotionEndTime }}</a-descriptions-item
        >
        <a-descriptions-item label="是否展示倒计时">{{
          detailsData?.showCountDown === 0 ? "不展示" : "展示"
        }}</a-descriptions-item>
        <a-descriptions-item label="当前活动状态">
          <div
            :style="{
              color: activeState.color,
              border: `1px solid ${activeState.borderColor}`,
              backgroundColor: activeState.background,
            }"
            class="tag-main"
          >
            {{ activeState.name }}
          </div>
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
  <div class="basic-information">
    <div class="title-main">
      <div class="title-icon"></div>
      <div class="title-name">活动规则</div>
      <div class="title-synopsis">
        特惠活动，采用一口价形式，在商品上配置优惠价格、限购数量。
      </div>
    </div>
    <div class="descriptions-css" style="margin-left: 12px">
      <a-descriptions
        layout="vertical"
        :column="4"
        :labelStyle="{ color: '#636D7E', fontSize: '14px', fontWeight: 400 }"
        :contentStyle="{ color: '#05082C', fontSize: '14px', fontWeight: 400 }"
      >
        <a-descriptions-item label="达到限购后">无法购买</a-descriptions-item>
        <a-descriptions-item label="互斥活动" :span="3"
          >供货仓 -- 满减</a-descriptions-item
        >
      </a-descriptions>
    </div>
  </div>
  <div class="basic-information bottom-main" ref="mainRef">
    <div class="title-main">
      <div class="title-icon"></div>
      <div class="title-name">活动商品</div>
    </div>
    <div class="table-details-search">
      <a-form
        layout="vertical"
        :model="searchTableData"
        :labelCol="{ style: { width: '150px' } }"
      >
        <a-row>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品名称">
              <a-input
                v-model:value="searchTableData.prodName"
                placeholder="请输入商品名称"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品ID">
              <a-input
                v-model:value="searchTableData.prodId"
                placeholder="请输入商品ID"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品状态">
              <a-select
                v-model:value="searchTableData.prodStatus"
                placeholder="请选择商品状态"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in commodityStates"
                  :key="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="活动内状态">
              <a-select
                v-model:value="searchTableData.promotionSkuStatus"
                placeholder="请选择活动内状态"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in activateStatusList"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="skuID">
              <a-input
                v-model:value="searchTableData.skuId"
                placeholder="请输入skuID"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="sku状态">
              <a-select
                v-model:value="searchTableData.skuStatus"
                placeholder="请选择sku状态"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in skuStates"
                  :key="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="商品来源">
              <a-select
                v-model:value="searchTableData.prodSource"
                placeholder="请选择商品来源"
                style="width: 100%"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in sourceList"
                  :key="item.prodSourceCode"
                  >{{ item.prodSourceDesc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
            <a-form-item label="所属店铺">
              <a-select
                v-model:value="searchTableData.shopId"
                placeholder="请选择所属店铺"
                style="width: 100%"
                :not-found-content="null"
                :default-active-first-option="false"
                :filter-option="false"
                :options="shopList"
                allowClear
                show-search
                @search="handleShopSearch"
              >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :span="6"
            class="a-col-center"
            style="padding-left: 12px; padding-right: 12px"
          >
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 16px"
              >重置</a-button
            >
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-details-main" style="padding-top: 24px">
      <div class="table-content" ref="tableContentRef">
        <a-table
          :columns="columns"
          :data-source="tableList"
          :scroll="{ x: tableWidth }"
          :pagination="false"
          :loading="tableLoading"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'prodName'">
              <div class="prod-main">
                <a-image
                  :src="record.prodPic"
                  alt=""
                  :width="60"
                  :height="60"
                />
                <div class="prod-info">
                  <a-tooltip placement="top" color="#ffffff">
                    <template #title>
                      <div style="color: #05082c">{{ record.prodName }}</div>
                      <div style="color: #05082c">ID:{{ record.prodId }}</div>
                    </template>
                    <div class="prod-info-name">{{ record.prodName }}</div>
                    <div class="prod-info-id">ID:{{ record.prodId }}</div>
                  </a-tooltip>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'prodStatus'">
              <div class="putaway-icon" v-if="record.prodStatus === 1">
                上架
              </div>
              <div class="sold-out-icon" v-if="record.prodStatus === 0">
                下架
              </div>
              <div class="error-icon" v-if="record.prodStatus === 2">
                违规下架
              </div>
            </template>
            <template v-if="column.dataIndex === 'skuName'">
              <div>规格名：{{ record.skuName }}</div>
              <div>规格ID：{{ record.skuId }}</div>
            </template>
            <template v-if="column.dataIndex === 'skuStatus'">
              <div class="putaway-icon" v-if="record.skuStatus === 1">上架</div>
              <div class="sold-out-icon" v-else>下架</div>
            </template>
            <template v-if="column.dataIndex === 'prodSource'">
              <div>{{ prodSourceName(record.prodSource) }}</div>
            </template>
            <template v-if="column.dataIndex === 'prodLimit'">
              <div>
                {{ record.prodLimit === -1 ? "不限购" : record.prodLimit }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'promotionSkuStatus'">
              <div
                class="status-main"
                :style="{
                  color: commodityStatus(record.promotionSkuStatus).color,
                }"
              >
                <div
                  class="status-icon"
                  :style="{
                    background: commodityStatus(record.promotionSkuStatus)
                      .background,
                  }"
                ></div>
                <div>{{ commodityStatus(record.promotionSkuStatus).name }}</div>
              </div>
            </template>
          </template>
        </a-table>
      </div>
      <div class="table-pagination">
        <div>共 {{ total }} 项数据</div>
        <a-pagination
          v-model:current="searchTableData.page"
          v-model:page-size="searchTableData.size"
          show-size-changer
          show-quick-jumper
          :total="total"
          @change="changePagination"
        />
      </div>
    </div>
  </div>
  <div class="bottom-button" :style="{ width: mainWidth + 'px' }">
    <a-button @click="goBack">返回</a-button>
    <a-button
      type="primary"
      style="margin-left: 16px"
      v-if="[0, 1, 2].includes(detailsData.promotionStatus)"
      @click="goEdit"
      >编辑</a-button
    >
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from "vue";
import {
  COMMODITY_COLUMNS,
  searchCommodityType,
} from "@/pages/marketingCenter/flashSales/constants/constants";
import router from "@/router";
import { getDictionaries, getSourceConfigList } from "@/api/common";
import {
  getShopPage,
  getPromotionId,
  getSkuPage,
  getPromotionDetails,
} from "@/api/flashSales";
import { useRoute } from "vue-router";
import { message } from "woody-ui";

//路由传参
const route = useRoute();

//活动详情
const detailsData = ref<any>({});

//当前活动状态
const activeState = computed(() => {
  let obj = {
    name: "未发布",
    color: "#FF436A",
    borderColor: "#FF436A",
    background: "#FFEDF0",
  };
  switch (detailsData.value.promotionStatus) {
    case 0:
      obj = {
        name: "未发布",
        color: "#FF436A",
        borderColor: "#FFBDC3",
        background: "#FFF0F0",
      };
      return obj;
    case 1:
      obj = {
        name: "未开始",
        color: "#FF9B26",
        borderColor: "#FFCF96",
        background: "#FFF9F3",
      };
      return obj;
    case 2:
      obj = {
        name: "进行中",
        color: "#1A7AF8",
        borderColor: "#94CDFF",
        background: "#E6F4FF",
      };
      return obj;
    case 9:
      obj = {
        name: "已结束",
        color: "#1BB599",
        borderColor: "#8ADBC4",
        background: "#E6F5F0",
      };
      return obj;
    case 8:
      obj = {
        name: "已下线",
        color: "#05082C",
        borderColor: "#E0E8ED",
        background: "#F0F4F9",
      };
      return obj;
  }
  return obj;
});

//商品状态
const commodityStatus = computed(() => (status: number) => {
  let obj = { name: "未激活", color: "#FF436A", background: "#FF436A" };
  switch (status) {
    case 0:
      obj = { name: "未激活", color: "#FF436A", background: "#FF436A" };
      return obj;
    case 1:
      obj = { name: "已激活", color: "#1BB599", background: "#1BB599" };
      return obj;
    case 2:
      obj = { name: "已失效", color: "#818999", background: "#818999" };
      return obj;
    case 3:
      obj = { name: "已删除", color: "#818999", background: "#818999" };
      return obj;
  }
  return obj;
});

//列表查询数据
const searchTableData = ref<searchCommodityType>({
  //商品名称
  prodName: undefined,
  //商品id
  prodId: undefined,
  //商品状态
  prodStatus: undefined,
  //活动内状态
  promotionSkuStatus: undefined,
  //skuID
  skuId: undefined,
  //sku状态
  skuStatus: undefined,
  //商品来源
  prodSource: undefined,
  //所属店铺
  shopId: undefined,
  //活动id
  promotionId: undefined,
  //页码
  page: 1,
  //每页条数
  size: 10,
});

//列表配置
const columns = COMMODITY_COLUMNS;

//列表数据
const tableList = ref<Array<any>>([]);
//列表总数
const total = ref<number>(1);
//获取列表数据
const getTableList = async () => {
  tableLoading.value = true;
  searchTableData.value.promotionId = promotionId.value;
  await getSkuPage(searchTableData.value)
    .then((res) => {
      tableList.value = res.data.records;
      total.value = res.data.total;
    })
    .catch((err) => {
      tableList.value = [];
      total.value = 1;
      message.error(err.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

//table内容ref
const tableContentRef = ref();
//table内容宽高
const tableWidth = ref<number>(0);

//商品状态
const commodityStates = ref<Array<any>>([
  { label: "上架", value: 1 },
  { label: "下架", value: 0 },
  { label: "违规下架", value: 2 },
]);
//sku状态
const skuStates = ref<Array<any>>([
  { label: "上架", value: 1 },
  { label: "下架", value: 0 },
]);

//活动内状态
const activateStatusList = ref<Array<any>>([]);
//获取活动内状态
const getActivateStatusList = () => {
  let info = {
    dictCode: "PROMOTION_SKU_STATUS",
  };
  getDictionaries(info).then((res) => {
    activateStatusList.value = res.data;
  });
};

//商品来源
const sourceList = ref<Array<any>>([]);
//获取商品来源
const getSourceList = () => {
  getSourceConfigList().then((res) => {
    sourceList.value = res.data.prodSourceConfigList;
  });
};

//商品来源名称
const prodSourceName = computed(() => (prodSource: number) => {
  let name = undefined;
  sourceList.value.forEach((item) => {
    if (String(item.prodSourceCode) === String(prodSource)) {
      name = item.prodSourceDesc;
    }
  });
  return name;
});

//所属店铺
const shopList = ref<Array<any>>([]);
//查询所属店铺
const handleShopSearch = (val: any) => {
  if (val) {
    getShopList(val);
  } else {
    shopList.value = [];
  }
};
//定时器实例
const timeout = ref();
//获取所属店铺
const getShopList = (val: any) => {
  if (timeout.value) {
    clearTimeout(timeout.value);
    timeout.value = null;
  }
  function fake() {
    let info = {
      page: 1,
      size: 10000,
      shopName: val,
    };
    getShopPage(info).then((res) => {
      shopList.value = [];
      if (res.data.records && res.data.records.length > 0) {
        res.data.records.forEach((item: any) => {
          shopList.value.push({ value: item.shopId, label: item.shopName });
        });
      }
    });
  }
  timeout.value = setTimeout(fake, 300);
};

//列表loading
const tableLoading = ref<boolean>(false);

//查询
const handleSearch = () => {
  searchTableData.value.page = 1;
  getTableList();
};

//重置
const handleReset = () => {
  searchTableData.value.page = 1;
  searchTableData.value.prodName = undefined;
  searchTableData.value.prodId = undefined;
  searchTableData.value.prodStatus = undefined;
  searchTableData.value.promotionSkuStatus = undefined;
  searchTableData.value.skuId = undefined;
  searchTableData.value.skuStatus = undefined;
  searchTableData.value.prodSource = undefined;
  searchTableData.value.shopId = undefined;
  getTableList();
};

//切换分页
const changePagination = (page: number, pageSize: number) => {
  searchTableData.value.page = page;
  searchTableData.value.size = pageSize;
  getTableList();
};

//内容宽度
const mainWidth = ref<number>(0);
//内容ref
const mainRef = ref();

//返回
const goBack = () => {
  router.go(-1);
};

//编辑
const goEdit = () => {
  localStorage.setItem("mode", "edit");
  router.push({
    path: "/activity/activityOnlineShop/edit",
    query: {
      id: promotionId.value,
    },
  });
};

//活动id
const promotionId = ref<any>(undefined);
//获取活动id
const getActivityId = async () => {
  if (route.query.id) {
    promotionId.value = route.query.id;
  } else {
    await getPromotionId({}).then((res) => {
      promotionId.value = res.data;
    });
  }
};

//获取活动详情
const getDetailsData = async () => {
  let info = {
    id: promotionId.value,
  };
  await getPromotionDetails(info).then((res) => {
    detailsData.value = res.data;
  });
};

onMounted(async () => {
  await nextTick(() => {
    tableWidth.value = tableContentRef.value.offsetWidth;
    mainWidth.value = mainRef.value.offsetWidth;
  });
  await getActivityId();
  getActivateStatusList();
  getSourceList();
  await getDetailsData();
  await getTableList();
});
</script>

<style scoped lang="less">
.basic-information {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  .title-main {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 32px;
    height: 28px;
    .title-icon {
      width: 4px;
      height: 16px;
      border-radius: 4px;
      background: #1a7af8;
    }
    .title-name {
      font-weight: 600;
      font-size: 20px;
      color: #05082c;
    }
    .title-synopsis {
      font-weight: 400;
      font-size: 14px;
      color: #636d7e;
    }
  }
  .table-details-search {
    .ant-form-item {
      margin-bottom: 24px;
    }
  }
  .descriptions-css {
    :deep(.ant-descriptions-item) {
      padding-bottom: 8px;
    }
    :deep(.ant-descriptions-item-label)::after {
      content: "";
    }
  }
}
.tag-main {
  font-weight: 400;
  font-size: 12px;
  width: 50px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
.basic-information + .basic-information {
  margin-top: 16px;
}
.table-details-main {
  display: flex;
  flex-direction: column;
  .table-content {
    margin-left: 12px;
  }
  .table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    margin-left: 12px;
  }
}
.a-col-center {
  display: flex;
  align-items: center;
}
.bottom-main {
  margin-bottom: 40px;
}
.bottom-button {
  height: 56px;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  left: 272px;
  z-index: 999;
  border: 1px solid #f2f5f9;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px 16px 0 0;
}
.prod-main {
  display: flex;
  align-items: center;
  width: 100%;
  .prod-info {
    flex: 1;
    width: 1px;
    display: flex;
    flex-direction: column;
    margin-left: 8px;
    .prod-info-name {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
    }
    .prod-info-id {
      font-weight: 400;
      font-size: 14px;
      color: #818999;
    }
  }
}
.putaway-icon {
  width: 40px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #94cdff;
  background: #e6f4ff;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #1a7af8;
}
.sold-out-icon {
  width: 40px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e8ed;
  background: #f0f4f9;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #495366;
}
.error-icon {
  width: 60px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ffbdc3;
  background: #fff0f0;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #ff436a;
}
.status-main {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  gap: 4px;
  .status-icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
</style>
