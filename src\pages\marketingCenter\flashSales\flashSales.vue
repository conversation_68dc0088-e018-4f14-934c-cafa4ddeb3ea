<template>
  <div class="table-search">
    <a-form
      layout="vertical"
      :model="searchTableData"
      :labelCol="{ style: { width: '150px' } }"
    >
      <a-row :gutter="[{ xs: 8, sm: 16, md: 24, lg: 32, xl: 32, xxl: 40 }, 10]">
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="活动ID">
            <a-input
              v-model:value="searchTableData.promotionCode"
              placeholder="请输入活动ID"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="活动名称">
            <a-input
              v-model:value="searchTableData.promotionName"
              placeholder="请输入活动名称"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="活动状态">
            <a-select
              v-model:value="searchTableData.promotionStatus"
              placeholder="请选择活动状态"
              style="width: 100%"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in activityStates"
                :key="item.value"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :span="6"
          class="a-col-center"
          style="padding-left: 12px; padding-right: 12px"
        >
          <a-form-item label="">
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 16px"
              >重置</a-button
            >
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div class="table-main">
    <div class="table-button">
      <a-button @click="viewLog" style="margin-right: 16px">操作日志</a-button>
      <a-button type="primary" @click="addActivity">
        <template #icon><PlusCircleOutlined /></template>
        创建活动
      </a-button>
    </div>
    <div class="table-content" ref="tableContentRef">
      <a-table
        :columns="columns"
        :data-source="tableList"
        :scroll="{ x: tableContent.x }"
        :pagination="false"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'bizType'">
            <view>{{ bizTypeName(record) }}</view>
          </template>
          <template v-if="column.dataIndex === 'activityTime'">
            <view
              >{{ record.promotionStartTime }} 至
              {{ record.promotionEndTime }}</view
            >
          </template>
          <template v-if="column.dataIndex === 'promotionStatus'">
            <div
              :style="{
                color: activeState(record).color,
                border: `1px solid ${activeState(record).borderColor}`,
                backgroundColor: activeState(record).background,
              }"
              class="tag-main"
            >
              {{ activeState(record).name }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <div class="btn-main">
              <div v-for="item in isShowBtnList(record)" :key="item.name">
                <template v-if="item.isBtn">
                  <a-button type="link" class="btn-css" @click="item.method">{{
                    item.name
                  }}</a-button>
                </template>
                <template v-else>
                  <a-dropdown placement="bottomCenter" trigger="hover">
                    <template #overlay>
                      <a-menu>
                        <a-menu-item
                          v-for="item1 in item.children"
                          :key="item1.name"
                          class="item-popover"
                          @click="item1.method"
                          >
                          <a-button type="link" class="btn-css">{{item1.name}}</a-button>
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <a-button type="link" class="btn-css"
                      >{{ item.name }} <DownOutlined
                    /></a-button>
                  </a-dropdown>
                </template>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <div class="table-pagination">
      <div>共 {{ total }} 项数据</div>
      <a-pagination
        v-model:current="searchTableData.page"
        v-model:page-size="searchTableData.size"
        show-size-changer
        show-quick-jumper
        :total="total"
        @change="changePagination"
      />
    </div>
  </div>

  <!-- 弹窗-发布/下线 -->
  <a-modal v-model:open="openModal" wrapClassName="modal-inform-class">
    <template #title>
      <div class="title-content">
        <ExclamationCircleFilled :style="{ color: '#ff436a' }" />
        <div>通知</div>
      </div>
    </template>
    <div class="back-box">
      <div class="title" v-if="modalType === PUBLISH">
        发布后将自动激活活动商品，确认发布？
      </div>
      <div class="title" v-else>确定下线吗？</div>
    </div>
    <template #footer>
      <a-button @click="openModal = false">取消</a-button>
      <a-button type="primary" :loading="btnLoading" @click="handleAgree"
        >确定</a-button
      >
    </template>
  </a-modal>

  <!-- 弹窗-复制 -->
  <copy-modal ref="copyRef" @onClose="onClose" />
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, computed } from "vue";
import {
  DOWN_LINE,
  FLASH_SALES_COLUMNS,
  PUBLISH,
  searchDataType,
  tableContentType,
} from "@/pages/marketingCenter/flashSales/constants/constants";
import {
  PlusCircleOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons-vue";
import router from "@/router";
import copyModal from "@/pages/marketingCenter/flashSales/components/copyModal.vue";
import { getDictionaries } from "@/api/common";
import { getPromotionPage, updateStatus } from "@/api/flashSales";
import { message } from "woody-ui";
import { DownOutlined } from "@ant-design/icons-vue";

//列表查询数据
const searchTableData = ref<searchDataType>({
  //活动id
  promotionCode: undefined,
  //活动名称
  promotionName: undefined,
  //活动状态
  promotionStatus: undefined,
  //页码
  page: 1,
  //每页条数
  size: 10,
});

//列表配置
const columns = FLASH_SALES_COLUMNS;

//列表数据
const tableList = ref<Array<any>>([]);
//列表总数
const total = ref(1);
//获取列表数据
const getTableList = () => {
  tableLoading.value = true;
  getPromotionPage(searchTableData.value)
    .then((res) => {
      tableList.value = res.data?.records;
      total.value = res.data?.total;
    })
    .catch((err) => {
      tableList.value = [];
      total.value = 1;
      message.error(err.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

//活动状态数据
const activityStates = ref<Array<any>>([]);
//获取活动状态数据
const getActivityStates = () => {
  let info = {
    dictCode: "PROMOTION_STATUS",
  };
  getDictionaries(info).then((res) => {
    activityStates.value = res.data;
  });
};

//查询Loading
const tableLoading = ref<boolean>(false);

//查询数据
const handleSearch = () => {
  searchTableData.value.page = 1;
  getTableList();
};

//重置
const handleReset = () => {
  searchTableData.value.page = 1;
  searchTableData.value.promotionCode = undefined;
  searchTableData.value.promotionName = undefined;
  searchTableData.value.promotionStatus = undefined;
  getTableList();
};

//切换分页
const changePagination = (page: number, pageSize: number) => {
  searchTableData.value.page = page;
  searchTableData.value.size = pageSize;
  getTableList();
};

//业务类型
const bizTypeName = computed(() => (item: any) => {
  let name = undefined;
  switch (item.bizType) {
    case 0:
      name = "我店优选";
      return name;
    case 1:
      name = "金币商城";
      return name;
    case 2:
      name = "AI电商";
      return name;
  }
  return name;
});

//当前活动状态
const activeState = computed(() => (item: any) => {
  let obj = {
    name: "未发布",
    color: "#FF436A",
    borderColor: "#FF436A",
    background: "#FFEDF0",
  };
  switch (item.promotionStatus) {
    case 0:
      obj = {
        name: "未发布",
        color: "#FF436A",
        borderColor: "#FFBDC3",
        background: "#FFF0F0",
      };
      return obj;
    case 1:
      obj = {
        name: "未开始",
        color: "#FF9B26",
        borderColor: "#FFCF96",
        background: "#FFF9F3",
      };
      return obj;
    case 2:
      obj = {
        name: "进行中",
        color: "#1A7AF8",
        borderColor: "#94CDFF",
        background: "#E6F4FF",
      };
      return obj;
    case 9:
      obj = {
        name: "已结束",
        color: "#1BB599",
        borderColor: "#8ADBC4",
        background: "#E6F5F0",
      };
      return obj;
    case 8:
      obj = {
        name: "已下线",
        color: "#05082C",
        borderColor: "#E0E8ED",
        background: "#F0F4F9",
      };
      return obj;
  }
  return obj;
});

//按钮展示
const isShowBtnList = computed(() => (item: any) => {
  let btnList = [];
  switch (item.promotionStatus) {
    case 0:
      return [
        {
          name: "发布",
          isBtn: true,
          method: () => publish(item),
          children: [],
        },
        {
          name: "编辑",
          isBtn: true,
          method: () => handleEdit(item),
          children: [],
        },
        {
          name: "更多",
          isBtn: false,
          children: [
            {
              name: "查看",
              isBtn: true,
              method: () => handleDetail(item),
              children: [],
            },
            {
              name: "复制",
              isBtn: true,
              method: () => openCopyActivity(item),
              children: [],
            },
          ],
        },
      ];
    case 1:
    case 2:
      return [
        {
          name: "下线",
          isBtn: true,
          method: () => downLine(item),
          children: [],
        },
        {
          name: "编辑",
          isBtn: true,
          method: () => handleEdit(item),
          children: [],
        },
        {
          name: "更多",
          isBtn: false,
          children: [
            {
              name: "查看",
              isBtn: true,
              method: () => handleDetail(item),
              children: [],
            },
            {
              name: "复制",
              isBtn: true,
              method: () => openCopyActivity(item),
              children: [],
            },
          ],
        },
      ];
    case 9:
      return [
        {
          name: "查看",
          isBtn: true,
          method: () => handleDetail(item),
          children: [],
        },
        {
          name: "复制",
          isBtn: true,
          method: () => openCopyActivity(item),
          children: [],
        },
      ];
    case 8:
      return [
        {
          name: "查看",
          isBtn: true,
          method: () => handleDetail(item),
          children: [],
        },
        {
          name: "复制",
          isBtn: true,
          method: () => openCopyActivity(item),
          children: [],
        },
      ];
  }
  return btnList;
});

//创建活动
const addActivity = () => {
  localStorage.setItem("mode", "add");
  router.push({ path: "/activity/activityOnlineShop/add" });
};

//操作日志
const viewLog = () => {
  router.push({ path: "/activity/activityOnlineShop/flashSalesLog" });
};

//查看
const handleDetail = (record: any) => {
  router.push({
    path: "/activity/activityOnlineShop/detail",
    query: {
      id: record.id,
    },
  });
};

//编辑
const handleEdit = (record: any) => {
  localStorage.setItem("mode", "edit");
  router.push({
    path: "/activity/activityOnlineShop/edit",
    query: {
      id: record.id,
    },
  });
};

//弹窗
const openModal = ref<boolean>(false);
//弹窗类型
const modalType = ref<string>("");
//活动id
const promotionCode = ref<string>("");

//发布
const publish = (record: any) => {
  modalType.value = PUBLISH;
  promotionCode.value = record.id;
  openModal.value = true;
};

//下线
const downLine = (record: any) => {
  modalType.value = DOWN_LINE;
  promotionCode.value = record.id;
  openModal.value = true;
};

//复制ref
const copyRef = ref();
//复制
const openCopyActivity = (record: any) => {
  copyRef.value.openCopyModal(record);
};
//复制关闭
const onClose = () => {
  handleSearch();
};

//按钮loading
const btnLoading = ref(false);

//弹窗确定
const handleAgree = () => {
  btnLoading.value = true;
  let info = {
    id: promotionCode.value,
  };
  updateStatus(info)
    .then(() => {
      message.success(modalType.value === PUBLISH ? "发布成功" : "下线成功");
      openModal.value = false;
      handleSearch();
    })
    .catch((err) => {
      message.error(err.message);
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

//table内容ref
const tableContentRef = ref();
//table内容宽高
const tableContent = ref<tableContentType>({
  x: undefined,
});

onMounted(() => {
  localStorage.removeItem("mode");
  localStorage.removeItem("activityForm");
  localStorage.removeItem("promotionId");
  nextTick(() => {
    tableContent.value.x = tableContentRef.value.offsetWidth;
  });
  getActivityStates();
  getTableList();
});
</script>

<style scoped lang="less">
.tag-main {
  font-weight: 400;
  font-size: 12px;
  width: 50px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
.btn-main {
  display: flex;
  align-items: center;
}
.item-popover {
  width: 148px;
  height: 28px;
  padding-left: 8px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 3px;
  font-weight: 400;
  font-size: 14px;
  color: #05082c;
}
.item-popover:hover {
  background-color: #f2f1f8;
  cursor: pointer;
}
</style>
