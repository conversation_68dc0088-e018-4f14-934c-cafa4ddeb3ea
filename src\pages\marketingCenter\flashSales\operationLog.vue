<template>
  <div class="table-search">
    <a-form
      layout="vertical"
      :model="searchTableData"
      :labelCol="{ style: { width: '150px' } }"
    >
      <a-row :gutter="[{ xs: 8, sm: 16, md: 24, lg: 32, xl: 32, xxl: 40 }, 10]">
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="操作人">
            <a-input
              v-model:value="searchTableData.oprUserName"
              placeholder="请输入操作人"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="活动id">
            <a-input
              v-model:value="searchTableData.promotionCode"
              placeholder="请输入活动id"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6" style="padding-left: 12px; padding-right: 12px">
          <a-form-item label="活动名称">
            <a-input
              v-model:value="searchTableData.promotionName"
              placeholder="请输入活动名称"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col
          :span="6"
          class="a-col-center"
          style="padding-left: 12px; padding-right: 12px"
        >
          <a-form-item label="">
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 16px"
              >重置</a-button
            >
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div class="table-main">
    <div class="table-content" ref="tableContentRef">
      <a-table
        :columns="columns"
        :data-source="tableList"
        :scroll="{ x: tableContent.x }"
        :pagination="false"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">
            <view v-if="record.type === 0">创建活动</view>
            <view v-if="record.type === 1">修改活动</view>
            <view v-if="record.type === 2">删除活动</view>
          </template>
          <template v-if="column.dataIndex === 'message'">
            <template v-if="record.vo.changes && record.vo.changes.length > 0">
              <view
                v-for="(item, index) in record.vo.changes"
                :key="index"
                class="message-main"
                v-if="record.type === 1"
              >
                <view class="message"
                  >修改前：{{ item?.filedName }}：{{
                    item?.leftTranslated
                  }}</view
                >
                <view class="message"
                  >修改后：{{ item?.filedName }}：{{
                    item?.rightTranslated
                  }}</view
                >
              </view>
              <view
                v-for="(item, index) in record.vo.changes"
                :key="index"
                class="message-main"
                v-if="record.type === 0"
              >
                <view class="message"
                  >新增：{{ item?.filedName }}：{{
                    item?.rightTranslated
                  }}</view
                >
              </view>
            </template>
          </template>
        </template>
      </a-table>
    </div>
    <div class="table-pagination" style="margin: 16px 0">
      <div>共 {{ total }} 项数据</div>
      <a-pagination
        v-model:current="searchTableData.page"
        v-model:page-size="searchTableData.size"
        show-size-changer
        show-quick-jumper
        :total="total"
        @change="changePagination"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import {
  OPERATION_LOG_TYPE,
  tableContentType,
} from "@/pages/marketingCenter/flashSales/constants/constants";
import { getOprLog } from "@/api/flashSales";
import { message } from "woody-ui";

interface searchDataType {
  //操作人
  oprUserName: string;
  //活动id
  promotionCode: string;
  //活动名称
  promotionName: string;
  //页码
  page: number;
  //每页条数
  size: number;
}

//列表查询数据
const searchTableData = ref<searchDataType>({
  //操作人
  oprUserName: undefined,
  //活动id
  promotionCode: undefined,
  //活动名称
  promotionName: undefined,
  //页码
  page: 1,
  //每页条数
  size: 10,
});

//列表配置
const columns = OPERATION_LOG_TYPE;

//列表数据
const tableList = ref<Array<any>>([]);
//列表总数
const total = ref(1);

//获取列表数据
const getTableList = () => {
  tableLoading.value = true;
  getOprLog(searchTableData.value)
    .then((res) => {
      tableList.value = res.data.records;
      total.value = res.data.total;
    })
    .catch((err) => {
      message.error(err.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

//列表Loading
const tableLoading = ref<boolean>(false);

//查询数据
const handleSearch = () => {
  searchTableData.value.page = 1;
  getTableList();
};

//重置
const handleReset = () => {
  searchTableData.value.page = 1;
  searchTableData.value.oprUserName = undefined;
  searchTableData.value.promotionCode = undefined;
  searchTableData.value.promotionName = undefined;
  getTableList();
};

//切换分页
const changePagination = (page: number, pageSize: number) => {
  searchTableData.value.page = page;
  searchTableData.value.size = pageSize;
  getTableList();
};

//table内容ref
const tableContentRef = ref();
//table内容宽高
const tableContent = ref<tableContentType>({
  x: undefined,
});

onMounted(() => {
  nextTick(() => {
    tableContent.value.x = tableContentRef.value.offsetWidth;
  });
  getTableList();
});
</script>

<style scoped lang="less">
.message-main {
  display: flex;
  align-items: center;
  gap: 16px;
  .message {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }
}
</style>
