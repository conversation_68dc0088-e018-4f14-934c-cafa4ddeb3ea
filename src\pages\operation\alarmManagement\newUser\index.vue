<template>
  <div class="section">
    <div class="title-box">
      <div class="line"></div>
      <div class="text">新人福利订单预警设置</div>
    </div>
    <div class="content">
      <div class="alarm-item">
        <div class="name">单商品库存低于预警</div>
        <a-input-number
          v-model:value="alarmData.prodStockRatio"
          class="value"
          addon-after="%"
          :disabled="!isEdit"
        />
      </div>
      <div class="alarm-item">
        <div class="name">日订单量大于预警</div>
        <a-input-number
          v-model:value="alarmData.alarmDayOrderCount"
          class="value"
          :disabled="!isEdit"
        />
      </div>
      <div class="alarm-item">
        <div class="name">预警触发后再次提醒间隔</div>
        <a-input-number
          v-model:value="alarmData.alarmInterval"
          class="value"
          addon-after="分钟"
          :disabled="!isEdit"
        />
      </div>
    </div>
    <div class="btn-box flex">
      <div v-if="!isEdit">
        <a-button type="primary" @click="isEdit = true">编辑</a-button>
      </div>
      <div class="flex" v-else>
        <a-button style="margin-right: 24px" @click="handleCancel"
          >取消</a-button
        >
        <a-button type="primary" @click="handleSubmit">保存</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'woody-ui';
import { onMounted, ref } from 'vue'
import { newComerAlarmDetail, newComerAlarmEdit } from '@/api/newcomer'

const isEdit = ref(false);
const alarmData = ref({
  prodStockRatio: "",
  alarmDayOrderCount: "",
  alarmInterval: "",
});

const getData = () => {
  newComerAlarmDetail().then((res) => {
    if (res.code === 0) {
      alarmData.value = res.data;
    }
  });
};
onMounted(() => {
  getData();
});

const handleCancel = () => {
  isEdit.value = false;
  getData();
};
const handleSubmit = () => {
  newComerAlarmEdit({ ...alarmData.value }).then((res) => {
    if (res.code === 0) {
      isEdit.value = false;
      message.success("保存成功");
    }
  });
};
</script>

<style lang="less" scoped>
.section {
  height: 92vh;
  padding: 32px;
  position: relative;
}
.title-box {
  font-weight: bold;
  font-size: 20px;
  color: #05082c;
  margin-bottom: 32px;
  display: flex;
  .line {
    width: 4px;
    height: 16px;
    background: #1a7af8;
    border-radius: 4px 4px 4px 4px;
    margin-right: 12px;
    margin-top: 3px;
  }
}
.content {
  width: 30%;
}
.alarm-item {
  margin-bottom: 24px;
  .name {
    margin-bottom: 8px;
    font-size: 14px;
    color: #495366;
  }
  .value {
    width: 100%;
  }
}
.btn-box {
  width: 100%;
  height: 56px;
  border-top: 1px solid #f2f5f9;
  position: absolute;
  bottom: 0;
  right: 0;
  justify-content: center;
}
</style>
