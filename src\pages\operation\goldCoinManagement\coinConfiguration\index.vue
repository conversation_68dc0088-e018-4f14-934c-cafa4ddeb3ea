<template>
  <div class="search-form" style="height: 100%">
    <div class="flexl">
      <div class="line"></div>
      <div class="font">金币配置</div>
    </div>
    <a-form ref="formRef" :model="formData" :colon="false">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
        <a-col class="gutter-row">
          <a-form-item label="金币取得汇率消费1.00元获得" name="amountRatio">
            <a-input
              :disabled="isFlag"
              v-model:value="formData.amountRatio"
              allow-clear
              style="width: 300px"
              placeholder="请输入金币"
            />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row">
          <a-form-item label="金币"> </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="24">
          <a-form-item
            :wrapper-col="{ span: 24, offset: 0 }"
            style="
              position: fixed;
              bottom: 100px;
              width: 70%;
              text-align: center;
            "
          >
            <a-button v-show="isFlag" type="primary" @click="handleEdit"
              >编辑</a-button
            >
            <a-button
              v-show="!isFlag"
              style="margin-right: 10px"
              @click="handleReset"
              >取消</a-button
            >
            <a-button
              v-show="!isFlag"
              :loading="isLoading"
              type="primary"
              @click="handleSave"
              >保存</a-button
            >
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { FROM_DATA } from "./constants";
import { getCoinRatio, getUpdateCoin } from "@/api/operation/coinConfiguration";

const isLoading = ref(false);
const formRef = ref(null);
const formData = reactive({
  amountRatio: undefined,
  createTime: undefined,
  createUserId: undefined,
  id: undefined,
  updateTime: undefined,
  updateUserId: undefined,
});
const isFlag = ref(true);
onMounted(async () => {
  getDetail();
});

//获取金币配置

const getDetail = async () => {
  try {
    const res = await getCoinRatio();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    Object.assign(formData, res.data);
  } catch (error) {
    message.error(error.message);
  }
};

// 保存
const handleSave = async () => {
  try {
    isLoading.value = true;
    const params = {
      ...formData,
    };
    const res = await getUpdateCoin(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isFlag.value = true;
  } catch (error) {
    message.error(error.message);
  }
};
const handleEdit = () => {
  isFlag.value = false;
};
const handleReset = () => {
  getDetail();
  isFlag.value = true;
};
</script>
<style lang="less" scoped>
.flexl {
  display: flex;
  margin: 0 0 15px 0;
  .line {
    width: 4px;
    height: 22px;
    background: #1a7af8;
    border-radius: 4px 4px 4px 4px;
    margin: 0 5px 0 0;
  }
  .font {
    font-weight: 600;
    font-size: 20px;
  }
}
@import url("@/style/plat.less");
</style>
