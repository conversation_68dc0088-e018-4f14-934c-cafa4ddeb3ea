<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <!-- <a-col class="gutter-row" :span="6">
            <a-form-item label="创建时间" name="startTime">
              <a-range-picker
                v-model:value="formData.startTime"
                :locale="locale"
                :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('11:59:59', 'HH:mm:ss'),
                  ],
                }"
                allow-clear
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6" style="margin: 30px 0 0 0">
            <a-radio-group @change="handleTabPositionChange">
              <a-radio-button value="day">今日</a-radio-button>
              <a-radio-button value="lastday">昨日</a-radio-button>
              <a-radio-button value="month">本月</a-radio-button>
              <a-radio-button value="lastmonth">上月</a-radio-button>
            </a-radio-group>
          </a-col> -->
          <a-col class="gutter-row" :span="6">
            <a-form-item label="会员信息" name="mobile">
              <a-input
                v-model:value="formData.mobile"
                allow-clear
                style="width: 100%"
                placeholder="请输入会员信息"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="订单编号" name="orderNumber">
              <a-input
                v-model:value="formData.orderNumber"
                allow-clear
                style="width: 100%"
                placeholder="请输入订单编号"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="变动类型" name="coinBizType">
              <a-select
                v-model:value="formData.coinBizType"
                placeholder="请选择变动类型"
                allow-clear
                style="width: 100%"
              >
                <a-select-option value="ORDER_EXPENSE"
                  >金币商城消费</a-select-option
                >
                <a-select-option value="ORDER_EXPENSE_REFUND"
                  >金币商城消费退款</a-select-option
                >
                <a-select-option value="ORDER_AWARD">购物所得</a-select-option>
                <a-select-option value="ORDER_AWARD_REFUND"
                  >退货扣除</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-info flex">
      <div class="tab-line">
        <div class="mb10">获得金币</div>
        <div class="f30">{{ isData?.awardNum }}</div>
      </div>
      <div class="tab-line">
        <div class="mb10">消耗金币</div>
        <div class="f30">{{ isData?.expenseNum }}</div>
      </div>
      <div class="tab-line">
        <div class="mb10">获得金币会员数</div>
        <div class="f30">{{ isData?.awardAccount }}</div>
      </div>
      <div class="tab-line">
        <div class="mb10">消耗金币会员数</div>
        <div class="f30">{{ isData?.expenseAccount }}</div>
      </div>
    </div>
    <div class="table-columns">
      <a-radio-group
        v-model:value="tabVal"
        button-style="solid"
        @change="handleTStatus"
        style="margin: 0 0 20px 0"
      >
        <a-radio-button value="">全部</a-radio-button>
        <a-radio-button value="ADD">获得</a-radio-button>
        <a-radio-button value="REDUCE">消耗</a-radio-button>
      </a-radio-group>
      <a-table
        :data-source="
          isStatus
            ? tableData.filter((item) => item.flowDirectionType === isStatus)
            : tableData
        "
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 会员信息 -->
          <template v-if="column.key == 'userNickName'">
            <a-space direction="vertical">
              <span>{{ record.userNickName }}</span>
              <span>{{ record.userMobile }}</span>
            </a-space>
          </template>
          <!-- 变动类型 -->
          <template v-if="column.key == 'coinBizType'">
            {{
              record.coinBizType === "ORDER_AWARD"
                ? "购物所得"
                : record.coinBizType === "ORDER_AWARD_REFUND"
                ? "购物所得退款扣除"
                : record.coinBizType === "ORDER_EXPENSE"
                ? "金币商城消费"
                : record.coinBizType === "ORDER_EXPENSE_REFUND"
                ? "金币商城消费退款"
                : ""
            }}
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button type="link" class="btn-css"> 暂时没有详情 </a-button>
          </template>
        </template>
      </a-table>
    </div>
    <!-- <add-modal :open="isOpen" @is-modal-open="handleOk"></add-modal> -->
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import dayjs, { Dayjs } from "dayjs";
import { message } from "woody-ui";
import { ref, onMounted, reactive, watch } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import { getPage } from "@/api/operation/coinGManage";
// import addModal from "./addModal/index.vue";

const isLoading = ref(false);
const formRef = ref(null);
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({
  startTime: undefined,
  mobile: undefined,
  orderNumber: undefined,
  coinBizType: undefined,
});
const isData = ref();
const isDetail = ref(false);
const tabVal = ref("");
onMounted(async () => {
  // formData.startTime = getDays();
  // getPageList();
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  isData.value = undefined;
  tableData.value = undefined;
  pagination.total = undefined;
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
      // startTime: formData.startTime ? formData.startTime[0] : null,
      // endTime: formData.startTime ? formData.startTime[1] : null,
    };
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    isData.value = res.data;
    tableData.value = res.data.coinPage.records;
    pagination.total = res.data.coinPage.total;
  } catch (error) {
    message.error(error.message);
  }
};
//选择时间今天，昨天，本月，上月

const getDays = () => {
  const endDate = new Date();
  const startDate = new Date(endDate);
  startDate.setDate(startDate.getDate());
  return [
    dayjs(startDate).format("YYYY-MM-DD 00:00:00"),
    dayjs(endDate).format("YYYY-MM-DD HH:mm:ss"),
  ];
};

const getLastDays = () => {
  const endDate = new Date();
  const startDate = new Date(endDate);
  startDate.setDate(startDate.getDate() - 1);
  return [
    dayjs(startDate).format("YYYY-MM-DD 00:00:00"),
    dayjs(endDate).format("YYYY-MM-DD 23:59:59"),
  ];
};

const getThisMonthDays = () => {
  const date = new Date();
  const startDate = new Date(date.getFullYear(), date.getMonth(), 1);
  const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  return [
    dayjs(startDate).format("YYYY-MM-DD HH:mm:ss"),
    dayjs(endDate).format("YYYY-MM-DD 23:59:59"),
  ];
};

const getLastMonthDays = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth();
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);
  return [
    dayjs(startDate).format("YYYY-MM-DD HH:mm:ss"),
    dayjs(endDate).format("YYYY-MM-DD 23:59:59"),
  ];
};
const handleTabPositionChange = (e) => {
  if (e.target.value === "day") {
    formData.startTime = getDays();
  } else if (e.target.value === "lastday") {
    formData.startTime = getLastDays();
  } else if (e.target.value === "month") {
    formData.startTime = getThisMonthDays();
  } else if (e.target.value === "lastmonth") {
    formData.startTime = getLastMonthDays();
  }
};
//切换
const isStatus = ref("");
const handleTStatus = (e) => {
  isStatus.value = e.target.value;
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
