<template>
  <div class="search-form" style="height: 100%">
    <a-form>
      <a-form-item labelAlign="right" label="我店生活最低订单金额配置:">
        <div class="formItem">
          <a-form-item noStyle name="amount" v-bind="validateInfos.amount">
            <a-input-number
              v-model:value="form.amount"
              :disabled="!isShow"
              :min="0"
              :max="10000"
              class="inputStyle"
              placeholder="请输入金额"
              addonAfter="元"
            />
          </a-form-item>
          <div class="inputDesc">设置后，用户订单最低金额不能低于设置金额</div>
        </div>
      </a-form-item>
    </a-form>
    <div
      style="
        position: fixed;
        bottom: 50px;
        width: calc(100% - 340px);
        text-align: center;
      "
    >
      <a-divider />
      <a-button
        v-show="!isShow"
        type="primary"
        @click="
          () => {
            isShow = true;
          }
        "
        >编辑</a-button
      >
      <a-button
        v-show="isShow"
        style="margin-right: 10px"
        @click="
          () => {
            isShow = false;
            form.amount = rawAmount;
          }
        "
        >取消</a-button
      >
      <a-button
        v-show="isShow"
        :loading="isLoading"
        type="primary"
        @click="onEdit"
        >保存</a-button
      >
    </div>
  </div>
  <a-modal
    title="提示"
    :open="isModalOpen"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <p style="text-align: center">
      您设置的金额高于某些供货仓的下单限制金额，点击保存会将所有低于
    </p>
    <p style="text-align: center">
      平台下单金额限制的供货仓下单限制金额修改为和平台一致
    </p>
  </a-modal>
</template>
<script setup lang="ts">
import { Form, message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import {
  getStartList,
  getListPage,
  getSaveCnfSysCheck,
} from "@/api/operation/shopMallRestrictions";

const isLoading = ref(false);
const isShow = ref(false);
const isModalOpen = ref(false);

// 表单数据
let form = ref({
  amount: "",
});
const rawAmount = ref("");
const rules = reactive({
  amount: [
    {
      required: true,
      message: "请输入金额",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

const fetchListData = async () => {
  let keyText = "WD_ORDER_MIN_PRICE";
  isLoading.value = true;
  try {
    const result = await getStartList(keyText as string);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    form.value.amount = result.data;
    rawAmount.value = result.data;
  } catch (error) {
    message.error((error as any).message);
  }
  isLoading.value = false;
};

const onListPage = async () => {
  let params = {
    keyVl: "WD_ORDER_MIN_PRICE", //健  WD_ORDER_MIN_PRICE 我店生活下单最小金额
    val: form.value.amount, //值 （金额）
  };
  isLoading.value = true;
  try {
    const result = await getListPage(params as any);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    fetchListData();
    isShow.value = false;
    isModalOpen.value = false;
    message.success("编辑成功");
  } catch (error) {
    message.error((error as any).message);
  }
  isLoading.value = false;
};

const handleOk = () => {
  onListPage();
};
const handleCancel = () => {
  isModalOpen.value = false;
};

const onEdit = async () => {
  try {
    await validate();
    await handleEditFun();
  } catch (error) {
    console.error("提交失败", error);
  }
};

const handleEditFun = async () => {
  let params = {
    val: form.value.amount, //值 （金额）
  };
  isLoading.value = true;
  try {
    const result = await getSaveCnfSysCheck(params as any);
    if (result.code !== 0) {
      return message.error(result.message || "请求失败");
    }
    if (result.data > 0) {
      isModalOpen.value = true;
    } else {
      onListPage();
    }
  } catch (error) {
    message.error((error as any).message);
  }
  isLoading.value = false;
};

onMounted(async () => {
  fetchListData();
});
</script>

<style lang="less" scoped>
.formItem {
  align-items: flex-end;
}
.inputStyle {
  width: 100px;
}
:deep(.ant-form-item-label) {
  margin-right: 10px;
  & > label {
    font-size: 17px;
  }
}
.inputDesc {
  font-size: 14px;
  color: #999;
  margin-top: 20px;
  margin-left: -230px;
}
@import url("@/style/plat.less");
</style>
