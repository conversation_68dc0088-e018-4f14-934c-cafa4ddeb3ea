<template>
  <div class="config-form">
    <div class="search-form" style="height: 100%">
      <div class="flexl">
        <div class="line"></div>
        <div class="font">页面配置</div>
      </div>
      <div style="margin-top: 40px">
        <p class="title">短剧页面</p>
        <a-radio-group
          v-model:value="alarmData.onOff"
          :disabled="!isEdit"
          style="margin-top: 20px"
        >
          <a-radio :value="1">开</a-radio>
          <a-radio :value="0">关</a-radio>
        </a-radio-group>
      </div>
      <div class="flexl" style="margin-top: 50px">
        <div class="line"></div>
        <div class="font">激励视频项</div>
      </div>
      <div class="alarm-item">
        <a-form ref="formRef" :model="alarmData" layout="vertical">
          <a-form-item
            label="可免费观看（不会超过每部剧的前20%）"
            name="freeEpisodesCount"
            :rules="[{ required: true, message: '必填项' }]"
            class="mt20"
          >
            <a-input-number
              v-model:value="alarmData.freeEpisodesCount"
              class="value"
              placeholder="请输入0-20"
              :min="0"
              :max="20"
              :disabled="!isEdit"
              :addon-after="'集'"
            />
          </a-form-item>
          <a-form-item
            label="每完播一个激励视频可解锁"
            name="unlockEpisodesCountUsingAD"
            :rules="[{ required: true, message: '必填项' }]"
            class="mt20"
          >
            <a-input-number
              v-model:value="alarmData.unlockEpisodesCountUsingAD"
              class="value"
              placeholder="请输入1-20"
              :min="1"
              :max="20"
              :addon-after="'集'"
              :disabled="!isEdit"
            />
          </a-form-item>
          <div class="flexl" style="margin-top: 50px">
            <div class="line" style="margin-top: 6px"></div>
            <div class="font">
              规则说明
              <span class="rule-css"
                >如需更新规则，请将文案提供给前端开发，生成新的URL并填入下方</span
              >
            </div>
          </div>
          <a-form-item
            label="URL"
            name="ruleUrl"
            :rules="[{ required: true, message: '必填项' }]"
            style="margin-top: 35px"
          >
            <a-input
              :rules="[{ required: true, message: '必填项' }]"
              v-model:value="alarmData.ruleUrl"
              class="value"
              :disabled="!isEdit"
              style="width: 450px"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>

    <div class="btn-box flex">
      <div v-if="!isEdit">
        <a-button type="primary" @click="isEdit = true">编辑</a-button>
      </div>
      <div class="flex" v-else>
        <a-button style="margin-right: 24px" @click="handleCancel"
          >取消</a-button
        >
        <a-button type="primary" @click="handleSubmit">保存</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { message } from "woody-ui";
import { onMounted, ref } from "vue";
import { saveEdit, getSearch } from "@/api/shortDramaCenter/index";

const isEdit = ref(false);
const alarmData = ref({
  prodStockRatio: "",
  freeEpisodesCount: "",
  unlockEpisodesCountUsingAD: "",
  ruleUrl: "",
  onOff: 0,
});
const serverKey = ref("");
const id = ref(0);

const getData = () => {
  getSearch().then((res) => {
    if (res.code === 0) {
      alarmData.value = res.data.context.drama;
      id.value = res.data.id;
      serverKey.value = res.data.context.serverKey;
    }
  });
};

onMounted(() => {
  getData();
});

const handleCancel = () => {
  isEdit.value = false;
  getData();
};

//url校验
const validateURL = (url) => {
  const urlRegex =
    /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(\/[\w-]*)*(\?[a-zA-Z0-9&=]*)?(#[a-zA-Z0-9_-]*)?$/;
  return urlRegex.test(url);
};
const formRef = ref();
const handleSubmit = () => {
  const params = {
    id: id.value,
    context: {
      drama: { ...alarmData.value },
      serverKey: serverKey.value,
    },
  };
  formRef.value.validate().then(() => {
    if (!validateURL(alarmData.value.ruleUrl)) {
      message.error("请输入正确的URL");
      return;
    }
    saveEdit(params).then((res) => {
      if (res.code === 0) {
        isEdit.value = false;
        message.success("保存成功");
      }
    });
  });
};
</script>

<style lang="less" scoped>
@import url("@/style/plat.less");
.flexl {
  display: flex;
  margin: 0 0 15px 0;
  .line {
    width: 4px;
    height: 17px;
    background: #1a7af8;
    border-radius: 4px 4px 4px 4px;
    margin: 2px 10px 0 0;
  }
  .font {
    font-weight: 600;
    font-size: 20px;
  }
}
.config-form {
  height: 90vh;
  position: relative;
}
.title-box {
  font-weight: bold;
  font-size: 20px;
  color: #05082c;
  margin-bottom: 32px;
  .line {
    width: 4px;
    height: 16px;
    background: #1a7af8;
    border-radius: 4px 4px 4px 4px;
    margin-right: 12px;
  }
}
.title {
  font-size: 14px;
  color: #495366;
}
.alarm-item {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  .name {
    margin-bottom: 8px;
    font-size: 14px;
    color: #495366;
  }
  .value {
    width: 450px;
    margin-right: 10px;
  }
}
.rule-css {
  margin-left: 8px;
  font-size: 14px;
  color: #495366;
  font-weight: 400;
}

.btn-box {
  width: 100%;
  height: 56px;
  border-top: 1px solid #f2f5f9;
  position: absolute;
  bottom: 0;
  right: 0;
  justify-content: center;
}
</style>
