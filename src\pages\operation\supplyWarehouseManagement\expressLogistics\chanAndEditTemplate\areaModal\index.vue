<template>
  <a-modal
    v-model:open="isOpen"
    width="540px"
    title="配送地区"
    :destroy-on-close="true"
    @cancel="handleModalCancel"
  >
    <a-tree
      @check="handleCheck"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      v-model:checkedKeys="checkedKeys"
      checkable
      :tree-data="treeData"
      :fieldNames="{
        children: 'areas',
        title: 'areaName',
        key: 'areaCode',
      }"
    >
    </a-tree>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleModalCancel"
        >取消</a-button
      >
      <a-button :loading="loading" type="primary" @click="handleModalOk"
        >提交</a-button
      >
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, watch, defineEmits } from "vue";
import { getArea } from "@/api/area";

const isOpen = ref(false);
const loading = ref(false);
const treeData = ref();
const expandedKeys = ref<string[]>(["0"]);
const selectedKeys = ref<string[]>([]);
const checkedKeys = ref<string[]>();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
  },
});
watch(props, (newProps) => {
  isOpen.value = newProps.open;
  checkedKeys.value = [];
  if (isOpen.value) {
    getAreaList();
    checkedKeys.value = (props.data || []).map((item) => String(item));
    isFlag.value = false;
  }
});
const emit = defineEmits(["isModalOpen", "isArea"]);
// 省市区
const getAreaList = async () => {
  try {
    const res = await getArea();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    treeData.value = [{ areaCode: "0", areaName: "全国", areas: res.data }];
  } catch (error) {
    message.error(error.message);
  }
};

const isArea = ref([]);
const isFlag = ref(false);
const isCheckedKeys = ref();
//选中
const handleCheck = (checkedKeys: any, e: any) => {
  isArea.value = [];
  isCheckedKeys.value = [];
  isFlag.value = true;
  isCheckedKeys.value = checkedKeys;
};

const handleModalCancel = () => {
  isOpen.value = false;
  isArea.value = [];
  //将市过滤出来
  treeData.value[0].areas.map((item: any) => {
    item.areas.map((item: any) => {
      if (props.data.includes(item.areaCode)) {
        isArea.value.push(item);
      }
    });
  });
  emit("isModalOpen", isArea.value);
};
const handleModalOk = () => {
  isOpen.value = false;
  isArea.value = [];
  //将市过滤出来
  treeData.value[0].areas.map((item: any) => {
    item.areas.map((item: any) => {
      if (
        isFlag.value
          ? isCheckedKeys.value.includes(item.areaCode)
          : props.data.includes(item.areaCode)
      ) {
        isArea.value.push(item);
      }
    });
  });
  emit("isModalOpen", isArea.value);
};
</script>
<style lang="less" scoped></style>
