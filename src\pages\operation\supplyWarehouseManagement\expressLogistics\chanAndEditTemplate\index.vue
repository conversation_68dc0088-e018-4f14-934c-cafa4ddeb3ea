<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="24">
            <a-form-item
              label="模板名称"
              name="groupName"
              :rules="[{ required: true, message: '请输入模板名称' }]"
            >
              <a-input
                :disabled="isFlag"
                v-model:value="formData.groupName"
                allow-clear
                style="width: 335px"
                placeholder="请输入模板名称"
                show-count
                :maxlength="30"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="24">
            <a-tabs
              :disabled="isFlag"
              v-model:activeKey="isIndex"
              :type="isFlag ? 'card' : 'editable-card'"
              @edit="onEdit"
              @change="handlePane"
            >
              <a-tab-pane
                v-for="(item, index) in formData?.list"
                :key="item.key"
                :tab="item.transName ? item.transName : item.title"
              >
                <a-col>
                  <p class="info_p">基本信息</p>
                </a-col>
                <a-col class="gutter-row" :span="24">
                  <a-form-item
                    label="配置名称"
                    :name="['list', index, 'transName']"
                    :rules="[{ required: true, message: '请输入配置名称' }]"
                  >
                    <a-input
                      :disabled="isFlag"
                      v-model:value="item.transName"
                      allow-clear
                      style="width: 335px"
                      placeholder="请输入配置名称"
                      show-count
                      :maxlength="10"
                    />
                  </a-form-item>
                </a-col>
                <a-col class="gutter-row" :span="24">
                  <a-form-item
                    label="默认起重"
                    :name="['list', index, 'startWight']"
                    :rules="[{ required: true, message: '请输入默认起重' }]"
                  >
                    <a-input
                      :disabled="isFlag"
                      v-model:value="item.startWight"
                      allow-clear
                      style="width: 335px"
                      placeholder="请输入默认起重"
                      addon-after="kg"
                    />
                  </a-form-item>
                </a-col>
                <a-col class="gutter-row" :span="24">
                  <a-form-item
                    label="起重价格"
                    :name="['list', index, 'firstWightPrice']"
                    :rules="[{ required: true, message: '请输入起重价格' }]"
                  >
                    <a-input
                      :disabled="isFlag"
                      v-model:value="item.firstWightPrice"
                      allow-clear
                      style="width: 335px"
                      placeholder="请输入起重价格"
                      addon-after="元"
                    />
                  </a-form-item>
                </a-col>
                <a-col class="gutter-row" :span="24">
                  <a-form-item
                    label="默认续重"
                    :name="['list', index, 'continuousWightUnit']"
                    :rules="[{ required: true, message: '请输入默认续重' }]"
                  >
                    <a-input
                      :disabled="isFlag"
                      v-model:value="item.continuousWightUnit"
                      allow-clear
                      style="width: 335px"
                      placeholder="请输入默认续重"
                      addon-after="元"
                    />
                  </a-form-item>
                </a-col>
                <a-col class="gutter-row" :span="24">
                  <a-form-item
                    label="续重价格"
                    :name="['list', index, 'continuousWightPrice']"
                    :rules="[{ required: true, message: '请输入续重价格' }]"
                  >
                    <a-input
                      :disabled="isFlag"
                      v-model:value="item.continuousWightPrice"
                      allow-clear
                      style="width: 335px"
                      placeholder="请输入续重价格"
                      addon-after="元"
                    />
                  </a-form-item>
                </a-col>
                <a-col class="gutter-row" :span="24">
                  <a-form-item label="包邮门槛" name="exemptionAmount">
                    <a-input
                      :disabled="isFlag"
                      v-model:value="item.exemptionAmount"
                      allow-clear
                      style="width: 335px"
                      placeholder="请输入包邮门槛"
                      addon-after="元"
                    />
                  </a-form-item>
                </a-col>
                <a-col>
                  <p class="info_p"><i>*</i>适用地区&包邮地区</p>
                </a-col>
                <a-col style="margin: 0 0 20px 0">
                  <a-button
                    :disabled="isFlag"
                    @click="handleAddArea"
                    type="primary"
                    >添加</a-button
                  >
                </a-col>
                <a-col>
                  <a-form-item
                    :name="['list', index, 'areaTable']"
                    :rules="[{ required: true, message: '请选择包邮地区' }]"
                  >
                    <a-table
                      :disabled="isFlag"
                      :pagination="false"
                      :data-source="item.areaTable"
                      :columns="shopColumns"
                    >
                      <template #bodyCell="{ column, record }">
                        <!-- 操作 -->
                        <template v-if="column.key == 'operate'">
                          <a-button
                            :disabled="isFlag"
                            type="link"
                            class="btn-css"
                            @click="handleDelete(record)"
                            >删除</a-button
                          >
                        </template>
                      </template>
                    </a-table>
                  </a-form-item>
                </a-col>
              </a-tab-pane>
            </a-tabs>
          </a-col>
          <a-col class="gutter-row" :span="24" style="margin: 20px 0 0 0">
            <a-form-item style="align-self: flex-end; text-align: center">
              <a-button style="margin-right: 10px" @click="handleBack"
                >返回</a-button
              >
              <a-button
                v-if="!isFlag"
                :loading="isLoading"
                type="primary"
                @click="handleSave"
                >保存</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <area-modal
      :open="isOpen"
      :data="tableData"
      @is-modal-open="handleModalOk"
    ></area-modal>
    <a-modal
      v-model:open="isDelOpen"
      width="520px"
      title="删除"
      :destroy-on-close="true"
      @cancel="handleDelCancel"
    >
      <div
        style="color: rgb(25, 144, 255); text-align: center; margin: 10px 0 0 0"
      >
        是否删除该模板？
      </div>
      <template #footer>
        <a-button @click="handleDelCancel">取消</a-button>
        <a-button type="primary" @click="handleDelOk">确定</a-button>
      </template>
    </a-modal>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import { message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import { COLUMNS } from "./constants";
import {
  getGroupInfo,
  getGroupInfoById,
} from "@/api/operation/expressLogistics";
import router from "@/router";
import areaModal from "./areaModal/index.vue";
import { getArea } from "@/api/area";
import { useRoute } from "vue-router";

const route = useRoute();
const isLoading = ref(false);
const formRef = ref(null);
const formData = reactive({
  groupName: undefined,
  list: [
    {
      title: "新建1",
      transName: undefined,
      startWight: undefined,
      firstWightPrice: undefined,
      continuousWightUnit: undefined,
      continuousWightPrice: undefined,
      exemptionAmount: undefined,
      areaTable: [],
      areaFreeIdList: [],
      key: "1",
    },
  ],
});
const isDetail = ref(false);
const activeKey = ref("0");
const newTabIndex = ref(formData.list.length - 1);
const shopColumns = reactive(COLUMNS);
const isGroupId = ref();
const isFlag = ref(false);
const isIndex = ref("0");

onMounted(async () => {
  isGroupId.value = route.query.groupId;
  isFlag.value = route.query.type === "edit" ? false : true;
});

const add = () => {
  activeKey.value = `新建${++newTabIndex.value}`;
  isIndex.value = formData.list.length.toString();
  formData.list.push({
    title: `${activeKey.value}`,
    transName: "",
    startWight: "",
    firstWightPrice: "",
    continuousWightUnit: "",
    continuousWightPrice: "",
    exemptionAmount: "",
    areaTable: [],
    areaFreeIdList: [],
    key: formData.list.length.toString(),
  });
};

const isDelOpen = ref(false);
const isTargetKey = ref();
const handleDelOk = () => {
  isIndex.value = (Number(isTargetKey.value) - 1).toString();
  let lastIndex = 0;
  formData.list.forEach((pane, i) => {
    if (pane.key === isTargetKey.value) {
      lastIndex = i - 1;
    }
  });
  formData.list = formData.list.filter(
    (pane) => pane.key !== isTargetKey.value
  );
  if (formData.list.length && activeKey.value === isTargetKey.value) {
    if (lastIndex >= 0) {
      activeKey.value = formData.list[lastIndex].key;
    } else {
      activeKey.value = formData.list[0].key;
    }
  }
  getSaveInfo();
  isDelOpen.value = false;
};
const handleDelCancel = () => {
  isDelOpen.value = false;
};
const remove = (targetKey: string) => {
  if (formData.list.length === 1) {
    return message.error("无法删除，需要保留最少一个配置项");
  }
  isDelOpen.value = true;
  isTargetKey.value = targetKey;
};

const onEdit = (targetKey: string | MouseEvent, action: string) => {
  if (action === "add") {
    formRef.value
      .validate()
      .then(async () => {
        add();
      })
      .catch((error) => {
        message.error("所有配置中存在必填项末填写，请填写后再次新增或删除");
      });
  } else {
    remove(targetKey as string);
  }
};

onMounted(async () => {
  getAreaList();
});

const handlePane = (e) => {
  isIndex.value = e;
};
//获取省市区getArea

const areaList = ref();
const getAreaList = async () => {
  try {
    const res = await getArea();
    if (res.code !== 0) {
      return message.error(res.message);
    }
    areaList.value = res.data;
    if (areaList.value) {
      getGroupInfoDetail();
    }
  } catch (error) {
    message.error(error.message);
  }
};
const isAreaList = ref([]);
//获取模板详情
const getGroupInfoDetail = async () => {
  try {
    const res = await getGroupInfoById(isGroupId.value);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    res.data.list.map((item, index) => {
      isAreaList.value = [];
      item.title = item.transName;
      item.key = index.toString();
      item.areaTable = item.areaFreeIdList.map((items) => {
        return {
          areaCode: undefined,
          areaName: "",
          areaId: items,
          areas: "",
          parentCode: "",
          parentName: "",
        };
      });
      areaList.value.map((items) => {
        if (items.areas && items.areas.length) {
          items.areas.map((itemt) => {
            if (item.areaFreeIdList.includes(itemt.areaId)) {
              isAreaList.value.push(itemt);
            }
          });
        }
      });
      item.areaTable = isAreaList.value;
      item.areaFreeIdList = isAreaList.value;
    });
    areaList.value.map((titem) => {
      res.data.list.map((sitem) => {
        sitem.areaTable.map((mitem) => {
          if (mitem.parentCode === titem.areaCode) {
            mitem.parentName = titem.areaName;
          }
        });
      });
    });
    Object.assign(formData, res.data);
  } catch (error) {
    message.error(error.message);
  }
};
//添加地区
const isOpen = ref(false);
const tableData = ref([]);
const handleAddArea = () => {
  tableData.value = [];
  formData.list[isIndex.value].areaTable.map((item) => {
    tableData.value.push(item.areaCode);
  });
  isOpen.value = !isOpen.value;
};

//列表删除
const handleDelete = (e) => {
  formData.list[isIndex.value].areaTable = formData.list[
    isIndex.value
  ].areaTable.filter((item) => e.areaCode !== item.areaCode);
};

const handleModalOk = (e) => {
  isOpen.value = false;
  areaList.value.map((item) => {
    e.map((items) => {
      if (item.areaCode === items.parentCode) {
        items.parentName = item.areaName;
      }
    });
  });
  Object.assign(formData.list[isIndex.value], {
    areaTable: e,
  });
};

//返回
const handleBack = () => {
  router.go(-1);
};
const getSaveInfo = async () => {
  formData.list.map((item, index, items) => {
    item.areaFreeIdList = [];
    item.areaTable.map((items) => {
      item.areaFreeIdList.push(items.areaId as any);
    });
  });
  const params = {
    groupId: isGroupId.value,
    groupName: formData.groupName,
    list: formData.list,
  };
  //删除多余字段---要回显之前定义字段不满足接口需求。这里转换后删除
  // params.list.map((itemp) => {
  //   delete itemp.areaTable;
  //   delete itemp.key;
  //   delete itemp.title;
  //   delete (itemp as any).customAreaTransportResponseList;
  //   delete (itemp as any).areaDistributionIdList;
  // });
  try {
    const res = await getGroupInfo(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("保存成功");
    getGroupInfoDetail();
  } catch (error) {
    message.error(error.message);
  }
};

//保存
const handleSave = () => {
  formRef.value
    .validate()
    .then(async () => {
      getSaveInfo();
    })
    .catch((error) => {
      message.error("请填写必填项");
    });
};
</script>
<style lang="less" scoped>
.info_p {
  background: rgb(242, 242, 242);
  height: 40px;
  font-family: Roboto;
  font-size: 18px;
  font-weight: 500;
  color: rgb(51, 51, 51);
  padding: 0px 30px;
  line-height: 40px;
  margin: 20px 0;
  i {
    color: red;
    font-style: normal;
  }
}
@import url("@/style/plat.less");
</style>
