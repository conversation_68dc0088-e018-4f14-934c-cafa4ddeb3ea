<template>
  <div v-if="!isDetail">
    <div class="search-form">
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 16 }">
          <a-col class="gutter-row" :span="6">
            <a-form-item label="模板名称" name="groupName">
              <a-input
                v-model:value="formData.groupName"
                allow-clear
                style="width: 100%"
                placeholder="请输入模板名称"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="创建时间" name="createTime">
              <a-range-picker
                v-model:value="formData.createTime"
                :locale="locale"
                :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('23:59:59', 'HH:mm:ss'),
                  ],
                }"
                allow-clear
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item label="更新时间" name="updateTime">
              <a-range-picker
                v-model:value="formData.updateTime"
                :locale="locale"
                :show-time="{
                  hideDisabledOptions: true,
                  defaultValue: [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('23:59:59', 'HH:mm:ss'),
                  ],
                }"
                allow-clear
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="6">
            <a-form-item
              :wrapper-col="{ span: 24, offset: 0 }"
              style="align-self: flex-end; text-align: left"
              label="&nbsp;"
            >
              <a-button type="primary" @click="onSubmit">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-columns">
      <div class="table-operate-box">
        <a-button
          style="margin: 0 15px 0 0"
          type="primary"
          @click="() => handleCreate()"
        >
          <plus-circle-outlined />
          新增快递模板
        </a-button>
      </div>
      <a-table
        :data-source="tableData"
        :loading="isLoading"
        :columns="shopColumns"
        :scroll="{ x: 1500 }"
        :pagination="pagination"
        :customRow="customRow"
        @change="pageChange"
      >
        <template #bodyCell="{ column, record }">
          <!-- 是否开启 -->
          <template v-if="column.key == 'status'">
            <a-switch
              @change="handleSwitch(record)"
              :checked="record.status === 1"
            />
          </template>
          <!-- 操作 -->
          <template v-if="column.key == 'operate'">
            <a-button
              type="link"
              class="btn-css"
              @click="handleChatAndEdit(record.groupId, 'edit')"
            >
              编辑
            </a-button>
            <a-button
              type="link"
              class="btn-css"
              @click="handleChatAndEdit(record.groupId, 'chat')"
            >
              查看
            </a-button>
            <a-button type="link" class="btn-css" @click="handleCopy(record)"> 副本 </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <router-view></router-view>
</template>
<script setup lang="ts">
import locale from "woody-ui/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import dayjs from "dayjs";
import { message } from "woody-ui";
import { ref, onMounted, reactive, watch } from "vue";
import { COLUMNS, FROM_DATA } from "./constants";
import {
  getPage,
  getGroupStatus,
  getCopyGroup,
} from "@/api/operation/expressLogistics";
import { PlusCircleOutlined } from "@ant-design/icons-vue";
import router from "@/router";

const isLoading = ref(true);
const formRef = ref(null);
const customRow = () => ({
  style: {
    height: "46px",
  },
});
const shopColumns = reactive(COLUMNS);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
  pageSizeOptions: ["10", "20", "50", "100"],
});
const tableData = ref([]);
const formData = reactive({
  groupName: undefined,
  createTime: undefined,
  updateTime: undefined,
});
const isDetail = ref(false);

watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (
      newVal.path ===
        "/operationManagement/supplyWarehouseManagement/expressLogistics/createTemplate" ||
      newVal.path ===
        "/operationManagement/supplyWarehouseManagement/expressLogistics/chanAndEditTemplate"
    ) {
      isDetail.value = true;
    } else {
      isDetail.value = false;
    }
  },
  {
    immediate: true,
  }
);
watch(
  () => isDetail.value,
  (newVal) => {
    if (!isDetail.value) {
      getPageList();
    }
  }
);
onMounted(async () => {
  getPageList();
});

// 获取表格板数据
const onSubmit = () => {
  pagination.current = 1;
  getPageList();
};

// 重置表单
const reset = () => {
  formRef.value.resetFields();
  setTimeout(() => {
    getPageList();
  }, 100);
};

// 分页逻辑
const pageChange = (event) => {
  const { current, pageSize } = event;
  pagination.current = current;
  pagination.pageSize = pageSize;
  getPageList();
};

// 获取分页数据
const getPageList = async () => {
  try {
    isLoading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      ...formData,
      createTimeStart: formData.createTime ? formData.createTime[0] : null,
      createTimeEnd: formData.createTime ? formData.createTime[1] : null,
      updateTimeStart: formData.updateTime ? formData.updateTime[0] : null,
      updateTimeEnd: formData.updateTime ? formData.updateTime[1] : null,
    };
    delete params.updateTime;
    delete params.createTime;
    const res = await getPage(params as any);
    isLoading.value = false;
    if (res.code !== 0) {
      return message.error(res.message);
    }
    tableData.value = res.data.records;
    pagination.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
};

//是否开启
const handleSwitch = async (e) => {
  const params = {
    groupId: e.groupId,
    status: e.status == 1 ? 0 : 1,
  };
  try {
    const res = await getGroupStatus(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
//新增快递模板
const handleCreate = () => {
  router.push({
    path: "/operationManagement/supplyWarehouseManagement/expressLogistics/createTemplate",
  });
};
//查看/编辑
const handleChatAndEdit = (id, text) => {
  router.push({
    path: `/operationManagement/supplyWarehouseManagement/expressLogistics/chanAndEditTemplate`,
    query: {
      groupId: id,
      type: text,
    },
  });
};
//副本

const handleCopy = async (e) => {
  const params = {
    groupId: e.groupId,
  };
  try {
    const res = await getCopyGroup(params);
    if (res.code !== 0) {
      return message.error(res.message);
    }
    message.success("生成副本成功");
    getPageList();
  } catch (error) {
    message.error(error.message);
  }
};
</script>
<style lang="less" scoped>
@import url("@/style/plat.less");
</style>
