<template>
  <div v-if="!isDetail">
    <search-antd :form-list="formList" @on-search="handleSearch" />
    <div class="table-css">
      <div class="btn-box">
        <a-button
          type="primary"
          @click="
            () => {
              createTemplate();
            }
          "
          class="ml10"
          >新增配送费模板</a-button
        >
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        class="mt10 table-list"
        :scroll="{ x: 1000 }"
        @change="handlePageChange($event)"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'transportName'">
            {{ text }}
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space>
              <a-button
                type="link"
                class="btn-css"
                @click="() => onEdit(record.transportId)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                class="btn-css"
                @click="() => onLook(record.transportId)"
                >查看</a-button
              >
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <router-view></router-view>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import { getSameCityListService } from "@/api/operation/supplyWarehouseManagement";
import { message } from "woody-ui";
import { formList, columns } from "./constants";
import { useRouter } from "vue-router";

const queryParams = ref({});
const currentId = ref();
const isDetail = ref(false);
const router = useRouter();

const handleSearch = (formData) => {
  const { createTime, updateTime, ...restFormData } = formData;
  const [createTimeStart, createTimeEnd] = createTime || [];
  const [updateTimeStart, updateTimeEnd] = updateTime || [];
  queryParams.value = {
    createTimeStart: createTimeStart
      ? `${createTimeStart} 00:00:00`
      : undefined,
    createTimeEnd: createTimeEnd ? `${createTimeEnd} 23:59:59` : undefined,
    updateTimeStart: updateTimeStart
      ? `${updateTimeStart} 00:00:00`
      : undefined,
    updateTimeEnd: updateTimeEnd ? `${updateTimeEnd} 23:59:59` : undefined,
    ...restFormData,
  };
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  fetchListData();
};

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`,
});

const dataSource = ref([]);
const loading = ref(false);

// 列表数据
const fetchListData = async () => {
  loading.value = true;
  const params = {
    ...queryParams.value,
    current: pagination.value.current,
    size: pagination.value.pageSize,
  };
  try {
    const res = await getSameCityListService(params);
    if (res.code !== 0) {
      return message.error(res.message || "请求失败!");
    }
    dataSource.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {
    message.error(error.message);
  }
  loading.value = false;
};

const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchListData();
};

const createTemplate = () => {
  currentId.value = "";
  router.push({
    path: "/operationManagement/supplyWarehouseManagement/sameCityLogistics/sameCityCreate",
    query: { type: "create" },
  });
};

const onEdit = (transportId) => {
  currentId.value = transportId;
  router.push({
    path: "/operationManagement/supplyWarehouseManagement/sameCityLogistics/sameCityCreate",
    query: { type: "edit", transportId },
  });
};

const onLook = (transportId) => {
  currentId.value = transportId;
  router.push({
    path: "/operationManagement/supplyWarehouseManagement/sameCityLogistics/sameCityLogisticsDetail",
    query: { type: "look", transportId },
  });
};

watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (
      newVal.path ===
        "/operationManagement/supplyWarehouseManagement/sameCityLogistics/sameCityCreate" ||
      newVal.path ===
        "/operationManagement/supplyWarehouseManagement/sameCityLogistics/sameCityLogisticsDetail"
    ) {
      isDetail.value = true;
    } else {
      isDetail.value = false;
      //1.代替onMounted 2.解决回退时不刷新的问题
      fetchListData();
    }
  },
  {
    immediate: true,
  }
);
</script>
<style lang="less" scoped>
.table-css {
  padding: 20px;
  background: #ffffff;
  margin-top: 16px;
  border-radius: 16px;
  margin-bottom: 16px;
  .btn-box {
    float: right;
    height: 40px;
    margin-bottom: 10px;
  }
}
.operate-text {
  color: #1a7af8;
  cursor: pointer;
}
</style>
