<template>
  <div class="search-form" style="height: 100%">
    <div class="createMain">
      <div class="title">
        <div class="title_main">基本信息</div>
        <div class="title_desc">
          商家选择自行配送时，用户支付的配送费用也会归属于商家
        </div>
      </div>
      <div class="createForm">
        <a-form
          :labelCol="{ span: 2 }"
          :wrapperCol="{ span: 22 }"
          autoComplete="off"
          layout="vertical"
        >
          <a-form-item
            label="模板名称:"
            name="templateName"
            required
            v-bind="validateInfos.templateName"
          >
            <a-input
              v-model:value="form.templateName"
              style="width: 200px"
              showCount
              :maxlength="10"
              placeholder="请输入内容"
              :disabled="queryParams.type === 'look'"
            />
          </a-form-item>
          <div style="margin-right: 5px; margin-top: 24px" class="red-start">
            配送费(元):
          </div>
          <div style="display: flex; align-items: center; margin-top: 8px">
            <a-form-item
              noStyle
              name="defaultDistance"
              v-bind="validateInfos.defaultDistance"
            >
              <a-input-number
                v-model:value="form.defaultDistance"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span class="pd24line">km内按</span>
            <a-form-item
              noStyle
              name="defaultFreight"
              v-bind="validateInfos.defaultFreight"
            >
              <a-input-number
                v-model:value="form.defaultFreight"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span class="pd24line">元来收取配送费，每超出</span>
            <a-form-item
              noStyle
              name="exceedDistance"
              v-bind="validateInfos.exceedDistance"
            >
              <a-input-number
                v-model:value="form.exceedDistance"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span class="pd24line">km，配送费增加</span>
            <a-form-item
              noStyle
              name="exceedDistancePrice"
              v-bind="validateInfos.exceedDistancePrice"
            >
              <a-input-number
                v-model:value="form.exceedDistancePrice"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span style="padding-left: 24px">元。</span>
          </div>
          <div style="margin-right: 5px; margin-top: 24px" class="red-start">
            续重收费:
          </div>
          <div style="margin-top: 8px; display: flex; align-items: center">
            <span style="margin-right: 24px">商品重量</span>
            <a-form-item
              noStyle
              name="defaultWeight"
              v-bind="validateInfos.defaultWeight"
            >
              <a-input-number
                v-model:value="form.defaultWeight"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span class="pd24line">kg内不额外收费，每超出</span>
            <a-form-item
              noStyle
              name="exceedWeight"
              v-bind="validateInfos.exceedWeight"
            >
              <a-input-number
                v-model:value="form.exceedWeight"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span class="pd24line">kg，续重费增加</span>
            <a-form-item
              noStyle
              name="exceedWeightPrice"
              v-bind="validateInfos.exceedWeightPrice"
            >
              <a-input-number
                v-model:value="form.exceedWeightPrice"
                style="width: 200px"
                :controls="true"
                :step="0.1"
                :disabled="queryParams.type === 'look'"
              />
            </a-form-item>
            <span style="padding-left: 24px">元。</span>
          </div>
        </a-form>
      </div>
    </div>
    <div
      style="
        position: fixed;
        bottom: 50px;
        width: calc(100% - 340px);
        text-align: center;
      "
    >
      <a-divider />
      <a-button
        style="margin-right: 10px"
        @click="
          () => {
            router.go(-1);
          }
        "
        >返回</a-button
      >
      <template v-if="queryParams.type !== 'look'">
        <a-button
          style="margin-right: 10px"
          @click="
            () => {
              resetFields();
            }
          "
          >重置</a-button
        >
        <a-button
          type="primary"
          @click="
            () => {
              handleSave();
            }
          "
          >保存</a-button
        >
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Form, message } from "woody-ui";
import { ref, onMounted, reactive } from "vue";
import {
  createSameCityService,
  getSameCityDetailService,
  ISameCityDetailResult,
  updateSameCityService,
} from "@/api/operation/supplyWarehouseManagement";
import router from "@/router";
import { useRoute } from "vue-router";
import debounce from "lodash/debounce";

const sameCityDetail = ref();
const route = useRoute();
const queryParams = route.query;

// 表单数据
let form = ref({
  templateName: "",
  defaultDistance: "",
  defaultFreight: "",
  exceedDistance: "",
  exceedDistancePrice: "",
  defaultWeight: "",
  exceedWeight: "",
  exceedWeightPrice: "",
});
const rules = reactive({
  templateName: [
    {
      required: true,
      message: "请输入模版名称",
    },
  ],
  defaultDistance: [
    {
      required: true,
      message: "",
    },
  ],
  defaultFreight: [
    {
      required: true,
      message: "",
    },
  ],
  exceedDistance: [
    {
      required: true,
      message: "",
    },
  ],
  exceedDistancePrice: [
    {
      required: true,
      message: "",
    },
  ],
  defaultWeight: [
    {
      required: true,
      message: "",
    },
  ],
  exceedWeight: [
    {
      required: true,
      message: "",
    },
  ],
  exceedWeightPrice: [
    {
      required: true,
      message: "",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

const handleSave = debounce(
  async () => {
    try {
      await validate();
      fetchSameSaveOrUpdateHandle();
    } catch (errorInfo) {
      // 验证失败的处理
      console.error("表单验证失败", errorInfo);
      message.error("请将信息填写完整");
    }
  },
  2500,
  { leading: true, trailing: false }
);

async function fetchSameSaveOrUpdateHandle() {
  try {
    const params = {
      isDefault: 0,
      ...form.value,
    };
    let result;
    if (queryParams.type === "create") {
      result = await createSameCityService(params);
    } else if (queryParams.type === "edit") {
      result = await updateSameCityService({
        ...params,
        id: Number(queryParams?.transportId),
        isDefault: sameCityDetail.value.isDefault ?? 0,
        shopId: sameCityDetail.value.shopId ?? "",
      });
    }
    if (result.code == 0) {
      message.success({
        content: "操作成功",
        onClose() {
          router.go(-1);
        },
      });
    } else {
      message.error(result.message);
    }
  } catch (error) {
    message.error((error as any).message);
  }
}

// 获取编辑回显数据
async function getSameCityInfoService() {
  try {
    const result = await getSameCityDetailService({
      id: Number(queryParams?.transportId),
    });
    if (result.code == 0) {
      sameCityDetail.value = result.data;
      const sameCityInfo: ISameCityDetailResult = result.data;
      form.value = { ...sameCityInfo } as any;
    } else {
      message.error(result.message);
    }
  } catch (error) {
    message.error((error as any).message);
  }
}

onMounted(async () => {
  if (queryParams.type === "edit" || queryParams.type === "look") {
    getSameCityInfoService();
  }
});
</script>

<style lang="less" scoped>
.createMain {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  .title {
    width: 100%;
    display: flex;
    align-items: center;
    height: 40px;
    background: rgba(242, 242, 242, 1);
    padding: 10px;
    box-sizing: border-box;
    border-radius: 4px;
    .title_main {
      height: 20px;
      line-height: 20px;
      margin-right: 20px;
      font-size: 18px;
      font-weight: 700;
      color: #333;
    }
    .title_desc {
      height: 20px;
      line-height: 30px;
      font-size: 14px;
      color: #999;
    }
  }
}
.createForm {
  width: 100%;
  margin-top: 20px;
}

.red-start::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
.pd24line {
  padding: 0 24px;
}
@import url("@/style/plat.less");
</style>
