<template>
  <div class="detail-container">
    <sub-title title="基本信息" style="margin-bottom: 12px" />
    <span style="color: #636d7e"
      >商家选择自行配送时，用户支付的配送费用也会归属于商家</span
    >
    <div class="section-line item-wrap">
      <div class="item">
        <div class="label">模板名称</div>
        <div>{{ sameCityDetail?.templateName }}</div>
      </div>
      <div class="item">
        <div class="label">配送费(元)</div>
        <div>
          <span style="font-weight: bold">{{
            sameCityDetail?.defaultDistance
          }}</span
          >km内按<span style="font-weight: bold">{{
            sameCityDetail?.defaultFreight
          }}</span
          >元来收取配送费，每超出<span style="font-weight: bold">{{
            sameCityDetail?.exceedDistance
          }}</span
          >km，配送费增加<span style="font-weight: bold">
            {{ sameCityDetail?.exceedDistancePrice }} </span
          >元。
        </div>
      </div>
      <div class="item">
        <div class="label">续重收费</div>
        <div>
          商品重量<span style="font-weight: bold">
            {{ sameCityDetail?.defaultWeight }} </span
          >kg内不额外收费，每超出<span style="font-weight: bold">{{
            sameCityDetail?.exceedWeight
          }}</span
          >kg，续重费增加
          <span style="font-weight: bold">{{
            sameCityDetail?.exceedWeightPrice
          }}</span
          >元。
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import SubTitle from "@/components/SubTitle.vue";
import { getSameCityDetailService } from "@/api/operation/supplyWarehouseManagement";
import { useRoute } from "vue-router";
import { message } from "woody-ui";

const route = useRoute();
const queryParams = route.query;
const sameCityDetail = ref();

onMounted(async () => {
  if (queryParams.type === "look") {
    getSameCityInfoService();
  }
});

// 获取详情数据
async function getSameCityInfoService() {
  try {
    const result = await getSameCityDetailService({
      id: Number(queryParams?.transportId),
    });
    if (result.code == 0) {
      sameCityDetail.value = result.data;
    } else {
      message.error(result.message);
    }
  } catch (error) {
    message.error((error as any).message);
  }
}
</script>
<style lang="less" scoped>
.detail-container {
  padding: 32px;
  background-color: #fff;
  border-radius: 16px;
  margin-bottom: 16px;
  position: relative;
  height: 100vh;
  .section-line {
    display: flex;
    margin-top: 24px;

    .item {
      max-width: 500px;
      margin-right: 42px;
    }
  }
}
</style>
