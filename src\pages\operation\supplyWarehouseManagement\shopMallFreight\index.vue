<template>
  <div class="search-form" style="height: 100%">
    <div class="freightDesc">
      <div class="freightDescMain">
        <div class="mainTitle">运费补贴说明：</div>
        <div class="mainContent">
          根据平台拆单规则用户一笔订单下可能产生多笔运费（含快递物流及同城配送费用）
        </div>
      </div>
    </div>
    <div class="freightForm">
      <a-form layout="vertical">
        <a-form-item
          labelAlign="right"
          name="logisticsFlag"
          label="物流费用补贴:"
          valuePropName="checked"
          v-bind="validateInfos.logisticsFlag"
        >
          <a-switch v-model:checked="form.logisticsFlag" />
        </a-form-item>
        <a-form-item
          labelAlign="right"
          label="补贴金额&起始金额:"
          v-bind="validateInfos.logisticsAmount"
        >
          <div class="formItem">
            <a-form-item noStyle name="logisticsAmount">
              <a-input
                v-model:value="form.logisticsAmount"
                class="inputStyle"
                placeholder="请输入补贴金额&起始金额"
                addonAfter="元"
              />
            </a-form-item>
            <div class="inputDesc">
              <span style="color: red">*</span>
              设置后，用户订单配送费合计大于设置金额才会触发补贴；且补贴金额最大为设置金额
            </div>
          </div>
        </a-form-item>
        <a-form-item
          labelAlign="right"
          label="配送费用补贴:"
          name="deliveryFlag"
          valuePropName="checked"
          v-bind="validateInfos.deliveryFlag"
        >
          <a-switch v-model:checked="form.deliveryFlag" />
        </a-form-item>
        <a-form-item
          labelAlign="right"
          label="补贴金额&起始金额:"
          v-bind="validateInfos.deliveryAmount"
        >
          <div class="formItem">
            <a-form-item name="deliveryAmount" noStyle>
              <a-input
                v-model:value="form.deliveryAmount"
                class="inputStyle"
                addonAfter="元"
                placeholder="请输入补贴金额&起始金额"
              />
            </a-form-item>
            <div class="inputDesc">
              <span style="color: red">*</span>
              设置后，用户订单配送费合计大于设置金额才会触发补贴；且补贴金额最大为设置金额
            </div>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <div
      style="
        position: fixed;
        bottom: 50px;
        width: calc(100% - 340px);
        text-align: center;
      "
    >
      <a-divider />
      <a-button
        style="margin-right: 10px"
        @click="() => {}"
        >返回</a-button
      >
      <a-button
        style="margin-right: 10px"
        @click="
          () => {
            fetchGetSubsidyInfo();
          }
        "
        >重置</a-button
      >
      <a-button
        type="primary"
        @click="
          () => {
            fetchCreateOrUpdataSubsidy();
          }
        "
        >保存</a-button
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Form, message } from "woody-ui";
import { onMounted, reactive, ref } from "vue";
import {
  createOrUpdataSubsidyervice,
  ICreateOrUpdataSubsidy,
  getSubsidyInfoService,
} from "@/api/operation/supplyWarehouseManagement";

// 表单数据
let form = ref({
  id: "",
  logisticsFlag: false,
  logisticsAmount: "",
  deliveryFlag: false,
  deliveryAmount: "",
});
const rules = reactive({
  logisticsFlag: [
    {
      required: true,
      message: "",
    },
  ],
  logisticsAmount: [
    {
      required: true,
      message: "请输入补贴金额&起始金额",
    },
  ],
  deliveryFlag: [
    {
      required: true,
      message: "",
    },
  ],
  deliveryAmount: [
    {
      required: true,
      message: "补贴金额&起始金额",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(form, rules, {
  onValidate: (...args) => console.log(...args),
});

onMounted(() => {
  fetchGetSubsidyInfo();
});

const subsidyInfo = ref();
// 获取详情数据
async function fetchGetSubsidyInfo() {
  try {
    const result = await getSubsidyInfoService();
    if (result.code === 0) {
      subsidyInfo.value = result.data;
      form.value = {
        id: result.data.id,
        logisticsFlag: result.data.logisticsFlag == 1,
        logisticsAmount: result.data.logisticsAmount,
        deliveryFlag: result.data.deliveryFlag == 1,
        deliveryAmount: result.data.deliveryAmount,
      };
    }
  } catch (error) {
    message.error((error as any).message);
  }
}

// 创建、更新数据
async function fetchCreateOrUpdataSubsidy() {
  try {
    await validate();
    handleCreateOrUpdate();
  } catch (errorInfo) {
    // 验证失败的处理
    console.error("表单验证失败", errorInfo);
  }
}

const handleCreateOrUpdate = async () => {
  let formParams: ICreateOrUpdataSubsidy = {
    logisticsFlag: Number(form.value.logisticsFlag),
    logisticsAmount: Number(form.value.logisticsAmount),
    deliveryFlag: Number(form.value.deliveryFlag),
    deliveryAmount: Number(form.value.deliveryAmount),
    id: subsidyInfo.value?.id,
  };
  try {
    const result = await createOrUpdataSubsidyervice(formParams);
    if (result.code == 0) {
      message.success({
        content: "操作成功",
        onClose() {
          fetchGetSubsidyInfo();
        },
      });
    } else {
      message.error(result.message);
    }
  } catch (error) {
    message.error((error as any).message);
  }
};
</script>
<style scoped lang="scss">
.freightDesc {
  margin: 20px 0;
  width: 100%;
  padding: 0 30px 0 30px;
  box-sizing: border-box;
  .freightDescMain {
    width: 100%;
    padding: 10px 20px;
    box-sizing: border-box;
    background: #f5f7fa;
    border-radius: 5px;
    .mainTitle {
      font-size: 26px;
      font-weight: 700;
      color: #333;
    }
    .mainContent {
      width: 100%;
      /* padding-left: 180px; */
      padding-right: 80px;
      box-sizing: border-box;
      font-size: 15px;
      color: #666;
      line-height: 30px;
    }
  }
}
.freightForm {
  width: 100%;
  padding: 0 30px 0 30px;
  box-sizing: border-box;
  .formItem {
    display: flex;
    align-items: flex-end;
  }
  .inputStyle {
    width: 200px;
  }
  .ant-form-item-label {
    width: 160px;
    margin-right: 10px;
  }
  .inputDesc {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    margin-left: 10px;
  }
}
@import url("@/style/plat.less");
</style>
