<template>
  <a-modal
    v-model:open="disAgreeDia.visible"
    title="驳回"
    :destroy-on-close="true"
    width="35%"
    class="disagree-modal"
    @cancel="cancelClick"
  >
    <div class="dia-box">
      <div class="f-item">
        <div class="item-name">驳回原因<span class="require">*</span></div>
        <a-select
          v-model:value="disAgreeDia.data.rejectReason"
          allow-clear
          placeholder="请选择驳回原因"
          style="width: 100%"
        >
          <a-select-option
            v-for="(item, index) in reasonList"
            :key="index"
            :value="item.value"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <div class="f-item" style="margin-bottom: 4px">
        <div class="item-name">备注<span class="require">*</span></div>
        <a-textarea
          v-model:value="disAgreeDia.data.rejectNote"
          class="textarea"
          :maxlength="200"
          :show-count="true"
          placeholder="请输入驳回原因"
        />
      </div>
      <div class="f-item">
        <div class="item-name">图片</div>
        <div class="upload-flex">
          <wd-upload
            biz-type="in_coming"
            :file-size="1"
            :max-width="750"
            :max-count="1"
            @get-url-list="afferentUrlChange"
            :file-list="fileList"
          />
          <p>支持.jpg、.gif、.jpeg、.png格式，最多1张，上传图片宽度限制为750px，大小限制1M</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div style="text-align: right;">
        <a-button
          type="primary"
          :loading="disAgreeDia.loading"
          @click="submitDisAgree"
          style="margin-right: 8px;"
        >确定</a-button>
        <a-button @click="cancelClick">取消</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { message } from "woody-ui";
import { ref, watch, onUnmounted } from "vue";
import { httpBatchReject, httpReject } from "@/api/product";
import { getQueryDictItems } from "@/api/common";
import { auditListTs } from "../constants";
import WdUpload from "@/components/WdUpload/index.vue";

const props = defineProps({
  disagreeData: {
    type: Object,
    default: () => ({}),
  },
  auditList: {
    type: Array<auditListTs>,
    default: () => [],
  },
});

const emits = defineEmits(["onSucceed"]);

const accept = ref(".png,.gif,.jpg");

const reasonList = ref([]);
const fileList = ref([]);
// 获取驳回原因
const getReasonList = (code: String) => {
  const params = {
    dictCode: code,
  };
  getQueryDictItems(params)
    .then((res) => {
      reasonList.value = res.data;
    })
    .catch((err) => {
      console.log(err);
    });
};

const disAgreeDia = ref({
  visible: false,
  data: {
    rejectReason: "",
    rejectNote: "",
    rejectImageUrl: "",
  },
  loading: false,
});

const cancelClick = () => {
  disAgreeDia.value.visible = false;
};
const uploadRef = ref();
// 判断是否是批量审核
const isBatchAudit = ref<Boolean>(false);
const open = (code: String, isAudit: Boolean) => {
  disAgreeDia.value = {
    visible: true,
    data: {
      rejectReason: "",
      rejectNote: "",
      rejectImageUrl: "",
    },
    loading: false,
  };
  isBatchAudit.value = isAudit;
  getReasonList(code);
  // uploadRef.value.handleClear();
};
const updates = ref("");
const timer = ref(null);
const afferentUrlChange = (data) => {
  if (data && data.length) {
    disAgreeDia.value.loading = true;
    disAgreeDia.value.data.rejectImageUrl = data[0].url;
    fileList.value = [{ name: "", url: data[0].url }];
    timer.value = setTimeout(()=>{
      disAgreeDia.value.loading = false;
    },1500)
  } else {
    disAgreeDia.value.data.rejectImageUrl = "";
    fileList.value = [];
  }
};
const submitDisAgree = () => {
  if (!disAgreeDia.value.data.rejectReason)
    return message.error("请选择驳回原因");
  if (!disAgreeDia.value.data.rejectNote)
    return message.error("请输入驳回原因");

  disAgreeDia.value.loading = true;
  if (isBatchAudit.value) {
    const list = [];
    props.auditList.forEach((item) => {
      const info = {
        auditId: item.auditId,
        nodeId: item.nodeId,
        rejectReason: disAgreeDia.value.data.rejectReason,
        rejectNote: disAgreeDia.value.data.rejectNote,
        rejectImageUrl: disAgreeDia.value.data.rejectImageUrl,
      };
      list.push(info);
    });
    httpBatchReject(list)
      .then((res) => {
        if (res.code === 0) {
          disAgreeDia.value.loading = false;
          disAgreeDia.value.visible = false;
          message.success("提交成功");
          fileList.value = [];
          onSucceed();
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        disAgreeDia.value.loading = false;
      });
  } else {
    const { auditId, nodeId } = props.disagreeData;
    httpReject({ ...disAgreeDia.value.data, auditId, nodeId })
      .then((res) => {
        if (res.code === 0) {
          disAgreeDia.value.loading = false;
          disAgreeDia.value.visible = false;
          message.success("提交成功");
          fileList.value = [];
          onSucceed();
        }
      })
      .finally(() => {
        disAgreeDia.value.loading = false;
      });
  }
};

// 驳回成功
const onSucceed = () => {
  emits("onSucceed");
};
onUnmounted(() =>{
  timer.value = null
});

defineExpose({
  open,
});
</script>

<style lang="less">
.disagree-modal {
  .ant-modal-header {
    padding: 0 32px 0 0;
    height: 40px;
    color: #05082c;
  }
}
</style>

<style lang="less" scoped>
.dia-box {
  margin: 16px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #05082c;
    margin-bottom: 16px;
  }
  .sub-title {
    font-size: 14px;
    color: #495366;
    white-space: nowrap;
  }
}
.f-item {
  margin-bottom: 24px;
  .item-name {
    margin-bottom: 8px;
    font-size: 14px;
    color: #05082c;
    .require {
      font-size: 14px;
      color: #ff436a;
      margin-left: 3px;
    }
  }
}
.upload-flex {
  p {
    margin-top: 8px;
    font-size: 12px;
    color: #8592a6;
  }
}
</style>
