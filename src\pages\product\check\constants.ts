export const EXAMINE_COLUMNS = [
  {
    title: "商品ID",
    dataIndex: "prodId",
    key: "prodId",
    fixed: true,
    align: "left",
    width: 200,
  },
  {
    title: "商品来源",
    dataIndex: "prodSource",
    key: "prodSource",
    align: "left",
    width: 200,
  },
  {
    title: "供货仓",
    dataIndex: "supplierName",
    key: "supplierName",
    align: "left",
    width: 200,
  },
  {
    title: "商品信息",
    dataIndex: "goodsInfo",
    key: "goodsInfo",
    align: "left",
    width: 200,
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
    key: "applyTime",
    align: "left",
    width: 200,
  },
  {
    title: "审核状态",
    dataIndex: "auditStatusName",
    key: "auditStatusName",
    align: "left",
    width: 200,
  },
  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
    align: "left",
    fixed:"right",
    width: 200,
  },
];

export const SKU_COLUMNS = [
  {
    title: '成本价(元)',
    dataIndex: 'costPrice',
    key: 'costPrice',
    align: "left",
    fixed:"left",
    width: 100,
  },
  {
    title: '市场价(元)',
    dataIndex: 'oriPrice',
    key: 'oriPrice',
    align: "left",
    width: 100,
  },
  {
    title: '销售价(元)',
    dataIndex: 'price',
    key: 'price',
    align: "left",
    width: 100,
  },
  {
    title: '库存',
    dataIndex: 'stocks',
    key: 'stocks',
    align: "left",
    width: 100,
  },
  {
    title: '规格',
    dataIndex: 'prodName',
    key: 'prodName',
    align: "left",
    width: 100,
  },
  {
    title: '商品体积(m³)',
    dataIndex: 'volume',
    key: 'volume',
    align: "left",
    width: 100,
  },
  {
    title: '商品重量(kg)',
    dataIndex: 'weight',
    key: 'weight',
    align: "left",
    width: 100,
  },
  {
    title: '国标码',
    dataIndex: 'skuNumber',
    key: 'skuNumber',
    align: "left",
    width: 100,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    align: "left",
    width: 100,
  },
];

export const prodSourceArr = [
  {
    label: '我店生活',
    value: 0,
  },
  {
    label: '我店优选',
    value: 7,
  },
];

export const auditTypes = [
  {
    label: '系统',
    name: 'system',
    value: 1,
  },
  {
    label: '营销',
    name: 'marketing',
    value: 2,
  },
  {
    label: '风控',
    name: 'risk',
    value: 3,
  },
  {
    label: '运营',
    name: 'operation',
    value: 4,
  },
];

// 驳回原因
export const causeOfRejection = {
  // 营销
  marketerRejectCause: 'MARKETER_REJECT_CAUSE',
  // 风控
  riskControllerRejectCause: 'RISK_CONTROLLER_REJECT_CAUSE',
};
// 审核传参
export interface auditListTs {
  // 审核id
  auditId: String;
  // 审核节点id
  nodeId: String;
}
