<template>
  <div class="detail-box">
    <div class="divider1"></div>
    <!-- 商品类型 -->
    <div class="detail-sec small-sec">
      <div class="title-box">
        <div class="line"></div>
        <div class="text">商品类型</div>
      </div>
      <div class="item">
        <div class="name">商品类型</div>
        <div class="value">实物商品（物流发货）</div>
      </div>
    </div>
    <!-- 配送方式 -->
    <div
      class="detail-sec small-sec"
      v-if="!isEmptyValue(detail.deliveryModelDesc)"
    >
      <div class="title-box">
        <div class="line"></div>
        <div class="text">配送方式</div>
      </div>
      <a-row :gutter="40">
        <a-col :span="5">
          <div
            :class="['item', isDiffFile.includes('deliveryModel') ? 'new' : '']"
          >
            <div class="name">配送方式</div>
            <div class="value">{{ detail.deliveryModelDesc }}</div>
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- 平台分类 -->
    <div
      class="detail-sec small-sec"
      v-if="
        !isEmptyValue(detail.platParentCategoryName) ||
        !isEmptyValue(detail.platCategoryName)
      "
    >
      <div class="title-box">
        <div class="line"></div>
        <div class="text">后台分类</div>
      </div>
      <a-row :gutter="40">
        <a-col :span="5">
          <div
            :class="[
              'item',
              isDiffFile.includes('platCategoryId') ? 'new' : '',
            ]"
          >
            <div class="name">后台分类</div>
            <div class="value">
              {{ detail.topPlatCategoryName }}/{{
                detail.platParentCategoryName
              }}/{{ detail.platCategoryName }}
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- 基本信息 -->
    <div class="detail-sec small-sec">
      <div class="title-box">
        <div class="line"></div>
        <div class="text">基本信息</div>
      </div>
      <a-row :gutter="[40, 40]">
        <a-col :span="4" v-if="!isEmptyValue(detail.prodName)">
          <div :class="['item', isDiffFile.includes('prodName') ? 'new' : '']">
            <div class="name">商品名称</div>
            <div class="value">{{ detail.prodName }}</div>
          </div>
        </a-col>
        <a-col :span="4" v-if="!isEmptyValue(detail.buyingPointTags)">
          <div
            :class="[
              'item',
              isDiffFile.includes('buyingPointTags') ? 'new' : '',
            ]"
          >
            <div class="name">卖点标签</div>
            <div class="value">{{ detail.buyingPointTags }}</div>
          </div>
        </a-col>
        <a-col :span="4" v-if="!isEmptyValue(detail.brief)">
          <div :class="['item', isDiffFile.includes('brief') ? 'new' : '']">
            <div class="name">商品卖点</div>
            <div class="value">{{ detail.brief }}</div>
          </div>
        </a-col>
        <a-col :span="4" v-if="!isEmptyValue(detail.brandName)">
          <div :class="['item', isDiffFile.includes('brandId') ? 'new' : '']">
            <div class="name">商品品牌</div>
            <div class="value">{{ detail.brandName }}</div>
          </div>
        </a-col>
        <a-col :span="4">
          <div
            :class="[
              'item',
              isDiffFile.includes('afterSalePeriod') ? 'new' : '',
            ]"
          >
            <div class="name">售后期限</div>
            <div class="value">
              {{
                detail.afterSalePeriod === 0
                  ? "不支持售后"
                  : detail?.afterSalePeriod + "天内可发起售后"
              }}
            </div>
          </div>
        </a-col>
        <a-col :span="4" v-if="!isEmptyValue(detail.prodTags)">
          <div :class="['item', isDiffFile.includes('prodTags') ? 'new' : '']">
            <div class="name">近义词</div>
            <div style="display: flex; align-items: center">
              <div
                v-for="(prodTag, index) in detail.prodTags"
                :key="index"
                class="value"
              >
                {{ prodTag.prodTag }}
                <span
                  v-if="
                    detail.prodTags &&
                    detail.prodTags?.length > 0 &&
                    index !== detail.prodTags?.length - 1
                  "
                  >、</span
                >
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
      <div
        class="divider"
        v-if="detail.picInfos && detail.picInfos.length"
      ></div>
      <div
        :class="['item', isDiffFile.includes('imgIds') ? 'new' : '']"
        v-if="detail.picInfos && detail.picInfos.length"
      >
        <div class="name">商品图片</div>
        <div v-if="detail.picInfos && detail.picInfos.length">
          <image-viewer
            :images="detail.picInfos?.map((img: any) => img.path)"
          />
        </div>
      </div>
      <div class="divider" v-if="!isEmptyValue(detail.videoInfo?.path)"></div>
      <div
        :class="['item', isDiffFile.includes('videoId') ? 'new' : '']"
        v-if="!isEmptyValue(detail.videoInfo?.path)"
      >
        <div class="name">商品视频</div>
        <video :width="324" :src="detail.videoInfo?.path" controls>
          您的浏览器不支持 video 标签。
        </video>
      </div>
    </div>
    <!-- 规格库存 -->
    <div class="detail-sec" v-if="detail.skuList && detail.skuList.length > 0">
      <div class="title-box2">
        <div class="name-box">
          <div class="line"></div>
          <div class="text">规格库存</div>
        </div>
        <div v-if="isDiffFile.includes('skuList')" class="new-box">
          <div class="text1">新</div>
          <div class="text2">内容有更新</div>
        </div>
      </div>
      <div class="item">
        <div class="name">价格及库存</div>
        <a-table min-width="2000" :data-source="detail.skuList" :columns="SKU_COLUMNS" :scroll="{ x: 1500 }">
          <template #bodyCell="{ column, record }">
            <div v-if="column.key === 'prodName'" style="display: flex; align-items: center; column-gap: 10px">
              <img alt="" :src="record?.picInfo?.path" width="50" height="50" />
              <div>{{ record?.properties }}</div>
            </div>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 商品详情 -->
    <div class="detail-sec">
      <div class="title-box2">
        <div class="name-box">
          <div class="line"></div>
          <div class="text">商品详情</div>
        </div>
        <div v-if="isDiffFile.includes('content')" class="new-box">
          <div class="text1">新</div>
          <div class="text2">内容有更新</div>
        </div>
      </div>
      <div
        v-if="detail.content"
        class="detail-content"
        v-html="detail.content"
      />
      <div v-else class="empty-box">
        <img
          :src="`${VITE_API_IMG}/2024/11/2f2031ef877f479f8b0144dd0fdb74d1.png`"
          alt=""
          class="pic"
        />
        <div class="text">暂无商品详情</div>
      </div>
    </div>
    <div class="bottom-btns">
      <a-button class="btn mr10" @click="onBack"
        >返回</a-button
      >
      <a-button
        type="primary"
        class="btn mr10"
        v-if="detailType !== '1'"
        @click="openDisAgreeDia"
        >驳回</a-button
      >
      <a-button
        type="primary"
        class="btn"
        v-if="detailType !== '1'"
        @click="showConfirm"
        >同意</a-button
      >
    </div>
  </div>

  <!-- 弹窗-驳回 -->
  <dis-agree-dia
    ref="disAgreeRef"
    :disagree-data="disAgreeData"
    @on-succeed="onSucceed"
  />

  <!-- 弹窗-同意 -->
  <!-- <a-modal
    v-model:visible="agreeVisible"
    theme="danger"
    title="通过"
    @ok="handleAgree"
  >
    <div class="back-box">
      <div class="title">确定通过吗？</div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleAgree">确定</a-button>
      <a-button @click="agreeVisible = false"
        >取消</a-button
      >
    </template>
  </a-modal> -->
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import { message } from "woody-ui";
import { SKU_COLUMNS, causeOfRejection } from "./constants";
import disAgreeDia from "./components/disAgreeDia.vue";
import ImageViewer from "@/components/ImageViewer/index.vue";
import { httpApproval, httpDetails, httpExamDetail } from "@/api/product";
import router from "@/router";
import { isEmptyValue } from "@/utils";

import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { Modal } from 'woody-ui';

const route = useRoute();
const { nodeType, productId, auditType, auditId, nodeId } = route.params;
const detailType = ref(nodeType);
const type = ref(auditType);

// 详情
const detail = ref<any>({});
const isDiffFile = ref<string[]>([]);
const getDetail = async () => {
  if (type.value === "3") {
    const res = await httpExamDetail({ productId });
    if (res.code === 0) {
      detail.value = res.data.platProductVO;
      isDiffFile.value = res.data?.diffField;
    }
  } else {
    const res = await httpDetails({ productId });
    if (res.code === 0) {
      detail.value = res.data;
    }
  }
};
onMounted(() => {
  getDetail();
});

const showConfirm = () => {
  Modal.confirm({
    title: '确定通过吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      handleAgree()
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};

// 驳回
const disAgreeRef = ref();
const disAgreeData = reactive<any>({
  auditId: "",
  nodeId: "",
});
const openDisAgreeDia = () => {
  disAgreeData.auditId = auditId;
  disAgreeData.nodeId = nodeId;
  let code;
  if (detailType.value === "2") {
    code = causeOfRejection.marketerRejectCause;
    disAgreeRef.value.open(code, false);
  }
  if (detailType.value === "3") {
    code = causeOfRejection.riskControllerRejectCause;
    disAgreeRef.value.open(code, false);
  }
};

// 驳回成功
const onSucceed = () => {
  onBack();
};
const handleAgree = () => {
  const params = {
    auditId,
    nodeId,
  };
  httpApproval(params)
    .then(() => {
      message.success("提交成功");
      onBack();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 返回
const onBack = () => {
  router.go(-1);
};
</script>

<style lang="less" scoped>
.detail-box {
  background-color: #ffffff;
  border-radius: 10px;
}
.detail-sec {
  margin-left: 16px;
  margin-bottom: 40px;
}
.small-sec {
  width: 80%;
}
.title-box {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  .line {
    width: 4px;
    height: 16px;
    background: #1a7af8;
    border-radius: 4px;
    margin-right: 8px;
  }
  .text {
    font-weight: bold;
    font-size: 20px;
    color: #05082c;
  }
}
.item {
  padding: 0 12px;
  position: relative;
  .name {
    font-size: 14px;
    color: #636d7e;
    margin-bottom: 8px;
  }
  .value {
    font-size: 14px;
    color: #05082c;
  }
}
.new {
  background: #f0f9ff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #d8ecff;
  padding: 8px;
  box-sizing: border-box;
  &::after {
    content: "新";
    background-color: red;
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 20px;
    background: #ff436a;
    border-radius: 0px 7px 0px 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 12px;
  }
}
.divider {
  height: 40px;
}
.divider1 {
  height: 32px;
}
.goods-img {
  width: 110px;
  height: 110px;
  background: #f1f6f8;
  border-radius: 15px 15px 15px 15px;
  margin-right: 16px;
  margin-bottom: 16px;
}
.detail-content {
  margin-bottom: 120px;
}
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: -30px;
  margin-bottom: 120px;
  .pic {
    width: 100px;
    height: 100px;
    margin-bottom: -6px;
  }
  .text {
    font-size: 14px;
    color: #a2abbd;
  }
}
.bottom-btns {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #f2f5f9;
  padding: 12px;
  background-color: #fff;
  margin-left: -16px;
  .btn {
    position: relative;
    left: -114px;
  }
}
video {
  border-radius: 15px;
}
.dia-box {
  margin: 16px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #05082c;
    margin-bottom: 16px;
  }
  .sub-title {
    font-size: 14px;
    color: #495366;
    white-space: nowrap;
  }
}
.title-box2 {
  height: 48px;
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  .name-box {
    margin-right: 20px;
    display: flex;
    align-items: center;
    .line {
      width: 4px;
      height: 16px;
      background: #1a7af8;
      border-radius: 4px;
      margin-right: 8px;
    }
    .text {
      font-weight: bold;
      font-size: 20px;
      color: #05082c;
    }
  }
  .new-box {
    height: 48px;
    display: flex;
    align-items: center;
    flex: 1;
    // background: #ecf7ff;
    background: #f0f9ff;
    border-radius: 6px;
    padding: 0 15px;
    .text1 {
      width: 20px;
      height: 20px;
      background: #ff436a;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #fff;
      margin-right: 10px;
    }
    .text2 {
      color: #05082c;
    }
  }
}
.back-box {
  margin: 16px 32px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #05082c;
    margin-bottom: 16px;
  }
  .sub-title {
    font-size: 14px;
    color: #495366;
    white-space: nowrap;
  }
}

:deep(th) {
  background-color: @table-th-bg !important;
  color: @table-th-color;
  font-weight: 400;

  &:first-child {
    border-top-left-radius: 8px;
  }

  &:last-child {
    border-top-right-radius: 8px;
  }
}

:deep(td) {
  border-bottom-color: @table-boder-color;
}

:deep(.t-table__pagination) {
  padding-top: 28px;

  .t-pagination {
    .t-pagination__jump {
      margin-left: 16px;
    }
  }
}
</style>
