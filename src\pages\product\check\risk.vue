<template>
  <div v-if="!isSubPage">
    <search-antd
      :form-list="formList"
      @on-search="handleSearch"
    />
    <!-- table  -->
    <div class="section mt10">
      <div class="batch-audit">
        <a-button :disabled="auditList.length === 0" @click="batchAgree"
          >批量同意</a-button
        >
        <a-button :disabled="auditList.length === 0" class="ml10" @click="batchReject"
          >批量驳回</a-button
        >
      </div>
      <a-table
        table-layout="fixed"
        :data-source="examineTable.data.records"
        :columns="examineColumns"
        :rowKey="(record) => record"
        :scroll="{ x: 1000 }"
        :row-selection="rowSelection"
        :pagination="pagination"
        :loading="searchExamineLoading"
        @change="changeExaminePage"
      >
      <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'prodSource'">
            <div>{{ record.prodSource === 0 ? "我店生活" : "我店优选" }}</div>
          </template>
          <template v-if="column.key === 'goodsInfo'">
            <div style="display: flex; align-items: center; column-gap: 10px">
            <div class="tdesign-demo-image-viewer__base">
              <a-image :width="80" :src="record.picUrl"></a-image>
            </div>
            <div class="item-prodName">{{ record.prodName }}</div>
          </div>
          </template>
          <template v-if="column.key === 'auditStatusName'">
            <a-tag
              size="large"
              color="blue"
              v-if="record.auditType === 1"
              >{{ record.auditStatusName }}</a-tag
            >
            <a-tag size="large" color="blue" v-else>{{
              record.auditStatusName
            }}</a-tag>
          </template>
          <template v-if="column.key === 'operation'">
            <a-button type="link" class="btn-css" @click="goDetail(record)">查看</a-button>
            <a-button type="link" class="btn-css" @click="openAgreeVisible(record)">同意</a-button>
            <a-button type="link" class="btn-css" @click="openDisAgreeDia(record)">驳回</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <router-view></router-view>

  <!-- 弹窗-驳回 -->
  <dis-agree-dia ref="disAgreeRef" :disagree-data="disAgreeData" :audit-list="auditList" @on-succeed="onSucceed" />

  <!-- 弹窗-同意 -->
  <a-modal v-model:visible="agreeVisible" theme="danger" title="通过" @ok="handleAgree">
    <div class="back-box">
      <div class="title"><InfoCircleOutlined :style="{fontSize: '16px', color: '#faad14'}" class="mr10"/>确定通过吗？</div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleAgree">确定</a-button>
      <a-button @click="agreeVisible = false">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch,computed } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'woody-ui';
import { EXAMINE_COLUMNS, auditTypes, prodSourceArr, causeOfRejection, auditListTs } from './constants';
import { ExamineTableData, examineParams } from '@/types/product/index';
import { httpApproval, httpBatchApproval, httpExamList, httpSupplierList } from '@/api/product';
import disAgreeDia from './components/disAgreeDia.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import SearchAntd from "@/components/SearchAntd/index.vue";

const AUDIT_TYPE = auditTypes.find((item) => item.name === 'risk').value;

const sourceArr = ref([]);
let formData = {};
const remoteMethod = (search) => {
  setTimeout(() => {
    searchSource(search);
  }, 1000);
};
const formList = [
  {
    label: "商品来源",
    name: "prodSource",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: prodSourceArr,
    showSearch:true,
    needFilter: true,
    span: 6,
  },
  {
    label: "商品名称",
    name: "prodName",
    type: "input", // 输入框
    span: 6,
  },
  {
    label: "商品ID",
    name: "prodIds",
    type: "input", // 输入框
    span: 6,
  },
  {
    type: "rangePicker",
    label: "申请时间",
    name: "time",
    // showTime: true,
    span: 6,
  },
  {
    label: "供货仓",
    name: "supplierIds",
    type: "select", // 下拉框
    placeholder: "请选择",
    options: sourceArr,
    showSearch:true,
    needFilter: true,
    remoteMethod,
    mode:'multiple',
    maxTagCount:2,
    span: 6,
  },
];
//  table-列表
const searchExamineLoading = ref(false);
const examineColumns = EXAMINE_COLUMNS;
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共${total}条数据`,
});
const examineTable = reactive(new ExamineTableData());
const selectedRowKeys = ref([]);
// 批量审核传参
const auditList = ref<Array<auditListTs>>([]);
// 列表复选框 事件
const onSelectChange = (newSelectedRowKeys) => {
  auditList.value = [];
  selectedRowKeys.value = newSelectedRowKeys
  newSelectedRowKeys.forEach((item: any) => {
    const info = {
      auditId: item.auditId,
      nodeId: item.nodeId,
    };
    auditList.value.push(info);
  });
};
const rowSelection = computed(() => ({
  type: "checkout",
  selectedRowKeys,
  onChange: onSelectChange,
  getCheckboxProps: (record) => ({
    disabled: record.checked === true, // Column configuration not to be checked
  }),
}));
const getExamineData = () => {
  searchExamineLoading.value = true;
  const params = {
    page:pagination.value.current,
    size:pagination.value.pageSize,
    productAuditNodeType:AUDIT_TYPE,
    ...formData,
    
  }
  httpExamList(params)
    .then((res) => {
      if (res.code === 0) {
        const { records, total } = res.data;
        examineTable.data = { records, total };
        pagination.value = { ...pagination.value, total };
      }
    })
    .finally(() => {
      searchExamineLoading.value = false;
    });
};
const changeExaminePage = (e: any) => {
  pagination.value.current = e.current;
  pagination.value.pageSize = e.pageSize;
  getExamineData();
};
onMounted(() => {
  getExamineData();
  searchSource('');
});
const sourceLoading = ref(false);
const searchSource = (keyword: string) => {
  sourceLoading.value = true;
  httpSupplierList({ name: keyword })
    .then((res) => {
      if (res.code === 0) {
        const tempArr = [];
        res.data.forEach((item) => {
          tempArr.push({
            value: item.supplierId,
            label: item.supplierName,
          });
        });
        sourceArr.value = tempArr;
      }
    })
    .finally(() => {
      sourceLoading.value = false;
    });
};
const handleSearch = (param) => {
  console.log(param, "pram");
  const { time,prodIds,supplierIds } = param;
  formData = param;
  formData["prodIds"] = prodIds ? [prodIds] : [];
  formData["supplierIds"] = supplierIds ? supplierIds : [];
  formData["applyStartTime"] = Array.isArray(time)
    ? time[0] + " 00:00:00"
    : "";
  formData["applyEndTime"] = Array.isArray(time) ? time[1] + " 23:59:59" : "";
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
  getExamineData();
};
const goDetail = (row: any) => {
  const id = row.prodId;
  const { auditType, auditId, nodeId } = row;
  router.push(`/product/check/risk/detail/${AUDIT_TYPE}/${id}/${auditType}/${auditId}/${nodeId}`);
};

const router = useRouter();
const isSubPage = ref(false);
watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (newVal.path.indexOf('/product/check/risk/detail') > -1) {
      isSubPage.value = true;
    } else {
      getExamineData()
      isSubPage.value = false;
    }
  },
  {
    immediate: true,
  },
);

// 驳回
const disAgreeRef = ref();
const disAgreeData = reactive({
  auditId: '',
  nodeId: '',
});
const openDisAgreeDia = (row: any) => {
  disAgreeData.auditId = row.auditId;
  disAgreeData.nodeId = row.nodeId;
  isAudit.value = false;
  disAgreeRef.value.open(causeOfRejection.riskControllerRejectCause, isAudit.value);
};

// 通过弹窗
const agreeVisible = ref(false);
// 打开通过弹窗
const openAgreeVisible = (row: any) => {
  disAgreeData.auditId = row.auditId;
  disAgreeData.nodeId = row.nodeId;
  agreeVisible.value = true;
  isAudit.value = false;
};
// 通过确认
const handleAgree = () => {
  if (isAudit.value) {
    httpBatchApproval(auditList.value)
      .then(() => {
        agreeVisible.value = false;
        message.success('提交成功');
        auditList.value = []
        getExamineData();
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    const params = {
      auditId: disAgreeData.auditId,
      nodeId: disAgreeData.nodeId,
    };
    httpApproval(params)
      .then(() => {
        agreeVisible.value = false;
        message.success('提交成功');
        getExamineData();
      })
      .catch((err) => {
        console.log(err);
      });
  }
};

// 判断是否是批量审核
const isAudit = ref<Boolean>(false);
// 批量同意
const batchAgree = () => {
  agreeVisible.value = true;
  isAudit.value = true;
};
// 批量驳回
const batchReject = () => {
  isAudit.value = true;
  disAgreeRef.value.open(causeOfRejection.riskControllerRejectCause, isAudit.value);
};

// 驳回成功
const onSucceed = () => {
  auditList.value = []
  getExamineData();
};


</script>

<style lang="less" scoped>
.end {
  display: flex;
  justify-content: flex-end;
  margin-top: 36px;
}
.opt_text{
  color:#167FFF;
  cursor: pointer;
  margin-right:16px;
}
.item-prodName{
  width: 440px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}
.tdesign-demo-image-viewer__ui-image {
  width: 50px;
  height: 50px;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}
.tdesign-demo-image-viewer__ui-image--hover {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}
.tdesign-demo-image-viewer__ui-image:hover .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: 50px;
  height: 50px;
  cursor: pointer;
  position: absolute;
}
.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 50px;
  width: 50px;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%);
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}
.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  width: 100px;
}
.back-box {
  margin: 16px 32px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #05082c;
    margin-bottom: 16px;
  }
  .sub-title {
    font-size: 14px;
    color: #495366;
    white-space: nowrap;
  }
}

:deep(th) {
  background-color: @table-th-bg !important;
  color: @table-th-color;
  font-weight: 400;

  &:first-child {
    border-top-left-radius: 8px;
  }

  &:last-child {
    border-top-right-radius: 8px;
  }
}

:deep(td) {
  border-bottom-color: @table-boder-color;
}

:deep(.t-table__pagination) {
  padding-top: 28px;

  .t-pagination {
    .t-pagination__jump {
      margin-left: 16px;
    }
  }
}
.t-table__content--scrollable-to-right {
  :deep(.t-table__cell--fixed-right-first) {
    &::after {
      border-left: 5px solid @table-boder-color;
    }
  }
}
.item-name {
  margin-right: 6px;
}
.batch-audit {
  margin-bottom: 16px;
}
</style>
