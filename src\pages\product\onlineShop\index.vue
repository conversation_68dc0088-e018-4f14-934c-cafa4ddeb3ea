<template>
  <div class="jdSort-header">
    <a-form :model="fromData" layout="vertical">
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="后台分类">
            <a-cascader
              v-model:value="fromData.platIds"
              :options="categoryArr"
              change-on-select
              :field-names="{
                label: 'categoryName',
                value: 'categoryId',
                children: 'newCategoryModelDtos',
              }"
              placeholder="请选择后台分类"
              style="width: 100%"
            />
            <!-- <a-cascader
              size="middle"
              v-model:value="fromData.platIds"
              style="width: 100%"
              multiple
              :max-tag-count="3"
              :allowClear="false"
              :options="categoryArr"
              placeholder="请选择"
              @change="changeCascader"
              :fieldNames="{
                value: 'categoryId',
                label: 'categoryName',
                children: 'children',
              }"
            /> -->
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="&nbsp;">
            <a-button type="primary" size="middle" @click="httpJdplatJd">
              搜索
            </a-button>
            <a-button style="margin-left: 15px" size="middle" @click="reset">
              重置
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div class="jdSort-content">
    <a-table
      :columns="jdSortColumns"
      :loading="isLoading"
      :indentSize="50"
      rowKey="platCategoryId"
      :data-source="jdSortTableData"
      :scroll="{ y: 1000 }"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'jdCategoryList'">
          <template v-for="(item, index) in record.jdCategoryList" :key="index">
            <div style="margin-bottom: 4px">
              <a-tag class="woody-tag-cg">
                {{ item?.jdCategoryNameFullName }}
              </a-tag>
            </div>
          </template>
        </template>
        <template v-else-if="column.dataIndex == 'productCount'">
          <template v-if="record.grade === 2">
            <span>{{ record.productCount }}</span>
          </template>
          <span v-else></span>
        </template>
        <template v-if="column.key == 'operate'">
          <template v-if="record.grade === 2">
            <a-button type="link" class="btn-css"
              @click="handleEdit(record)"
            >
              编辑
            </a-button>
          </template>
        </template>
      </template>
    </a-table>
  </div>
  <!-- 京东分类编辑弹框 -->
  <a-modal
    :open="isShowDialog"
    title="编辑"
    @ok="clickSubmit"
    @cancel="
      () => {
        isShowDialog = false;
      }
    "
    :cancelText="'取消'"
    :okText="'确定'"
  >
    <div class="edit-dialog">
      <div class="edit-text">
        <span>后台分类：</span>
        <span>{{ levelTitle }}</span>
      </div>
      <div class="edit-line">
        <span>关联供应链分类：</span>
        <div class="edit-line-select">
          <a-select
            v-model:value="jdIds"
            mode="multiple"
            show-search
            :filter-option="false"
            placeholder="请选择内容"
            style="width: 100%"
            :options="baseJdListArr"
            allowClear
            :fieldNames="{ value: 'jdCategoryId', label: 'jdCategoryName' }"
            @search="remoteMethod"
            @focus="handleFocus"
          />
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from "woody-ui";
import { ref, reactive, onMounted } from "vue";
import { FROM_DATA, JDSORT_COLUMNS } from "./const";
import { listTree, jdplatJd } from "@/api/jsSortListApi";
import { baseJdList, jdPlatBindJd } from "@/api/jsSortListApi";
// import { queryCategory } from "@/api/common";
import { fetchCategory } from "@/utils";

const fromData = reactive(FROM_DATA);
const categoryArr = ref<Array<any>>([]);
const isLoading = ref<boolean>(false);
const jdSortColumns = reactive(JDSORT_COLUMNS);
const jdSortTableData = ref<Array<any>>([]);
const isShowDialog = ref<boolean>(false);
const levelTitle = ref<string | null>(null);
const baseJdListArr = ref<Array<any>>([]);
const platIdsArr = ref<Array<any>>([]);
const platIdsObj = ref<Array<any>>([]);
const platId = ref<string | null>(null);
const jdIds = ref<Array<any>>([]);

const treeConfig = {
  childrenKey: "children",
  treeNodeColumnIndex: 0,
  indent: 25,
  expandTreeNodeOnClick: true,
};
// 重置平台分类
const reset = () => {
  fromData.platIds = [];
  platIdsObj.value = [];
  platIdsArr.value = [];
  jdSortTableData.value = [];
  httpJdplatJd();
};
// 平台的分类(不分页)
const httpCategoryList = async () => {
  categoryArr.value = await fetchCategory();
  // fetchCategory().then((res) => {
  //   if (res.data) {
  //     categoryArr.value = res.data;
  //   }
  // });
  // try {
  //   const res = await listTree({ categoryId: 0 });
  //   if (res.code == 0) {
  //     categoryArr.value = res.data;
  //   }
  // } catch (error) {
  //   categoryArr.value = [];
  // }
};
// 平台-京东分类
const httpJdplatJd = async () => {
  // formData["firstCategoryId"] = Array.isArray(cascader) ? cascader[0] : "";
  // formData["secondCategoryId"] = Array.isArray(cascader) ? cascader[1] : "";
  // if (cascader && cascader.length === 3) {
  //   formData["threeCategoryId"] = cascader[2];
  // }

  // console.log(fromData.platIds);
  isLoading.value = true;
  let obj: any = {};
  if (fromData.platIds) {
    obj.firstCategoryId = fromData.platIds[0];
    obj.secondCategoryId = fromData.platIds[1];
    obj.threeCategoryId = fromData.platIds[2];
  }
  const res = await jdplatJd(obj);
  isLoading.value = false;
  jdSortTableData.value = processCategories(res.data);
  // try {
  //   jdSortTableData.value = [];
  //   platIdsObj.value = [
  //     ...platIdsArr.value,
  //     ...fromData.platIds.map((subArray) => subArray[subArray.length - 1]),
  //   ];
  //   isLoading.value = true;
  //   const res = await jdplatJd({ platIds: platIdsObj.value });
  //   isLoading.value = false;
  //   if (res.code == 0) {
  //     jdSortTableData.value = processCategories(res.data);
  //   }
  // } catch (error) {
  //   isLoading.value = false;
  //   jdSortTableData.value = [];
  // }
};
// 删除children为空的属性
const processCategories = (categories) => {
  return categories.map((category, index) => {
    category.key = index;
    const { children, ...rest } = category;
    if (children && children.length > 0) {
      const processedChildren = processCategories(children);
      if (processedChildren.length === 0) {
        return rest;
      } else {
        return { ...rest, children: processedChildren };
      }
    } else {
      return { ...rest, isLastLevel: true };
    }
  });
};
// // 选择后台分类
// const changeCascader = (value, options) => {
//   platIdsArr.value = [];
//   options.forEach((item) => {
//     if (item.length == 1) {
//       item[0].children.forEach((sub) => {
//         platIdsArr.value.push(sub.categoryId);
//       });
//     } else {
//       platIdsArr.value.push(item[1].categoryId);
//     }
//   });
// };
// 编辑弹框显示
const handleEdit = (row) => {
  console.log(row, "row");
  isShowDialog.value = true;
  jdIds.value = [];
  levelTitle.value = row.platCategoryFullName;
  platId.value = row.platCategoryId;
  if (row.jdCategoryList && row.jdCategoryList.length != 0) {
    row.jdCategoryList.forEach((item) => {
      jdIds.value.push(item.jdCategoryId);
    });
  } else {
    jdIds.value = [];
  }
  httpBaseJdList();
};
// 京东分类下拉查询
const httpBaseJdList = async (jdCategoryName?) => {
  try {
    const res = await baseJdList({ level: 3, jdCategoryName });
    if (res.code == 0) baseJdListArr.value = res.data;
  } catch (error) {
    baseJdListArr.value = [];
  }
};
// 编辑京东分类下拉筛选
const remoteMethod = (search) => {
  httpBaseJdList(search);
};

// 展开下拉菜单的回调
const handleFocus = () => {
  httpBaseJdList();
};
// 编辑页面
const clickSubmit = async () => {
  try {
    let param = { platId: platId.value, jdIds: jdIds.value };
    const res = await jdPlatBindJd(param);
    if (res.code == 0) {
      message.success("编辑成功！");
      fromData.platIds = [];
      isShowDialog.value = false;
      httpJdplatJd();
    }
  } catch (error) {
    console.error(error);
  }
};

onMounted(() => {
  httpCategoryList();
  httpJdplatJd();
});
</script>
<style lang="less" scoped>
.jdSort-header {
  border-radius: 16px;
  background: #fff;
  padding: 24px 32px 12px 32px;
}

.jdSort-content {
  border-radius: 16px;
  background: #fff;
  padding: 32px;
  margin-top: 16px;
  height: calc(100vh - 246px);
}

.edit-dialog {
  padding-top: 20px;

  .edit-text {
    display: flex;
    align-items: center;
    color: #000;
  }

  .edit-line {
    display: flex;
    align-items: center;
    color: #000;
    padding-top: 15px;
    padding-bottom: 10px;

    .edit-line-select {
      width: 65%;
    }
  }
}

:deep(.ant-table-thead > tr > th) {
  background-color: #f1f6f8 !important;
}
</style>
