const formList = [
  {
    type: "input",
    label: "商品名称",
    name: "productName",
    maxlength: 30,
    span: 6,
  },
  {
    type: "input",
    label: "商品ID",
    name: "prodId",
    maxlength: 30,
    span: 6,
  },
  {
    type: "cascader",
    label: "后台分类",
    name: "platCategoryId",
    labelKey: "categoryName",
    valueKey: "categoryId",
    childrenKey: "newCategoryModelDtos",
    searchFn: "common",
    changeOnSelect: true,
    span: 6,
  },
  {
    type: "rangeInput",
    label: "销售价",
    name: ["minSalePrice", "maxSalePrice"],
    max: 1000000,
    span: 6,
  },
  // {
  //   type: "input",
  //   label: "商品品牌",
  //   name: "brand",
  //   maxlength: 30,
  //   span: 6,
  // },
  {
    type: "select",
    label: "商品状态",
    name: "statusList",
    mode: "multiple",
    showSearch: true,
    options: [
      {
        label: "上架",
        value: "1",
      },
      {
        label: "下架",
        value: "0",
      },
      {
        label: "违规下架",
        value: "2",
      },
    ],
    span: 6,
  },
];
const columns = [
  {
    dataIndex: "prodId",
    key: "prodId",
    title: "商品ID",
    align: "center",
    width: 200,
    fixed: "left",
  },
  {
    dataIndex: "imageUrl",
    key: "imageUrl",
    title: "商品信息",
    align: "center",
    width: 300,
  },
  {
    dataIndex: "brand",
    key: "brand",
    title: "商品品牌",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "platCategoryName",
    key: "platCategoryName",
    title: "后台分类",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "jdPrice",
    key: "jdPrice",
    title: "京东价（¥）",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "salePrice",
    key: "salePrice",
    title: "销售价（¥）",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "costPrice",
    key: "costPrice",
    title: "成本价（¥）",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "waterSoldNum",
    key: "waterSoldNum",
    title: "注水销量",
    align: "center",
    width: 200,
  },
  {
    dataIndex: "soldNum",
    key: "soldNum",
    title: "真实销量",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "logisticsName",
    key: "logisticsName",
    title: "配送类型",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "isSelfSell",
    key: "isSelfSell",
    title: "是否京东自营",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "startQty",
    key: "startQty",
    title: "京东起购数",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "updateTime",
    key: "updateTime",
    title: "最近更新时间",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "tripartiteProdStatus",
    key: "tripartiteProdStatus",
    title: "京东状态",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "status",
    key: "status",
    title: "商品状态",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "operate",
    key: "operate",
    title: "操作",
    align: "center",
    width: 200,
    fixed: "right",
  },
];
const columsDetail = [
  {
    dataIndex: "costPrice",
    key: "costPrice",
    title: "成本价（元）",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "jdPrice",
    key: "jdPrice",
    title: "京东价（元）",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "salePrice",
    key: "salePrice",
    title: "销售价（元）",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "stock",
    key: "stock",
    title: "库存",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "properties",
    key: "properties",
    title: "规格名称",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "volume",
    key: "volume",
    title: "商品体积",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "weight",
    key: "weight",
    title: "商品重量",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "saleUnit",
    key: "saleUnit",
    title: "单位",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "productArea",
    key: "productArea",
    title: "产地",
    align: "center",
    width: 150,
  },
  {
    dataIndex: "skuNumber",
    key: "skuNumber",
    title: "国标码",
    align: "center",
    width: 200,
  },
];

export { columsDetail, formList, columns };
