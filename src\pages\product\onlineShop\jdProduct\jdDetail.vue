<template>
  <div class="product-detail-container">
    <div class="back">
      <a-breadcrumb>
        <a-breadcrumb-item
          ><a href="" @click.prevent="goBack"
            >京东商品列表</a
          ></a-breadcrumb-item
        >
        <a-breadcrumb-item>详情</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <div class="product-intro">
      <div class="title">
        <div>商品类型</div>
        <div>配送类型</div>
        <div>是否京东自营</div>
      </div>
      <div class="desc">
        <div>{{ detailData.prodType }}</div>
        <div>{{ detailData.logisticsName }}</div>
        <div>{{ detailData.isSelfSell ? "是" : "否" }}</div>
      </div>
    </div>
    <div>
      <div class="title">
        <div>后台分类</div>
      </div>
      <div class="desc">
        <div>
          <span>后台分类：</span>
          <span>{{ detailData.platCategoryName || "--" }}</span>
        </div>
      </div>
    </div>
    <div>
      <div class="title">基本信息</div>
      <div class="basic-info">
        <div>
          <span>商品ID：</span>
          <span>{{ detailData.prodId }}</span>
        </div>
        <div>
          <span>商品名称：</span>
          <span>{{ detailData.productName }}</span>
        </div>
        <div style="height: fit-content !important">
          <span>商品图片：</span>
          <span
            v-for="(item, index) in detailData?.imageUrls?.split(',')"
            :key="index"
          >
            <a-image
              style="width: 90px; height: 90px; margin-right: 10px"
              :src="item"
              alt=""
            />
          </span>
        </div>
        <div>
          <span>商品品牌：</span>
          <span>{{ detailData.brand }}</span>
        </div>
        <div>
          <span>售后期限：</span>
          <span>{{ detailData.afterSalePeriod }}天可发起售后</span>
        </div>
      </div>
    </div>
    <div>
      <div class="title">规格库存</div>
      <div>
        <div style="line-height: 50px">
          <span>商品规格：</span>
          <span>规格名：{{ detailData.propertyName }}</span>
        </div>
        <div style="margin-left: 70px; display: flex; margin-bottom: 30px">
          <div
            v-for="item in detailData.skuList"
            style="text-align: left; margin-right: 20px"
          >
            <div style="margin-bottom: 10px">规格值：{{ item.properties }}</div>
            <a-image
              style="width: 90px; height: 90px"
              :src="item.imageUrl"
              alt=""
            />
          </div>
        </div>
        <div class="table-container">
          <span>价格及库存：</span>
          <div class="price-table">
            <a-table
              :dataSource="detailData.skuList"
              :pagination="false"
              :columns="columsDetail"
            ></a-table>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="title">商品详情</div>
      <div class="detail-content">
        <div>
          <span>规格参数：</span>
          <div v-if="detailData.jdParamGroupAttrList" style="width: 800px">
            <div
              class="param-info"
              v-for="item in detailData.jdParamGroupAttrList"
              style="margin-bottom: 10px"
            >
              <div style="padding-left: 10px">{{ item.paramGroupName }}</div>
              <div class="desc-param" v-for="items in item.paramAttributeList">
                <span>{{ items.paramAttrName }}</span>
                <span v-for="ite in items.paramAttrValList">
                  <span v-if="ite" style="margin-right: 5px">{{
                    ite.toString()
                  }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div>
          <span>质保说明：</span>
          <span>{{ detailData.jdWarrantDesc }}</span>
        </div>
        <div v-if="detailData.content">
          <a-image
            v-for="item in detailData.content.split(',')"
            width="100%"
            :src="item"
            :key="index"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { columsDetail } from "./const";
import { queryDetail } from "@/api/jsSortListApi";

const route = useRoute();
const router = useRouter();
const detailData = ref({});

onMounted(() => {
  fetchDetail();
});
// 查详情
const fetchDetail = () => {
  queryDetail({ prodId: route.query.id }).then((res) => {
    console.log(res, "res");
    detailData.value = res.data || {};
  });
};
// 返回
const goBack = () => {
  router.back();
};
</script>
<style lang="less" scoped>
.product-detail-container {
  padding: 16px;
  padding-top: 0;
  border-radius: 16px;
  > div {
    padding: 16px;
    background-color: #fff;
    margin-bottom: 16px;
    border-radius: 16px;
  }
  .layoutStyle {
    display: flex;
    > div {
      flex: 1;
    }
  }
  .title {
    padding: 0 15px;
    height: 45px;
    line-height: 45px;
    color: #05082c;
    font-weight: bold;
    font-size: 20px;
    border-radius: 8px;
    position: relative;
    &::before {
      content: " ";
      position: absolute;
      width: 4px;
      height: 16px;
      background: #1a7af8;
      border-radius: 4px;
      left: 0;
      top: 15px;
    }
    .layoutStyle;
  }
  .desc {
    margin: 30px 0;
    > div {
      padding-left: 15px;
    }
    .layoutStyle;
  }
  .basic-info {
    padding-left: 15px;
    > div {
      height: 50px;
      line-height: 50px;
    }
  }
  .table-container {
    display: flex;
    > div {
      flex: 1;
    }
  }
  .detail-content > div {
    line-height: 50px;
  }
  .param-info {
    border: 1px solid #f0f0f0;
    .desc-param {
      border-top: 1px solid #f0f0f0;
      > span {
        display: inline-block;
        padding: 0 10px;
        &:first-child {
          border-right: 1px solid #f0f0f0;
        }
      }
    }
    // > div {
    //   &:last-child {
    //     span {
    //       display: inline-block;
    //       margin-right: 10px;
    //       &:first-child {
    //         border-right: 1px solid #f0f0f0;
    //       }
    //     }
    //   }
    // }
  }
  // .back {
  //   font-size: 18px;
  //   font-weight: bold;
  //   cursor: pointer;
  //   // position: fixed;
  //   // width: 100%;
  // }
}
</style>
