<template>
  <div class="jd-product-container">
    <div class="form-info">
      <search-antd :formList="formList" @onSearch="queryFunc" />
    </div>
    <div class="table-container">
      <div class="intro">商品总数：{{ pagination.total }}</div>
      <div style="margin-bottom: 16px">
        <a-button
          type="primary"
          style="margin-right: 10px"
          @click="undercarriage(checkedKeys)"
          >违规下架</a-button
        >
        <a-button type="primary" @click="download">导出</a-button>
        <span
          style="color: red; margin-left: 10px; position: relative; top: 5px"
          >导出最大支持20万sku</span
        >
      </div>
      <div>
        <TableComp
          :columns="columns"
          rowKey="prodId"
          :tableData="tableData"
          :pagination="pagination"
          :loading="loading"
          :scroll-obj="{ x: 1500, y: 400 }"
          @onChange="pageChange"
          :row-selection="{
            selectedRowKeys: checkedKeys,
            onChange: onSelectChange,
            getCheckboxProps: (record) => ({
              disabled: record.status === 2,
            }),
          }"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'imageUrl'">
              <div style="display: flex; text-align: left">
                <div class="product-info">
                  <a-image
                    :width="70"
                    :height="70"
                    :src="record.imageUrl"
                    alt=""
                  />
                </div>
                <div style="margin-left: 10px">
                  {{ record.productName }}
                </div>
              </div>
            </template>
            <template v-else-if="column.dataIndex === 'isSelfSell'">
              {{ record.isSelfSell ? "是" : "否" }}
            </template>
            <template v-else-if="column.dataIndex === 'tripartiteProdStatus'">
              京东{{ record.tripartiteProdStatus === "0" ? "上架" : "下架" }}
            </template>
            <template v-else-if="column.dataIndex === 'status'">
              {{ prdStatus[record.status] }}
            </template>
            <template v-else-if="column.dataIndex === 'waterSoldNum'">
              <a-input-number
                v-if="editableData[record.prodId]"
                :min="0"
                style="width: 70%"
                v-model:value="editableData[record.prodId][column.dataIndex]"
              />
              <span v-else class="currentCount">{{ text }}</span>
              <CheckOutlined
                v-if="editableData[record.prodId]"
                @click="save(record.prodId)"
                style="color: #1a7af8; margin-left: 10px; cursor: pointer"
              />
              <EditOutlined
                v-else
                @click="edit(record.prodId)"
                style="color: #1a7af8; margin-left: 10px; cursor: pointer"
              />
            </template>
            <template v-else-if="column.dataIndex === 'operate'">
              <a-button
                type="link"
                class="btn-css"
                @click="goDetail(record.prodId)"
                >查看</a-button
              >
              <a-button
                type="link"
                class="btn-css"
                @click="undercarriage([record.prodId])"
                v-if="record.status !== 2"
                >违规下架</a-button
              >
              <a-button
                type="link"
                class="btn-css"
                v-if="record.status === 2"
                @click="viewReason(record.prodId)"
                >下架原因</a-button
              >
            </template>
          </template>
        </TableComp>
      </div>
    </div>
    <a-modal v-model:open="open" title="违规信息" :footer="null">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 5 }">
        <a-form-item v-if="disabledReason" label="操作人">
          <span class="ant-form-text">{{ reason.operatorUserName }}</span>
        </a-form-item>
        <a-form-item
          label="下架原因"
          name="violationReason"
          :rules="[{ required: true, message: '请输入下架原因' }]"
        >
          <a-textarea
            v-model:value="formData.violationReason"
            show-count
            :disabled="disabledReason"
            :rows="4"
            :maxlength="200"
          />
        </a-form-item>
        <a-form-item v-if="disabledReason" label="下架时间">
          <span class="ant-form-text">{{ reason.operateTime }}</span>
        </a-form-item>
      </a-form>
      <div style="text-align: right">
        <a-button @click="cancenFunc">取消</a-button>
        <a-button
          v-if="!disabledReason"
          @click="handleOk"
          type="primary"
          style="margin-left: 16px"
          >确定</a-button
        >
      </div>
    </a-modal>
    <modalComp
      :visible="downloadVisible"
      @downloadBack="downloadBack"
      taskName="new_platform_jd_product_export"
      :info="queryParam"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import type { UnwrapRef } from "vue";
import { useRouter } from "vue-router";
import { message } from "woody-ui";
import { CheckOutlined, EditOutlined } from "@ant-design/icons-vue";
import SearchAntd from "@/components/SearchAntd/index.vue";
import TableComp from "@/components/TableList/tableComp.vue";
import modalComp from "@/components/download/mdoalComp.vue";
import { formList, columns } from "./const";
import {
  jsProductList,
  waterSoldNum,
  queryReason,
  offSale,
} from "@/api/jsSortListApi";

interface DataItem {
  prodId: string | number;
  waterSoldNum: string | number;
}
const router = useRouter();
const prdStatus = ["下架", "上架", "违规下架"];
const tableData = ref([]);
const formData = reactive({ violationReason: undefined });
const formRef = ref();
const editableData: UnwrapRef<Record<string, DataItem>> = reactive({});
const checkedKeys = ref([]);
const open = ref(false);
const disabledReason = ref(true);
const reason = ref({
  operateTime: "",
  operatorUserName: "",
});
const downloadVisible = ref(false);
const queryParam = ref({});
const idList = ref();
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});
const loading = ref(false);

onMounted(() => {
  queryList();
});
// 列表查询
const queryList = (param = {}) => {
  loading.value = true;
  jsProductList({
    page: 1,
    size: 10,
    ...param,
  })
    .then((res) => {
      if (res.data) {
        pagination.total = res.data.total;
        tableData.value = res.data.records;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
// 查询
const queryFunc = (param) => {
  if (param.platCategoryId) {
    const [firstCategoryId, secondCategoryId, threeCategoryId] =
      param.platCategoryId;
    param.firstCategoryId = firstCategoryId;
    param.secondCategoryId = secondCategoryId;
    param.threeCategoryId = threeCategoryId;
    param.platCategoryId = param.platCategoryId[param.platCategoryId.length - 1]
      ? param.platCategoryId[param.platCategoryId.length - 1]
      : param.platCategoryId[param.platCategoryId.length - 2];
  }
  const { minSalePrice, maxSalePrice } = param;
  if (
    minSalePrice &&
    maxSalePrice &&
    Number(minSalePrice) > Number(maxSalePrice)
  ) {
    message.warning("请输入正确的金额范围！");
    return;
  }
  queryParam.value = param;
  pagination.current = 1;
  pagination.pageSize = 10;
  queryList({ ...param });
};
// 编辑表格输入内容
const edit = (key: string) => {
  // console.log(key, "key", tableData.value);
  editableData[key] = JSON.parse(
    JSON.stringify(tableData.value.filter((item) => key === item.prodId)[0])
  );
};
// 保存表格内输入内容
const save = (key: string) => {
  waterSoldNum({
    waterSoldNum: editableData[key].waterSoldNum,
    prodId: key,
  })
    .then((res) => {
      console.log(res, "res");
      if (res.code === 0) {
        message.success("修改成功！");
        const { current, pageSize } = pagination;
        queryList({
          page: current,
          size: pageSize,
          ...queryParam.value,
        });
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      delete editableData[key];
    });
  // Object.assign(
  //   tableData.value.filter((item) => key === item.id)[0],
  //   editableData[key]
  // );
  // delete editableData[key];
};
// 多选逻辑
const onSelectChange = (selectedRowKeys: any) => {
  console.log("selectedRowKeys changed: ", selectedRowKeys);
  checkedKeys.value = selectedRowKeys;
};

// 京东商品导出
const download = () => {
  // console.log(param, "99966");
  // queryParam.value = param;
  downloadVisible.value = true;
};

const downloadBack = (params) => {
  downloadVisible.value = false;
};

// 弹框确认
const handleOk = () => {
  formRef.value.validate().then(() => {
    offSale({
      productIds: idList.value,
      reason: formData.violationReason,
    })
      .then((res) => {
        if (res.code === 0) {
          setTimeout(() => {
            message.success("下架成功！");
            initPage();
            queryList(queryParam.value);
          }, 2000);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        cancenFunc();
      });
  });
};
// 弹框关闭
const cancenFunc = () => {
  open.value = false;
  reason.value = {
    operateTime: "",
    operatorUserName: "",
  };
  checkedKeys.value = [];
  disabledReason.value = true;
  formData.violationReason = undefined;
};
// 违规下架
const undercarriage = (id: any) => {
  if (id.length === 0) {
    message.warning("请选择要下架的商品");
  } else {
    idList.value = id;
    disabledReason.value = false;
    open.value = true;
  }
};

// 下架原因
const viewReason = (prodId) => {
  open.value = true;
  // idList.value = id;
  // 查下架信息
  queryReason({ prodId })
    .then((res) => {
      // console.log(res);
      if (res.data) {
        reason.value = res.data || {};
        disabledReason.value = true;
        formData.violationReason = res.data.violationReason;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
// 分页切换
const pageChange = (e) => {
  pagination.current = e.current;
  pagination.pageSize = e.pageSize;
  queryList({
    page: e.current,
    size: e.pageSize,
    ...queryParam.value,
  });
};

// 查看详情
const goDetail = (id) => {
  router.push({
    path: "/product/ecommerceGoods/jdDetail",
    query: { id },
  });
};
// 分页初始化
const initPage = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
};
</script>
<style lang="less" scoped>
.jd-product-container {
  .form-info {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
  }
  .table-container {
    margin-top: 16px;
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    .intro {
      font-weight: bold;
      font-size: 16px;
      line-height: 50px;
    }
  }
  :deep(.currentCount) {
    display: inline-block;
    width: 70%;
    border-radius: 6px;
    text-align: center;
    background-color: #eee;
    height: 30px;
    line-height: 30px;
    opacity: 0.7;
  }
}
</style>
