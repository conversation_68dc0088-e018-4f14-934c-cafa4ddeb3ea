.section-top {
  border-radius: 16px;
  background: #fff;
}

.search-configuration-wrapper {
  width: 100%;
  // min-height: 660px;
  background: #fff;
  border-radius: 16px 16px 16px 16px;
  margin-top: 8px;
  padding: 32px;

  .drawer-footer-btn {
    display: flex;
    justify-content: flex-end;
  }

  .drawer-title-wrapper {
    text-align: left;
    margin-top: 10px;
    margin-left: 10px;

    .showStyle {
      margin-bottom: 40px;
    }

    .drawer-title {
      font-weight: 600;
      font-size: 14px;
      color: #05082c;
      line-height: 22px;
      margin-bottom: 30px;
    }
    .showStar::before {
      display: inline-block;
      margin-right: var(--td-comp-margin-xs);
      color: var(--td-error-color);
      line-height: var(--td-line-height-body-medium);
      content: '*';
    }
  }

  .table-bottom {
    padding-bottom: 24px;
    text-align: right;
    svg {
      margin-right: 3px;
    }
  }

  .page-title {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }

  .dialog-body-text {
    font-weight: 400;
    font-size: 14px;
    color: #495366;
    text-align: left;
    margin-left: 18px;
  }
}
