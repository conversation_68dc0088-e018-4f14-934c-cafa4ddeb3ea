<template>
  <div class="section-top">
    <search-antd :form-list="formList" @on-search="onSubmit" />
  </div>
  <div class="search-configuration-wrapper">
    <div class="table-bottom">
      <a-button type="primary" @click="handleEdit()"> 新建版本 </a-button>
    </div>
    <a-table
      row-key="id"
      :data-source="listData"
      :columns="columns"
      :pagination="pagination"
      @change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <div v-if="column.key === 'operate'">
          <a-button
            type="link"
            class="btn-css"
            @click="handleEdit(record)"
            >编辑</a-button
          >
          <a-button
            type="link"
            class="btn-css"
            @click="handleDisposition(record)"
            >配置</a-button
          >
          <a-button type="link" class="btn-css" @click="handleCopy(record)">复制</a-button>
        </div>
      </template>
    </a-table>
    <a-modal
      v-model:open="visibleDialog"
      :title="headerText"
      @cancel="closeDialog"
      @ok="onClickConfirm"
      destroy-on-close
    >
      <a-form
        ref="formHandleRef"
        layout="vertical"
        :model="formHandleData"
        class="mt20"
        :colon="true"
        :rules="FORM_RULES"
      >
        <a-row>
          <a-col>
            <a-form-item label="版本名称" name="versionName">
              <a-input
                v-model:value="formHandleData.versionName"
                placeholder="请输入版本名称"
                maxlength="30"
                style="width: 420px"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <a-drawer
      v-model:open="visible"
      title="配置"
      :footer="true"
      width="30%"
      :close-on-overlay-click="false"
    >
      <a-form :model="optionsCheck">
        <a-row :gutter="16">
          <a-col :span="24">
            <div :class="optionsCheck.isShow ? '' : 'showStyle'">
              <h3 class="drawer-title mb20">搜索tab配置</h3>
              展示
              <a-switch
                v-model:checked="optionsCheck.isShow"
                :disabled="confirmOrNot"
                style="margin-left: 12px"
                @change="onChange"
              />
              <a-checkbox-group
                v-if="optionsCheck.isShow"
                v-model:value="optionsCheck.searchTapConfig"
                :options="options.searchTapConfig"
                :disabled="confirmOrNot"
                class="mt20"
              >
              </a-checkbox-group>
              <h3 class="flex mt30">
                搜索商品来源<span
                  style="font-size: 12px; margin-left: 15px; color: red"
                  >仅针对全部或不展示tap时生效</span
                >
              </h3>
              <a-checkbox-group
                v-model:value="optionsCheck.searchProdSource"
                :options="options.searchProdSource"
                :disabled="confirmOrNot"
                class="mt20"
              >
              </a-checkbox-group>
              <h3 class="drawer-title mt30">搜索页面配置</h3>
              <div class="flex mt20">
                <div
                  v-for="(item, index) in options.searchContentConfig"
                  :key="index"
                >
                  {{ item.name }}
                  <a-switch
                    v-model:checked="item.enable"
                    :disabled="confirmOrNot"
                    style="margin: 0 20px 0 12px"
                    @change="(e) => searchPageChange(index, e)"
                  />
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form>
      <template #extra>
        <a-space>
          <a-button @click="onClickClose">取消</a-button>
          <a-button type="primary" @click="onClickDisposition">{{
            confirmOrNot ? "编辑" : "保存"
          }}</a-button>
        </a-space>
      </template>
    </a-drawer>
    <a-modal
      v-model:open="visibleQuitDialog"
      theme="danger"
      title="确定要退出吗？"
      :body="true"
      @ok="onClickConfirmDialog"
    >
      <div class="dialog-body-text">
        当前页面内容尚未发布，如若退出会丢失编辑的内容！
      </div>
      <template #footer>
        <div class="drawer-footer-btn">
          <a-button @click="onClickConfirmDialog()">确认退出</a-button>
          <a-button type="primary" @click="onClickCloseDialog()">取消</a-button>
        </div>
      </template>
    </a-modal>
    <copy-dialog
      :visible="copyVisible"
      :data-obj="dialogDataObj"
      :maxlength="30"
      @on-close="handleCopyClose"
      @on-confirm="confirmCopy"
    />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "woody-ui";
import SearchAntd from "@/components/SearchAntd/index.vue";
import CopyDialog from "@/components/CopyDialog/index.vue";
import AddCircle from "@/assets/add-circle.svg";
import { getSysUserPage } from "@/api/cms/reception";
import { columns, FORM_RULES } from "./setData.js";
import { isEmptyValue } from '@/utils';
import {
  searchPageList,
  addVersion,
  updateVersion,
  updateConfig,
  configDetail,
} from "@/api/cms/searchConfig";

const listData = ref([]);
const visibleDialog = ref(false);
const visibleQuitDialog = ref(false);
const headerText = ref("");
const visible = ref(false);
const confirmOrNot = ref(true);
const formHandleRef = ref(null);
const searchParam = ref({});
const rowData = ref({});
const copyVisible = ref(false);
const dialogDataObj = ref({});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

const formHandleData = reactive({
  versionName: "",
});

const optionsCheck = reactive({
  isShow: true,
  searchTapConfig: ["ALL"],
  searchProdSource: ["0", "4", "7", "5"],
});
const options = reactive({
  searchTapConfig: [
    { value: "ALL", label: "全部", disabled: true },
    { value: "WD_LIFE", label: "生活购物" },
    { value: "AIKUCUN", label: "品牌特卖" },
    { value: "GOLD_COIN", label: "金币商城" },
    { value: "SHOP", label: "生活圈" },
  ],
  searchProdSource: [
    { value: "0", label: "供货仓" },
    { value: "4", label: "爱库存" },
    { value: "7", label: "大鲲" },
    { value: "5", label: "云中鹤" },
    { value: "8", label: "京东" },
  ],
  searchContentConfig: [
    { key: "entry", enable: true, name: "品牌入口" },
    { key: "hot", enable: true, name: "热门搜索" },
    { key: "card", enable: true, name: "榜单卡片" },
  ],
});

const userArr = ref([]);

const getSysUserData = (username) => {
  const params = {
    page: 1,
    size: 50,
  };
  if (!isEmptyValue(username)) {
    params.username = username;
  }
  // formList[0][1].loading = true;
  getSysUserPage(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        // formList[0][1].options = res.data.records;
        // resolve(res.data.records);
        userArr.value = res.data.records;
      } else {
        // formList[0][1].options = [];
      }
    })
    .finally(() => {
      // formList[0][1].loading = false;
    });
};
const remoteMethod = (search) => {
  setTimeout(() => {
    getSysUserData(search);
  }, 1000);
};

const formList = [
  {
    label: "版本名称",
    name: "versionName",
    type: "input", // 输入框
    maxlength: 30,
    span: 6,
  },
  {
    label: "创建人",
    name: "createUserId",
    type: "select",
    remoteMethod,
    labelKey: "username",
    valueKey: "userId",
    showSearch: true,
    needFilter: true,

    options: userArr,
    span: 6,
  },
  {
    label: "创建日期",
    name: "createTime",
    type: "datePicker",
    showTime: true,
    span: 6,
  },
];

// 查表格数据
const queryList = (
  param = { page: pagination.current, size: pagination.pageSize }
) => {
  searchPageList(param).then((res) => {
    if (res.code === 0) {
      listData.value = res.data.records;
      pagination.total = res.data.total;
    }
  });
};
const onClickConfirm = () => {
  formHandleRef.value.validate().then(() => {
    let request;
    if (rowData.value.id) {
      request = updateVersion({
        ...formHandleData,
        id: rowData.value.id,
      });
    } else {
      request = addVersion({
        ...formHandleData,
      });
    }

    request.then((res) => {
      if (res.code === 0) {
        message.success(`${rowData.value.id ? "编辑" : "新增"}成功`);
        pagination.current = 1;
        queryList();
        closeDialog();
      }
    });
  });
};

const onSubmit = (param) => {
  searchParam.value = param;
  pagination.current = 1;
  queryList({
    ...param,
    page: 1,
    size: pagination.pageSize,
  });
};

const closeDialog = () => {
  visibleDialog.value = false;
  rowData.value = {};
  formHandleRef.value.reset();
};

const handleEdit = (row) => {
  visibleDialog.value = true;
  if (row) {
    headerText.value = "修改版本";
    formHandleData.versionName = row.versionName;
    rowData.value = row;
  } else {
    headerText.value = "新建版本";
    formHandleData.versionName = "";
  }
};

const handleDisposition = (row) => {
  rowData.value = row;
  queryDetail(row.id);
  visible.value = true;
};

// 查询设置详情
const queryDetail = (id) => {
  configDetail({ id }).then((res) => {
    if (res.code === 0) {
      const { searchContentConfig, searchProdSource, searchTapConfig } =
        res.data;
      optionsCheck.isShow = searchTapConfig
        ? JSON.parse(searchTapConfig).isShow
        : true;
      optionsCheck.searchTapConfig = searchTapConfig
        ? [...JSON.parse(searchTapConfig).tapList]
        : ["ALL"];
      if (!optionsCheck.searchTapConfig.includes("ALL"))
        optionsCheck.searchTapConfig.unshift("ALL");
      optionsCheck.searchProdSource = searchProdSource
        ? JSON.parse(searchProdSource)
        : ["0", "4", "7", "5"];
      if (searchContentConfig) {
        options.searchContentConfig = JSON.parse(searchContentConfig);
      } else {
        options.searchContentConfig = [
          { key: "entry", enable: true, name: "品牌入口" },
          { key: "hot", enable: true, name: "热门搜索" },
          { key: "card", enable: true, name: "榜单卡片" },
        ];
      }
    }
  });
};
// 复制功能
const handleCopy = (row) => {
  Object.keys(row).forEach((key) => {
    dialogDataObj.value[key] = row[key];
  });
  copyVisible.value = true;
};

// 关闭复制弹框
const handleCopyClose = () => {
  copyVisible.value = false;
};

const confirmCopy = (info) => {
  addVersion({
    id: info.id,
    versionName: info.categoryName,
  }).then((res) => {
    if (res.code === 0) {
      message.success(`复制成功`);
      copyVisible.value = false;
      pagination.current = 1;
      queryList();
    }
  });
};

const defaultValue = () => {
  visible.value = false;
  confirmOrNot.value = true;
  rowData.value = {};
};

const onClickClose = () => {
  if (confirmOrNot.value) {
    defaultValue();
  } else {
    visibleQuitDialog.value = true;
  }
};

const onClickDisposition = () => {
  if (confirmOrNot.value) {
    confirmOrNot.value = false;
  } else {
    if (optionsCheck.searchProdSource.length === 0) {
      message.warning("请指定至少一处商品来源");
      return;
    }
    const obj = {
      isShow: optionsCheck.isShow,
      tapList: optionsCheck.searchTapConfig,
    };
    updateConfig({
      createUserId: rowData.value.createUserId,
      id: rowData.value.id,
      searchProdSource: optionsCheck.searchProdSource.length
        ? JSON.stringify(optionsCheck.searchProdSource)
        : null,
      searchTapConfig: JSON.stringify(obj),
      searchContentConfig: JSON.stringify(options.searchContentConfig),
    }).then((res) => {
      if (res.code === 0) {
        message.success("配置成功");
        defaultValue();
        queryList();
      }
    });
  }
};

const onClickConfirmDialog = () => {
  visibleQuitDialog.value = false;
  defaultValue();
};

const onClickCloseDialog = () => {
  visibleQuitDialog.value = false;
};

const onChange = (val) => {
  optionsCheck.radioChecked = val;
};

const searchPageChange = (index, e) => {
  options.searchContentConfig[index].enable = e;
};

// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  const obj = {
    page: pageInfo.current,
    size: pageInfo.pageSize,
    ...searchParam.value,
  };
  queryList(obj);
};

onMounted(() => {
  getSysUserData("");
  queryList();
});
</script>
<style lang="less" scoped>
@import url("./index.less");
</style>
