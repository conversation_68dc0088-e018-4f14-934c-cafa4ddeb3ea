.section-top {
  width: 100%;
  height: 116px;
  background: #ffffff;
  border-radius: 16px 16px 16px 16px;
  display: flex;
  align-items: center;
  padding: 32px;
  .scroll-name-wrapper {
    margin-right: 72px;
    .scroll-title {
      font-weight: 400;
      font-size: 14px;
      color: #636d7e;
    }
    .scroll-title-text {
      font-weight: 400;
      font-size: 14px;
      color: #05082c;
      margin-top: 16px;
    }
  }
}

.settings-wrapper {
  width: 100%;
  // min-height: 660px;
  background: #fff;
  border-radius: 16px 16px 16px 16px;
  margin-top: 8px;
  padding: 32px;

  .drawer-footer-btn {
    display: flex;
    justify-content: flex-end;
  }

  .drawer-title-wrapper {
    text-align: left;
    margin-top: 10px;
    margin-left: 10px;

    .showStyle {
      margin-bottom: 40px;
    }

    .drawer-title {
      font-weight: 600;
      font-size: 14px;
      color: #05082c;
      line-height: 22px;
      margin-bottom: 30px;
    }

    .search-page {
      display: flex;
    }
  }

  .table-bottom {
    padding-bottom: 24px;
    text-align: right;
  }

  .page-title {
    font-weight: 400;
    font-size: 14px;
    color: #05082c;
  }

  .dialog-body-text {
    font-weight: 400;
    font-size: 14px;
    color: #495366;
    text-align: left;
    margin-left: 18px;
  }
}
.t-input-number {
  width: 180px;
}
