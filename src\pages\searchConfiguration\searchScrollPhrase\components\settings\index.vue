<template>
  <div class="section-top">
    <div class="scroll-name-wrapper">
      <div class="scroll-title">搜索滚动词组名称</div>
      <div class="scroll-title-text">{{ groupInfo.wordGroupName }}</div>
    </div>
    <div class="scroll-name-wrapper">
      <div class="scroll-title">创建人</div>
      <div class="scroll-title-text">{{ groupInfo.createUserName }}</div>
    </div>
    <div class="scroll-name-wrapper">
      <div class="scroll-title">创建时间</div>
      <div class="scroll-title-text">{{ groupInfo.createTime }}</div>
    </div>
  </div>
  <div class="settings-wrapper">
    <div class="table-bottom">
      <a-button type="primary" @click="handleEdit()">新建搜索词</a-button>
    </div>

    <a-table
      row-key="id"
      :data-source="listData"
      :columns="columns"
      :pagination="pagination"
      @change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operate'">
          <div class="flex-box">
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <a-popconfirm
              ok-text="确定"
              cancel-text="取消"
              title="确认后将删除该搜索词！"
              class="btn-css"
              @confirm="handleDelete(record)"
            >
              <a-button type="link">删除</a-button>
            </a-popconfirm>
          </div>
        </template>
      </template>
    </a-table>
    <a-modal
      v-model:open="visibleDialog"
      :title="headerText"
      destroyOnClose
      @cancel="closeDialog"
      @ok="onClickConfirm"
    >
      <a-form
        ref="formRef"
        layout="vertical"
        :model="formData"
        :colon="true"
        :rules="FORM_RULES"
        class="mt20"
      >
        <a-row>
          <a-col>
            <a-form-item label="搜索词" name="rollingWord">
              <a-input
                v-model:value="formData.rollingWord"
                placeholder="请输入搜索词"
                maxlength="10"
                style="width: 420px"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item label="排序" name="wordOrder">
              <a-input-number
                v-model:value="formData.wordOrder"
                :step="1"
                min="0"
                max="999"
                :allow-input-over-limit="false"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import { message } from "woody-ui";
import { columns, FORM_RULES } from "./setData.js";
import {
  updateRollingWord,
  addRollingWord,
  searchRollingWordList,
  deleteSearchRollingWords,
} from "@/api/cms/searchConfig";

const route = useRoute();
const listData = ref([]);
const visibleDialog = ref(false);
const headerText = ref("");
const formRef = ref(null);
const groupInfo = ref({});
const rowData = ref({});
const formData = reactive({
  rollingWord: "",
  wordOrder: "",
});
const pagination = {
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
};

// 查表格数据
const queryList = (
  param = {
    page: pagination.current,
    size: pagination.pageSize,
    groupId: route.query.id,
  }
) => {
  searchRollingWordList(param).then((res) => {
    if (res.code === 0) {
      listData.value = res.data.records;
      pagination.total = res.data.total;
    }
  });
};

const onClickConfirm = () => {
  formRef.value.validate().then((res) => {
    let request;
    if (rowData.value.id) {
      request = updateRollingWord({
        ...formData,
        id: rowData.value.id,
        groupId: groupInfo.value.id,
      });
    } else {
      request = addRollingWord({
        ...formData,
        groupId: groupInfo.value.id,
      });
    }

    request.then((res) => {
      if (res.code === 0) {
        message.success(`${rowData.value.id ? "编辑" : "新增"}成功`);
        pagination.current = 1;
        formRef.value.resetFields();
        queryList();
        closeDialog();
      }
    });
  });
};

const closeDialog = () => {
  visibleDialog.value = false;
  rowData.value = {};
  formRef.value.resetFields();
};

const handleEdit = (row) => {
  visibleDialog.value = true;
  if (row) {
    headerText.value = "修改搜索词组";
    formData.rollingWord = row.rollingWord;
    formData.wordOrder = row.wordOrder;
    rowData.value = row;
  } else {
    formData.rollingWord = "";
    formData.wordOrder = "";
    headerText.value = "新建搜索词组";
  }
};

// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  const obj = {
    page: pageInfo.current,
    size: pageInfo.pageSize,
    groupId: route.query.id,
  };
  queryList(obj);
};

const handleDelete = (row) => {
  deleteSearchRollingWords({
    id: row.id,
    groupId: groupInfo.value.id,
  }).then((res) => {
    if (res.code === 0) {
      message.success("删除成功");
      pagination.current = 1;
      queryList();
    }
  });
};

onMounted(() => {
  groupInfo.value = route.query;
  queryList();
});
</script>
<style lang="less" scoped>
@import url("./index.less");
.line-css {
  margin-right: 20px;
  cursor: pointer;
}
.flex-box {
  display: flex;
  flex-direction: row;
}
</style>
