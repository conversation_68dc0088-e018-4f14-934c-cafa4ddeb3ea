<template>
  <div class="section-top">
    <search-antd :form-list="formList" @on-search="onSubmit" />
  </div>
  <div class="search-scroll-phrase-wrapper">
    <div class="table-bottom">
      <a-button type="primary" @click="handleEdit()"> 新建词组 </a-button>
    </div>

    <a-table
      row-key="id"
      :data-source="listData"
      :columns="columns"
      :pagination="pagination"
      @change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <div v-if="column.key === 'operate'">
          <a-button
            type="link"
            class="btn-css"
            @click="handleEdit(record)"
            >编辑</a-button
          >
          <a-button
            type="link"
            class="btn-css"
            @click="jumpSettings(record)"
            >设置</a-button
          >
          <a-button
            type="link"
            class="btn-css"
            @click="handleCopy(record)"
            >复制</a-button
          >
        </div>
      </template>
    </a-table>
    <a-modal
      v-model:open="visibleDialog"
      :title="headerText"
      @cancel="closeDialog"
      @ok="onClickConfirm"
    >
      <a-form
        ref="formHandleRef"
        layout="vertical"
        :model="formHandleData"
        :colon="true"
        :rules="FORM_RULES"
      >
        <a-row>
          <a-col>
            <a-form-item label="热搜滚动词组名称" name="wordGroupName">
              <a-input
                v-model:value="formHandleData.wordGroupName"
                show-count
                placeholder="请输入热搜滚动词组名称"
                maxlength="30"
                style="width: 420px"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <copy-dialog
      :visible="copyVisible"
      :data-obj="dialogDataObj"
      :maxlength="30"
      @on-close="handleCopyClose"
      @on-confirm="confirmCopy"
    />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { message } from "woody-ui";
import SearchAntd from "@/components/SearchAntd/index.vue";
import CopyDialog from "@/components/CopyDialog/index.vue";
import AddCircle from "@/assets/add-circle.svg";
import { columns, FORM_RULES } from "./setData.js";
import { addGroup, updateGroup, pageGroupList } from "@/api/cms/searchConfig";
import { getSysUserPage } from "@/api/cms/reception";
import { isEmptyValue } from "@/utils";

const router = useRouter();
const formHandleRef = ref(null);
const listData = ref([]);
const visibleDialog = ref(false);
const headerText = ref("");
const searchParam = ref();
const rowData = ref({});
const copyVisible = ref(false);
const dialogDataObj = ref({});
const userArr = ref([]);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPreviousAndNextBtn: false,
});

const remoteMethod = (search) => {
  setTimeout(() => {
    getSysUserData(search);
  }, 1000);
};

// 获取创建人列表数据
const getSysUserData = (username) => {
  const params = {
    page: 1,
    size: 50,
  };
  if (!isEmptyValue(username)) {
    params.username = username;
  }
  // formList[0][1].loading = true;
  getSysUserPage(params)
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data?.records)) {
        // formList[0][1].options = res.data.records;
        // resolve(res.data.records);
        userArr.value = res.data.records;
      } else {
        // formList[0][1].options = [];
      }
    })
    .finally(() => {
      // formList[0][1].loading = false;
    });
};
const formList = [
  {
    label: "热搜滚动词组名称",
    name: "wordGroupName",
    type: "input", // 输入框
    maxlength: 30,
    span: 6,
  },
  {
    label: "创建人",
    name: "createUserId",
    type: "select",
    remoteMethod,
    labelKey: "username",
    valueKey: "userId",
    showSearch: true,
    needFilter: true,

    options: userArr,
    span: 6,
  },
  {
    label: "创建日期",
    name: "createTime",
    type: "datePicker",
    showTime: true,
    span: 6,
  },
];

const formHandleData = reactive({
  wordGroupName: "",
});

const onClickConfirm = () => {
  formHandleRef.value.validate().then((res) => {
    let request;
    if (rowData.value.id) {
      request = updateGroup({
        ...formHandleData,
        id: rowData.value.id,
      });
    } else {
      request = addGroup({
        ...formHandleData,
      });
    }

    request.then((res) => {
      if (res.code === 0) {
        message.success(`${rowData.value.id ? "编辑" : "新增"}成功`);
        pagination.current = 1;
        queryList();
        closeDialog();
      }
    });
  });
};

// 查表格数据
const queryList = (
  param = { page: pagination.current, size: pagination.pageSize }
) => {
  pageGroupList(param).then((res) => {
    if (res.code === 0) {
      listData.value = res.data.records;
      pagination.total = res.data.total;
    }
  });
};

// 分页逻辑
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  const obj = {
    page: pageInfo.current,
    size: pageInfo.pageSize,
    ...searchParam.value,
  };
  queryList(obj);
};

const onSubmit = (param) => {
  searchParam.value = param;
  pagination.current = 1;
  queryList({
    ...param,
    page: 1,
    size: pagination.pageSize,
  });
};

const closeDialog = () => {
  visibleDialog.value = false;
  rowData.value = {};
  formHandleRef.value.reset();
};

const handleEdit = (row) => {
  visibleDialog.value = true;
  if (row) {
    headerText.value = "修改热搜滚动词组名称";
    formHandleData.wordGroupName = row.wordGroupName;
    rowData.value = row;
  } else {
    headerText.value = "新建热搜滚动词组名称";
  }
};

const jumpSettings = (row) => {
  router.push({
    path: "/cms/searchConfiguration/settings",
    query: row,
  });
};

// 复制功能
const handleCopy = (row) => {
  Object.keys(row).forEach((key) => {
    dialogDataObj.value[key] = row[key];
  });
  copyVisible.value = true;
};

// 关闭复制弹框
const handleCopyClose = () => {
  copyVisible.value = false;
};

const confirmCopy = (info) => {
  addGroup({
    id: info.id,
    wordGroupName: info.categoryName,
  }).then((res) => {
    if (res.code === 0) {
      message.success(`复制成功`);
      copyVisible.value = false;
      pagination.current = 1;
      queryList();
    }
  });
};

onMounted(() => {
  getSysUserData("");
  queryList();
});
</script>
<style lang="less" scoped>
@import url("./index.less");
</style>
