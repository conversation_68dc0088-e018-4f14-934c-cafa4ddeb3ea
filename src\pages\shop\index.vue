<template>
  <div v-if="!isLogPage">
    <div class="section">
      <a-row class="row-gap">
        <a-col :span="4">
          <div class="form-item">
            <div class="item-name">渠道门店名称：</div>
            <a-select
              v-model:value="shopTable.params.channelShopName"
              filterable
              placeholder="请输入内容"
              :loading="shopNameLoading"
              :options="shopNames"
              style="width: 100%"
              clearable
              @search="searchShopName"
              @input-change="inputSearchShopName($event)"
            />
          </div>
        </a-col>
        <a-col :span="4">
          <div class="form-item">
            <div class="item-name">渠道门店状态：</div>
            <a-select
              v-model:value="shopTable.params.channelShopStatus"
              :options="statusArr"
              placeholder="请选择"
              filterable
              clearable
            />
          </div>
        </a-col>
      </a-row>
      <div class="end">
        <a-button type="primary" :loading="searchShopLoading" @click="handleSearchShop">查询</a-button>
        <a-button @click="resetShopData">重置</a-button>
      </div>
    </div>
    <div class="section">
      <div class="end">
        <a-button type="primary" @click="goLogHistoryPage">操作日志</a-button>
      </div>
      <!-- table - 店铺列表  -->
      <a-table
        :data-source="shopTable.data.records"
        :columns="shopColumns"
        :row-key="rowKey"
        hover
        :pagination="shopPagination"
        @change="changeShopPage($event)"
      >
        <template #bodyCell="{ column, record }">
          <div v-if="column.dataIndex === 'channelShopStatus'">
            <a-tag v-if="record.channelShopStatus === 1" color="success" variant="light">正常</a-tag>
            <a-tag v-else-if="record.channelShopStatus === 0" color="error" variant="light">已删除</a-tag>
          </div>
          <div v-if="column.dataIndex === 'lifeShopStatus'">
            <a-tag v-if="record.lifeShopStatus === -1" color="error" variant="light">已删除</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 0" color="error" variant="light">停业中</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 1" color="success" variant="light">营业中</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 2" color="default" variant="light">违规下线</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 3" color="warning" variant="light">违规审核</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 4" color="default" variant="light">开店申请中</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 5" color="warning" variant="light">开店申请待审核</a-tag>
            <a-tag v-else-if="record.lifeShopStatus === 9" color="error" variant="light-outline">店铺封禁</a-tag>
          </div>
          <div v-if="column.dataIndex === 'pvPercent'">
            <div>{{ record.pvPercent }}%</div>
          </div>
          <div v-if="column.dataIndex === 'operation'">
            <div class="opt-box">
              <a-button
                v-if="record.bindingStatus === 0 || record.bindingStatus === 2"
                type="primary"
                @click="openBindDia(record)"
              >
                绑定
              </a-button>
              <a-popconfirm
                v-if="record.bindingStatus === 1"
                title="确认解绑吗"
                placement="bottom"
                @confirm="handleUnbind(record)"
              >
                <a-button type="primary">解绑</a-button>
              </a-popconfirm>

              <a-button v-if="record.bindingStatus === 1" type="primary" @click="openScoreDia(record)">
                积分
              </a-button>
            </div>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 弹窗 - 绑定 -->
    <a-modal
      title="绑定生活圈店铺"
      width="50%"
      :close-on-overlay-click="false"
      :visible="lifeShopDia.visible"
      @cancel="closeBindDia"
      prevent-scroll-through
      placement="center"
    >
        <div class="flex" style="justify-content: space-between">
          <div class="form-item">
            <div class="item-name bold">店铺名称：</div>
            <a-input v-model:value="lifeShopDia.params.lifeShopName" placeholder="请输入内容" clearable />
          </div>
          <div class="form-item" style="margin-right: 0">
            <div class="item-name bold">绑定电话：</div>
            <a-input v-model:value="lifeShopDia.params.lifeShopTel" placeholder="请输入内容" clearable />
          </div>
        </div>
        <div class="end">
          <a-button type="primary" variant="base" :loading="searchBindLoading" @click="getLifeShopData">查询</a-button>
          <a-button type="default" variant="outline" @click="resetLifeShopData">重置</a-button>
        </div>
        <div class="section">
          <!-- table -->
          <a-table
            v-model:selectedRowKeys="selectedRowKeys"
            row-key="shopId"
            height="300"
            :columns="lifeShopColumns"
            :data-source="lifeShopDia.data.records"
            :pagination="lifeShopPagination"
            hover
            @change="changeLifeShopPage($event)"
          >
            <template #pvRatio="{ row }">
              <div class="flex">
                <a-input-number v-model:value="row.pvRatio" class="percent-input" />
                <div class="percent-txt">%</div>
              </div>
            </template>
          </a-table>
        </div>
      <template #footer>
        <a-button type="default" @click="closeBindDia">取消</a-button>
        <a-button type="primary" :loading="submitBindLoading" @click="submitBindDia">确定</a-button>
      </template>
    </a-modal>

    <!-- 弹窗 - 积分 -->
    <a-modal
      title="设置渠道同步签约比例"
      width="30%"
      :close-on-overlay-click="false"
      v-model:open="scoreDia.visible"
      @cancel="closeScoreDia"
    >
        <div class="score-dia-body">
          <div class="item">店铺名称：{{ scoreDia.data.lifeShopName }}</div>
          <div class="item">绑定电话：{{ scoreDia.data.lifeShopTel }}</div>
          <div class="item flex">
            <div style="color: red">*&nbsp;&nbsp;</div>
            <div>渠道同步积分比例：</div>
            <a-input-number v-model:value="scoreDia.data.pvPercent" class="percent-input" />
            <div style="margin-left: 6px">%</div>
          </div>
        </div>
      <template #footer>
        <a-button type="default" @click="closeScoreDia">取消</a-button>
        <a-button type="primary" :loading="scoreLoading" @click="submitScoreDia">确定</a-button>
      </template>
    </a-modal>

    <!-- 弹窗 - 不允许绑定 -->
    <a-modal
      v-model:open="notAllowBindDia.visible"
      width="46%"
      :close-on-overlay-click="false"
      theme="warning"
      title="提示"
      confirm-btn="我知道了"
      :cancel-btn="null"
      @cancel="() => (notAllowBindDia.visible = false)"
      @ok="() => (notAllowBindDia.visible = false)"
    >
      <div class="not-allow-txt">
        该门店处于解绑等待期， {{ notAllowBindDia.data.canBindTime }} 后可绑定新的生活圈店铺
      </div>
    </a-modal>
  </div>
  <router-view></router-view>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue';
import { message } from 'woody-ui';
import { useRouter } from 'vue-router';
import { ROW_KEY, STATUS_OPTIONS, SHOP_COLUMNS, LIFE_SHOP_COLUMNS } from './constants';
import { ShopTableData, shop, shopParams } from '@/types/shop/shop';
import { LifeShopTableData, lifeShop } from '@/types/shop/lifeShop';
import { getShopListDataService, getLifeShopListDataService, setChannelRate, getShopName } from '@/api/shop';

const rowKey = ROW_KEY;

// table-shop列表
const searchShopLoading = ref(false);
const statusArr = STATUS_OPTIONS;
const shopColumns = SHOP_COLUMNS;
const shopPagination = ref({
  current: 1,
  pageSize: 10,
  defaultCurrent: 1,
  defaultPageSize: 10,
  total: 0,
});
const shopTable = reactive(new ShopTableData());
let shopSearchParam: shopParams = {
  page: 1,
  size: 10,
  channelShopName: '',
  channelShopStatus: null,
};
const getShopData = (shopSearchParam: shopParams) => {
  getShopListDataService(shopSearchParam)
    .then((res) => {
      if (res.code === 0) {
        const { records, total } = res.data;
        shopTable.data = { records, total };
        shopPagination.value = { ...shopPagination.value, total };
      }
    })
    .finally(() => {
      searchShopLoading.value = false;
    });
};
const changeShopPage = (e: any) => {
  const page = e.pagination.current;
  const size = e.pagination.pageSize;
  shopPagination.value.current = page;
  shopPagination.value.pageSize = size;
  shopSearchParam.page = page;
  shopSearchParam.size = size;
  getShopData(shopSearchParam);
};
const handleSearchShop = () => {
  searchShopLoading.value = true;
  shopTable.params.page = 1;
  shopPagination.value.current = 1;
  shopSearchParam = { ...shopTable.params };
  getShopData(shopSearchParam);
};
const resetShopData = () => {
  shopTable.params = {
    page: 1,
    size: 10,
    channelShopName: '',
    channelShopStatus: null,
  };
  shopPagination.value.current = 1;
  shopPagination.value.pageSize = 10;
  shopSearchParam = { ...shopTable.params };
  getShopData(shopSearchParam);
  searchShopName('');
};
onMounted(() => {
  getShopData(shopSearchParam);
  searchShopName('');
});

const shopNames = ref([]);
const shopNameLoading = ref(false);
const searchShopName = (keyword: string) => {
  shopNameLoading.value = true;
  getShopName({ keyword })
    .then((res) => {
      if (res.code === 0) {
        const tempArr = [];
        res.data.forEach((item) => {
          tempArr.push({
            value: item,
            label: item,
          });
        });
        shopNames.value = tempArr;
      }
    })
    .finally(() => {
      shopNameLoading.value = false;
    });
};
const inputSearchShopName = (e: any) => {
  shopTable.params.channelShopName = e;
};

const notAllowBindDia = reactive({
  visible: false,
  data: {
    canBindTime: null,
  },
});

const timestampToTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const Y = `${date.getFullYear()}-`;
  const M = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
  const D = `${date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()} `;
  const h = `${date.getHours()}:`;
  const m = `${date.getMinutes()}:`;
  const s = date.getSeconds();
  return Y + M + D + h + m + s;
};
// 操作
const activeShopId = ref(null);
const openBindDia = (shop: shop) => {
  activeShopId.value = shop.channelShopId;
  searchBindLoading.value = false;
  submitBindLoading.value = false;

  const canBindTimeStr = new Date(shop.thawingTime).getTime();
  if (canBindTimeStr > new Date().getTime()) {
    notAllowBindDia.visible = true;
    notAllowBindDia.data.canBindTime = timestampToTime(canBindTimeStr);
  } else {
    lifeShopDia.visible = true;
    resetLifeShopData();
  }
};
const closeBindDia = () => {
  lifeShopDia.visible = false;
};
const openScoreDia = (rowData: shop) => {
  scoreDia.visible = true;
  scoreLoading.value = false;
  scoreDia.data = JSON.parse(JSON.stringify(rowData));
};
const closeScoreDia = () => {
  scoreDia.visible = false;
};
const handleUnbind = (rowData: shop) => {
  const data = { ...rowData };
  const submitData = {
    lifeShopId: data.lifeShopId,
    channelShopId: data.channelShopId,
    bindingStatus: 2,
  };

  setChannelRate(submitData).then((res) => {
    if (res.code === 0) {
      message.success('解绑成功');
      getShopData(shopSearchParam);
    }
  });
};

// 弹窗-绑定生活圈店铺
// table-生活圈店铺列表
const lifeShopColumns = LIFE_SHOP_COLUMNS;
const lifeShopPagination = ref({
  defaultCurrent: 1,
  defaultPageSize: 10,
  total: 0,
});
const selectedRowKeys = ref([]);
const searchBindLoading = ref(false);
let lifeShopDia = reactive({
  visible: false,
  ...new LifeShopTableData(),
});
const getLifeShopData = () => {
  if (!lifeShopDia.params.lifeShopName && !lifeShopDia.params.lifeShopTel) {
    message.error('请添加筛选条件');
    return;
  }
  searchBindLoading.value = true;
  getLifeShopListDataService(lifeShopDia.params)
    .then((res) => {
      if (res.code === 0) {
        const { records, total } = res.data;
        lifeShopDia.data = { records, total };
        lifeShopPagination.value = { ...lifeShopPagination.value, total };
      }
    })
    .finally(() => {
      searchBindLoading.value = false;
    });
};
const changeLifeShopPage = (e: any) => {
  const page = e.pagination.current;
  const size = e.pagination.pageSize;
  lifeShopDia.params.page = page;
  lifeShopDia.params.size = size;
  getLifeShopData();
};
const resetLifeShopData = () => {
  lifeShopDia = Object.assign(lifeShopDia, { ...new LifeShopTableData() });
};
// 提交绑定
const submitBindLoading = ref(false);
const submitBindDia = () => {
  if (lifeShopDia.data.records.length === 0) {
    message.error('请查询并选择店铺');
    return;
  }
  const selectIdList = [...selectedRowKeys.value];
  if (!selectIdList.length) {
    message.error('请选择绑定店铺');
    return;
  }
  if (lifeShopDia.data.records.find((item: lifeShop) => item.shopId === selectIdList[0] && !item.pvRatio)) {
    message.error('请填写选中店铺的积分比例');
    return;
  }
  const selectLifeShop = lifeShopDia.data.records.find((item) => item.shopId === selectIdList[0]);
  if (selectLifeShop.pvRatio < 3 || selectLifeShop.pvRatio > 20) {
    message.error('积分范围应在 3 ~ 20');
    return;
  }
  if (`${selectLifeShop.pvRatio}`.includes('.')) {
    message.error('积分必须是整数');
    selectLifeShop.pvRatio = null;
    return;
  }
  const data = { ...selectLifeShop };
  const submitData = {
    lifeShopId: data.shopId,
    channelShopId: activeShopId.value,
    pointRate: data.pvRatio,
    bindingStatus: 1,
    lifeShopName: data.lifeShopName,
  };
  setChannelRate(submitData)
    .then((res) => {
      submitBindLoading.value = true;
      if (res.code === 0) {
        message.success('绑定成功');
        closeBindDia();
        getShopData(shopSearchParam);
      }
    })
    .finally(() => {
      submitBindLoading.value = false;
    });
};

// 弹窗-积分
const scoreLoading = ref(false);
const scoreDia = reactive({
  visible: false,
  data: {} as shop,
});
const submitScoreDia = () => {
  if (!scoreDia.data.pvPercent && scoreDia.data.pvPercent !== 0) {
    message.error('请输入积分');
    return;
  }
  if (scoreDia.data.pvPercent < 3 || scoreDia.data.pvPercent > 20) {
    message.error('积分范围应在 3 ~ 20');
    return;
  }
  if (`${scoreDia.data.pvPercent}`.includes('.')) {
    message.error('积分必须是整数');
    scoreDia.data.pvPercent = null;
    return;
  }
  scoreLoading.value = true;

  const data = { ...scoreDia.data };
  const submitData = {
    lifeShopId: data.lifeShopId,
    channelShopId: data.channelShopId,
    pointRate: data.pvPercent,
  };

  setChannelRate(submitData)
    .then((res) => {
      if (res.code === 0) {
        message.success('保存成功');
        getShopData(shopSearchParam);
        closeScoreDia();
      }
    })
    .finally(() => {
      scoreLoading.value = false;
    });
};

const router = useRouter();
const goLogHistoryPage = () => {
  router.push('/shop/index/logHistory');
};
const isLogPage = ref(false);
watch(
  () => router.currentRoute.value,
  (newVal) => {
    if (newVal.path === '/shop/index/logHistory') {
      isLogPage.value = true;
    } else {
      isLogPage.value = false;
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
.end {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}

.percent-input {
  width: 100px;
}

.percent-txt {
  margin-left: 6px;
}

.score-dia-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: --td-color-text-primary;

  .item {
    margin: 10px 0;
    font-weight: bold;
  }
}

.opt-box {
  display: flex;
  justify-content: flex-start;
  margin-left: -10px;
}

.not-allow-txt {
  color: #333;
  font-size: 16px;
  margin: 20px;
  font-weight: bold;
}
</style>
