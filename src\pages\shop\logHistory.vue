<template>
  <div class="section">
    <a-row class="row-gap">
      <a-col :span="4">
        <div class="form-item">
          <div class="item-name">操作人：</div>
          <a-input v-model:value="logTable.params.createUserName" placeholder="请输入内容" clearable />
        </div>
      </a-col>
      <a-col :span="7">
        <div class="form-item">
          <div class="item-name">操作时间：</div>
          <a-range-picker allow-input clearable @change="onChangeTime" />
        </div>
      </a-col>
    </a-row>
    <div class="end">
      <a-button type="primary" :loading="searchLogLoading" @click="handleSearch">查询</a-button>
      <a-button type="default" @click="onReset">重置</a-button>
    </div>
  </div>
  <div class="section">
    <!-- table - 日志列表 -->
    <a-table
      :data-source="logTable.data.records"
      :columns="logColumns"
      :row-key="rowKey"
      :pagination="logPagination"
      @change="changePage($event)"
    >
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { ROW_KEY, LOG_COLUMNS } from './constants';
import { LogHistoryTableData, logHistoryParams } from '@/types/shop/logHistory';
import { getLogListDataService } from '@/api/shop';

// table - 日志列表
const logColumns = LOG_COLUMNS;
const logPagination = ref({
  current: 1,
  pageSize: 10,
  defaultCurrent: 1,
  defaultPageSize: 10,
  total: 0,
});
const rowKey = ROW_KEY;
const logTable = reactive(new LogHistoryTableData());
let logSearchParam: logHistoryParams = {
  page: 1,
  size: 10,
  startTime: '',
  endTime: '',
  createUserName: '',
};
const searchLogLoading = ref(false);
const getLogData = (logSearchParam: logHistoryParams) => {
  getLogListDataService(logSearchParam)
    .then((res) => {
      if (res.code === 0) {
        const { records, total } = res.data;
        logTable.data = { records, total };
        logPagination.value = { ...logPagination.value, total };
      }
    })
    .finally(() => {
      searchLogLoading.value = false;
    });
};
const changePage = (e: any) => {
  const page = e.pagination.current;
  const size = e.pagination.pageSize;
  logPagination.value.current = page;
  logPagination.value.pageSize = size;
  logSearchParam.page = page;
  logSearchParam.size = size;
  getLogData(logSearchParam);
};
onMounted(() => {
  getLogData(logSearchParam);
});

const onChangeTime = (value, context) => {
  if (context.dayjsValue.length) {
    logTable.params.startTime = context.dayjsValue[0].format('YYYY-MM-DD 00:00:00');
    logTable.params.endTime = context.dayjsValue[1].format('YYYY-MM-DD 23:59:59');
  } else {
    logTable.params.startTime = '';
    logTable.params.endTime = '';
  }
};
const handleSearch = () => {
  searchLogLoading.value = true;
  logTable.params.page = 1;
  logPagination.value.current = 1;
  logSearchParam = { ...logTable.params };
  getLogData(logSearchParam);
};
const onReset = () => {
  logTable.params = {
    page: 1,
    size: 10,
    startTime: '',
    endTime: '',
    createUserName: '',
  };
  logPagination.value.current = 1;
  logPagination.value.pageSize = 10;
  logSearchParam = { ...logTable.params };
  getLogData(logSearchParam);
};
</script>

<style lang="less" scoped>
.end {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}
.percent-input {
  width: 100px;
}
.percent-txt {
  margin-left: 6px;
}
.score-dia-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: --td-color-text-primary;
  .item {
    margin: 10px 0;
    font-weight: bold;
  }
}
</style>
