import { message } from 'woody-ui';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style

import { getPermissionStore, getUserStore } from '@/store';
import router from '@/router';

NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, from, next) => {
  NProgress.start();

  const userAuth = JSON.parse(localStorage.getItem('user-auth')) || '';
  const accessToken = userAuth ? userAuth.accessToken : '';

  const userStore = getUserStore();
  const permissionStore = getPermissionStore();
  const whiteListRouters = ['/login'];

  if (accessToken) {
    if (to.path === '/login') {
      next();
      return;
    }
    const { roles } = userStore;
    if (roles && roles.length > 0) {
      permissionStore.initRoutes(roles);
      next();
    } else {
      try {
        const userInfo = await userStore.getUserInfo();
        permissionStore.initRoutes(userInfo.roles);
        if (router.hasRoute(to.name)) {
          next();
        } else {
          next(`/`);
        }
      } catch (error) {
        // eslint-disable-next-line no-underscore-dangle
        if (window.__MICRO_APP_ENVIRONMENT__) {
          // 发送跳转 login 的事件
          window.parent.navigateTo("/login", { redirect: encodeURIComponent(to.fullPath),project:'market' } );
        } else {
          next({
            path: "/login",
            query: { redirect: encodeURIComponent(to.fullPath) },
          });
          NProgress.done();
        }
      }
    }
  } else {
    // 没有accessToken时
    if (whiteListRouters.indexOf(to.path) !== -1) {
      next();
    } else if (window.__MICRO_APP_ENVIRONMENT__) {
      // 发送跳转 login 的事件
      window.parent.navigateTo("/login",{ redirect: encodeURIComponent(to.fullPath),project:'market' });
    } else {
      next({
        path: "/login",
        query: { redirect: encodeURIComponent(to.fullPath) },
      });
    }
    NProgress.done();
  }
});

router.afterEach((to) => {
  if (to.path === '/login') {
    const userStore = getUserStore();
    const permissionStore = getPermissionStore();

    userStore.logout();
    permissionStore.restore();
  }
  NProgress.done();
});
