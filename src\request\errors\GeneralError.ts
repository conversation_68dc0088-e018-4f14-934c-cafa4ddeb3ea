class GeneralError {
  #status: number;

  #error: string;

  #message: string;

  constructor(status: number, error: string, message: string) {
    this.#status = status;
    this.#error = error;
    this.#message = message;
  }

  get status() {
    return this.#status;
  }

  get error() {
    return this.#error;
  }

  get message() {
    return this.#message;
  }

  toString() {
    return this.#message || this.#error;
  }
}

export default GeneralError;
