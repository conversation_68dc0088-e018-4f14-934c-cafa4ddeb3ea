import { message } from "woody-ui";
import {
  TokenProvider,
  tokenStore,
  getDefaultTokenProvider,
} from "./tokenProvider";
import FetchError from "./errors/FetchError";
import GeneralError from "./errors/GeneralError";
import UnknownError from "./errors/UnknownError";
import router from "@/router";
import { useRoute } from "vue-router";
import { ref } from "vue";
const count = ref(0); // 计数器，遇到同一页面同时请求多个借口，同时报错之弹一次错误提醒
const tokenCount = ref(0); // 计算token次数

export interface RequestParams {
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  path: string;
  headers?: { [key: string]: string };
  tokenProvider?: TokenProvider;
  data?: FormData | { [key: string]: any };
  includeCredentials?: boolean;
  showMsgError?: boolean;
}

const buildPayload = async (response: Response) => {
  const responseType = response.headers.get("content-type") || "";
  if (responseType.startsWith("text/")) {
    return response.text();
  }
  if (responseType.indexOf("application/json") !== -1) {
    return response.json();
  }
  return response.blob();
};

const codeList = [802901, 802902, 802903, 802471, 401]; // 接口返回code码都需要重新登录
const isMessage = ref(false); // 判断是否需要弹出错误消息

const buildResponse = async <T>(response: Response, showMsgError) => {
  tokenCount.value = 0;
  const { status, statusText } = response;
  const payload = await buildPayload(response);
  console.log(payload,'payload')
  const path = router.options.history.location;
  if (codeList.includes(Number(payload.code))) {
    // eslint-disable-next-line no-underscore-dangle
    isMessage.value = true;
    if (window.__MICRO_APP_ENVIRONMENT__) {
      // 01s后发送跳转 login 的事件
      setTimeout(() => {
        window.parent.navigateTo("/login", {
          redirect: encodeURIComponent(path),
          project: "market",
        });
      }, 1000);

      // throw new TokenError(payload.message ?? statusText);
      throw new GeneralError(status, payload.code, payload.message);
    } else {
      router.push({
        path: "/login",
        query: {
          redirect: encodeURIComponent(path),
        },
      });
    }
  } else {
    isMessage.value = false;
  }
  //接口正常，但业务接口code错误
  if (status == 200 && payload.code != 0 && showMsgError) {
    if (typeof payload === "object" && payload.code && payload.message) {
      message.error(`${payload.code}: ${payload.message}`);
      return;
    }
    
  }
  if (
    (status >= 400 && status < 600) ||
    (payload.code >= 400 && payload.code < 600)
  ) {
    if (typeof payload === "object" && payload.code && payload.message) {
      throw new GeneralError(status, payload.code, payload.message);
    }
    throw new UnknownError(status, statusText);
  }
  if (status > 600 || payload.code > 600) {
    if (typeof payload === "object" && payload.code && payload.message) {
      throw new GeneralError(status, payload.code, payload.message);
    }
    throw new UnknownError(status, statusText);
  }
  
  return payload as T;
};

const { VITE_API_URL } = import.meta.env;
const request = async <T>(params: RequestParams): Promise<any> => {
  count.value = 0;
  let tokenProvider;
  /*
    判断是否在微环境运行，是的话则调用主应用刷新token的方法， window.__MICRO_APP_ENVIRONMENT__ == true 表示在微环境
    子应用调用主应用方法获取刷新token--window.parent.mainRefreshToken()
    脱离主应用单独运行刷新token的方法--reactive(getDefaultTokenProvider())
  */
  if (window.__MICRO_APP_ENVIRONMENT__) {
    tokenProvider = await window.parent.mainRefreshToken();
  } else {
    tokenProvider = getDefaultTokenProvider();
  }
  console.log(tokenProvider, "基座拿到的token");
  const {
    method,
    data,
    includeCredentials,
    headers,
    showMsgError = true,
  } = params;
  const path = VITE_API_URL + params.path;
  const isRequestJson = method !== "GET" && !(data instanceof FormData);
  let response: Response;
  console.log(path, "调接口");
  try {
    response = await window.fetch(path, {
      method,
      headers: {
        ...(isRequestJson ? { "Content-Type": "application/json" } : {}),
        ...(tokenProvider
          ? { Authorization: `${tokenProvider.accessToken}` }
          : {}),
        ...(headers || {}),
        // 'X-Accept-Version': 'yanghongfa', // 杨宏发
        // 'X-Accept-Version': 'wx1', // 王旭
      },
      ...(method === "GET"
        ? {}
        : { body: data instanceof FormData ? data : JSON.stringify(data) }),
      ...(includeCredentials ? { credentials: "include" } : {}),
    });
  } catch (e) {
    throw new FetchError(`fetch error ${e}`);
  }
  let requestError: any;
  try {
    return await buildResponse<any>(response, showMsgError);
  } catch (e) {
    if (e.message === "Unauthorized") {
      if (window.__MICRO_APP_ENVIRONMENT__) {
        // 01s后发送跳转 login 的事件
        setTimeout(() => {
          window.parent.navigateTo("/login", {
            redirect: encodeURIComponent(path),
            project: "market",
          });
        }, 1000);
      } else {
        router.push({
          path: "/login",
          query: {
            redirect: encodeURIComponent(path),
          },
        });
      }
    } else {
      if (showMsgError) {
        message.error(e.message);
      }
    }
    requestError = e;
  }
  count.value += 1;
  if (count.value === 1) {
    // 错误信息提示
    console.log(isMessage, "isMessage");
    if (isMessage.value && showMsgError) {
      message.error(requestError.message || requestError.statusText);
    }
  }

  throw requestError;
};

export default request;
