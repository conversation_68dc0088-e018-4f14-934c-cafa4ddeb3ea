import Layout from "@/layouts/index.vue";
import goodsIcon from "@/assets/images/goods_icon.svg?url";
import activeGoodsIcon from "@/assets/images/goods_icon_act.svg?url";

export const goodsCenter = {
    path: "/product",
    component: Layout,
    name: "product",
    nameEn: "npGoodsCenter",
    meta: {
      title: "商品中心",
      icon: goodsIcon,
      activeIcon: activeGoodsIcon,
      expanded: false,
      state: "IS_SHOW",
    },
    children: [
      {
        path: "localLife",
        name: "npEcommerceLocalLife",
        nameEn: "npEcommerceLocalLife",
        meta: { title: "本地生活", expanded: true },
        redirect: "/localLife/index",
        children: [
          {
            path: "groupCommodity",
            name: "npGroupCommodity",
            nameEn: "npGroupCommodity",
            component: () =>
              import("@/pages/goodsCenter/localLife/groupCommodity/index.vue"),
            meta: { title: "团购商品管理", expanded: true },
            children: [],
          },
          {
            path: "groupAudit",
            name: "npGroupAudit",
            nameEn: "npGroupAudit",
            component: () =>
              import("@/pages/goodsCenter/localLife/groupAudit/index.vue"),
            meta: { title: "团购商品审核", expanded: true },
            children: [],
          },
          {
            path: "packageClassify",
            name: "npPackageClassify",
            nameEn: "npPackageClassify",
            component: () =>
              import("@/pages/goodsCenter/localLife/packageClassify/index.vue"),
            meta: { title: "吃喝玩乐套餐分类", expanded: true },
            children: [],
          },
          {
            path: "propertyProjectManage",
            name: "propertyProjectManage",
            nameEn: "npPropertyProjectManage",
            component: () =>
              import("@/pages/goodsCenter/localLife/propertyProjectManage/index.vue"),
            meta: { title: "房地产项目管理", expanded: true },
            children: [],
          },
          {
            path: "createPropertyInfo",
            name: "createPropertyInfo",
            nameEn: "",
            component: () =>
              import(
                "@/pages/goodsCenter/localLife/propertyProjectManage/create/index.vue"
              ),
            meta: {
              hidden: true,
              replace: "propertyProjectManage",
              bread: ["房地产项目管理", "创建楼盘信息"],
            },
          },
        ],
      },
      {
        path: "ecommerceGoods",
        name: "npEcommerceGoods",
        nameEn: "npEcommerceGoods",
        meta: { title: "电商商品", expanded: true },
        redirect: "/ecommerceGoods/index",
        children: [
          {
            path: "index",
            name: "npGoodsList",
            nameEn: "npGoodsList",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/index.vue"),
            meta: { title: "商品列表", expanded: true },
            children: [],
          },
          {
            path: "jdProduct",
            name: "jdProduct",
            nameEn: "npJdProduct",
            meta: { title: "京东商品列表" },
            component: () =>
              import("@/pages/product/onlineShop/jdProduct/jdList.vue"),
          },
          {
            path: "jdDetail",
            name: "jdDetail",
            nameEn: "",
            component: () =>
              import("@/pages/product/onlineShop/jdProduct/jdDetail.vue"),
            meta: { title: "详情", hidden: true },
          },
          {
            path: "operationUp",
            name: "npOperationUp",
            nameEn: "npOperationUp",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/operationUp.vue"),
            meta: { title: "运营上架审核", expanded: true },
            children: [],
          },
          {
            path: "operationOff",
            name: "npOperationOff",
            nameEn: "npOperationOff",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/operationOff.vue"),
            meta: { title: "运营下架审核", expanded: true },
            children: [],
          },
          {
            path: "operationEdit",
            name: "npOperationEdit",
            nameEn: "npOperationEdit",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/operationEdit.vue"),
            meta: { title: "运营修改审核", expanded: true },
            children: [],
          },
          {
            path: "specsManagement",
            name: "specsManagement",
            nameEn: "npSpecsManagement",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/specsManagement.vue"),
            meta: { title: "规格管理", expanded: true },
            children: [],
          },
          {
            path: "goodsPoints",
            name: "npGoodsPoints",
            nameEn: "npGoodsPoints",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/goodsPoints.vue"),
            meta: { title: "商品积分调整", expanded: true },
            children: [],
          },
          {
            path: "pointsLog",
            name: "pointsLog",
            nameEn: "",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/pointsLog.vue"),
            meta: { title: "操作日志", expanded: true, hidden: true },
            children: [],
          },
          {
            path: "reviewHistory",
            name: "reviewHistory",
            nameEn: "",
            component: () =>
              import("@/pages/goodsCenter/ecommerceGoods/reviewHistory.vue"),
            meta: { title: "审核历史", expanded: true, hidden: true },
            children: [],
          },
          {
            path: "goldShopList",
            name: "goldShopList",
            nameEn: "npGoldShopList",
            component: () =>
              import("@/pages/goodsCenter/goldShopList/index.vue"),
            meta: { title: "金币商城商品列表", expanded: true },
            children: [],
          },
        ],
      },
      {
        path: "ecommerceBrand",
        name: "npEcommerceBrand",
        nameEn: "npEcommerceBrand",
        meta: { title: "电商品牌", expanded: true },
        redirect: "/ecommerceBrand/index",
        children: [
          {
            path: "brandList",
            name: "npBrandList",
            nameEn: "npBrandList",
            component: () =>
              import("@/pages/goodsCenter/ecommerceBrand/brandList/index.vue"),
            meta: { title: "品牌列表", expanded: true },
            children: [],
          },
          {
            path: "platformClassification",
            name: "npPlatformClassification",
            nameEn: "npPlatformClassification",
            component: () =>
              import(
                "@/pages/goodsCenter/ecommerceBrand/platformClassification/index.vue"
              ),
            meta: { title: "爱库存品牌分类", expanded: true },
            children: [],
          },
          // 这个为跳转页
          {
            path: "brandManagement/:categoryId/:categoryName",
            name: "brandManagement",
            nameEn: "",
            component: () =>
              import(
                "@/pages/goodsCenter/ecommerceBrand/brandManagement/index.vue"
              ),
            meta: { title: "品牌管理", expanded: true, hidden: true },
            children: [],
          },
        ],
      },
      {
        path: "ecommerceClassify",
        name: "npEcommerceClassify",
        nameEn: "npEcommerceClassify",
        meta: { title: "电商分类", expanded: true },
        children: [
          {
            path: "backendClassifiyList",
            name: "npBackendClassifiyList",
            nameEn: "npBackendClassifiyList",
            component: () =>
              import(
                "@/pages/goodsCenter/ecommerceClassify/backendClassifiyList.vue"
              ),
            meta: { title: "后台分类列表", expanded: true },
            children: [],
          },
          {
            path: "smallShopClassifiyList",
            name: "npSmallShopClassifiyList",
            nameEn: "npSmallShopClassifiyList",
            component: () =>
              import(
                "@/pages/goodsCenter/ecommerceClassify/smallShopClassifiyList.vue"
              ),
            meta: { title: "小店分类列表", expanded: true },
            children: [],
          },
          {
            path: "classificationAKCList",
            name: "npClassificationAKCList",
            nameEn: "npClassificationAKCList",
            component: () =>
              import(
                "@/pages/goodsCenter/ecommerceClassify/classificationAKCList/index.vue"
              ),
            meta: { title: "爱库存分类列表", expanded: true },
            children: [],
          },
          {
            path: "jsSortList",
            name: "jsSortList",
            nameEn: "npJsSortList",
            component: () => import("@/pages/product/onlineShop/index.vue"),
            meta: { title: "京东分类列表", expanded: true },
          },
        ],
      },
      {
        path: "check",
        name: "productCheck",
        nameEn: "npRiskControlAudit",
        meta: { title: "风控审核", state: "IS_SPOT" },
        children: [
          {
            path: "system",
            name: "productSystem",
            nameEn: "npSystemReview",
            component: () => import("@/pages/product/check/system.vue"),
            meta: { title: "系统审核" },
            children: [
              {
                path: "detail/:nodeType/:productId/:auditType/:auditId/:nodeId",
                name: "checkSysDetail",
                component: () => import("@/pages/product/check/detail.vue"),
                meta: { title: "查看", hidden: true },
              },
            ],
          },
          {
            path: "marketing",
            name: "productMarketing",
            nameEn: "npMarketReview",
            component: () => import("@/pages/product/check/marketing.vue"),
            meta: { title: "营销审核", expanded: true },
            children: [
              {
                path: "detail/:nodeType/:productId/:auditType/:auditId/:nodeId",
                name: "checkMarketDetail",
                component: () => import("@/pages/product/check/detail.vue"),
                meta: { title: "查看", hidden: true },
              },
            ],
          },
          {
            path: "risk",
            name: "productRisk",
            nameEn: "npGoods_riskControlAudit",
            component: () => import("@/pages/product/check/risk.vue"),
            meta: { title: "风控审核", expanded: true },
            children: [
              {
                path: "detail/:nodeType/:productId/:auditType/:auditId/:nodeId",
                name: "checkRiskDetail",
                component: () => import("@/pages/product/check/detail.vue"),
                meta: { title: "查看", hidden: true },
              },
            ],
          },
        ],
      },
    ],
  }