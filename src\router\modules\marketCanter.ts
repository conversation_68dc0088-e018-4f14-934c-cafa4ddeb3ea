import Layout from "@/layouts/index.vue";

import centerIcon from "@/assets/images/center_icon.svg?url";
import activeCenterIcon from "@/assets/images/center_icon_act.svg?url";

export const marketCanter = {
  path: "/activity",
  component: Layout,
  name: "activity",
  nameEn: "npActivityCenter",
  meta: {
    title: "营销中心",
    icon: centerIcon,
    activeIcon: activeCenterIcon,
    expanded: false,
    state: "IS_SHOW",
  },
  children: [
    {
      path: "activityOnlineShop",
      name: "activityOnlineShop",
      nameEn: "npOnlineStore",
      meta: { title: "线上电商", expanded: true },
      children: [
        {
          path: "flashSales",
          name: "flashSales",
          nameEn: "npFlashSales",
          component: () =>
            import("@/pages/marketingCenter/flashSales/flashSales.vue"),
          meta: { title: "限时特惠" },
        },
        {
          path: "flashSalesImport",
          name: "flashSalesImport",
          nameEn: "npFlashSalesImport",
          component: () =>
            import(
              "@/pages/marketingCenter/flashSalesImport/flashSalesImport.vue"
            ),
          meta: { title: "批量导入" },
        },
        //   {
        //     path: "platformFullReduction",
        //     name: "platformFullReduction",
        //     nameEn: "npPlatformFullReduction",
        //     component: () =>
        //       import(
        //         "@/pages/activity/onlineShop/platformFullReduction/index.vue"
        //       ),
        //     meta: { title: "平台满减", expanded: true },
        //     children: [
        //       {
        //         path: "platformFullReductionDetail",
        //         name: "platformFullReductionDetail",
        //         component: () =>
        //           import(
        //             "@/pages/activity/onlineShop/platformFullReduction/platDetail/index.vue"
        //           ),
        //         meta: { title: "查看详情", expanded: true },
        //         children: [
        //           {
        //             path: "platformProductManage",
        //             name: "platformProductManage",
        //             component: () =>
        //               import(
        //                 "@/pages/activity/onlineShop/platformFullReduction/productManage/index.vue"
        //               ),
        //             meta: { title: "管理商品", expanded: true },
        //           },
        //         ],
        //       },
        //     ],
        //   },
        {
          path: "specialOffer",
          name: "specialOffer",
          nameEn: "npSpecialOffer",
          component: () =>
            import("@/pages/activity/onlineShop/specialOffer/index.vue"),
          meta: { title: "特价专区", expanded: true },
          children: [],
        },
        {
          path: "specialDetail",
          name: "specialDetail",
          nameEn: "",
          component: () =>
            import(
              "@/pages/activity/onlineShop/specialOffer/specialDetail.vue"
            ),
          meta: { title: "专区详情", expanded: true },
          children: [],
        },
        {
          path: "goldCoinSeckill",
          name: "goldCoinSeckill",
          nameEn: "npGoldCoinSeckill",
          component: () =>
            import("@/pages/activity/onlineShop/goldCoinSeckill/index.vue"),
          meta: { title: "金币秒杀", expanded: true },
          children: [],
        },
        {
          path: "goldcoinCommodity",
          name: "goldcoinCommodity",
          component: () =>
            import(
              "@/pages/activity/onlineShop/goldCoinSeckill/goldcoinCommodity.vue"
            ),
          meta: { title: "设置场次商品", expanded: true },
          children: [],
        },
        {
          path: "newPeopleOffer",
          name: "newPeopleOffer",
          nameEn: "npNewPeopleOffer",
          component: () =>
            import("@/pages/activity/onlineShop/newPeopleOffer/index.vue"),
          meta: { title: "新人专区", expanded: true },
          children: [],
        },
        {
          path: "selectionOfficer",
          name: "selectionOfficer",
          nameEn: "npSelectionOfficer",
          component: () =>
            import("@/pages/activity/onlineShop/selectionOfficer/index.vue"),
          meta: { title: "选品官列表", expanded: true },
          children: [],
        },
        {
          path: "detail",
          name: "flashSalesDetail",
          nameEn: "npFlashSalesDetail",
          component: () =>
            import("@/pages/marketingCenter/flashSales/details.vue"),
          meta: {
            hidden: true,
            replace: "flashSales",
            bread: ["限时特惠", "查看"],
          },
        },
        {
          path: "add",
          name: "addFlashSales",
          nameEn: "npAddFlashSales",
          component: () =>
            import("@/pages/marketingCenter/flashSales/addOrEdit.vue"),
          meta: {
            hidden: true,
            replace: "flashSales",
            bread: ["限时特惠", "新增活动"],
          },
        },
        {
          path: "edit",
          name: "editFlashSales",
          nameEn: "npEditFlashSales",
          component: () =>
            import("@/pages/marketingCenter/flashSales/addOrEdit.vue"),
          meta: {
            hidden: true,
            replace: "flashSales",
            bread: ["限时特惠", "编辑活动"],
          },
        },
        {
          path: "addCommodity",
          name: "addCommodity",
          nameEn: "npAddActivityCommodity",
          component: () =>
            import("@/pages/marketingCenter/flashSales/addCommodity.vue"),
          meta: {
            hidden: true,
            replace: "flashSales",
            bread: ["限时特惠", "新增商品"],
          },
        },
        {
          path: "flashSalesLog",
          name: "flashSalesLog",
          nameEn: "npFlashSalesLog",
          component: () =>
            import("@/pages/marketingCenter/flashSales/operationLog.vue"),
          meta: {
            hidden: true,
            replace: "flashSales",
            bread: ["限时特惠", "操作日志"],
          },
        },
        {
          path: "ruralRevitalization",
          name: "ruralRevitalization",
          nameEn: "npRuralRevitalization",
          component: () =>
            import("@/pages/activity/onlineShop/ruralRevitalization/index.vue"),
          meta: { title: "乡村振兴", expanded: true },
          children: [],
        },
      ],
    },
    {
      path: "activityLocalLife",
      name: "activityLocalLife",
      nameEn: "npActivityLocalLife",
      meta: { title: "本地生活", expanded: true },
      children: [
        {
          path: "groupPoints",
          name: "groupPoints",
          nameEn: "npGroupPoints",
          component: () =>
            import("@/pages/activity/activityLocalLife/groupPoints/index.vue"),
          meta: { title: "团购推广积分", expanded: true },
          children: [],
        },
        {
          path: "productManagement",
          name: "productManagement",
          nameEn: "npProductManagement",
          component: () =>
            import(
              "@/pages/activity/activityLocalLife/productManagement/index.vue"
            ),
          meta: { title: "营销商品管理", expanded: true },
          children: [],
        },
      ],
    },
    {
      path: "marketTools",
      name: "marketTools",
      nameEn: "npMarketTools",
      meta: { title: "营销工具", expanded: true },
      children: [
        {
          path: "raffle",
          name: "raffle",
          nameEn: "npRaffle",
          component: () =>
            import("@/pages/activity/marketTools/raffle/index.vue"),
          meta: { title: "抽奖活动", expanded: true },
          children: [],
        },
        {
          path: "addActivity",
          name: "addActivity",
          component: () =>
            import("@/pages/activity/marketTools/raffle/addActivity.vue"),
          meta: { title: "新增抽奖", expanded: true, replace: "raffle" },
          children: [],
        },
        {
          path: "raffleDetail",
          name: "raffleDetail",
          component: () =>
            import("@/pages/activity/marketTools/raffle/detail.vue"),
          meta: {
            title: "抽奖活动详情",
            expanded: true,
            replace: "raffle",
            hidden: true,
          },
          children: [],
        },
        {
          path: "lotteryRecords",
          name: "lotteryRecords",
          nameEn: "npLotteryRecords",
          component: () =>
            import("@/pages/activity/marketTools/lotteryRecords/index.vue"),
          meta: { title: "抽奖记录", expanded: true },
          children: [],
        },
        {
          path: "dataStatistics",
          name: "dataStatistics",
          // nameEn: "npDataStatistics",
          component: () =>
            import("@/pages/activity/marketTools/dataStatistics/index.vue"),
          meta: { title: "数据统计", replace: "raffle" },
          children: [],
        },
      ],
    },
  ],
};
