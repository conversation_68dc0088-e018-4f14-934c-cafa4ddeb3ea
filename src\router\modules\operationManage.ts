import Layout from "@/layouts/index.vue";
import operationIcon from "@/assets/images/operation_icon.svg?url";
import activeOperationIcon from "@/assets/images/operation_icon_act.svg?url";


export const operationManage = {
    path: "/operationManagement",
    component: Layout,
    name: "operationManagement",
    nameEn: "npOperationManagement",
    meta: {
      title: "运营管理",
      icon: operationIcon,
      activeIcon: activeOperationIcon,
      expanded: false,
      state: "IS_SHOW",
    },
    children: [
      {
        path: "shopMallRestrictions",
        name: "npShopMallRestrictions",
        nameEn: "npShopMallRestrictions",
        meta: { title: "下单限制", expanded: true, state: "IS_SPOT" },
        component: () =>
          import("@/pages/operation/shopMallRestrictions/index.vue"),
        children: [],
      },
      {
        path: "supplyWarehouseManagement",
        name: "npSupplyWarehouseManagement",
        nameEn: "npOperationSupplyWarehouseManagement",
        meta: { title: "供货仓管理", expanded: true },
        children: [
          {
            path: "shopMallFreight",
            name: "npShopMallFreight",
            nameEn: "npShopMallFreight",
            component: () =>
              import(
                "@/pages/operation/supplyWarehouseManagement/shopMallFreight/index.vue"
              ),
            meta: { title: "商城运费", expanded: true },
          },
          {
            path: "expressLogistics",
            name: "expressLogistics",
            nameEn: "npExpressLogistics",
            component: () =>
              import(
                "@/pages/operation/supplyWarehouseManagement/expressLogistics/index.vue"
              ),
            meta: { title: "快递物流", expanded: true },
            children: [
              {
                path: "createTemplate",
                name: "createTemplate",
                component: () =>
                  import(
                    "@/pages/operation/supplyWarehouseManagement/expressLogistics/createTemplate/index.vue"
                  ),
                meta: { title: "快递模板", hidden: true },
              },
              {
                path: "chanAndEditTemplate",
                name: "chanAndEditTemplate",
                component: () =>
                  import(
                    "@/pages/operation/supplyWarehouseManagement/expressLogistics/chanAndEditTemplate/index.vue"
                  ),
                meta: { title: "快递模板", hidden: true },
              },
            ],
          },
          {
            path: "sameCityLogistics",
            name: "npSameCityLogistics",
            nameEn: "npSameCityLogistics",
            component: () =>
              import(
                "@/pages/operation/supplyWarehouseManagement/sameCityLogistics/index.vue"
              ),
            meta: { title: "同城配送", expanded: true },
            children: [
              {
                path: "sameCityCreate",
                name: "npSameCityCreate",
                nameEn: "npSameCityCreate",
                component: () =>
                  import(
                    "@/pages/operation/supplyWarehouseManagement/sameCityLogistics/sameCityCreate/index.vue"
                  ),
                meta: { title: "新增配送模板", hidden: true },
              },
              {
                path: "sameCityLogisticsDetail",
                name: "sameCityLogisticsDetail",
                component: () =>
                  import(
                    "@/pages/operation/supplyWarehouseManagement/sameCityLogistics/sameCityLogisticsDetail/index.vue"
                  ),
                meta: { title: "配送模板详情", hidden: true },
              },
            ],
          },
        ],
      },
      {
        path: "goldCoinManagement",
        name: "goldCoinManagement",
        nameEn: "npGoldCoinManagement",
        meta: { title: "金币商城管理", expanded: true },
        children: [
          {
            path: "coinConfiguration",
            name: "coinConfiguration",
            nameEn: "npCoinConfiguration",
            component: () =>
              import(
                "@/pages/operation/goldCoinManagement/coinConfiguration/index.vue"
              ),
            meta: { title: "金币配置", expanded: true },
          },
          {
            path: "coinGManagement",
            name: "coinGManagement",
            nameEn: "npCoinManagement",
            component: () =>
              import(
                "@/pages/operation/goldCoinManagement/coinGManage/index.vue"
              ),
            meta: { title: "金币管理", expanded: true },
          },
        ],
      },
      {
        path: "alarmManagement",
        name: "alarmManagement",
        nameEn: "alarmManage",
        meta: { title: "预警管理", expanded: true },
        redirect: "/operationManagement/alarmManagement/newUser",
        children: [
          {
            path: "newUser",
            name: "newUser",
            nameEn: "newComerBenefitsAlarm",
            component: () =>
              import("@/pages/operation/alarmManagement/newUser/index.vue"),
            meta: { title: "新人福利预警", expanded: true },
          },
        ],
      },
      {
        path: "shortDramaConfig",
        name: "npShortDramaConfig",
        nameEn: "npShortDramaConfig",
        meta: { title: "短剧配置", expanded: true, state: "IS_SPOT" },
        component: () =>
          import("@/pages/operation/shortDramaConfig/index.vue"),
        children: [],
      },
    ],
  }