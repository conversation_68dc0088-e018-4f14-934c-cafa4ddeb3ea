import { getDecorationStore } from '@/store';
import { getNextComponentId } from '@/api/decoration';
import { message } from 'woody-ui';

// 获取下一个组件ID
export const getNextComponentIdFun = async (): Promise<number> => {
  try {
    const res = await getNextComponentId({});
    if (res.code === 0 && res.data) {
      return res.data;
    }
  } catch (error) {
    console.log('获取组件ID失败，使用模拟ID', error);
  }
  
  // 如果API调用失败，返回模拟ID（当前时间戳）
  return Date.now();
};

// 创建关联组件
export const createRelatedComponent = async (
  sourceComponentId: string | number,
  componentType: 'GOODS_COMPONENT' | 'BRAND_COMPONENT',
  componentData: any
) => {
  const decorationStore = getDecorationStore();
  
  try {
    // 获取新组件ID
    const componentId = await getNextComponentIdFun();
    
    // 根据组件类型创建不同的组件
    let newComponent: any = {
      id: componentId,
      flagId: `${componentType === 'GOODS_COMPONENT' ? 'goods' : 'brand'}${Date.now()}`
    };
    
    if (componentType === 'GOODS_COMPONENT') {
      newComponent = {
        ...newComponent,
        templateName: '商品',
        templateId: 'goods',
        info: {
          style: componentData.style,
          feCategoryId: componentData.feCategoryId,
          feCategoryName: componentData.feCategoryName,
          imgGroup: componentData.imgGroup
        }
      };
    } else if (componentType === 'BRAND_COMPONENT') {
      newComponent = {
        ...newComponent,
        templateName: '品牌',
        templateId: 'brand',
        info: {
          type: componentData.type || '1',
          platCategoryId: componentData.platCategoryId,
          platCategoryName: componentData.platCategoryName,
          brands: componentData.brands || []
        }
      };
    }
    
    // 添加到store中
    decorationStore.addRelatedComponent(newComponent);
    
    // 返回创建的组件ID，用于关联
    return componentId;
  } catch (error) {
    console.error('创建关联组件失败', error);
    message.error('创建关联组件失败');
    return null;
  }
};

// 更新关联组件
export const updateRelatedComponent = (
  componentId: string | number,
  componentData: any
) => {
  const decorationStore = getDecorationStore();
  decorationStore.updateRelatedComponent(componentId, componentData);
};

// 删除关联组件
export const deleteRelatedComponent = (
  componentId: string | number
) => {
  const decorationStore = getDecorationStore();
  decorationStore.deleteRelatedComponent(componentId);
};

// 获取关联组件
export const getRelatedComponent = (
  componentId: string | number
) => {
  const decorationStore = getDecorationStore();
  const { decorationInfo } = decorationStore;
  
  if (!decorationInfo.relatedComponents) {
    return null;
  }
  
  return decorationInfo.relatedComponents.find(
    (item: any) => String(item.id) === String(componentId)
  );
}; 