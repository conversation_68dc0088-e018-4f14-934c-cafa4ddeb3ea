import { defineStore } from 'pinia';
import { store } from '@/store';
import { tokenStore } from '@/request/tokenProvider';
import { accountLogin } from '@/api/login';

interface LoginRequestParam {
  code: string;
  password: string;
  userName: string;
}

export const useUserStore = defineStore('user-info', {
  state: () => ({
    roles: [],
    permissions: [],
    userName: '',
  }),
  actions: {
    async login(loginData: LoginRequestParam): Promise<any> {
      const res = await accountLogin(loginData);
      if (res.code === 0) {
        this.userName = loginData.userName;

        localStorage.setItem('user-auth', JSON.stringify(res.data));
        tokenStore.setToken(res.data.accessToken);
        tokenStore.setRefreshToken(res.data.refreshToken);
      }
      return Promise.resolve(res.code);
    },
    async getUserInfo() {
      const data = {
        roles: ['all', 'admin'],
        permissions: ['shopCenter', 'shopList', 'orderCenter', 'orderList'],
      };

      const res = await new Promise((resolve) => {
        setTimeout(() => {
          resolve(data);
        }, 10);
      });

      this.roles = data.roles;
      this.permissions = data.permissions;

      return {
        roles: this.roles,
        permissions: this.permissions,
      };
    },
    async logout() {
      localStorage.removeItem('user-auth');
      localStorage.removeItem('setting');
      tokenStore.setToken('');
      tokenStore.setRefreshToken('');
      this.userName = '';
      this.roles = [];
      this.permissions = [];
    },
  },
  // persist: {},
});

export function getUserStore() {
  return useUserStore(store);
}
