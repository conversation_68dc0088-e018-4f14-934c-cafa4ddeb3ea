.t-layout__content {
  padding: 16px 16px 16px 10px !important;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  background: linear-gradient(to bottom, #D5EFFF, #ECE4FC, #EDF7FD);
  margin-bottom:60px;
  position: relative;
}
.t-layout {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(to bottom, #D5EFFF, #ECE4FC, #EDF7FD);
}
.t-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  .t-table__content {
    flex: 1;
  }
}
.t-table__pagination {
  padding-left: 0;
  padding-right: 0;
}
.t-pagination__btn-prev {
  display: flex;
  justify-content: flex-start;
}
.t-pagination__btn-next {
  display: flex;
  justify-content: flex-end;
}
.t-dialog--default {
  padding: 0;
}
.t-dialog__close {
  display: block;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
}
.t-dialog__header {
  padding: 10px 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}
.t-dialog__body {
  padding: 24px 32px !important;
  border-bottom: 1px solid #f2f5f9;
}
.t-dialog__footer {
  padding: 16px;
}

.t-table__header th {
  background-color: #f2f5f9 !important;
  font-weight: bold;
  color: var(--td-text-color-primary);
  border-bottom: none !important;
}

.t-menu--scroll,
.t-menu--scroll + .t-menu__operations {
  border-right: 1px solid var(--td-border-level-1-color);
}

.flex {
  display: flex;
  align-items: center;
}
.bold {
  font-weight: bold;
}
.form-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 20px;
  .item-name {
    white-space: nowrap;
    color: var(--td-text-color-primary);
  }
}
.section {
  background-color: #fff;
  padding: 20px 16px;
  // margin-top: 10px;
  border-radius: 16px;
}
.mb60{
  margin-bottom:60px;
}
.row-gap {
  margin-bottom: 20px;
}
.t-input--auto-width {
  width: 100%;
  height: 32px;
}
.line-css{
  color:#187fff;
  cursor: pointer;
  margin-right:10px;
}
.upload-flex{
  display: flex;
  flex-direction:column;
  color:#666;
  font-size:12px;
}
.wth300{
  width:300px;
}
.mt20{
  margin-top:20px;
}
.mt10{
  margin-top:10px;
}
.mt30{
  margin-top:30px;
}
.mb20{
  margin-bottom:20px;
}
.mr20{
  margin-right:20px;
}
.mr10{
  margin-right:10px;
}
.ml10{
  margin-left:10px;
}
// .opt-css{
//   color:#167FFF;
//   cursor: pointer;
//   margin-right:16px;
// }
.btn-css {
  padding: 0px 8px;
}
