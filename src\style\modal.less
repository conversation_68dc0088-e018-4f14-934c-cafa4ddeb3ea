.modal-class{
  .ant-modal-header{
    height: 72px;
    margin-bottom:0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f4f6fa;
    .ant-modal-title{
      font-weight: 600;
      font-size: 16px;
      color: #05082C;
      margin-left: 32px;
    }
  }
  .ant-modal-content{
    padding: 0;
    .ant-modal-close{
      position: absolute;
      right: 32px;
      top:27px;
    }
  }
  .ant-modal-body{
    padding: 24px 32px 0 32px;
  }
  .ant-modal-footer{
    height: 72px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-top: 1px solid #f4f6fa;
    padding-right: 32px;
  }
}

.modal-inform-class{
  .title-content{
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .back-box {
    margin: 16px 0;
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #495366;
      margin-bottom: 16px;
    }
  }
}

.drawer-class{
  .ant-drawer-header{
    padding: 16px;
    .drawer-title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-name{
        font-weight: 600;
        font-size: 16px;
        color: #05082C;
      }
    }
  }
  .ant-drawer-footer{
    padding: 12px 16px;
  }
}