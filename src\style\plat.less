.search-form{
  background-color:#FFFFFF;
  border-radius: 16px;
  padding: 32px 32px 16px 32px;
  margin:0 0 16px 0;
}
  .table-columns{
    background-color:#FFFFFF;
    border-radius: 16px;
    padding: 32px;
  }
  .message{
    font-size: 18px;
    border-bottom: 1px solid #dfdfdf;
    padding-bottom: 5px;
    margin-bottom: 24px;
  }
  .table-operate-box {
    padding-bottom: 16px;
    text-align: right;
  }
  .status-header{
    display: flex;
    justify-content: space-between;
    padding: 20px 50px;
    background-color: #fafafa;
    border-radius: 16px 16px 0 0;
    margin: 24px 0;
    font-size: 16px;
  }
  .form-item{
   .form-item-width{
    width: 410px;
    margin: 0 0 0 44px;
   }
    .form-submit{
      width: 100%;
      :deep(.ant-form-item-control-input-content){
      text-align: center;
    }
   }
   .desc{
    color: red;
    margin: 5px 10px 0 0;
    display: inline-block;
  }
  .descFooter{
    color: rgb(51, 51, 51);
    margin:5px 0 0 10px;
    background: rgb(255, 255, 255);
    display: inline-block;
  }
    .licenseAreaCode{   
      margin: -15px 0 15px 0;
      color: rgb(153, 153, 153);
      font-size: 12px;
    }
  }
  .flex{
    display: flex;
    justify-content: space-between;
  }
  .p1{
    font-size: 18px;
    text-align: center;
    font-weight: bold;
  }
  .f30{
    font-size: 30px;
  }
  .mb10{
    margin: 0 0 10px 0;
  }
  .mt32{
    margin: 32px 0 0 0;
  }
  .form-submit{
    width: 100%;
    text-align: center;
  }
  .table-operate-box-left{
    padding-bottom: 16px;
    text-align: left;
  }
  .table-operate-box {
    padding-bottom: 16px;
    text-align: right;
  }
  .table-operate{
    display: flex;
    justify-content: space-between;
  }
  .table-info{
    background-color:#FFFFFF;
    border-radius: 16px;
    padding: 32px;
    margin: 16px 0;
    .tab-line{
      width: 300px;
      height: 112px;
      padding: 24px;
      background: #F0F4F9;
      border-radius: 16px 16px 16px 16px;
    }
  }
  .opt-css{
    color:#1677ff;
    cursor: pointer;
    margin-right:20px;
  }
  .ant-btn-primary:disabled {
    color: rgba(0, 0, 0, 0.25) !important;
  }
 :deep(.ant-table-thead>tr>th){
  padding: 11.5px 16px;
 }
 :deep(.ant-table-tbody>tr>td){
  padding: 7px 16px;
 }
:deep(.ant-pagination){
  margin: 16px 0 0 0!important;
}