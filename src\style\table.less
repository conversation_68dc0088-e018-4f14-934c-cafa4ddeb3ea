.table-search{
  border-radius: 16px;
  background: #fff;
  padding: 32px 32px 8px 32px;
  .ant-form-item{
    margin-bottom: 24px;
  }
}
.table-main{
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  background: #fff;
  padding:32px 32px 16px 32px;
  box-sizing: border-box;
  margin-top: 16px;
  .table-button{
    padding-bottom: 16px;
    text-align: right;
  }
  .table-content{

  }
  .table-pagination{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
  }
}

.a-col-center{
  display: flex;
  align-items: flex-end;
}

.ant-table-body,.ant-table-content{
  // 设置滚动条的宽高
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  // 设置滚动条的圆角和颜色
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #e0e8ed;
  }

  // 设置滚动条轨道的背景色
  &::-webkit-scrollbar-track {
    border-radius: 5px;
    background: transparent;
  }

  // 设置边角位置的圆角与背景色
  &::-webkit-scrollbar-corner {
    border-radius: 5px;
    background: transparent;
  }
}

:where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper .ant-table-thead >tr>th{
  background: #f1f6f8;
  font-weight: 400;
  font-size: 14px;
  color: #636D7E;
}
:where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
  display: none;
}
:where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper .ant-table-thead>tr>th, :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper .ant-table-tbody>tr>td, :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper tfoot>tr>th, :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper tfoot>tr>td{
  padding: 12px 16px;
}
