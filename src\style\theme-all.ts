/**
 * 主题编辑器自定义样式（UI提供）
 * https://www.antdv.com/theme-editor-cn
 */

export default {
    "token": {
      "colorPrimary": "#1A7AF8",
      "colorPrimaryActive": "#0A5AD1",
      "colorPrimaryBg": "#E6F4FF",
      "colorPrimaryBgHover": "#BDE2FF",
      "colorPrimaryBorder": "#94CDFF",
      "colorPrimaryBorderHover": "#6BB5FF",
      "colorPrimaryHover": "#429AFF",
      "colorPrimaryText": "#1A7AF8",
      "colorPrimaryTextActive": "#0A5AD1",
      "colorPrimaryTextHover": "#429AFF",
      "colorSuccess": "#1bb599",
      "colorSuccessActive": "#0E8F7C",
      "colorSuccessBorder": "#8ADBC4",
      "colorSuccessBorderHover": "#B7E8D9",
      "colorSuccessBg": "#E6F5F0",
      "colorSuccessHover": "#61CFB3",
      "colorSuccessText": "#1BB599",
      "colorSuccessTextActive": "#0E8F7C",
      "colorSuccessTextHover": "#3CC2A5",
      "colorWarning": "#FF9B26",
      "colorWarningActive": "#D97716",
      "colorWarningBg": "#FFF9F3",
      "colorWarningBgHover": "#FFE2C0",
      "colorWarningBorder": "#FFCF96",
      "colorWarningBorderHover": "#F8B260",
      "colorWarningHover": "#F8B260",
      "colorWarningText": "#FF9B26",
      "colorWarningTextActive": "#D97716",
      "colorWarningTextHover": "#FFB34F",
      "colorError": "#ff436a",
      "colorErrorBg": "#FFF0F0",
      "colorErrorBgHover": "#FFE6E7",
      "colorErrorBorder": "#FFBDC3",
      "colorErrorBorderHover": "#FF94A2",
      "colorErrorHover": "#FF94A2",
      "colorErrorActive": "#FF436A",
      "colorErrorTextHover": "#FF6B84",
      "colorErrorText": "#FF436A",
      "colorErrorTextActive": "#D92E55",
      "colorInfo": "#1a7af8",
      "colorInfoBg": "#E6F5FF",
      "colorInfoBgHover": "#BDE2FF",
      "colorInfoBorder": "#94CDFF",
      "colorInfoBorderHover": "#6BB5FF",
      "colorInfoHover": "#6BB5FF",
      "colorInfoActive": "#0A5AD1",
      "colorInfoText": "#1A7AF8",
      "colorInfoTextHover": "#429AFF",
      "colorInfoTextActive": "#0A5AD1",
      "colorTextBase": "#05082c",
      "colorText": "#05082c",
      "colorTextSecondary": "#495366",
      "colorTextTertiary": "#636d7e",
      "colorTextQuaternary": "#818999",
      "colorBorder": "#E0E8ED",
      "colorBorderSecondary": "#E0E8ED",
      "colorFill": "#e1eaf6",
      "colorFillSecondary": "#eaeff5",
      "colorFillTertiary": "#f0f4f9",
      "colorFillQuaternary": "#f7faff",
      "colorBgLayout": "#f5f5f5",
      "colorBgMask": "rgba(5, 8, 44, 0.45)"
    },
    "components": {
      // "Menu": {
      //   "itemMarginInline": 0,
      //   "padding": 10,
      //   "colorItemBgHover": "#F7FAFF",
      //   "colorSubItemBg": "rgba(0, 0, 0, 0)",
      //   "lineType": "none",
      //   "colorItemText": "#04152F",
      //   "colorItemTextSelected": "#1A7AF8",
      //   "controlHeightLG":48,
      // },
      // "Table": {
      //   "colorFillAlter": "#F0F1F9",
      //   "colorLink": "#1A7AF8",
      //   "colorLinkActive": "#0A5AD1",
      //   "colorLinkHover": "#429AFF",
      //   "colorIcon": "#636D7E",
      //   "colorIconHover": "#04152F",
      //   "colorFillContent": "#E0E8ED",
      //   "colorSplit": "##E0E8ED",
      //   "colorBorderSecondary": "#E0E8ED",
      //   "controlHeight": 40
      // },
      // "Button": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32,
      //   "paddingContentHorizontal": 23,
      //   "colorLink": "#1A7AF8",
      //   "colorText": "#1A7AF8",
      //   "colorLinkActive": "#1A7AF8",
      //   "colorLinkHover": "#1A7AF8",
      //   "controlOutlineWidth": 0,
      // },
      // "Input": {
      //   "controlHeight": 40,
      //   "controlHeightSM": 32,
      //   "controlHeightLG": 48,
      //   "colorTextPlaceholder":"#AEB9CE"
      // },
      // "InputNumber": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32
      // },
      // "Mentions": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32
      // },
      // "Radio": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32,
      //   "controlOutlineWidth": 2,
      //   "padding": 24
      // },
      // "Select": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32,
      //   "controlHeightXS": 24
      // },
      // "Slider": {
      //   "colorPrimaryBorder": "#1A7AF8",
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32
      // },
      // "Switch": {
      //   "controlHeight": 40
      // },
      // "Transfer": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "listWidth": 250,
      //   "listHeight": 270,
      //   "listWidthLG": 320
      // },
      // "TreeSelect": {
      //   "controlHeightSM": 32,
      //   "controlInteractiveSize": 24,
      //   "paddingXS": 24
      // },
      // "Calendar": {
      //   "paddingSM": 12
      // },
      // "DatePicker": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32
      // },
      // "Pagination": {
      //   "controlHeight": 40,
      //   "controlHeightLG": 48,
      //   "controlHeightSM": 32,
      //   "colorFill": "#fff",
      // },
      // "Dropdown": {
      //   "controlHeight": 40
      // },
      // "Cascader": {
      //   "controlHeight": 40
      // },
      // "Checkbox": {
      //   "controlInteractiveSize": 20
      // },
      // "Tag": {
      //   "fontSizeSM": 16,
      //   "fontSize": 16,
      //   "marginXS": 8,
      //   "paddingXXS": 8
      // },
      // "Tree": {
      //   "controlHeightSM": 32,
      //   "controlInteractiveSize": 24,
      //   "paddingXS": 8
      // },
      // "Anchor": {
      //   "padding": 16,
      //   "paddingXXS": 4
      // },
      // "Card": {
      //   "fontSize": 14,
      //   "paddingLG": 24,
      //   "padding": 18,
      //   "paddingXS": 16,
      //   "borderRadiusLG": 16,
      // },
      // "Form": {
      //   "controlHeight": 40,
      //   "colorTextHeading":"#495366",
      // },
      // "Descriptions": {
      //   "padding":24
      // },
    }
  }
  