// 全局样式，常用边距
.mt10 {
  margin-top: 10px;
}

// Ant design dropdown-menu 组件配合UI修改

// 按钮样式
.ant-btn-primary {
  background-color: @button-primary-bg-color;
  &:not(:disabled):hover {
    background: @button-primary-bg-color;
  }
}
.ant-btn-default {
  border-color: @button-default-border-color;
  color: @button-default-color;
  &:not(:disabled):hover {
    border-color: @button-default-hover-border-color;
    color: @button-default-hover-color;
  }
}
.ant-btn-link {
  color: @button-link-color;
  &:not(:disabled):hover {
    color: @button-link-color;
  }
}

//========== 表格样式 ===================================
.ant-table {
  color: @global-color !important;
  scrollbar-width: 3px;
  scrollbar-color: #888 #eee;
  .ant-table-thead {
    tr {
      th {
        color: @table-th-color;
        font-weight: 600;
        background: @table-th-bg;
        padding: 12px 0 11px 8px;
        &:not(&:last-child) {
          &:not(.ant-table-selection-column) {
            &:not(.ant-table-row-expand-icon-cell) {
              &:not([colspan]) {
                &::before {
                  background-color: @table-th-border-color !important;
                }
              }
            }
          }
        }
      }
    }
  }
  .ant-table-tbody {
    > tr {
      > td {
        padding: 12px 0 12px 8px;
        border-top-color: @table-td-border-color !important;
      }
    }
  }
  .ant-table-body {
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 5px;
    }
  }
}

//========== 分页样式 ===================================
.ant-pagination {
  color: @pagination-color;
  .ant-pagination-item a {
    color: @global-color;
  }
  .ant-pagination-item-active {
    border-color: @pagination-border-color !important;
    a {
      color: @pagination-button-active-color !important;
    }
  }
  .ant-pagination-options-quick-jumper {
    color: @pagination-jump-color;
    input {
      border-color: @form-border-color;
      color: @global-color;
    }
  }
}

//========== 表单样式 ====================================
//====== label ====== 样式
.ant-form-item {
  color: @form-label-color;
}
.ant-form-item-label {
  > label {
    color: @form-label-color !important;
  }
}
//===== 数字文本框样式 ==========
.ant-input-number {
  border-color: @form-border-color;
  .ant-input-number-handler {
    border-inline-start-color: @form-border-color;
  }
  .ant-input-number-handler-down {
    border-block-start-color: @form-border-color;
  }
}
.ant-input-number-input {
  color: @global-color !important;
}
.ant-input-number-group-addon {
  border-color: @form-border-color !important;
  color: @global-color !important;
}
//======= 下拉选择框样式 =========
.ant-select {
  color: @global-color;
  &:not(.ant-select-customize-input) {
    .ant-select-selector {
      border-color: @form-border-color;
    }
  }
  .ant-select-arrow {
    color: @form-icon-color;
  }
  .ant-select-selection-placeholder {
    color: @form-placeholder-color;
  }
}
.ant-select-single {
  .ant-select-selector {
    color: @global-color;
  }
}
.ant-select-dropdown {
  color: @global-color;
  .ant-select-item {
    color: @global-color;
  }
  .ant-select-item-option-active {
    &:not(.ant-select-item-option-disabled) {
      background-color: @dropdown-bg-color;
    }
  }
}
//========= 普通输入框文本样式 ===========
.ant-input-affix-wrapper {
  color: @global-color;
  border-color: @form-border-color;
  input {
    &::placeholder {
      color: @form-placeholder-color;
    }
  }
}
//======== 日期选择插件样式 ===========
.ant-picker {
  border-color: @form-border-color;
  .ant-picker-input {
    input {
      color: @global-color;
      border-color: @form-border-color;
    }
  }
  .ant-picker-suffix {
    color: @form-icon-color;
  }
}
//========== 层级下拉选择插件样式 ==========
.ant-cascader-dropdown {
  .ant-cascader-checkbox {
    &.ant-cascader-checkbox-indeterminate {
      .ant-cascader-checkbox-inner {
        &::after {
          background-color: @form-border-selected-color;
        }
      }
    }
    .ant-cascader-checkbox-inner {
      border-color: @form-border-color;
    }
  }
  .ant-cascader-checkbox-checked {
    .ant-cascader-checkbox-inner {
      background-color: @form-border-selected-color;
      border-color: @form-border-selected-color;
    }
  }
}
.ant-cascader-menu-item-expand-icon {
  color: @form-border-color;
}
//========== checkbox 样式 =============
.ant-checkbox {
  .ant-checkbox-inner {
    border-color: @form-border-color;
  }
}
// .ant-checkbox-checked {
//   .ant-checkbox-inner {
//     background-color: @form-border-selected-color;
//     border-color: @form-border-selected-color;
//   }
// }
//========= 单选框 样式 ==============
.ant-radio-wrapper {
  .ant-radio-inner {
    border-color: @form-border-color;
  }
  .ant-radio-checked {
    .ant-radio-inner {
      border-color: @form-border-selected-color;
      background-color: @form-border-selected-color;
    }
  }
}
