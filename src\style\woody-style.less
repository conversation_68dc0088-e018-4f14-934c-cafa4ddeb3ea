// Ant design Tag 标签配合UI修改色阶，对应CSS标签
/* Tag成功标签  */
.woody-tag-cg {
  color: #0e8f7c;
  background: #e6f5f0;
  border-color: #8adbc4;
}

/* Tag失败标签  */
.woody-tag-sb {
  color: #ff436a;
  background: #fff0f0;
  border-color: #ffbdc3;
}

/* Tag代办标签  */
.woody-tag-db {
  color: #ff9b26;
  background: #fff9f3;
  border-color: #ffcf96;
}

/* Tag进行中标签  */
.woody-tag-jxz {
  color: #1a7af8;
  background: #e6f5ff;
  border-color: #94cdff;
}

// Ant design table 表格对照UI修改样式
/* table 对应修改表格头部背景，字体，颜色大小权重 */
.ant-table-thead > tr > th {
  background-color: #f1f6f8 !important;
  color: #636d7e !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

/* table 对应修改表格头部背景，字体，颜色大小权重 */
.ant-pagination-total-text {
  margin-right: auto !important;
}

// Ant design button 样式对照UI修改
/* 解决按钮type="link" 状态下存在padding 状态 */
.woody-btn-link {
  padding: 0 !important;
}

/* 解决按钮type="link"状态下，切换字体颜色变成黑色问题，应该和腾讯的UI组件库有冲突 */
.ant-btn-link {
  // color: #1677ff !important;
}
.ant-btn-primary:disabled {
    color: rgba(0, 0, 0, 0.25) !important;
  }

/* 解决按钮type="button"状态下，切换字体颜色变成黑色问题，应该和腾讯的UI组件库有冲突 */
.ant-btn-primary {
  color: #ffffff !important;
}
.ant-btn-background-ghost {
  color: #1677ff !important;
}
.ant-btn-dangerous {
  color: #ff4d4f !important;
}
