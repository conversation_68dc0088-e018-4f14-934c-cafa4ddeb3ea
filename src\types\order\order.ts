export interface order {
  id: number;
  orderCode: string;
  channelOrderCode: string;
  orderType: string;
  orderChannel: string;
  channelShopId: string;
  channelShopName: string;
  shopName: string;
  nickName: string;
  phoneNum: string;
  orderTime: string;
  orderStatus: string;
  money: string;
  point: string;
  refundMoney: string;
  refundPoint: string;
}

interface orderData {
  records: order[];
  total: number;
}
export interface orderParams {
  page: number;
  size: number;
  time: Array<string>;
  startTime: string;
  endTime: string;
  orderCode: string;
  channelOrderCode: string;
  channelShopName: string;
}
export class OrderTableData {
  data: orderData = {
    records: [],
    total: 0,
  };

  params: orderParams = {
    page: 1,
    size: 10,
    time: [],
    startTime: '',
    endTime: '',
    orderCode: '',
    channelOrderCode: '',
    channelShopName: '',
  };
}

export interface orderExportParams {
  startTime: string;
  endTime: string;
  orderCode: string;
  channelOrderCode: string;
  channelShopName: string;
}
