export interface examineItem {
  prodIds: string;
  prodSource: string;
  supplierIds: string;
  goodsImg: string;
  prodName: string;
  applyTime: string;
  examineStatus: string;
}

export interface examineParams {
  page: number;
  size: number;
  prodSource: string;
  prodName: string;
  prodIds: Array<string>;
  supplierIds: Array<string>;
  time: Array<string>;
  applyStartTime: string;
  applyEndTime: string;
  productAuditNodeType: number;
}
interface examineData {
  records: examineItem[];
  total: number;
}

export class ExamineTableData {
  params: examineParams = {
    page: 1,
    size: 10,
    prodSource: '',
    prodName: '',
    prodIds: [],
    supplierIds: [],
    time: [],
    applyStartTime: '',
    applyEndTime: '',
    productAuditNodeType: null,
  };

  data: examineData = {
    records: [],
    total: 0,
  };
}
