export interface lifeShop {
  shopId: string;
  lifeShopName: string;
  lifeShopTel: string;
  pvRatio: number;
}
export interface lifeShopParams {
  page: number;
  size: number;
  lifeShopName: string;
  lifeShopTel?: string;
}
interface lifeShopData {
  records: lifeShop[];
  total: number;
}

export class LifeShopTableData {
  params: lifeShopParams = {
    page: 1,
    size: 10,
    lifeShopName: '',
    lifeShopTel: '',
  };

  data: lifeShopData = {
    records: [],
    total: 0,
  };
}

export interface lifeShopRateParam {
  lifeShopId: string;
  channelShopId: string;
  pointRate?: number;
  bindingStatus?: number;
  lifeShopName?: string;
}
