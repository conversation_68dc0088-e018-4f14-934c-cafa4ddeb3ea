export interface logHistory {
  id: number;
  createUserName: string;
  createTime: string;
  logDesc: string;
}

export interface logHistoryParams {
  page: number;
  size: number;
  startTime: string;
  endTime: string;
  createUserName: string;
}
interface logHistoryData {
  records: logHistory[];
  total: number;
}

export class LogHistoryTableData {
  data: logHistoryData = {
    records: [],
    total: 0,
  };

  params: logHistoryParams = {
    page: 1,
    size: 10,
    startTime: '',
    endTime: '',
    createUserName: '',
  };
}
