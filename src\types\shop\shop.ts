export interface shop {
  id: number;
  channelShopId: string;
  channelShopName: string;
  channelShopStatus: number;
  lifeShopName: string;
  lifeShopId?: string;
  lifeShopTel: string;
  lifeShopStatus?: number;
  pvPercent?: number;
  bindingTime: string;
  thawingTime?: string;
}

export interface shopParams {
  page: number;
  size: number;
  channelShopName: string;
  channelShopStatus?: number;
}

interface shopData {
  records: shop[];
  total: number;
}

export class ShopTableData {
  params: shopParams = {
    page: 1,
    size: 10,
    channelShopName: '',
    channelShopStatus: null,
  };

  data: shopData = {
    records: [],
    total: 0,
  };
}
