import { RouteRecordRaw } from "vue-router";

export function findBreadcrumbs(
  routes: RouteRecordRaw[],
  currentPath: string
): RouteRecordRaw[] {
  const breadcrumbs: RouteRecordRaw[] = [];

  const recursiveSearch = (
    routes: RouteRecordRaw[],
    path: string,
    parentPaths: RouteRecordRaw[] = []
  ): boolean => {
    for (const route of routes) {
      const fullPath = route.path.startsWith("/")
        ? route.path
        : (parentPaths[parentPaths.length - 1]?.path || "") + "/" + route.path;

      if (fullPath === path) {
        breadcrumbs.push(...parentPaths, route);
        return true;
      }

      if (route.children && route.children.length > 0) {
        const found = recursiveSearch(
          route.children,
          path,
          [...parentPaths, { ...route, path: fullPath }]
        );
        if (found) return true;
      }
    }
    return false;
  };

  recursiveSearch(routes, currentPath);
  return breadcrumbs;
}
