export function moveElementToFront(arr: Array<any>, index: number) {
  if (index >= 0 && index < arr.length) {
    const element = arr.splice(index, 1)[0];
    arr.unshift(element);
  }
  return arr;
}

export function moveElementForward(arr: Array<any>, index: number) {
  if (index > 0 && index < arr.length) {
    const element = arr.splice(index, 1)[0];
    arr.splice(index - 1, 0, element);
  }
  return arr;
}

export function moveElementBackward(arr: Array<any>, index: number) {
  if (index >= 0 && index < arr.length - 1) {
    const element = arr.splice(index, 1)[0];
    arr.splice(index + 1, 0, element);
  }
  return arr;
}

export function moveElementToEnd(arr: Array<any>, index: number) {
  if (index >= 0 && index < arr.length) {
    const element = arr.splice(index, 1)[0];
    arr.push(element);
  }
  return arr;
}
