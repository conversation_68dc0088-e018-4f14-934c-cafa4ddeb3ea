import { PIC_PREFIX, PIC_TAG } from "@/config/global";

import { queryCategory } from "@/api/common";
/**
 * 判断是否为字符串类型
 * @param val 判断的值
 * @returns Boolean 类型
 */
export const isString = (val: string): Boolean => typeof val === "string";

/**
 * 判断是否为对象
 * @param obj 判断的数据
 * @returns Boolean 类型
 */
export const isObj = (obj: Object): Boolean =>
  Object.prototype.toString.call(obj) === "[object Object]";

/**
 * 判断是否为空值
 * @param val 判断的值
 * @returns Boolean 类型
 */
export const isEmptyValue = (val: any): Boolean => {
  if (
    val === null ||
    val === undefined ||
    (isString(val) && !val.trim().length)
  ) {
    return true;
  }
  return false;
};

/**
 * 展开所有数据，以Map形式展示。（柯里化，方便后续根据 key 值拿到对应的数据）
 * @param data 处理的数据，数组类型
 * @param keys 用于扩展后的key名，后续根据这个key取指定数据。数组形式
 * @returns Map 类型
 */
export const expendData = (
  data: Array<any>,
  keys: Array<string>,
  childrenName = "areas"
): Object => {
  const map = new Map();
  const fn = (data) => {
    if (Array.isArray(data)) {
      data.forEach((item) => {
        const children = item[childrenName];
        delete item[childrenName];
        if (Array.isArray(keys)) {
          keys.forEach((key) => {
            map.set(item[key], item);
          });
        }
        if (Array.isArray(children)) {
          fn(children);
        }
      });
    }
  };
  fn(JSON.parse(JSON.stringify(data)));
  return map;
};

/**
 * 将平铺数据转换成 tree
 * @param data 需要处理的数据，数组类型
 * @returns 数组类型
 */
export const setTreeData = (data: Array<object>): Array<object> => {
  if (Array.isArray(data)) {
    const result: Array<object> = [];
    const resultMap: Map<number, any> = new Map();
    let level = 1;
    const fn = (data, map) => {
      const operationMap = new Map();
      const childrenData: Array<object> = [];
      data.forEach((item) => {
        if (item.level === level) {
          const parent = map.get(item.parentId);
          operationMap.set(item.id, item);
          if (isObj(parent)) {
            if (!Array.isArray(parent.children)) {
              parent.children = [];
            }
            item.parentName = parent.categoryName;
            parent.children.push(item);
          } else {
            resultMap.set(item.id, item);
          }
        } else {
          childrenData.push(item);
        }
      });
      if (0 in childrenData) {
        level++;
        fn(childrenData, operationMap);
      }
    };
    fn(data, resultMap);

    resultMap.forEach((item) => {
      result.push(item);
    });

    return result;
  }
  return data;
};

export const uploadPicPath = (val: string): string => {
  if (!isString(val) || isEmptyValue(val)) {
    return "";
  }
  let arr = val.split(",");
  arr = arr.map((url, index) => {
    if (index === 0) {
      url = url.replace(PIC_PREFIX, `${PIC_PREFIX}${PIC_TAG}`);
    } else {
      url = url.replace(PIC_PREFIX, "");
    }
    return url;
  });
  return arr.join(",");
};

/**
 * 匹配图片格式
 * @param val 处理的数据，字符串类型
 * @returns 字符串类型
 */
export const setPicPath = (val: string): string => {
  if (!isString(val) || isEmptyValue(val)) {
    return "";
  }
  const arr = val.split(",");
  const mapArr = arr.map((item) => {
    if (item.indexOf(`${PIC_PREFIX}${PIC_TAG}`) === -1) {
      if (item.indexOf(PIC_PREFIX) === -1) {
        return `${PIC_PREFIX}${item}`;
      }
      return item;
    }
    return item.replace(`${PIC_PREFIX}${PIC_TAG}`, PIC_PREFIX);
  });
  return mapArr.join(",");
};

/**
 * 解决图片因缓存问题导致不展示的情况
 * @param src 图片地址，字符串类型
 * @returns 字符串类型
 */
export const showImg = (src: string): string => {
  const index = src.lastIndexOf("?");
  src += `${index !== -1 ? "&" : "?"}update=${new Date().getTime()}`;
  return src;
};

// 处理后台分类
export async function fetchCategory() {
  const res = await queryCategory();
  if (res.data) {
    res.data = dealWithAll(res.data);
    res.data.forEach((item: any) => {
      if (item.newCategoryModelDtos?.length) {
        item.newCategoryModelDtos = dealWithAll(item.newCategoryModelDtos);
        item.newCategoryModelDtos.forEach((items: any) => {
          if (items.newCategoryModelDtos?.length) {
            items.newCategoryModelDtos = dealWithAll(
              items.newCategoryModelDtos
            );
          }
        });
      }
    });
  }
  return res.data;
}

function dealWithAll(arr: any) {
  arr.unshift({
    categoryId: "",
    categoryName: "全部",
  });
  return arr;
}
