// 删除对象中的空数组为null
export function setEmptyArraysToNull(obj) {
  Object.keys(obj).forEach((key) => {
    const value = obj[key];
    // 如果值是数组且为空，则设置为null
    if (Array.isArray(value) && value.length === 0) {
      obj[key] = null;
    } else if (typeof value === "object" && value !== null) {
      // 如果值是对象，则递归调用函数
      setEmptyArraysToNull(value);
    }
  });
  return obj;
}

//在url后拼接查询参数
export function appendParamsToUrl(
  url: string,
  params: Record<string, any>
): string {
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(
      ([key, value]) => value !== null && value !== undefined && value !== ""
    )
  );

  const queryString = new URLSearchParams(
    filteredParams as Record<string, string>
  ).toString();

  return queryString ? `${url}?${queryString}` : url;
}

// 如果某个节点的children为空数组，则删除该节点的children属性
export const removeEmptyChildren = (arr) => {
  return arr.map((item) => {
    if (item.children) {
      item.children = removeEmptyChildren(item.children);
      if (item.children.length === 0) {
        delete item.children;
      }
    }
    return item;
  });
};
