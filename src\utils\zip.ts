import J<PERSON>Z<PERSON> from 'jszip';
import FileSaver from 'file-saver';

// 通过url 转为blob格式的数据
function getImgArrayBuffer(url) {
  // let _this = this;
  return new Promise((resolve, reject) => {
    // 通过请求获取文件blob格式
    const xmlhttp = new XMLHttpRequest();
    xmlhttp.open('GET', url, true);
    xmlhttp.responseType = 'blob';
    xmlhttp.onload = function () {
      if (this.status === 200) {
        resolve(this.response);
      } else {
        reject(this.status);
      }
    };
    xmlhttp.send();
  });
}
// imgDataUrl 数据的url数组
export function downImg(imagesParams, callback) {
  // let that = this;
  const zip = new JSZip();
  const cache = {};
  const promises = [];
  let num = 0;
  // that.title = '正在加载压缩文件';
  for (const item of imagesParams) {
    // eslint-disable-next-line no-loop-func
    const promise = getImgArrayBuffer(item.url).then((data: any) => {
      // 下载文件, 并存成ArrayBuffer对象(blob)
      const keys = Object.keys(zip.files);

      if (item.title.includes('.')) {
        if (keys.includes(item.title)) {
          num += 1;
          const arr = item.title.split('.');
          item.title = `${arr[0]}(${num}).${arr[1]}`;
        }
      } else {
        const formatPic = data.type.split('/')[1];
        const str = `${item.title}.${formatPic}`;
        if (keys.includes(str)) {
          num += 1;
          item.title = `${item.title}(${num}).${formatPic}`;
        } else {
          item.title = str;
        }
      }

      zip.file(item.title, data, { binary: true }); // 逐个添加文件
      cache[item.title] = data;
    });
    promises.push(promise);
  }
  Promise.all(promises)
    .then(() => {
      zip.generateAsync({ type: 'blob' }).then((content) => {
        // that.title = '正在压缩';
        // 生成二进制流
        // console.log(content, 'content');
        const year = new Date().getFullYear();
        const month = new Date().getMonth();
        const date = new Date().getDate();
        const hour = new Date().getHours();
        const minutes = new Date().getMinutes();
        FileSaver.saveAs(content, `图片下载-${year}年${month + 1}月${date}日${hour}时${minutes}分.zip`); // 利用file-saver保存文件  自定义文件名
        // that.title = '压缩完成';
        callback();
      });
    })
    .catch((res) => {
      // that.$message.error('文件压缩失败');
    });
}
